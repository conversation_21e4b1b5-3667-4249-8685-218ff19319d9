import { resolve } from 'path'
import { defineConfig, mergeConfig } from 'vitest/config'
import viteConfig from './vite.config'

export default mergeConfig(
  viteConfig,
  defineConfig({
    resolve: {
      alias: {
        '@tests': resolve(__dirname, './tests'),
        // This is needed to include vitest setup files into testing library: https://github.com/testing-library/vue-testing-library/issues/279
        '@vue/test-utils': '/node_modules/@vue/test-utils/dist/vue-test-utils.cjs.js',
      },
    },
    server: {
      strictPort: false,
    },
    test: {
      clearMocks: true,
      environment: 'jsdom',
      /*
       * Vitest 3 no longer provides default fakeTimers.toFake options.
       * Now, Vitest will mock any timer-related API if it is available (except nextTick).
       * See:
       * - https://vitest.dev/guide/migration.html#fake-timers-defaults
       * - https://github.com/vitest-dev/vitest/issues/7288
       * - https://github.com/sinonjs/sinon/issues/2620
       */
      fakeTimers: {
        toFake: [
          'Date',
          'setTimeout',
          'clearTimeout',
          'setImmediate',
          'setInterval',
          'clearInterval',
        ],
      },
      globals: true,
      globalSetup: './tests/vitest.global.ts',
      outputFile: './var/logs/vitest.xml',
      setupFiles: './tests/vitest.setup.ts',
      unstubGlobals: true,
    },
  })
)
