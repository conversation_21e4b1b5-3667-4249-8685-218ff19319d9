/**
 * BASE STYLES
 */
@import 'tailwindcss/base';
@import 'base/sortable.css';
@import 'base/elements.css';
@import 'base/typography.css';
@import 'base/selection.css';
@import 'base/page-content.css';
@import 'base/variables.css';
@import 'floating-vue/dist/style.css';

/**
 * COMPONENTS
 */
@import 'tailwindcss/components';
@import 'component/collapsible-section.css';
@import 'component/data-group.css';
@import 'component/document-section.css';
@import 'component/document-widget.css';
@import 'component/document-widget-placeholder.css';
@import 'component/form/message.css';
@import 'component/form/widget/base-local-group-money.css';
@import 'component/form/widget/item-value.css';
@import 'component/status.css';
@import 'component/table/base.css';
@import 'component/table/data-table-wrapper.css';
@import 'component/table/table-data.css';
@import 'component/table/table-header.css';
@import 'component/table/layout/aside-information-table.css';
@import 'component/table/layout/bordered-table.css';
@import 'component/table/layout/condensed-table.css';
@import 'component/table/layout/data-table.css';
@import 'component/table/layout/hovered-table.css';
@import 'component/layout-template-table.css';
@import 'component/lists.css';
@import 'component/icons.css';

/**
 * UTILITIES
 */
@import 'tailwindcss/utilities';

@layer base {
  /* ==========================================================================
     DEBUGGING HELPERS
     ========================================================================== */

  /* Development debugging utilities - commented out for production */
  *,
  ::before,
  ::after {
    /* Add outline to everything for layout debugging */

    /*
    outline: 2px solid lime;
    */

    /* Add background colour to help view element layering */

    /*
    background-color: hsl(0 100% 50% / .1);
    */
  }

  /* ==========================================================================
     WEBKIT DATETIME INPUT NORMALIZATION
     ========================================================================== */

  /*
   * WebKit-specific fixes for datetime inputs to ensure consistent appearance
   * across browsers. These normalize padding and display behavior.
   */
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-date-and-time-value {
    min-height: theme('lineHeight.6');
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  /* Remove default padding from all datetime edit components */
  ::-webkit-datetime-edit,
  ::-webkit-datetime-edit-year-field,
  ::-webkit-datetime-edit-month-field,
  ::-webkit-datetime-edit-day-field,
  ::-webkit-datetime-edit-hour-field,
  ::-webkit-datetime-edit-minute-field,
  ::-webkit-datetime-edit-second-field,
  ::-webkit-datetime-edit-millisecond-field,
  ::-webkit-datetime-edit-meridiem-field {
    padding-bottom: 0;
    padding-top: 0;
  }

  /* ==========================================================================
     INPUT PLACEHOLDER STYLING
     ========================================================================== */

  /* Consistent placeholder styling across all input types */
  input::placeholder,
  textarea::placeholder {
    color: theme('colors.gray.500');
    opacity: 1;
  }

  /* ==========================================================================
     CHECKBOX AND RADIO BUTTON CONTROLS
     ========================================================================== */

  /*
   * Custom checkbox and radio button styling that replaces browser defaults
   * with consistent, accessible controls across all browsers.
   */
  [type='checkbox'],
  [type='radio'] {
    /* Base styling - replaces browser defaults */
    appearance: none;
    background-color: theme('colors.white');
    background-origin: border-box;
    border-color: theme('colors.gray.500');
    border-width: theme('borderWidth.DEFAULT');
    color: theme('colors.blue.600');
    cursor: pointer;
    display: inline-block;
    flex-shrink: 0;
    height: theme('spacing.4');
    padding: 0;
    print-color-adjust: exact;
    user-select: none;
    vertical-align: middle;
    width: theme('spacing.4');

    --tw-shadow: 0 0 transparent;

    /* Focus state styling with custom ring colors */
    &:focus {
      border-color: theme('colors.blue.600');
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      outline: var(--app-outline-width) solid transparent;
      outline-offset: var(--app-outline-offset);

      --tw-ring-color: theme('ringColor.skin.base');
      --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
      --tw-ring-offset-width: var(--app-ring-offset-width);
      --tw-ring-offset-color: theme('colors.white');
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
        var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
        calc(var(--app-ring-width) + var(--tw-ring-offset-width)) var(--tw-ring-color);
    }

    /* Checked state base styling */
    &:checked {
      background-color: currentcolor;
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-color: transparent;

      /* Maintain checked state on hover and focus */
      &:hover,
      &:focus {
        background-color: currentcolor;
        border-color: transparent;
      }
    }
  }

  /* Checkbox-specific styling */
  [type='checkbox'] {
    border-radius: theme('borderRadius.DEFAULT');

    /* Checkmark icon for checked state */
    &:checked {
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");

      /* High contrast mode fallback */
      @media (forced-colors: active) {
        appearance: auto;
      }
    }

    /* Indeterminate state (partially checked) */
    &:indeterminate {
      background-color: currentcolor;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
      background-position: center;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      border-color: transparent;

      /* Maintain indeterminate state on hover and focus */
      &:hover,
      &:focus {
        background-color: currentcolor;
        border-color: transparent;
      }

      /* High contrast mode fallback */
      @media (forced-colors: active) {
        appearance: auto;
      }
    }
  }

  /* Radio button-specific styling */
  [type='radio'] {
    border-radius: 100%;

    /* Radio dot icon for checked state */
    &:checked {
      background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");

      /* High contrast mode fallback */
      @media (forced-colors: active) {
        appearance: auto;
      }
    }
  }

  /* ==========================================================================
     FORM LAYOUT AND BASE ELEMENTS
     ========================================================================== */

  /* Base form container with grid layout */
  form {
    display: grid;
    gap: var(--app-form-field-spacing);
    grid-template-columns: theme('gridTemplateColumns.1');
  }

  /* Typography normalization for form elements */
  button,
  label,
  select,
  textarea {
    font-family: theme('fontFamily.sans');
    font-size: theme('fontSize.base');
    line-height: theme('lineHeight.normal');
    margin: 0;
  }

  /* Form label styling */
  .form-label {
    color: theme('textColor.skin.label');
    font-size: theme('fontSize.sm');
    font-weight: theme('fontWeight.medium');
  }

  /* Interactive element cursor styling */
  button,
  [type='button'],
  [type='checkbox'],
  [type='radio'],
  [type='reset'],
  [type='submit'],
  label,
  select {
    cursor: pointer;
  }

  /* ==========================================================================
     TEXT INPUT CONTROLS
     ========================================================================== */

  /*
   * Comprehensive styling for all text-based input types including textareas.
   * Normalizes appearance across browsers and applies custom design system colors.
   */
  [type='color'],
  [type='date'],
  [type='datetime'],
  [type='datetime-local'],
  [type='email'],
  [type='month'],
  [type='number'],
  [type='password'],
  [type='search'],
  [type='tel'],
  [type='text'],
  [type='time'],
  [type='url'],
  [type='week'],
  input:where(:not([type])),
  [multiple],
  textarea {
    /* Remove browser default styling */
    appearance: none;

    /* Visual styling using design system variables */
    background-color: theme('colors.white');
    border-color: var(--app-input-color-border);
    border-radius: var(--app-input-border-radius);
    border-width: var(--app-input-border-width);
    box-shadow: var(--app-input-shadow);
    display: block;

    /* Typography */
    font-family: theme('fontFamily.sans');
    font-size: theme('fontSize.base');
    line-height: theme('lineHeight.tight');
    padding: var(--app-input-padding-y) var(--app-input-padding-x) var(--app-input-padding-y)
      var(--app-input-padding-x);
    width: theme('width.full');

    /* Tailwind shadow reset */
    --tw-shadow: 0 0 transparent;

    /* Interactive states */
    &:hover {
      border-color: var(--app-input-color-border-hover);
    }

    &:focus {
      border-color: var(--app-input-color-border-focus);
      box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
      outline: var(--app-outline-width) solid transparent;
      outline-offset: var(--app-outline-offset);

      /* Focus ring styling */
      --tw-ring-inset: var(--tw-empty, /*!*/ /*!*/);
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: theme('colors.white');
      --tw-ring-color: var(--app-input-color-ring);
      --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width)
        var(--tw-ring-offset-color);
      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width))
        var(--tw-ring-color);
    }
  }

  /* ==========================================================================
     SELECT DROPDOWN CONTROLS
     ========================================================================== */

  /*
   * Custom select dropdown styling with consistent arrow icon.
   * Handles both single and multiple select variations.
   */
  select {
    /* Custom dropdown arrow icon */
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--app-select-arrow-spacing) center;
    background-repeat: no-repeat;
    background-size: var(--app-select-arrow-size) var(--app-select-arrow-size);

    /* Layout and spacing */
    height: auto;
    margin: 0;
    min-width: theme('spacing.15');
    padding: var(--app-input-padding-y) var(--app-select-padding-right) var(--app-input-padding-y)
      var(--app-input-padding-x); /* Reserve space for dropdown arrow */

    /* Essential padding for select elements */
    print-color-adjust: exact;
  }

  /* Multiple select and sized select variations - remove dropdown arrow */
  [multiple],
  [size]:where(select:not([size='1'])) {
    background-image: initial;
    background-position: initial;
    background-repeat: unset;
    background-size: initial;
    padding-right: var(--app-input-padding-x);
    print-color-adjust: unset;
  }

  /* ==========================================================================
     SEARCH INPUT SPECIFIC STYLING
     ========================================================================== */

  /* WebKit search input cancel button positioning */
  [type='search']::-webkit-search-cancel-button {
    position: relative;
  }

  /* ==========================================================================
     FILE INPUT CONTROLS
     ========================================================================== */

  /*
   * File input styling that resets browser defaults while maintaining
   * accessibility and providing consistent height.
   */
  [type='file'] {
    /* Reset browser styling */
    background: unset;
    border-color: inherit;
    border-radius: 0;
    border-width: 0;
    font-size: unset;

    /* Custom height for consistency */
    height: theme('size.7');
    line-height: inherit;
    padding: 0;

    /* Focus state for accessibility */
    &:focus {
      outline: 1px solid ButtonText;
      outline: 1px auto -webkit-focus-ring-color;
    }
  }

  /* ==========================================================================
     DISABLED AND READONLY INPUT STATES
     ========================================================================== */

  /*
   * Styling for disabled and readonly form controls to provide clear
   * visual feedback about interaction availability.
   */

  /* Disabled text inputs and textareas */
  input[disabled],
  textarea[disabled] {
    background-color: var(--app-input-background-color-disabled);
    border-color: var(--app-input-color-border-disabled);
    box-shadow: none;
    color: theme('colors.gray.600');
    cursor: text;
    pointer-events: none;

    &:hover {
      border-color: var(--app-input-color-border-disabled);
    }
  }

  /* Readonly inputs and textareas */
  [readonly],
  textarea[readonly] {
    background-color: theme('backgroundColor.skin.disabled');
    border-color: theme('borderColor.skin.disabled');
    border-radius: theme('borderRadius.skin-base');
    box-shadow: none;
    cursor: text;

    &:hover {
      border-color: theme('borderColor.skin.disabled');
    }

    &:focus {
      border-color: theme('borderColor.skin.disabled');

      --tw-ring-color: theme('ringColor.skin.base');
    }
  }

  /* Disabled checkbox and radio button styling */
  input[disabled][type='checkbox'],
  input[disabled][type='radio'] {
    border-color: theme('colors.gray.300');
  }

  /* Universal disabled cursor for all form elements */
  button[disabled],
  input[disabled],
  select[disabled],
  textarea[disabled] {
    cursor: not-allowed;
  }

  /* ==========================================================================
     PRINT MEDIA STYLES
     ========================================================================== */

  /*
   * Print-specific styling to ensure forms display properly when printed.
   * Overrides layout constraints and adjusts typography for print media.
   */
  @media print {
    form {
      height: auto !important;
      max-height: none !important;
      overflow: visible !important;
    }

    button,
    input,
    label,
    select,
    textarea {
      font-size: 9pt !important;
      margin: 0;
    }
  }

  /* ==========================================================================
     SPECIALIZED INPUT TYPE STYLING
     ========================================================================== */

  /* Percentage input with space for % symbol */
  .app-input-percent input {
    padding-right: theme('spacing.8');
  }

  /* Number input with monospace font and right alignment */
  .app-input-number input {
    font-family: theme('fontFamily.mono');
    text-align: right;
  }

  /* ==========================================================================
     ERROR STATE STYLING
     ========================================================================== */

  /*
   * Error state styling for form validation feedback.
   * Applies consistent error colors across all interactive states.
   */
  .has-errors {
    border-color: theme('colors.bad');

    &:hover,
    &:focus,
    &:checked {
      border-color: theme('colors.bad');

      --tw-ring-color: theme('ringColor.bad');
    }
  }

  /* ==========================================================================
     FORM UTILITIES AND HELPERS
     ========================================================================== */

  /* Add-on text color for form decorations */
  .form-add-on-color {
    color: theme('colors.gray.400');
  }

  /* Grid layout for aside information displays */
  .aside-information-grid {
    display: grid;
    grid-template-columns: min-content minmax(0, 1fr);
  }

  /* Textarea styling for TinyMCE editor contexts */
  textarea:not(.expanding-textarea) {
    font-family: theme('fontFamily.sans');
    font-size: theme('fontSize.base');
    height: var(--app-textarea-height);
    overflow: auto;
    padding: theme('spacing.1') theme('spacing.1.5');
    vertical-align: top;
  }

  /* ==========================================================================
     FORM REQUIRED FIELD INDICATORS
     ========================================================================== */

  /*
   * Required field indicators using pseudo-elements.
   * Can be globally disabled with .no-required-stars class.
   */
  .form-required-icon {
    &::before {
      color: theme('colors.u2');
      content: '*';
      font-weight: theme('fontWeight.bold');
    }
  }

  /* Hide required stars when explicitly disabled */
  .no-required-stars .form-required-icon::before {
    content: '';
  }
}

@layer components {
  .tinymce-html img {
    display: inline;
  }

  .fields-grid {
    display: grid;
    gap: var(--app-form-field-spacing);
    grid-template-columns: theme('gridTemplateColumns.1');
  }
}

@layer utilities {
  /* Allows to hide the `input` element visually and from screen readers but lets browser native validation work */
  .hidden-no-sr {
    border: 0;
    clip: rect(theme('spacing.px'), theme('spacing.px'), theme('spacing.px'), theme('spacing.px'));
    height: theme('spacing.px');
    overflow: hidden;
    padding: 0;
    position: absolute;
    speak: never;
    width: theme('spacing.px');
  }
}
