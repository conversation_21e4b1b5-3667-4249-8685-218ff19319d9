import { defineComponent, useAttrs } from 'vue'
import AppChip from './components/AppChip.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const GenericTable = defineComponent({
  components: {
    AppChip,
  },
  inheritAttrs: false,
  setup() {
    const attrs = useAttrs()
    const isDataTable = (attrs.class as string).split(' ').includes('data-table')
    return {
      isDataTable,
    }
  },
  template: `
    <div class="flex border-t border-gray-200 mt-2 pt-4">
    <div :class="['flex-1 mr-1',{'data-table-wrapper': isDataTable}]">
      <table v-bind="$attrs">
        <thead>
          <tr>
            <th class="table-head-checkbox"><span>config</span></th>
            <th class="table-head-id">ID</th>
            <th class="table-head-workflow-status">Status</th>
            <th class="table-head-name">Name</th>
            <th class="table-head-text">Text</th>
            <th class="table-head-number">Number</th>
            <th class="table-head-currency">Currency</th>
            <th class="table-head-boolean">Boolean</th>
            <th class="table-head-user">User</th>
            <th class="table-head-date">Date</th>
            <th class="table-head-files">Count</th>
            <th class="table-head-action"></th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="table-data-checkbox"><input type="checkbox"></td>
            <td class="table-data-id"><a href="#">21</a></td>
            <td class="table-data-workflow-status"><span class="status open">open</span></td>
            <td class="table-data-name">Row name</td>
            <td class="table-data-text">Generic text column</td>
            <td class="table-data-number">1,337</td>
            <td class="table-data-currency"><span title="Euro">EUR</span></td>
            <td class="table-data-action"><span>yes</span></td>
            <td class="table-data-user">
              admin
            </td>
            <td class="table-data-date">05.11.2015</td>
            <td class="table-data-count">
              <AppChip color="gray">1</AppChip>
            </td>
            <td class="table-data-action"><a>edit</a></td>
          </tr>
          <tr>
            <td class="table-data-checkbox"><input type="checkbox"></td>
            <td class="table-data-id"><a href="#">22</a></td>
            <td class="table-data-workflow-status"><span class="status in-progress">in progress</span></td>
            <td class="table-data-name">Row name</td>
            <td class="table-data-text">Generic text column</td>
            <td class="table-data-number">1,337</td>
            <td class="table-data-currency"><span title="Euro">EUR</span></td>
            <td class="table-data-action"><span>no</span></td>
            <td class="table-data-user">
              admin
            </td>
            <td class="table-data-date">05.11.2015</td>
            <td class="table-data-count">
              <AppChip color="gray">0</AppChip>
            </td>
            <td class="table-data-action"><a>edit</a></td>
          </tr>
          <tr>
            <td class="table-data-checkbox"><input type="checkbox"></td>
            <td class="table-data-id"><a href="#">23</a></td>
            <td class="table-data-workflow-status"><span class="status in-progress">in review</span></td>
            <td class="table-data-name">Row name</td>
            <td class="table-data-text">Generic text column</td>
            <td class="table-data-number">1,337</td>
            <td class="table-data-currency"><span title="Euro">EUR</span></td>
            <td class="table-data-action"><span>yes</span></td>
            <td class="table-data-user">
              admin
            </td>
            <td class="table-data-date">05.11.2015</td>
            <td class="table-data-count">
              <AppChip color="gray">2</AppChip>
            </td>
            <td class="table-data-action"><a>edit</a></td>
          </tr>
          <tr>
            <td class="table-data-checkbox"><input type="checkbox"></td>
            <td class="table-data-id"><a href="#">24</a></td>
            <td class="table-data-workflow-status"><span class="status complete">done</span></td>
            <td class="table-data-name">Row name</td>
            <td class="table-data-text">Generic text column</td>
            <td class="table-data-number">1,337</td>
            <td class="table-data-currency"><span title="Euro">EUR</span></td>
            <td class="table-data-action"><span>yes</span></td>
            <td class="table-data-user">
              admin
            </td>
            <td class="table-data-date">05.11.2015</td>
            <td class="table-data-count">
              <AppChip color="gray">0</AppChip>
            </td>
            <td class="table-data-action"><a>edit</a></td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="table-data-number">1,337</td>
            <td class="table-data-currency">EUR</td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
        </tfoot>
      </table>
    </div>
    </div>
  `,
})

const meta: Meta<typeof GenericTable> = {
  title: 'Table/Layouts',
}

export default meta

export const Tables: StoryObj<typeof GenericTable> = {
  render: () => ({
    components: {
      GenericTable,
    },
    template: `
    <div>

    <h2>Basic Table Layouts</h2>
    <div class="flex">
      <div class="flex-1">
        <p>This basic table layouts may be used on their own, or combined to generate more complex layouts.</p>
      </div>
    </div>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <div class="flex-1">
        <p>Generic table, without any layout class.</p>
      </div>
    </div>
    <GenericTable/>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <h3>.bordered-table.horizontal</h3>
    </div>
    <GenericTable class="bordered-table horizontal"/>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <h3>.bordered-table.vertical</h3>
    </div>
    <GenericTable class="bordered-table vertical"/>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <h3>.bordered-table.out</h3>
    </div>
    <GenericTable class="bordered-table out"/>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <h3>.condensed-table</h3>
    </div>
    <GenericTable class="condensed-table"/>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <h3>.hovered-table</h3>
    </div>
    <GenericTable class="hovered-table"/>

    <h2>Combined Table Layouts</h2>
    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <div class="flex-1">
        <p>This combined table layouts are generated by mixing two or more basic table layouts and may have some extra styles added to them.</p>
      </div>
    </div>

    <div class="flex border-t border-gray-200 mt-2 pt-4">
      <h3>.data-table</h3>
      <div class="flex-1">
        <p>The layout for tables rendered with the TableBundle or that need to present big amounts of data.</p>
      </div>
    </div>
    <GenericTable class="data-table"/>

    <h2>Table Data</h2>
    <div class="flex">
      <div class="flex-1">
        <p>There are <code>.table-data-*</code> classes that should be added to the <code>td</code> elements regarding the type of data they contain.</p>
        <p>If using the TableBundle there is no need to add the class, just the type of column (the same as the classes but without the <code>table-data-</code>
          part and underscores instead of dashes).</p>
        <p>The available column types/classes are (sorted alphabetically, from A to Z):</p>
        <ul>
          <li><kbd>null</kbd> / <code>.table-data-text</code> for generic text content, <strong>this is the default class for table data</strong>.</li>

          <li><kbd>action</kbd> / <code>.table-data-action</code> for buttons.</li>
          <li><kbd>boolean</kbd> / <code>.table-data-action</code> can be rendered as an icon, with or without text.</li>
          <li><kbd>checkbox</kbd> / <code>.table-data-checkbox</code> for the row selection checkboxes.</li>
          <li><kbd>count</kbd> / <code>.table-data-count</code> for count columns, except user-defined ones.</li>
          <li><kbd>currency</kbd> / <code>.table-data-currency</code> for currencies (the TableBundle shows the ISO code and shows the full name in a tooltip).
          </li>
          <li><kbd>date</kbd> / <code>.table-data-date</code> for dates.</li>
          <li><kbd>datetime</kbd> / <code>.table-data-datetime</code> for dates with times/hours.</li>
          <li><kbd>description</kbd> / <code>.table-data-description</code> for the issue description.</li>
          <li><kbd>email</kbd> / <code>.table-data-email</code> for emails.</li>
          <li><kbd>icon</kbd> / <code>.table-data-icon</code> for columns where the only content is an icon, without text.</li>
          <li><kbd>id</kbd> / <code>.table-data-id</code> for the element ID.</li>
          <li><kbd>money</kbd> / <code>.table-data-money</code> for economic amounts.</li>
          <li><kbd>name</kbd> / <code>.table-data-name</code> for the element (unit, transaction, etc.) name.</li>
          <li><kbd>number</kbd> / <code>.table-data-number</code> numeric values that are not <em>money</em> or <em>percentage</em>.</li>
          <li><kbd>other</kbd> / <code>.table-data-other</code> generic content, center aligned.</li>
          <li><kbd>percentage</kbd> / <code>.table-data-percentage</code> for percentages, without the % symbol.</li>
          <li><kbd>review</kbd> / <code>.table-data-review</code> for review information.</li>
          <li><kbd>right_aligned</kbd> / <code>.table-data-right-aligned</code> generic content, right aligned.</li>
          <li><kbd>status</kbd> / <code>.table-data-status</code> same as the workflow status ones but for statuses not related to workflows.</li>
          <li><kbd>time</kbd> / <code>.table-data-time</code> for times/hours.</li>
          <li><kbd>user</kbd> / <code>.table-data-user</code> for user names.</li>
          <li><kbd>workflow_status</kbd> / <code>.table-data-workflow-status</code>.</li>
        </ul>
      </div>
    </div>
    </div>
  `,
  }),
}
