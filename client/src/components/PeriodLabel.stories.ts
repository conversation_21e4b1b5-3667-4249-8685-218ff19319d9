import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { createPeriod } from '@tests/__factories__/createPeriod'
import PeriodLabel from '@js/components/PeriodLabel.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const period1 = createPeriod({
  name: 'Quarter 1',
  id: 1,
  previousPeriod: createPeriod({ id: 2 })['@id'],
})
const period2 = createPeriod({ name: 'Quarter 2', id: 2, previousPeriod: period1['@id'] })
const periodWithoutPreviousPeriod = createPeriod({
  id: 5,
  name: 'Quarter 5',
  previousPeriod: undefined,
})
const periods = {
  [period1.id]: period1,
  [period2.id]: period2,
  [periodWithoutPreviousPeriod.id]: periodWithoutPreviousPeriod,
}

const meta: Meta<typeof PeriodLabel> = {
  title: 'Label/Period Label',
  parameters: {
    msw: {
      handlers: [
        http.get('/api/periods/:periodId', async (req) => {
          const { periodId } = req.params
          return HttpResponse.json(periods[periodId as unknown as number], {
            status: StatusCodes.OK,
          })
        }),
      ],
    },
  },
  argTypes: {},
  args: {},
}

export default meta

export const withPreviousPeriod: StoryObj<typeof PeriodLabel> = {
  render: (args) => ({
    components: { PeriodLabel },
    setup() {
      return {
        args: {
          ...args,
          period: period1,
        },
      }
    },
    template: `
      <PeriodLabel v-bind="args"/>
    `,
  }),
}
export const withoutPreviousPeriod: StoryObj<typeof PeriodLabel> = {
  render: (args) => ({
    components: { PeriodLabel },
    setup() {
      return {
        args: {
          ...args,
          period: periodWithoutPreviousPeriod,
        },
      }
    },
    template: `
      <PeriodLabel v-bind="args"/>
    `,
  }),
}
