<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { calendarApi } from '@js/api/calendarApi'
import AppDialog from '@js/components/AppDialog.vue'
import Routing from '@js/Routing'
import Translator from '@js/translator'
import type { CalendarDefinition } from '@js/model/calendar'

defineEmits<(event: 'close') => void>()
const loading = ref(true)
const calendars = ref<Array<CalendarDefinition>>([])

onMounted(async () => {
  const { data } = await calendarApi.fetchCalendarDefinitions()
  calendars.value = data.calendars
  loading.value = false
})
</script>

<template>
  <AppDialog
    :title="Translator.trans('u2_core.add_new_calendar_entry')"
    :loading="loading"
    @close="$emit('close')"
  >
    <div class="list-group w-96 max-w-full">
      <router-link
        v-for="(calendar, key) in calendars"
        :key="key"
        :to="{ path: Routing.generate(calendar.routeNew) }"
        class="list-group-item flex items-center justify-between"
      >
        {{ calendar.name }}
      </router-link>
    </div>
  </AppDialog>
</template>
