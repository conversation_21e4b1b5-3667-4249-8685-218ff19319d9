<script setup lang="ts">
import { createDocumentTemplate } from '@js/api/documentTemplateApi'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import invariant from 'tiny-invariant'
import { isAxiosError } from 'axios'
import { watch } from 'vue'
import { useMutation } from '@tanstack/vue-query'
import AppMessage from '@js/components/AppMessage.vue'
import FormFieldset from '@js/components/form/FormFieldset.vue'
import useForm from '@js/composable/useForm'
import Translator from '@js/translator'
import { usePageStore } from '@js/stores/page'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import FieldInputFile from '@js/components/form/FieldInputFile.vue'
import FieldTextarea from '@js/components/form/FieldTextarea.vue'
import FieldInputText from '@js/components/form/FieldInputText.vue'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import { documentTemplateTypes } from '@js/model/document-template'
import FieldSelect from '@js/components/form/FieldSelect.vue'
import type { DocumentTemplate } from '@js/model/document-template'

const FormDataSchema = z.object({
  type: z.enum(Object.keys(documentTemplateTypes) as [keyof typeof documentTemplateTypes]),
  uploadedFile: z
    .custom<File>()
    .refine((file) => file?.size <= 1000000, `Max file size is 5MB.`) // Translations
    .refine(
      (file) => ['application/xml', 'text/xml'].includes(file?.type),
      'Only .xml files are accepted.' // Translations
    ),
  name: z
    .string()
    .min(1)
    .max(120, { message: defaultValidationMessages.maxLength(120) }),
  description: z.string().nullable(),
})

const { handleSubmit, setResponseErrors, values, isFieldValid, setFieldValue } = useForm({
  validationSchema: toTypedSchema(FormDataSchema),
  initialValues: {
    uploadedFile: undefined,
    type: undefined,
    name: '',
    description: '',
  },
})

watch(
  () => isFieldValid('uploadedFile'),
  (newValue) => {
    if (newValue && values.uploadedFile) {
      extractNameAndDescription(values.uploadedFile)
    }
  }
)

const save = handleSubmit((submitData) =>
  documentTemplateMutation(submitData as unknown as z.infer<typeof FormDataSchema>)
)

function extractNameAndDescription(file: File) {
  const reader = new FileReader()
  reader.onload = function (e) {
    if (!e.target || !e.target.result) {
      return
    }

    const xmlContent = e.target.result
    if (typeof xmlContent !== 'string') {
      return
    }

    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlContent, 'application/xml')

    function nsResolver() {
      return xmlDoc.documentElement.namespaceURI || null
    }

    if (!values.name) {
      setFieldValue(
        'name',
        xmlDoc.evaluate('//document:name', xmlDoc, nsResolver, XPathResult.STRING_TYPE, null)
          .stringValue
      )
    }

    if (!values.description) {
      setFieldValue(
        'description',
        xmlDoc.evaluate('//document:description', xmlDoc, nsResolver, XPathResult.STRING_TYPE, null)
          .stringValue
      )
    }
  }

  reader.readAsText(file)
}

const pageStore = usePageStore()
const { resolveNotification } = useHandleAxiosErrorResponse()

const emit = defineEmits<(event: 'created', payload: DocumentTemplate) => void>()

const { mutate: documentTemplateMutation } = useMutation({
  mutationFn: (data: z.infer<typeof FormDataSchema>) => {
    pageStore.loading = true

    return createDocumentTemplate(data.uploadedFile, data.type, data.name, data.description)
  },
  onSuccess: (data) => {
    emit('created', data.data)
  },
  onError: async (error) => {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response, { domDocument: 'uploadedFile' })
  },
  onSettled: () => {
    pageStore.loading = false
  },
})
</script>

<template>
  <form
    id="document_template_import_form"
    name="document_template_import_form"
    @submit.prevent="save"
  >
    <FieldInputFile
      :label="Translator.trans('u2.xml_file')"
      name="uploadedFile"
      :supported-mime-types="['application/xml', 'text/xml']"
      :required="true"
      :max-file-upload-size="1000000"
    />

    <FieldSelect
      class="max-w-sm"
      name="type"
      :label="Translator.trans('u2_core.type')"
      required
      :options="[
        { id: 'local-file', name: Translator.trans('u2_tpm.local_file') },
        { id: 'master-file', name: Translator.trans('u2_tpm.master_file') },
        {
          id: 'country-by-country-report',
          name: Translator.trans('u2_tpm.country_by_country_report'),
        },
      ]"
      :placeholder="Translator.trans('u2.select_option')"
    />

    <FormFieldset :label="Translator.trans('u2_tcm.details')">
      <div class="fields-grid">
        <AppMessage v-if="!isFieldValid('uploadedFile')">
          {{ Translator.trans('u2.document_template.field_disabled_choose_file_first') }}
        </AppMessage>

        <FieldInputText
          :disabled="!isFieldValid('uploadedFile')"
          :label="Translator.trans('u2.name')"
          name="name"
          required
          maxlength="120"
        />

        <FieldTextarea
          :disabled="!isFieldValid('uploadedFile')"
          :label="Translator.trans('u2_core.description')"
          name="description"
        />
      </div>
    </FormFieldset>
  </form>
</template>
