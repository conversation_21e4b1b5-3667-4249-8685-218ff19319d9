<script lang="ts" setup>
import { deleteDocumentSection } from '@js/api/documentApi'
import FieldCheckbox from '@js/components/form/FieldCheckbox.vue'
import FormErrors from '@js/components/form/FormErrors.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { useNotificationsStore } from '@js/stores/notifications'
import { toTypedSchema } from '@vee-validate/zod'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import useForm from '@js/composable/useForm'
import { computed, ref } from 'vue'
import { StatusCodes } from 'http-status-codes'
import { useDocumentStore } from '@js/stores/document'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppDialog from '@js/components/AppDialog.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import type { DocumentSection as DocumentSectionModel } from '@js/model/document'
import { z } from 'zod'

const props = defineProps<{
  section: DocumentSectionModel
}>()

const documentStore = useDocumentStore()
const sectionNameWithNumbering = computed(() => {
  return documentStore.buildSectionNameWithNumbering(props.section)
})
const hasSectionSubsections = computed(() => documentStore.hasSubsections(props.section))

const emit = defineEmits<(event: 'close' | 'deleted') => void>()

const { handleSubmit, setResponseErrors, unmappedErrors } = useForm({
  validationSchema: toTypedSchema(
    z.object({
      'document_section_remove_form[removeSubsections]': z.boolean(),
    })
  ),
  initialValues: {
    'document_section_remove_form[removeSubsections]': false,
  },
})
const loading = ref(false)
const { resolveNotification } = useHandleAxiosErrorResponse()
const notificationsStore = useNotificationsStore()
const save = handleSubmit(async (values) => {
  loading.value = true
  try {
    const response = await deleteDocumentSection({
      id: props.section.id,
      type: props.section['@type'],
      shouldDeleteSubsections: values['document_section_remove_form[removeSubsections]'],
    })
    if (response.status === StatusCodes.OK) {
      notificationsStore.addByType(response.data.messages)
      emit('deleted')
    }
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    setResponseErrors(error.response)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <AppDialog
    :title="Translator.trans('u2_structureddocument.confirm_deletion')"
    :loading="loading"
    class="w-96 sm:w-full sm:max-w-xl"
    @close="emit('close')"
  >
    <p>
      {{
        Translator.trans('u2.delete_section_from_document.confirm', {
          section_name: sectionNameWithNumbering,
        })
      }}
    </p>

    <form
      id="document_section_remove_form"
      ref="form"
      name="document_section_remove_form"
      method="post"
    >
      <FormErrors :errors="unmappedErrors" />

      <FieldCheckbox
        v-if="hasSectionSubsections"
        name="document_section_remove_form[removeSubsections]"
        :label="Translator.trans('u2_structureddocument.also_delete_subsections')"
        class="-mx-1"
      />
    </form>

    <p :class="{ 'mt-2': hasSectionSubsections }">
      <SvgIcon icon="alert" size="large" class="text-warning relative top-1.5 mr-1" />
      <strong>{{ Translator.trans('u2_structureddocument.caution') }}:</strong>
      {{
        Translator.trans(
          'u2_structureddocument.delete_section.warning_attachments_will_also_be_removed'
        )
      }}
    </p>

    <template #buttons>
      <ButtonBasic :disabled="loading" @click="emit('close')">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonBasic :disabled="loading" button-style="solid" @click="save">
        {{ Translator.trans('u2_structureddocument.delete') }}
      </ButtonBasic>
    </template>
  </AppDialog>
</template>
