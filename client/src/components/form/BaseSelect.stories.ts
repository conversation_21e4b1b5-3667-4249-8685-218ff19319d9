import BaseSelect from './BaseSelect.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof BaseSelect> = {
  title: 'Form/Select/Form Row',
  argTypes: {
    'onUpdate:modelValue': { action: 'update:modelValue' },
    options: {
      table: {
        disable: true,
      },
    },
    errors: {
      control: {
        type: 'object',
      },
    },
    helpTooltip: {
      control: {
        type: 'text',
      },
    },
    warningTooltip: {
      control: {
        type: 'text',
      },
    },
  },
  args: {
    errors: ['Error 1', 'Error 2'],
    label: 'A Nice Label',
    helpTooltip: 'A Nice Help Text',
    placeholder: 'Here is a placeholder',
    modelValue: 2,
    required: false,
    disabled: false,
    options: [
      {
        id: '1',
        name: 'Option 1',
        disabled: false,
      },
      {
        id: '2',
        name: 'Option 2',
        disabled: false,
      },
      {
        id: '3',
        name: 'Option 3',
        disabled: true,
      },
      {
        id: '4',
        name: 'Option 4',
        disabled: false,
      },
    ],
  },
}

export default meta

export const Default: StoryObj<typeof BaseSelect> = {
  render: (args) => ({
    components: { BaseSelect },
    setup() {
      return { args }
    },
    template: `
    <div>
      <span class="">The selected value is: {{ args.modelValue }}</span>
      <BaseSelect v-bind="args"/>
    </div>`,
  }),
}
