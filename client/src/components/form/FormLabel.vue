<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'

defineProps<{
  required?: boolean
  helpTooltip?: string
  warningTooltip?: string
}>()

const emit = defineEmits<(event: 'helpClick') => void>()
</script>

<template>
  <label class="form-label">
    <slot />
    <span v-if="required" class="form-required-icon" />

    <VTooltip v-if="helpTooltip || !!$slots['help-tooltip']" class="inline">
      <SvgIcon
        icon="help-outline"
        size="small"
        class="cursor-pointer align-middle text-gray-500 hover:text-gray-700"
        @click.prevent="emit('helpClick')"
      />
      <template #popper>
        <slot name="help-tooltip">
          {{ helpTooltip }}
        </slot>
      </template>
    </VTooltip>

    <VTooltip v-if="warningTooltip || !!$slots['warning-tooltip']" class="inline">
      <SvgIcon
        size="small"
        icon="alert"
        class="cursor-pointer align-middle text-alert hover:text-alert-darker"
      />
      <template #popper>
        <slot name="warning-tooltip">
          {{ warningTooltip }}
        </slot>
      </template>
    </VTooltip>
  </label>
</template>
