<script setup lang="ts">
import type { BaseToggleProps } from '@js/components/form/BaseToggle.vue'
import { useField } from 'vee-validate'
import BaseToggle from '@js/components/form/BaseToggle.vue'

const props = defineProps<BaseToggleProps & { name: string }>()

const { value: selected, errors } = useField<boolean | undefined>(() => props.name, undefined, {
  bails: false,
})
</script>

<template>
  <BaseToggle v-model="selected" v-bind="props" :errors />
</template>
