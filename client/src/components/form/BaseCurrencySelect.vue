<script lang="ts">
import type { AppCurrencySelectProps } from '@js/components/form/AppCurrencySelect.vue'
import type { FormRowProps } from '@js/components/form/FormRow.vue'

export interface BaseCurrencySelectProps
  extends Omit<AppCurrencySelectProps, 'hasErrors'>,
    FormRowProps {
  name?: string
}
</script>

<script setup lang="ts">
import { useBindAttrs } from '@js/composable/useBindAttrs'
import AppCurrencySelect from '@js/components/form/AppCurrencySelect.vue'
import FormRow from '@js/components/form/FormRow.vue'
import Translator from '@js/translator'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

const {
  disabled = false,
  required = false,
  errors = [],
  currencies = [],
  placeholder = Translator.trans('u2.select_currency'),
  loading = false,
  name = undefined,
} = defineProps<BaseCurrencySelectProps>()

const modelValue = defineModel<string>()
</script>

<template>
  <FormRow
    :id
    :errors="errors"
    :required="required"
    :help-tooltip="helpTooltip"
    :warning-tooltip="warningTooltip"
    :label="label"
    v-bind="rootAttrs"
  >
    <template #default="{ fieldId }">
      <AppCurrencySelect
        :id="fieldId"
        v-model="modelValue"
        :api="true"
        :currencies="currencies"
        :placeholder="placeholder"
        :disabled="disabled"
        :has-errors="!!errors?.length"
        :required="required"
        :loading="loading"
        :name="name"
        v-bind="bindAttrs"
      />
    </template>
  </FormRow>
</template>
