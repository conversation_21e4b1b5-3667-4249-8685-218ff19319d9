<script setup lang="ts">
import { compile, defineComponent, nextTick, ref, toRef, watch } from 'vue'
import { useAsyncComponent } from '@js/composable/useAsyncComponent'
import FormBaseFieldset from '@js/components/form/FormBaseFieldset.vue'
import FormFieldset from '@js/components/form/FormFieldset.vue'
import FormRow from '@js/components/form/FormRow.vue'
import FormRowEnableable from '@js/components/form/FormRowEnableable.vue'
import ExpandingTextarea from '@js/components/form/ExpandingTextarea.vue'
import AppInputNumber from '@js/components/form/AppInputNumber.vue'
import AppInputPercent from '@js/components/form/AppInputPercent.vue'
import AppInputText from '@js/components/form/AppInputText.vue'
import AppUnitSelect from '@js/components/form/AppUnitSelect.vue'
import AppPeriodSelect from '@js/components/form/AppPeriodSelect.vue'
import AppRadioGroup from '@js/components/form/AppRadioGroup.vue'
import AppTaskChoiceSelect from '@js/components/form/AppTaskChoiceSelect.vue'
import FormLabel from '@js/components/form/FormLabel.vue'
import AppSelect from '@js/components/form/AppSelect.vue'
import AppCountrySelect from '@js/components/form/AppCountrySelect.vue'
import AppCurrencySelect from '@js/components/form/AppCurrencySelect.vue'
import AppToggle from '@js/components/form/AppToggle.vue'
import AppUnitHierarchySelect from '@js/components/form/AppUnitHierarchySelect.vue'
import HierarchyUnitSelector from '@js/components/form/HierarchyUnitSelector.vue'
import AppDatePicker from '@js/components/form/AppDatePicker.vue'
import AppCheckbox from '@js/components/form/AppCheckbox.vue'
import DatasheetFormFieldWrapperLegacy from '@js/components/datasheet/DatasheetFormFieldWrapperLegacy.vue'
import PeriodStatus from '@js/components/period/PeriodStatus.vue'
import AppMultiSelect from '@js/components/form/AppMultiSelect.vue'
import AppUserConfigurableDataTypeSelect from '@js/components/form/AppUserConfigurableDataTypeSelect.vue'

const props = defineProps<{ html: string }>()

defineOptions({ inheritAttrs: false })

const emit = defineEmits<(event: 'ready') => void>()

function onResolved() {
  nextTick(() => {
    emit('ready')
  })
}

/**
 * The following `key` is a workaround for the issue with dynamic components not being re-rendered
 * when the `html` prop changes. The `key` prop is used to force the re-rendering.
 * Using `Math.random()` is not ideal, but it's a simple way to make it work without having to
 * hash the `html` prop.
 */
const key = ref(Math.random())
const data = toRef(props, 'html')
watch(data, () => {
  key.value = Math.random()
})

const { defineAsyncComponentWithFallback } = useAsyncComponent()

const DynamicComponent = defineComponent({
  /* eslint-disable vue/no-unused-components */
  components: {
    // Following components should not affect the save prompt and therefore can be loaded async
    AppChip: defineAsyncComponentWithFallback(() => import('@js/components/AppChip.vue')),
    AppDate: defineAsyncComponentWithFallback(() => import('@js/components/AppDate.vue')),
    AppDateTime: defineAsyncComponentWithFallback(() => import('@js/components/AppDateTime.vue')),
    AppLink: defineAsyncComponentWithFallback(() => import('@js/components/buttons/AppLink.vue')),
    AppMessage: defineAsyncComponentWithFallback(() => import('@js/components/AppMessage.vue')),
    AttachmentInfoButtonPopover: defineAsyncComponentWithFallback(
      () => import('@js/components/file/AttachmentInfoButtonPopover.vue')
    ),
    DocumentWidgetPlaceholder: defineAsyncComponentWithFallback(
      () => import('@js/components/document/DocumentWidgetPlaceholder.vue')
    ),
    HorizontalScrollContainer: defineAsyncComponentWithFallback(
      () => import('@js/components/HorizontalScrollContainer.vue')
    ),
    InfoBox: defineAsyncComponentWithFallback(() => import('@js/components/InfoBox.vue')),
    PrintContainer: defineAsyncComponentWithFallback(
      () => import('@js/components/PrintContainer.vue')
    ),
    StatusBadge: defineAsyncComponentWithFallback(
      () => import('@js/components/workflow/StatusBadge.vue')
    ),
    SvgIcon: defineAsyncComponentWithFallback(() => import('@js/components/SvgIcon.vue')),
    UserLabel: defineAsyncComponentWithFallback(() => import('@js/components/UserLabel.vue')),
    YesNo: defineAsyncComponentWithFallback(() => import('@js/components/YesNo.vue')),
    BaseTextareaWysiwyg: defineAsyncComponentWithFallback(
      () => import('@js/components/form/BaseTextareaWysiwyg.vue')
    ),

    // Following components will affect the save prompt and therefore should be immediately loaded
    AppCheckbox,
    AppCountrySelect,
    AppCurrencySelect,
    AppDatePicker,
    AppInputNumber,
    AppInputPercent,
    AppInputText,
    AppMultiSelect,
    AppPeriodSelect,
    AppRadioGroup,
    AppSelect,
    AppTaskChoiceSelect,
    AppToggle,
    AppUnitHierarchySelect,
    AppUnitSelect,
    AppUserConfigurableDataTypeSelect,
    ExpandingTextarea,
    FormBaseFieldset,
    FormFieldset,
    FormLabel,
    FormRow,
    FormRowEnableable,
    HierarchyUnitSelector,
    DatasheetFormFieldWrapperLegacy,
    PeriodStatus,
  },
  setup() {
    return compile(`<div>${data.value}</div>`)
  },
} as never)
</script>

<template>
  <suspense @resolve="onResolved">
    <DynamicComponent v-bind="$attrs" :key="key" />
  </suspense>
</template>
