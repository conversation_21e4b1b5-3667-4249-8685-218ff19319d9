import AppRadioGroup from '@js/components/form/AppRadioGroup.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof AppRadioGroup> = {
  title: 'Form/Radio',
  argTypes: {
    'onUpdate:modelValue': { action: 'update:modelValue' },
  },
  args: {
    horizontal: false,
    disabled: false,
    modelValue: '3',
    options: [
      {
        id: '0',
        name: 'Cat',
        warning: 'It is a cat... it will betray you!',
      },
      {
        id: '1',
        name: '<PERSON>',
        help: 'Help me!',
      },
      {
        id: '2',
        name: 'CatDog',
        help: '?',
        warning: '!',
      },
      {
        id: '3',
        name: '<PERSON><PERSON>',
        disabled: true,
      },
    ],
  },
}

export default meta

export const Group: StoryObj<typeof AppRadioGroup> = {
  render: (args) => ({
    components: { AppRadioGroup },
    setup() {
      return { args }
    },
    template: `
          <AppRadioGroup name="pets" id="pets1" v-bind="args"/>
          <p>Selected: {{ args.modelValue }}</p>
        `,
  }),
}
