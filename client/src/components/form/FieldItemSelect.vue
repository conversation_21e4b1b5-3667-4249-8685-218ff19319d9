<script setup lang="ts">
import { useField } from 'vee-validate'
import { computed, ref } from 'vue'
import { debouncedRef } from '@vueuse/core'
import { keepPreviousData, skipToken, useQuery } from '@tanstack/vue-query'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import { queries } from '@js/query'
import queryClient from '@js/queryClient'
import { getIdFromIri } from '@js/utilities/api-resource'

const {
  allowCreate = false,
  disabled = false,
  label = undefined,
  name,
  placeholder = undefined,
} = defineProps<{
  allowCreate?: boolean
  disabled?: boolean
  label?: string | false
  name: string
  placeholder?: string
}>()

const { value: selected, errors } = useField<string>(() => name, undefined, {
  bails: false,
})

const searchQuery = ref('')
const debouncedSearchQuery = debouncedRef(searchQuery, 400)

const query = computed(() => ({
  search: debouncedSearchQuery.value,
  itemsPerPage: 250,
}))

const open = ref(false)
const { data: itemsResponse, isFetching: areItemsFetching } = useQuery({
  ...queries.items.list(query),
  placeholderData: keepPreviousData,
  queryFn: computed(() => (open.value ? queries.items.list(query).queryFn : skipToken)),
})

const initialSelectedValue = ref()
const items = computed(() => {
  const items = itemsResponse.value?.['hydra:member'] ?? []
  if (!initialSelectedValue.value || searchQuery.value) {
    return items
  }

  if (selected.value !== initialSelectedValue.value['@id']) {
    return items
  }

  const initial = items.findIndex((item) => item['@id'] === selected.value)
  if (initial === -1) {
    return [initialSelectedValue.value, ...items]
  }

  return items
})

const itemOptions = computed(() => {
  return items.value?.map((item) => ({ id: item['@id'], name: item.refId })) ?? []
})

const isInitialValueLoaded = ref(false)

if (selected.value) {
  isInitialValueLoaded.value = true
  await queryClient
    .fetchQuery(queries.items.single(getIdFromIri(selected.value)))
    .then((data) => {
      initialSelectedValue.value = data
    })
    .finally(() => {
      isInitialValueLoaded.value = false
    })
}

const isLoading = computed(() => areItemsFetching.value || isInitialValueLoaded.value)
</script>

<template>
  <BaseSelect
    v-model="selected"
    v-model:search-query="searchQuery"
    v-model:open="open"
    :label="label"
    :name="name"
    :allow-create="allowCreate"
    :placeholder="placeholder"
    :errors="errors"
    :options="itemOptions"
    :loading="isLoading"
    :enable-filter="false"
    :disabled="disabled"
  />
</template>
