<script setup lang="ts" generic="TOption extends SelectOption">
import { computed } from 'vue'
import { useVModel } from '@vueuse/core'
import BaseCheckboxGroup from '@js/components/form/BaseCheckboxGroup.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import BaseCheckbox from '@js/components/form/BaseCheckbox.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import useSearchRecords from '@js/composable/useSearchRecords'
import type { SelectOption } from '@js/types'
import type { Ref } from 'vue'

const props = withDefaults(
  defineProps<{
    modelValue?: Array<TOption['id']>
    options: Array<TOption>
  }>(),
  {
    modelValue: () => [],
  }
)

const emit = defineEmits<(event: 'update:modelValue') => void>()
const selectedOptionIds = useVModel(props, 'modelValue', emit, { passive: true }) as Ref<
  Array<TOption['id']>
>

const selectOption = (optionId: TOption['id']) => {
  selectedOptionIds.value.push(optionId)
}
const unselectOption = (optionId: TOption['id']) => {
  selectedOptionIds.value = selectedOptionIds.value.filter((id) => id !== optionId)
}

const selectedOptions = computed(() =>
  selectedOptionIds.value
    .map((id) => props.options.find((option) => option.id === id))
    .filter((option): option is TOption => option !== undefined)
    .sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }))
)
const notSelectedOptions = computed(() =>
  props.options
    .filter((option) => selectedOptionIds.value.find((id) => id === option.id) === undefined)
    .sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' }))
)

const { searchQuery: selectedSearchQuery, filteredRecords: filteredSelectedOptions } =
  useSearchRecords<SelectOption>(selectedOptions, ['name'], '')

const { searchQuery: notSelectedSearchQuery, filteredRecords: filteredNotSelectedOptions } =
  useSearchRecords<SelectOption>(notSelectedOptions, ['name'], '')

const unselectAllButtonText = computed(() => {
  if (selectedOptions.value.length === filteredSelectedOptions.value.length) {
    return Translator.trans('u2.deselect_all')
  }
  if (filteredSelectedOptions.value.length > 0) {
    return Translator.trans('u2.deselect_filtered')
  }
  return Translator.trans('u2.no_results')
})

const selectAllButtonText = computed(() => {
  if (notSelectedOptions.value.length === filteredNotSelectedOptions.value.length) {
    return Translator.trans('u2.select_all')
  }
  if (filteredNotSelectedOptions.value.length > 0) {
    return Translator.trans('u2.select_filtered')
  }
  return Translator.trans('u2.no_results')
})

function bulkSelect() {
  if (notSelectedOptions.value.length === filteredNotSelectedOptions.value.length) {
    selectedOptionIds.value = props.options.map((option: TOption) => option.id)
  }
  if (filteredNotSelectedOptions.value.length > 0) {
    selectedOptionIds.value.push(...filteredNotSelectedOptions.value.map((option) => option.id))
  }
}

function bulkUnselect() {
  if (selectedOptions.value.length === filteredSelectedOptions.value.length) {
    selectedOptionIds.value = selectedOptions.value
      .filter((option) => option.disabled)
      .map((option) => option.id)
    return
  }
  if (filteredSelectedOptions.value.length > 0) {
    const filteredGroupsIris = filteredSelectedOptions.value
      .filter((option) => !option.disabled)
      .map((group) => group.id)
    selectedOptionIds.value = selectedOptionIds.value.filter(
      (option) => !filteredGroupsIris.includes(option)
    )
  }
}
</script>

<template>
  <div class="grid grid-cols-2 gap-4">
    <div>
      <h2>{{ Translator.trans('u2_core.assigned') }} ({{ selectedOptions.length }})</h2>
      <div>
        <AppSearch
          v-model="selectedSearchQuery"
          class="mt-1 w-full"
          :placeholder="Translator.trans('u2_core.enter_filter_term')"
        />
        <ButtonBasic
          :disabled="filteredSelectedOptions.length === 0"
          class="mt-1"
          @click="bulkUnselect"
        >
          {{ unselectAllButtonText }}
        </ButtonBasic>
      </div>
      <BaseCheckboxGroup
        v-if="selectedOptions.length > 0"
        :aria-label="Translator.trans('u2_core.assigned')"
        class="mt-1"
      >
        <BaseCheckbox
          v-for="option in filteredSelectedOptions"
          :key="option.name"
          :model-value="true"
          :value="'1'"
          :label="option.name"
          :disabled="option.disabled"
          @update:model-value="unselectOption(option.id)"
        />
      </BaseCheckboxGroup>
      <p v-else>
        <em>{{ Translator.trans('u2_core.there_are_no_elements_assigned') }}</em>
      </p>
    </div>
    <div>
      <h2>{{ Translator.trans('u2.unassigned') }} ({{ notSelectedOptions.length }})</h2>
      <div>
        <AppSearch
          v-model="notSelectedSearchQuery"
          class="mt-1 w-full"
          :placeholder="Translator.trans('u2_core.enter_filter_term')"
        />
        <ButtonBasic
          :disabled="filteredNotSelectedOptions.length === 0"
          class="mt-1"
          @click="bulkSelect"
        >
          {{ selectAllButtonText }}
        </ButtonBasic>
      </div>
      <BaseCheckboxGroup
        v-if="notSelectedOptions.length > 0"
        :aria-label="Translator.trans('u2.unassigned')"
        class="mt-1"
      >
        <BaseCheckbox
          v-for="option in filteredNotSelectedOptions"
          :key="option.name"
          :model-value="false"
          :value="'1'"
          :label="option.name"
          :disabled="option.disabled"
          @update:model-value="selectOption(option.id)"
        />
      </BaseCheckboxGroup>
      <p v-else>
        <em>{{ Translator.trans('u2_core.there_are_no_elements_to_be_assigned') }}</em>
      </p>
    </div>
  </div>
</template>
