<script setup lang="ts">
import { computed } from 'vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import useUserAssignedUserGroupsQuery from '@js/composable/useUserAssignedUserGroupsQuery'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import type { SelectOption } from '@js/types'
import type { UserGroup } from '@js/model/userGroup'

defineOptions({ inheritAttrs: false })

withDefaults(
  defineProps<{
    label?: string | false
    disabled?: boolean
    required?: boolean
    name?: string
    errors?: Array<string>
  }>(),
  {
    label: false,
    name: undefined,
    disabled: false,
    required: false,
    errors: undefined,
  }
)

defineEmits<(event: 'update:modelValue', payload: UserGroup['@id'] | null) => void>()
const modelValue = defineModel<UserGroup['@id'] | null | undefined>({
  required: true,
  default: null,
})

const authStore = useAuthStore()
const currentUserId = computed(() => authStore.user?.id)
const userAssignedUserGroupsQuery = useUserAssignedUserGroupsQuery(currentUserId)
const isLoading = computed(() => userAssignedUserGroupsQuery.isLoading.value)

const userGroupOptions = computed(
  (): Array<SelectOption> =>
    userAssignedUserGroupsQuery.userGroups.value.map((userGroup: UserGroup) => ({
      id: userGroup['@id'],
      name: userGroup.name,
    }))
)
</script>

<template>
  <BaseSelect
    v-model="modelValue"
    :label="label"
    :required="required"
    :disabled="isLoading || disabled"
    :options="userGroupOptions"
    :name="name"
    :errors="errors"
    :placeholder="
      isLoading ? Translator.trans('u2.loading') : Translator.trans('u2_comment.unrestricted')
    "
  />
</template>
