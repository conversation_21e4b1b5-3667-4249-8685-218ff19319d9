<script lang="ts">
import type { AppCheckboxProps } from '@js/components/form/AppCheckbox.vue'
import type { FormRowProps } from '@js/components/form/FormRow.vue'
import type { Icon } from '@js/utilities/name-lists'

export interface BaseCheckboxProps extends Omit<AppCheckboxProps, 'hasErrors'>, FormRowProps {
  icon?: Icon
  tooltip?: string
  labelTooltip?: string
}
</script>

<script setup lang="ts">
import { useBindAttrs } from '@js/composable/useBindAttrs'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppCheckbox from '@js/components/form/AppCheckbox.vue'
import FormLabel from '@js/components/form/FormLabel.vue'
import FormRow from '@js/components/form/FormRow.vue'

defineOptions({ inheritAttrs: false })
const { rootAttrs, bindAttrs } = useBindAttrs()

const {
  disabled = false,
  required = false,
  errors = [],
  icon = undefined,
  tooltip = undefined,
  labelTooltip = undefined,
} = defineProps<BaseCheckboxProps>()

const modelValue = defineModel<boolean | undefined>({ required: true, default: false })
</script>

<template>
  <FormRow :id class="flex flex-col leading-none" :errors v-bind="rootAttrs" :label="false">
    <template #default="{ fieldId }">
      <div class="inline-flex items-start gap-x-1">
        <AppCheckbox
          :id="fieldId"
          v-model="modelValue"
          v-tooltip="tooltip"
          :state="state"
          :disabled="disabled"
          :has-errors="!!errors?.length"
          :required="required"
          :value="value"
          v-bind="bindAttrs"
        />

        <FormLabel
          v-tooltip="labelTooltip"
          :for="fieldId"
          class="text-base font-normal leading-none text-inherit"
          :required="required"
          :help-tooltip="helpTooltip"
          :warning-tooltip="warningTooltip"
        >
          <SvgIcon v-if="icon" :icon="icon" class="align-middle" />
          <span class="align-middle">{{ label }}</span>
        </FormLabel>
      </div>
    </template>
  </FormRow>
</template>
