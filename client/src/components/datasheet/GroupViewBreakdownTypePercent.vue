<script lang="ts" setup>
import type { Period } from '@js/api/periodApi'
import { computed } from 'vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import type { PercentBreakdownValue } from '@js/api/itemBreakdownApi'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { Datasheet } from '@js/model/datasheet'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'
import useLocaleNumberFormatter from '@js/composable/useLocaleNumberFormatter'

const props = defineProps<{
  itemUnitValues: Array<PercentBreakdownValue>
  groupValue: string
  period: Period
  hierarchy: UnitHierarchy
  layoutCollectionId: DatasheetCollection['id']
  layoutId?: Datasheet['id']
}>()

const { countriesByIri } = useCountriesAllQuery()

const values = computed(() => {
  return props.itemUnitValues.map((value) => ({
    id: value.unit.id,
    refId: value.unit.refId,
    name: value.unit.name,
    country: value.unit.country
      ? countriesByIri.value.get(value.unit.country)?.nameShort
      : undefined,
    value: value.value,
  }))
})
const numberFormatter = useLocaleNumberFormatter()
</script>

<template>
  <table class="data-table">
    <thead>
      <tr>
        <th class="table-head-text">Unit ID</th>
        <th class="table-head-text">Unit Name</th>
        <th class="table-head-text">Country</th>
        <th class="table-data-text">Group Value</th>
        <th class="table-head-action" />
      </tr>
    </thead>
    <tbody>
      <tr v-for="value in values" :key="value.id">
        <td class="table-data-text">
          {{ value.refId }}
        </td>
        <td class="table-data-text">
          {{ value.name }}
        </td>
        <td class="table-data-text">
          {{ value.country }}
        </td>
        <td class="table-data-percentage">
          {{ numberFormatter.format(+value.value * 100, '0.0000') }} %
        </td>
        <td class="table-data-action">
          <ButtonBasic
            icon="list"
            :tooltip="Translator.trans('u2.view_associated_tasks')"
            :to="{
              name: 'UnitPeriodList',
              query: { q: `Period = '${period.name}' and UnitRefId = '${value.refId}'` },
            }"
          />
          <ButtonBasic
            icon="table"
            :tooltip="Translator.trans('u2.unit_view')"
            :to="
              buildDatasheetRoute({
                layoutCollectionId: layoutCollectionId,
                layoutId,
                unitId: value.id,
                periodId: period.id,
              })
            "
          />
        </td>
      </tr>
    </tbody>
    <tfoot>
      <tr>
        <td></td>
        <td></td>
        <td></td>
        <td class="table-data-percentage">
          {{
            groupValue
              ? numberFormatter.format(+groupValue * 100, '0.0000')
              : Translator.trans('u2.n_a')
          }}
          %
        </td>
        <td></td>
      </tr>
    </tfoot>
  </table>
</template>
