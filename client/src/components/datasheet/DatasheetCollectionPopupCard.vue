<script setup lang="ts">
import { computed, toRefs } from 'vue'
import { useLink } from 'vue-router'
import Translator from '@js/translator'
import PopupCard from '@js/components/PopupCard.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import useLayoutCollectionAssignedLayoutsQuery from '@js/composable/useLayoutCollectionAssignedLayoutsQuery'
import DatasheetLabel from '@js/components/datasheet/DatasheetLabel.vue'
import AppChip from '@js/components/AppChip.vue'
import AppLink from '@js/components/buttons/AppLink.vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import DatasheetPopupCardContext from '@js/components/datasheet/DatasheetPopupCardContext.vue'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { DataSheetNavigationContext } from '@js/model/datasheet'

const props = withDefaults(
  defineProps<{
    layoutCollection: DatasheetCollection
    context?: DataSheetNavigationContext
  }>(),
  {
    context: () => ({
      type: 'unit',
      unitId: undefined,
      periodId: undefined,
    }),
  }
)

const { layoutCollection, context } = toRefs(props)
const layoutCollectionId = computed(() => layoutCollection.value?.id)
const unitId = computed(() => ('unitId' in props.context ? props.context.unitId : undefined))
const periodId = computed(() => props.context.periodId)
const unitHierarchyId = computed(() =>
  'unitHierarchyId' in props.context && !('unitId' in props.context)
    ? props.context.unitHierarchyId
    : undefined
)

const { items: layouts, isLoading } = useLayoutCollectionAssignedLayoutsQuery(layoutCollectionId)

const editRoute = computed(() => ({
  name: 'DatasheetCollectionEdit',
  params: { id: layoutCollectionId.value },
}))
const currentRouteLink = useLink({ to: editRoute })
const isActiveLink = computed(() => currentRouteLink.isActive.value)
const isNavigationContextValid = computed(
  () => !!(unitId.value && unitHierarchyId.value) && !!periodId.value
)
</script>

<template>
  <PopupCard>
    <template #header-title>
      {{ layoutCollection.name }}
    </template>

    <template #context>
      <DatasheetPopupCardContext :context="context" />
    </template>

    <template #header-chips>
      <AppChip class="text-sm italic">
        {{
          layoutCollection.public
            ? Translator.trans('u2.access_type.public')
            : Translator.trans('u2.not_public')
        }}
      </AppChip>
    </template>
    <template #header-actions>
      <ButtonDropdownEllipsis>
        <template #items>
          <ButtonDropdownItem
            icon="edit"
            :text="Translator.trans('u2.edit')"
            :disabled="isActiveLink"
            :tooltip="
              isActiveLink ? Translator.trans('u2.target_route_is_current_route') : undefined
            "
            :to="editRoute"
          />
          <ButtonDropdownItem
            v-tooltip="
              isNavigationContextValid
                ? Translator.trans('u2.inspect.insufficient_context')
                : undefined
            "
            icon="share"
            :text="Translator.trans('u2_core.show')"
            :disabled="isNavigationContextValid"
            :to="
              buildDatasheetRoute({
                layoutCollectionId: layoutCollection.id,
                unitId,
                periodId,
                hierarchyId: unitHierarchyId,
              })
            "
          />
        </template>
      </ButtonDropdownEllipsis>
    </template>

    <template #default>
      <h4 class="pt-2 text-left">
        {{
          Translator.trans('u2.datasheets.datasheet.plural') +
          ' (' +
          layoutCollection.layoutCount +
          ')'
        }}
      </h4>
      <AppLoader v-if="isLoading" size="medium" />
      <div v-else class="flex flex-col gap-1">
        <div v-for="layout in layouts" :key="layout.id" class="flex items-center gap-1">
          <DatasheetLabel :layout="layout" class="overflow-x-hidden" :context="context" />
          <AppLink
            v-tooltip="
              isNavigationContextValid
                ? Translator.trans('u2.inspect.insufficient_context')
                : undefined
            "
            :disabled="isNavigationContextValid"
            :to="
              unitHierarchyId
                ? buildDatasheetRoute({
                    layoutCollectionId: layoutCollection.id,
                    layoutId: layout.id,
                    hierarchyId: unitHierarchyId,
                    periodId: periodId,
                  })
                : unitId
                  ? buildDatasheetRoute({
                      layoutCollectionId: layoutCollection.id,
                      layoutId: layout.id,
                      unitId: unitId,
                      periodId: periodId,
                    })
                  : '#'
            "
            icon="share"
          />
        </div>
      </div>
    </template>
  </PopupCard>
</template>
