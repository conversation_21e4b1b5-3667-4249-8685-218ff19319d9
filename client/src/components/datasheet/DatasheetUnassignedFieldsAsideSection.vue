<script setup lang="ts">
import { computed, ref } from 'vue'
import { useQueryClient } from '@tanstack/vue-query'
import Translator from '@js/translator'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import { queries } from '@js/query'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import FieldEditor from '@js/components/FieldEditor.vue'
import DatasheetFieldLabel from '@js/components/datasheet/DatasheetFieldLabel.vue'
import LabelList from '@js/components/LabelList.vue'
import AppMessage from '@js/components/AppMessage.vue'
import type { DataSheetNavigationContext, Datasheet, Field } from '@js/model/datasheet'

const props = defineProps<{
  datasheet: Datasheet
  unusedFields?: Array<Field>
  context: DataSheetNavigationContext
  hasTemplate?: boolean
  fieldNameSuggestions: Array<string>
}>()

const layoutId = computed(() => props.datasheet.id)

const queryClient = useQueryClient()
function invalidateQueries() {
  queryClient.invalidateQueries({
    queryKey: queries.datasheets.single(layoutId.value)._ctx.fields.queryKey,
  })
}

const showFieldDialog = ref(false)
const fieldEditor = ref()
const isSaving = computed(() => fieldEditor.value?.state === 'saving')
const selectedField = ref()
function closeFieldDialog() {
  selectedField.value = undefined
  showFieldDialog.value = false
}
</script>

<template>
  <AsideSection
    :headline="Translator.trans('u2.datasheets.unassigned_fields')"
    :icon="unusedFields ? (unusedFields?.length === 0 ? 'check-rounded' : 'alert') : undefined"
    :collapsible="true"
  >
    <AppLoader v-if="hasTemplate === undefined" class="w-56" />
    <template v-if="!hasTemplate">
      <AppMessage type="warning" class="mb-2">
        <div class="flex items-center gap-x-2">
          {{ Translator.trans('u2.datasheets.missing_template') }}
        </div>
      </AppMessage>
    </template>

    <AppLoader v-if="unusedFields === undefined" class="w-56" />

    <template v-else-if="unusedFields.length === 0">
      <span class="italic">
        {{ Translator.trans('u2.datasheets.all_fields_assigned') }}
      </span>
    </template>

    <template v-else>
      <LabelList id="unassigned-fields" :items="unusedFields">
        <template #default="{ listItem }">
          <DatasheetFieldLabel
            :key="listItem.id"
            :field="listItem"
            :datasheet="datasheet"
            :context="context"
            :field-name-suggestions="fieldNameSuggestions"
          />
        </template>
      </LabelList>
    </template>

    <template #button>
      <div class="flex items-center space-x-1">
        <ButtonNew button-style="text" :show-text="false" @click="showFieldDialog = true" />
      </div>
    </template>

    <AppDialog
      v-if="showFieldDialog && datasheet"
      class="w-full max-w-5xl"
      :title="
        selectedField
          ? `${Translator.trans('u2.field')} #${selectedField.name}`
          : Translator.trans('u2.new_entity_type_name', {
              entity_type_name: Translator.trans('u2.field'),
            })
      "
      @close="closeFieldDialog"
    >
      <FieldEditor
        ref="fieldEditor"
        :datasheet="datasheet"
        :field="selectedField"
        :field-name-suggestions="fieldNameSuggestions"
        @saved="
          () => {
            closeFieldDialog()
            invalidateQueries()
          }
        "
      />

      <template #buttons>
        <ButtonBasic @click="closeFieldDialog">
          {{ Translator.trans('u2.cancel') }}
        </ButtonBasic>

        <ButtonSave form="field_form" button-style="solid" :disabled="isSaving" />
      </template>
    </AppDialog>
  </AsideSection>
</template>
