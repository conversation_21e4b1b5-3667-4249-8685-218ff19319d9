<script lang="ts" setup>
import type {
  CountryBreakdownCheckboxValue,
  CountryBreakdownData,
  CountryBreakdownDiffValue,
  CountryBreakdownMoneyValue,
  CountryBreakdownNumberValue,
  CountryBreakdownPercentValue,
  CountryBreakdownTextValue,
} from '@js/api/itemCountryBreakdownApi'
import GroupViewCountryBreakdownTypeCheckbox from '@js/components/datasheet/GroupViewCountryBreakdownTypeCheckbox.vue'
import GroupViewCountryBreakdownTypeDiff from '@js/components/datasheet/GroupViewCountryBreakdownTypeDiff.vue'
import GroupViewCountryBreakdownTypeMoney from '@js/components/datasheet/GroupViewCountryBreakdownTypeMoney.vue'
import GroupViewCountryBreakdownTypeNumber from '@js/components/datasheet/GroupViewCountryBreakdownTypeNumber.vue'
import GroupViewCountryBreakdownTypePercent from '@js/components/datasheet/GroupViewCountryBreakdownTypePercent.vue'
import GroupViewCountryBreakdownTypeText from '@js/components/datasheet/GroupViewCountryBreakdownTypeText.vue'
import type { Period } from '@js/api/periodApi'
import type { Currency } from '@js/model/currency'
import type { LayoutItem } from '@js/model/datasheet'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'

const { item, period, hierarchy } = defineProps<{
  item: LayoutItem
  period: Period
  hierarchy: UnitHierarchy
  currency: Currency
  data: CountryBreakdownData
}>()
</script>
<template>
  <GroupViewCountryBreakdownTypeCheckbox
    v-if="item.type === 'checkbox'"
    :item-unit-values="data.breakdownValues as Array<CountryBreakdownCheckboxValue>"
    :group-value="data.groupValue as boolean | null"
    :period="period"
    :hierarchy="hierarchy"
  />
  <GroupViewCountryBreakdownTypeDiff
    v-else-if="item.type === 'diff'"
    :item-unit-values="data.breakdownValues as Array<CountryBreakdownDiffValue>"
    :group-value="data.groupValue as string"
    :group-currency="currency"
    :period="period"
    :hierarchy="hierarchy"
  />
  <GroupViewCountryBreakdownTypeMoney
    v-else-if="item.type === 'money'"
    :item-unit-values="data.breakdownValues as Array<CountryBreakdownMoneyValue>"
    :group-value="data.groupValue as string"
    :group-currency="currency"
    :period="period"
    :hierarchy="hierarchy"
  />
  <GroupViewCountryBreakdownTypeNumber
    v-else-if="item.type === 'number'"
    :item-unit-values="data.breakdownValues as Array<CountryBreakdownNumberValue>"
    :group-value="data.groupValue as string"
    :group-currency="currency"
    :period="period"
    :hierarchy="hierarchy"
  />
  <GroupViewCountryBreakdownTypePercent
    v-else-if="item.type === 'percent'"
    :item-unit-values="data.breakdownValues as Array<CountryBreakdownPercentValue>"
    :group-value="data.groupValue as string"
    :group-currency="currency"
    :period="period"
    :hierarchy="hierarchy"
  />
  <GroupViewCountryBreakdownTypeText
    v-else-if="item.type === 'text'"
    :item-unit-values="data.breakdownValues as Array<CountryBreakdownTextValue>"
    :group-value="data.groupValue as string"
    :group-currency="currency"
    :period="period"
    :hierarchy="hierarchy"
  />
</template>
