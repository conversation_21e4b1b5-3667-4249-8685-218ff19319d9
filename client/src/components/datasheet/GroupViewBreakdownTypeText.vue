<script lang="ts" setup>
import type { Period } from '@js/api/periodApi'
import { computed } from 'vue'
import { buildDatasheetRoute } from '@js/router/datasheetCollections'
import Translator from '@js/translator'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import type { TextBreakdownValue } from '@js/api/itemBreakdownApi'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { Datasheet } from '@js/model/datasheet'

const props = defineProps<{
  itemUnitValues: Array<TextBreakdownValue>
  groupValue: string
  period: Period
  layoutCollectionId: DatasheetCollection['id']
  layoutId?: Datasheet['id']
}>()

const { countriesByIri } = useCountriesAllQuery()
const values = computed(() => {
  return props.itemUnitValues.map((value) => ({
    id: value.unit.id,
    refId: value.unit.refId,
    name: value.unit.name,
    country: value.unit.country
      ? countriesByIri.value.get(value.unit.country)?.nameShort
      : undefined,
    comment: value.comment,
  }))
})
</script>

<template>
  <table class="data-table">
    <thead>
      <tr>
        <th class="table-head-text">Unit ID</th>
        <th class="table-head-text">Unit Name</th>
        <th class="table-head-text">Country</th>
        <th class="table-head-text">Value</th>
        <th class="table-head-action" />
      </tr>
    </thead>
    <tbody>
      <tr v-for="value in values" :key="value.id">
        <td class="table-data-text">{{ value.refId }}</td>
        <td class="table-data-text">{{ value.name }}</td>
        <td class="table-data-text">{{ value.country }}</td>
        <td class="table-data-text">{{ value.comment }}</td>
        <td class="table-data-action">
          <ButtonBasic
            icon="list"
            tooltip="View associated tasks"
            :to="{
              name: 'UnitPeriodList',
              query: { q: `UnitRefId = '${value.refId}' and Period = '${period.name}'` },
            }"
          />
          <ButtonBasic
            icon="table"
            :tooltip="Translator.trans('u2.unit_view')"
            :to="
              buildDatasheetRoute({
                layoutCollectionId: layoutCollectionId,
                layoutId,
                unitId: value.id,
                periodId: period.id,
              })
            "
          />
        </td>
      </tr>
    </tbody>
    <tfoot>
      <tr>
        <td></td>
        <td></td>
        <td></td>
        <td class="table-data-text">{{ groupValue ? groupValue : Translator.trans('u2.n_a') }}</td>
        <td></td>
      </tr>
    </tfoot>
  </table>
</template>
