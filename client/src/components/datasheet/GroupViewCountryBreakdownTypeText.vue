<script lang="ts" setup>
import type { Period } from '@js/api/periodApi'
import { computed } from 'vue'
import Translator from '@js/translator'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import type { UnitHierarchy } from '@js/model/unit_hierarchy'
import type { CountryBreakdownTextValue } from '@js/api/itemCountryBreakdownApi'

const props = defineProps<{
  itemUnitValues: Array<CountryBreakdownTextValue>
  groupValue: string
  period: Period
  hierarchy: UnitHierarchy
}>()

const { countriesByIri } = useCountriesAllQuery()
const values = computed(() => {
  return props.itemUnitValues.map((value) => ({
    country: countriesByIri.value.get(value.country)?.nameShort,
    value: value.value ? value.value : Translator.trans('u2.n_a'),
  }))
})
</script>

<template>
  <table class="data-table">
    <thead>
      <tr>
        <th class="table-head-text">Country</th>
        <th class="table-head-text">Value</th>
        <th class="table-head-action"></th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="value in values" :key="value.country">
        <td class="table-data-text">{{ value.country }}</td>
        <td class="table-data-text">{{ value.value }}</td>
        <td class="table-data-action">
          <ButtonBasic
            icon="list"
            :tooltip="Translator.trans('u2.view_associated_tasks')"
            :to="{
              name: 'UnitPeriodList',
              query: {
                q: `Period = '${period.name}' and UnitRefId in hierarchyUnits('${hierarchy.name}', '${period.endDate}') and UnitCountry = '${value.country}'`,
              },
            }"
          />
        </td>
      </tr>
    </tbody>
    <tfoot>
      <tr>
        <td></td>
        <td class="table-data-text">{{ groupValue ? groupValue : Translator.trans('u2.n_a') }}</td>
        <td></td>
      </tr>
    </tfoot>
  </table>
</template>
