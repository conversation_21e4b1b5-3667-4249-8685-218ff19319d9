import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { createDatasheet } from '@tests/__factories__/createDatasheet'
import { createDatasheetCollection } from '@tests/__factories__/createDatasheetCollection'
import { createUser } from '@tests/__factories__/createUser'
import invariant from 'tiny-invariant'
import { faker } from '@faker-js/faker/locale/en'
import DatasheetCollectionLabel from '@js/components/datasheet/DatasheetCollectionLabel.vue'
import type { Meta, StoryObj } from '@storybook/vue3'
import type { DatasheetCollection } from '@js/model/datasheetCollection'
import type { Datasheet } from '@js/model/datasheet'

const layoutCollection1 = createDatasheetCollection({
  id: 'ID-1',
  layoutCount: 2,
  name: faker.string.uuid() + ': And some additional content',
})
const layoutCollection2 = createDatasheetCollection({
  id: 'ID-2',
  layoutCount: 2,
  name: faker.string.uuid(),
})
const layoutCollection3 = createDatasheetCollection({
  id: 'ID-3',
  layoutCount: 2,
  name: faker.string.uuid(),
})
const layoutCollection4 = createDatasheetCollection({
  id: 'ID-4',
  layoutCount: 2,
  name: faker.string.uuid() + ': And some additional content',
})
const layoutCollections = {
  [layoutCollection1.id]: layoutCollection1,
  [layoutCollection2.id]: layoutCollection2,
  [layoutCollection3.id]: layoutCollection3,
  [layoutCollection4.id]: layoutCollection4,
}

const layout1 = createDatasheet({
  id: 1,
  name: faker.string.uuid() + ': And some additional content',
})
const layout2 = createDatasheet({ id: 2, name: faker.string.uuid() })
const layout3 = createDatasheet({ id: 3, name: faker.string.uuid() })
const layout4 = createDatasheet({
  id: 4,
  name: faker.string.uuid() + ': And some additional content',
})
const layouts = {
  [layout1.id]: layout1,
  [layout2.id]: layout2,
  [layout3.id]: layout3,
  [layout4.id]: layout4,
}

const layoutCollectionToLayoutsMap = new Map<DatasheetCollection, Array<Datasheet>>([
  [layoutCollections['ID-1'], [layouts['1'], layouts['2']]],
  [layoutCollections['ID-2'], [layouts['3'], layouts['4']]],
  [layoutCollections['ID-3'], [layouts['1'], layouts['3']]],
  [layoutCollections['ID-4'], [layouts['2'], layouts['4']]],
])

const layoutToLayoutCollectionsMap = new Map<Datasheet, Array<DatasheetCollection>>([
  [layouts['1'], [layoutCollections['ID-3'], layoutCollections['ID-4']]],
  [layouts['2'], [layoutCollections['ID-2'], layoutCollections['ID-4']]],
  [layouts['3'], [layoutCollections['ID-3'], layoutCollections['ID-1']]],
  [layouts['4'], [layoutCollections['ID-1'], layoutCollections['ID-2']]],
])

const meta: Meta<typeof DatasheetCollectionLabel> = {
  title: 'Label',
  parameters: {
    msw: {
      handlers: [
        http.get('/api/users/1', async () => {
          return HttpResponse.json(createUser({ id: 1 }), { status: StatusCodes.OK })
        }),
        http.get('/api/layouts/:layoutId', async (req) => {
          const { layoutId } = req.params
          return HttpResponse.json(layouts[Number(layoutId)], { status: StatusCodes.OK })
        }),
        http.get('/api/layout-collections/:layoutCollectionId', async (req) => {
          const { layoutCollectionId } = req.params
          invariant(typeof layoutCollectionId === 'string')
          return HttpResponse.json(layoutCollections[layoutCollectionId], {
            status: StatusCodes.OK,
          })
        }),
        http.get('/api/layouts/:layoutId/layout-collections', async (req) => {
          const { layoutId } = req.params
          return HttpResponse.json(
            {
              'hydra:member': layoutToLayoutCollectionsMap.get(layouts[Number(layoutId)]),
            },
            { status: StatusCodes.OK }
          )
        }),
        http.get('/api/layout-collections/:layoutCollectionId/layouts', async (req) => {
          const { layoutCollectionId } = req.params
          invariant(typeof layoutCollectionId === 'string')
          return HttpResponse.json(
            {
              'hydra:member': layoutCollectionToLayoutsMap.get(
                layoutCollections[layoutCollectionId]
              ),
            },
            { status: StatusCodes.OK }
          )
        }),
      ],
    },
  },
  argTypes: {},
  args: {
    layoutCollection: layoutCollections['ID-1'],
  },
}

export default meta

export const layoutCollection: StoryObj<typeof DatasheetCollectionLabel> = {
  render: (args) => ({
    components: { LayoutCollectionLabel: DatasheetCollectionLabel },
    setup() {
      return { args }
    },
    template: `
      <LayoutCollectionLabel v-bind="args"/>
    `,
  }),
}
