<script setup lang="ts">
import { computed } from 'vue'
import groupBy from 'lodash/groupBy'
import mapValues from 'lodash/mapValues'
import omit from 'lodash/omit'
import { getIdFromIri } from '@js/utilities/api-resource'
import UserLabel from '@js/components/UserLabel.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import type { AuditLog } from '@js/model/auditLog'

const { entry } = defineProps<{
  entry: AuditLog
}>()

const entryGroupedByField = computed(() => {
  return {
    ...entry,
    changes: mapValues(groupBy(entry.changes, 'field'), (changes) =>
      changes.map((change) => omit(change, 'field'))
    ),
  }
})
</script>

<template>
  <div>
    <p class="mb-0 flex gap-1">
      <AppDateTime :date="entryGroupedByField.timestamp" :relative="true" />
      <UserLabel
        color="white"
        :fallback="
          entryGroupedByField.user ? undefined : (entryGroupedByField.username ?? undefined)
        "
        :user="entryGroupedByField.user ? getIdFromIri(entryGroupedByField.user) : undefined"
      />
    </p>

    <ul class="mb-3 last:mb-0">
      <li
        v-for="(fieldChanges, fieldName) in entryGroupedByField.changes"
        :key="fieldName"
        class="list-disc leading-normal"
      >
        <strong>
          {{ fieldName }}
        </strong>

        <ul class="compact-list list-none pl-0">
          <li v-for="(change, index) in fieldChanges" :key="index" class="flex items-center">
            <template v-if="change.type === 'addition'">
              <SvgIcon icon="add" size="small" class="mr-1" />
              {{ change.addedElement }}
            </template>

            <template v-else-if="change.type === 'removal'">
              <SvgIcon icon="dash" size="small" class="mr-1" />
              {{ change.removedElement }}
            </template>

            <template v-else-if="change.type === 'change'">
              {{
                change.from === ''
                  ? Translator.trans('u2.empty')
                  : change.from?.toString().trim().replace(/\n/g, ', ')
              }}
              <SvgIcon icon="arrow-right" size="small" class="mx-1 align-middle" />
              {{
                change.to === ''
                  ? Translator.trans('u2.empty')
                  : change.to?.toString().trim().replace(/\n/g, ', ')
              }}
            </template>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</template>
