<script setup lang="ts">
import { computed } from 'vue'
import invariant from 'tiny-invariant'
import Translator from '@js/translator'
import PopupCard from '@js/components/PopupCard.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import useDateFormat from '@js/composable/useDateFormat'
import AppSortableTree from '@js/components/AppSortableTree.vue'
import useUnitHierarchyStructureQuery from '@js/composable/useUnitHierarchyStructureQuery'
import AppLoader from '@js/components/loader/AppLoader.vue'
import { useAuthStore } from '@js/stores/auth'
import type { UnitHierarchy, UnitHierarchyStructureTreeNode } from '@js/model/unit_hierarchy'
import type { TreeNode } from '@js/types'
import type { Icon } from '@js/utilities/name-lists'

const { getRelativeDate, getDashedDate } = useDateFormat()

const props = defineProps<{
  unitHierarchy: UnitHierarchy
  date: Date
}>()

function transformFromApiStructure(
  structureTree: Array<UnitHierarchyStructureTreeNode>
): Array<TreeNode> {
  const transform = (node: UnitHierarchyStructureTreeNode): TreeNode => {
    invariant(node.title && node.icon, 'Attr and data must be defined')
    return {
      label: node.title,
      id: node.id,
      icon: node.icon.replace('icon-', '') as Icon,
      children: node.children.length > 0 ? node.children.flatMap(transform) : [],
    }
  }
  return structureTree.flatMap(transform)
}

const dashedDate = computed(() => getDashedDate(props.date))
const unitHierarchyId = computed(() => props.unitHierarchy.id)
const { data: unitHierarchyStructure, isLoading } = useUnitHierarchyStructureQuery(
  unitHierarchyId,
  dashedDate
)
const treeData = computed(() =>
  unitHierarchyStructure.value
    ? transformFromApiStructure(unitHierarchyStructure.value.tree)
    : undefined
)

const authStore = useAuthStore()
const permission = 'UNIT:MANAGE'
const isUnitHierarchyManager = computed(() => authStore.hasRoleOrAuthorization(permission))
</script>

<template>
  <PopupCard>
    <template #header-title> {{ unitHierarchy.name }} </template>
    <template #header-actions>
      <ButtonDropdownEllipsis>
        <template #items>
          <ButtonDropdownItem
            v-tooltip="
              !isUnitHierarchyManager
                ? Translator.trans('u2.no_permission_to_perform_action', { permission })
                : undefined
            "
            icon="attributes"
            :disabled="!isUnitHierarchyManager"
            :text="Translator.trans('u2_core.edit_attributes')"
            :to="{ name: 'HierarchyAttributesEdit', params: { id: unitHierarchy.id } }"
          />
          <ButtonDropdownItem
            v-tooltip="
              !isUnitHierarchyManager
                ? Translator.trans('u2.no_permission_to_perform_action', { permission })
                : undefined
            "
            icon="organisational-group"
            :text="Translator.trans('u2_core.edit_structure')"
            :disabled="!isUnitHierarchyManager"
            :to="{
              name: 'HierarchyEdit',
              params: {
                id: unitHierarchy.id,
                date: getDashedDate(date),
              },
            }"
          />
        </template>
      </ButtonDropdownEllipsis>
    </template>

    <template #default>
      <div class="space-y-2">
        <p v-if="unitHierarchy.description">
          {{ unitHierarchy.description }}
        </p>

        <h4 class="pt-2">
          {{
            Translator.trans('u2.unit_hierarchy_structure_at', {
              date: getRelativeDate(date),
            })
          }}
        </h4>

        <AppLoader v-if="isLoading" size="medium" />
        <AppSortableTree
          v-if="treeData"
          v-model="treeData"
          drag-and-drop-group="unit-hierarchy-structure"
          :readonly="true"
        />
      </div>
    </template>
  </PopupCard>
</template>
