import { ref } from 'vue'
import AppSortableTreePool from '@js/components/AppSortableTreePool.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import AppSortableTree from '@js/components/AppSortableTree.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const modelValueVariants = [
  [
    {
      icon: 'permanent-establishment',
      label: '<PERSON>',
      id: 5,
      children: [
        {
          icon: 'legal-unit',
          label: 'Juan <PERSON>',
          id: 8,
          children: [],
        },
        {
          label: '<PERSON>',
          id: 6,
          children: [
            { label: '<PERSON>', id: 9, children: [], icon: 'organisational-group' },
            {
              label: '<PERSON>',
              id: 7,
              children: [],
              icon: 'organisational-group',
            },
            { label: '<PERSON> Johnson', id: 10, children: [], icon: 'unit' },
          ],
          icon: 'unit',
        },
      ],
    },
    { label: '<PERSON>', id: 10, children: [], icon: 'star' },
  ],
  [
    {
      icon: 'permanent-establishment',
      label: '<PERSON>',
      id: 5,
      children: [
        {
          icon: 'legal-unit',
          label: '<PERSON>',
          id: 8,
          children: [],
        },
        {
          label: '<PERSON>',
          id: 6,
          children: [],
          icon: 'unit',
        },
      ],
    },
    {
      label: '<PERSON> <PERSON>',
      id: 9,
      children: [
        {
          label: '<PERSON>',
          id: 7,
          children: [],
          icon: 'organisational-group',
        },
        { label: '<PERSON> Johnson', id: 10, children: [], icon: 'unit' },
      ],
      icon: 'organisational-group',
    },
    { label: 'Ernesto', id: 10, children: [], icon: 'star' },
  ],
  [
    { label: 'Katie', id: 189, children: [], icon: 'document' },
    {
      icon: 'permanent-establishment',
      label: 'Juan',
      id: 5,
      children: [
        { label: 'Stan', id: 188, children: [], icon: 'dashboard' },
        {
          icon: 'legal-unit',
          label: 'Juan Juan',
          id: 8,
          children: [
            { label: 'Rick', id: 187, children: [], icon: 'unit' },
            {
              label: 'Edgar',
              id: 6,
              children: [
                {
                  label: 'Edgar Edgar',
                  id: 9,
                  children: [
                    {
                      label: 'Johnson',
                      id: 7,
                      children: [],
                      icon: 'organisational-group',
                    },
                    { label: 'Johnson Johnson', id: 10, children: [], icon: 'unit' },
                  ],
                  icon: 'organisational-group',
                },
              ],
              icon: 'unit',
            },
            { label: 'Ernesto', id: 10, children: [], icon: 'star' },
          ],
        },
        { label: 'Kyle', id: 186, children: [], icon: 'permanent-establishment' },
      ],
    },
  ],

  [
    {
      icon: 'permanent-establishment',
      label: 'Juan',
      id: 5,
      children: [],
    },

    {
      label: 'Ernesto',
      id: 10,
      children: [
        {
          label: 'Edgar Edgar',
          id: 9,
          children: [
            {
              label: 'Johnson',
              id: 7,
              children: [
                { label: 'Johnson Johnson', id: 10, children: [], icon: 'unit' },
                {
                  icon: 'legal-unit',
                  label: 'Juan Juan',
                  id: 8,
                  children: [],
                },
              ],
              icon: 'organisational-group',
            },
            {
              label: 'Edgar',
              id: 6,
              children: [],
              icon: 'unit',
            },
          ],
          icon: 'organisational-group',
        },
      ],
      icon: 'star',
    },
  ],
]

const meta: Meta<typeof AppSortableTree> = {
  title: 'Form / Tree',
  args: {
    disabled: false,
    readonly: false,
    modelValue: [
      {
        icon: 'permanent-establishment',
        label: 'Juan',
        id: 5,
        children: [
          {
            icon: 'legal-unit',
            label: 'Juan Juan',
            id: 8,
            children: [],
          },
          {
            label: 'Edgar',
            id: 6,
            children: [{ label: 'Edgar Edgar', id: 9, children: [], icon: 'organisational-group' }],
            icon: 'unit',
          },
          {
            label: 'Johnson',
            id: 7,
            children: [{ label: 'Johnson Johnson', id: 10, children: [], icon: 'unit' }],
            icon: 'organisational-group',
          },
        ],
      },
    ],
  },
  argTypes: {
    'onUpdate:modelValue': { action: 'update:modelValue' },
  },
}

export default meta

export const SortableTree: StoryObj<typeof AppSortableTree> = {
  render: (args) => ({
    components: { AppSortableTree, SvgIcon },
    setup() {
      return { args }
    },
    template: `
      <div class="flex gap-2 w-full">
        <AppSortableTree dragAndDropGroup="story" class="flex-1 w-full self-start" v-bind="args" />
      </div>
    `,
  }),
}

export const SortableTreeVariants: StoryObj<typeof AppSortableTree> = {
  render: (args) => ({
    components: { AppSortableTree, SvgIcon },
    setup() {
      return { args, modelValueVariants }
    },
    template: `
      <div class="flex gap-2 w-full">
        <template v-for="model in modelValueVariants">
          <AppSortableTree dragAndDropGroup="story" class="flex-1 w-full self-start" :model-value="model" />
        </template>
        
      </div>
    `,
  }),
}

export const SortableTreeWithSlots: StoryObj<typeof AppSortableTree> = {
  render: (args) => ({
    components: { AppSortableTree, SvgIcon },
    setup() {
      return { args }
    },
    template: `
      <div class="flex gap-2 w-full">
        <AppSortableTree dragAndDropGroup="slot-story" class="flex-1" v-bind="args">
          <template #item="{item: childItem}">
            <div class="flex items-center gap-1 max-h-full">
              <SvgIcon :icon="childItem.icon" size="large" class=text-good"></SvgIcon>
              <h3 class="text-action text-2xl whitespace-nowrap">{{childItem.label}}</h3>
            </div>
          </template>
        </AppSortableTree>
      </div>
    `,
  }),
}

export const SortableTreeWithPool: StoryObj<typeof AppSortableTree> = {
  render: (args) => ({
    components: { AppSortableTree, AppSortableTreePool, SvgIcon },
    setup() {
      return {
        args,
        poolOptions: ref([
          {
            icon: 'permanent-establishment',
            label: 'Mike',
            children: [],
            id: 15,
          },
          {
            label: 'Jerry',
            children: [],
            id: 16,
            icon: 'unit',
          },
          {
            label: 'Morty',
            children: [],
            id: 17,
            icon: 'organisational-group',
          },
        ]),
        treeOptions: ref([
          {
            icon: 'permanent-establishment',
            label: 'Juan',
            id: 5,
            children: [
              {
                icon: 'legal-unit',
                label: 'Juan Juan',
                id: 8,
                children: [],
              },
            ],
          },
          {
            icon: 'unit',
            label: 'Edgar',
            id: 6,
            children: [{ label: 'Edgar Edgar', id: 9, children: [], icon: 'organisational-group' }],
          },
        ]),
      }
    },
    template: `
      <div class="flex gap-2 w-full">
        <AppSortableTree dragAndDropGroup="story" class="flex-1 w-full" v-model="treeOptions" :disabled="args.disabled" :readonly="args.readonly" />
        <AppSortableTreePool dragAndDropGroup="story" class="flex-1 w-full" v-model="poolOptions" :disabled="args.disabled" :readonly="args.readonly" />
      </div>
    `,
  }),
}
