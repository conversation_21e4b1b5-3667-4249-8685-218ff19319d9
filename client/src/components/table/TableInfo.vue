<script setup lang="ts">
import Translator from '@js/translator'

withDefaults(
  defineProps<{
    recordCount: number
    selectedRecordsCount?: number
  }>(),
  {
    selectedRecordsCount: 0,
  }
)
</script>

<template>
  <span
    v-if="recordCount === 0"
    class="whitespace-nowrap"
    v-text="Translator.trans('u2.table.no_records')"
  />
  <span v-else class="whitespace-nowrap">
    {{
      Translator.transChoice('u2.table.count_records_found', recordCount, { count: recordCount })
    }}
    <template v-if="selectedRecordsCount > 0">
      ({{ Translator.trans('u2.table.selected_count', { count: selectedRecordsCount }) }})
    </template>
  </span>
</template>
