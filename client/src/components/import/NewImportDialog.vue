<script setup lang="ts">
import uniq from 'lodash/uniq'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import useImportTypesQuery from '@js/composable/useImportTypesQuery'
import AppDialog from '@js/components/AppDialog.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import Translator from '@js/translator'
import type { ImportType } from '@js/model/import'
import type { TranslationId } from '@js/translator'

const emit = defineEmits<(event: 'close') => void>()
function close() {
  emit('close')
}

const { allImportTypes, isLoading } = useImportTypesQuery()

const groups = computed(() => {
  return uniq(allImportTypes?.value.map((importType) => importType.group))
})

const groupConfigurations = computed(() => {
  return groups.value.reduce(
    (acc: Record<string, (option: ImportType) => boolean>, group: TranslationId) => {
      acc[Translator.trans(group)] = (option) => option.group === group
      return acc
    },
    {}
  )
})

const selectedType = ref()
const router = useRouter()
function redirectToImportStart() {
  if (!selectedType.value) {
    return
  }

  router.push({ name: 'ImportStart', params: { configurationKeySlug: selectedType.value } })
}
</script>

<template>
  <AppDialog :title="Translator.trans('u2_core.import.new')" @close="close">
    <AppLoader v-if="isLoading" />
    <BaseSelect
      v-else-if="allImportTypes.length > 0"
      v-model="selectedType"
      class="w-96 max-w-full"
      required
      value-key="shortName"
      label-key="readableName"
      :label="Translator.trans('u2_core.import.selection')"
      :options="allImportTypes"
      :group-configurations="groupConfigurations"
      :placeholder="Translator.trans('u2.select_option')"
      name="import-select"
    />
    <p v-else>
      <em>
        {{ Translator.trans('u2_core.you_are_not_authorized_to_import_any_data') }}
      </em>
    </p>

    <template #buttons>
      <ButtonBasic :disabled="isLoading" @click="close">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonBasic :disabled="isLoading" button-style="solid" @click="redirectToImportStart">
        {{ Translator.trans('u2_core.import.start_import') }}
      </ButtonBasic>
    </template>
  </AppDialog>
</template>
