<script setup lang="ts">
import { computed, ref } from 'vue'
import { useQueryClient } from '@tanstack/vue-query'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AsideSection from '@js/components/AsideSection.vue'
import AssignForm from '@js/components/form/AssignMultiselect.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import AppMessage from '@js/components/AppMessage.vue'
import { queries } from '@js/query'
import Translator from '@js/translator'
import UserGroupLabels from '@js/components/user-group/UserGroupLabels.vue'
import useSavedFilterAssignedUserGroupsQuery from '@js/composable/useSavedFilterAssignedUserGroupsQuery'
import useSavedFilterAssignedUserGroupsUpdateMutation from '@js/composable/useSavedFilterAssignedUserGroupsUpdateMutation'
import useUserGroupsQuery from '@js/composable/useUserGroupsQuery'
import { vAnimate } from '@js/directives/animate'
import type { UserGroup } from '@js/model/userGroup'
import type { SavedFilter } from '@js/model/saved-filter'

const props = withDefaults(
  defineProps<{
    savedFilter: SavedFilter
    disabled?: boolean
    showPublicVisibilityInfo?: boolean
  }>(),
  {
    disabled: false,
    showPublicVisibilityInfo: false,
  }
)

const headline = Translator.trans('u2.assigned_user_groups')
const showAssignmentDialog = ref(false)

const savedFilter = computed(() => props.savedFilter)

const groupsQuery = useSavedFilterAssignedUserGroupsQuery(savedFilter.value.id)
const allUserGroupsQuery = useUserGroupsQuery()
const assignmentOptions = computed(() =>
  allUserGroupsQuery.items.value.map((userGroup) => ({
    id: userGroup['@id'],
    name: userGroup.name,
  }))
)

const loading = computed(() => {
  return allUserGroupsQuery.isLoading.value || groupsQuery.isLoading.value
})

const queryClient = useQueryClient()
const selectedIds = ref<Array<NonNullable<UserGroup['@id']>>>([])

const { mutate: mutateAssigned } = useSavedFilterAssignedUserGroupsUpdateMutation()
function save() {
  mutateAssigned(
    { savedFilter: savedFilter.value, userGroupIds: selectedIds.value },
    {
      onSuccess: () => {
        showAssignmentDialog.value = false
      },
    }
  )
}

async function openDialog() {
  const data = await queryClient.fetchQuery(
    queries.savedFilters.single(savedFilter.value.id)._ctx.userGroups
  )
  selectedIds.value = data['hydra:member'].map((userGroups) => userGroups['@id'])
  showAssignmentDialog.value = true
}
</script>

<template>
  <AsideSection icon="users" :headline="headline">
    <template #button>
      <ButtonEdit class="mt-1" :disabled="disabled" @click="openDialog" />
    </template>

    <div v-animate>
      <AppLoader v-if="loading" />

      <template v-else>
        <AppMessage
          v-if="showPublicVisibilityInfo"
          class="mb-3"
          :text="Translator.trans('u2.saved_filter.visibility_user_group_permissions_warning')"
        />

        <UserGroupLabels
          v-else-if="groupsQuery.items.value.length"
          :groups="groupsQuery.items.value"
        />

        <AppEmptyState v-else>
          <template #title>
            {{ Translator.trans('u2.no_user_groups') }}
          </template>
          {{ Translator.trans('u2.saved_filter.no_user_groups_assigned_description') }}
          <template #action>
            <ButtonBasic :disabled="disabled" @click="openDialog">
              {{ Translator.trans('u2.add_user_groups') }}
            </ButtonBasic>
          </template>
        </AppEmptyState>
      </template>
    </div>

    <AppDialog
      v-if="showAssignmentDialog"
      :title="headline"
      :loading="loading"
      @close="showAssignmentDialog = false"
    >
      <AssignForm v-model="selectedIds" :options="assignmentOptions" />
      <template #buttons>
        <ButtonBasic :disabled="loading" @click="showAssignmentDialog = false">
          {{ Translator.trans('u2.cancel') }}
        </ButtonBasic>

        <ButtonBasic :disabled="loading" button-style="solid" @click="save">
          {{ Translator.trans('u2_core.save_groups') }}
        </ButtonBasic>
      </template>
    </AppDialog>
  </AsideSection>
</template>
