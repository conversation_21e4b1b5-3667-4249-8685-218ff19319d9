<script setup lang="ts">
import Translator from '@js/translator'
import PopupCard from '@js/components/PopupCard.vue'
import LabelWithMenu from '@js/components/LabelWithMenu.vue'
import type { UserGroup } from '@js/model/userGroup'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import { useAuthStore } from '@js/stores/auth'

defineProps<{
  userGroup: UserGroup
}>()

const authStore = useAuthStore()
</script>

<template>
  <LabelWithMenu class="transition-colors duration-700">
    <template #default>
      <span>
        {{ userGroup.name }}
      </span>
    </template>

    <template #content>
      <PopupCard>
        <template #header-title>
          {{ userGroup.name }}
        </template>

        <template #default>
          <div class="flex flex-col">
            <p v-if="userGroup.description" class="text-gray-500">
              {{ userGroup.description }}
            </p>
            <p v-else>
              {{ Translator.trans('u2.no_description') }}
            </p>
          </div>
        </template>

        <template #header-actions>
          <ButtonEdit
            v-if="authStore.hasRole('ROLE_USER_GROUP_ADMIN')"
            :to="{ name: 'UserGroupEdit', params: { id: userGroup.id } }"
          />
        </template>
      </PopupCard>
    </template>
  </LabelWithMenu>
</template>
