import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof AppPageWithAside> = {
  title: 'Page Structure/Page With Aside',
}

export default meta

export const Default: StoryObj<typeof AppPageWithAside> = {
  render: () => ({
    components: { AppPageWithAside },
    template: `
      <div class="flex">
        <AppPageWithAside>
          <template #header>
            <div class="bg-green-50 border border-green-100 max-w-full text-lg">Page header</div>
          </template>
          <template #asideBefore><div class="bg-action text-white text-lg">Content before</div></template>
          <div class="bg-blue-100">
            Content
          </div>
          <template #asideAfter><div class="bg-action text-white text-lg">Content after</div></template>
        </AppPageWithAside>
      </div>
        `,
  }),
}
