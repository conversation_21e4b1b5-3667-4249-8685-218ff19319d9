<script lang="ts" setup generic="T">
import { VueDraggable } from 'vue-draggable-plus'
import DraggableHandle from '@js/components/DraggableHandle.vue'
import type { SortableEvent } from 'sortablejs'

const modelValue = defineModel<Array<T>>({ required: true })
const emit = defineEmits<(event: 'start', payload: SortableEvent) => void>()

defineSlots<{
  default: () => unknown
  item: (props: { item: T; index: number }) => unknown
}>()
const onStart = (event: SortableEvent) => {
  emit('start', event)
}
</script>

<template>
  <VueDraggable v-model="modelValue" handle=".js-sortable-drag-handler" @start="onStart">
    <slot>
      <template v-for="(item, index) in modelValue" :key="item.id">
        <slot :item="item" :index="index" name="item">
          <DraggableHandle>
            {{ item }}
          </DraggableHandle>
        </slot>
      </template>
    </slot>
  </VueDraggable>
</template>
