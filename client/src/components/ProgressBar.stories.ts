import ProgressBar from './ProgressBar.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof ProgressBar> = {
  title: 'Progress Bar',
  argTypes: {
    max: { control: 'number' },
    value: { control: 'number' },
    color: { control: 'text' },
    mode: {
      options: ['compact', 'expanded'],
      control: {
        type: 'select',
      },
    },
  },
  args: {
    max: 100,
    value: 20,
    color: 'u2',
    mode: 'compact',
  },
}

export default meta

export const Default: StoryObj<typeof ProgressBar> = {
  render: (args) => ({
    components: { ProgressBar },
    setup() {
      return { args }
    },
    template: '<ProgressBar v-bind="args" />',
  }),
}
