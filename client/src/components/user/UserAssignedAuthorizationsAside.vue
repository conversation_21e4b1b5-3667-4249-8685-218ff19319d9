<script setup lang="ts">
import isEmpty from 'lodash/isEmpty'
import { computed, ref, toRef, watch } from 'vue'
import { skipToken, useQuery } from '@tanstack/vue-query'
import { fetchUserAuthorisations } from '@js/api/userApi'
import { useAuthStore } from '@js/stores/auth'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import InformationGridRow from '@js/components/InformationGridRow.vue'
import InformationGrid from '@js/components/InformationGrid.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import Translator from '@js/translator'
import { isUserWithAllProperties } from '@js/model/user'
import { queries } from '@js/query'
import type { User } from '@js/model/user'

const props = defineProps<{
  user: User
}>()

const user = toRef(props, 'user')
const { data: resolvedUser } = useQuery({
  ...queries.users.single(user.value.id),
  /*
  TODO: Move this queryFn definition into the query key factory when they are able to resolve the types correctly
    See: https://github.com/lukemorales/query-key-factory/issues/100
 */
  queryFn: computed(() => (user.value ? queries.users.single(user.value.id).queryFn : skipToken)),
  initialData:
    user.value && typeof user.value === 'object' && isUserWithAllProperties(user.value)
      ? user.value
      : undefined,
})

const authStore = useAuthStore()
const isUserGroupAdmin = computed(() => authStore.hasRole('ROLE_USER_GROUP_ADMIN'))

const authorisations = ref<Record<string, Array<string>>>()

async function getRights() {
  if (!resolvedUser.value) {
    return
  }
  const response = await fetchUserAuthorisations(resolvedUser.value.id)
  authorisations.value = response.data
}

const groups = computed(() =>
  resolvedUser.value && isUserWithAllProperties(resolvedUser.value)
    ? resolvedUser.value?.groups
    : []
)

watch(groups, () => {
  getRights()
})

getRights()
</script>

<template>
  <AsideSection
    icon="lock-closed"
    :headline="Translator.trans('u2_core.authorisation.authorisations')"
  >
    <template #button>
      <ButtonEdit v-if="isUserGroupAdmin" :to="{ name: 'AuthorizationList' }" />
    </template>
    <AppLoader v-if="!authorisations" />

    <template v-else>
      <AppEmptyState v-if="isEmpty(authorisations)">
        <template #title>
          {{ Translator.trans('u2.no_authorisations') }}
        </template>

        {{ Translator.trans('u2.user.no_authorisations_assigned_description') }}

        <template v-if="isUserGroupAdmin">
          {{ Translator.trans('u2.no_authorisations_assigned_admin') }}
        </template>

        <template v-else>
          {{ Translator.trans('u2.contact_admin') }}
        </template>

        <template v-if="isUserGroupAdmin" #action>
          <ButtonBasic :to="{ name: 'AuthorizationList' }">
            {{ Translator.trans('u2.assign_authorisations') }}
          </ButtonBasic>
        </template>
      </AppEmptyState>

      <InformationGrid v-else>
        <InformationGridRow v-for="(value, key) in authorisations" :key="key" :label="key">
          {{ value.join(', ') }}
        </InformationGridRow>
      </InformationGrid>
    </template>
  </AsideSection>
</template>
