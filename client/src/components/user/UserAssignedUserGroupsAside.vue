<script setup lang="ts">
import { computed, ref } from 'vue'
import { useQueryClient } from '@tanstack/vue-query'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppDialog from '@js/components/AppDialog.vue'
import AsideSection from '@js/components/AsideSection.vue'
import AssignForm from '@js/components/form/AssignMultiselect.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import { queries } from '@js/query'
import Translator from '@js/translator'
import UserGroupLabels from '@js/components/user-group/UserGroupLabels.vue'
import useUserAssignedUserGroupsQuery from '@js/composable/useUserAssignedUserGroupsQuery'
import useUserAssignedUserGroupsUpdateMutation from '@js/composable/useUserAssignedUserGroupsUpdateMutation'
import useUserGroupsQuery from '@js/composable/useUserGroupsQuery'
import type { UserGroup } from '@js/model/userGroup'
import type { User } from '@js/model/user'

const props = withDefaults(
  defineProps<{
    user: User
    disabled?: boolean
  }>(),
  {
    disabled: false,
  }
)

const headline = Translator.trans('u2.assigned_user_groups')
const showAssignmentDialog = ref(false)

const user = computed(() => props.user)

const groupsQuery = useUserAssignedUserGroupsQuery(() => user.value.id)
const userAssignedUserGroups = computed(() => groupsQuery.userGroups.value)
const isAssignedUserGroupsLoading = computed(() => groupsQuery.isLoading.value)

const allUserGroupsQuery = useUserGroupsQuery()
const assignmentOptions = computed(() =>
  allUserGroupsQuery.items.value.map((userGroup) => ({
    id: userGroup['@id'],
    name: userGroup.name,
  }))
)

const loading = computed(() => {
  return allUserGroupsQuery.isLoading.value || isAssignedUserGroupsLoading.value
})

const queryClient = useQueryClient()
const selectedIds = ref<Array<NonNullable<UserGroup['@id']>>>([])

const { mutate: mutateAssigned } = useUserAssignedUserGroupsUpdateMutation()
function save() {
  mutateAssigned(
    { user: user.value, userGroupIds: selectedIds.value },
    {
      onSuccess: () => {
        showAssignmentDialog.value = false
      },
    }
  )
}

async function openDialog() {
  const data = await queryClient.fetchQuery(queries.users.single(user.value.id)._ctx.userGroups)
  selectedIds.value = data['hydra:member'].map((userGroups) => userGroups['@id'])
  showAssignmentDialog.value = true
}
</script>

<template>
  <AsideSection icon="users" :headline="headline">
    <template #button>
      <ButtonEdit class="mt-1" :disabled="disabled" @click="openDialog" />
    </template>
    <AppLoader v-if="isAssignedUserGroupsLoading" />

    <UserGroupLabels v-else-if="userAssignedUserGroups.length" :groups="userAssignedUserGroups" />

    <AppEmptyState v-else>
      <template #title>{{ Translator.trans('u2.no_user_groups') }}</template>
      <template #default>
        {{ Translator.trans('u2.user.no_user_groups_assigned_description') }}
        <template v-if="disabled">{{ Translator.trans('u2.contact_admin') }}</template>
        <template v-else>{{ Translator.trans('u2.user.no_user_groups_assigned_admin') }}</template>
      </template>
      <template v-if="!disabled" #action>
        <ButtonBasic @click="openDialog">{{
          Translator.trans('u2.assign_user_groups')
        }}</ButtonBasic>
      </template>
    </AppEmptyState>

    <AppDialog
      v-if="showAssignmentDialog"
      :title="headline"
      :loading="loading"
      @close="showAssignmentDialog = false"
    >
      <AssignForm v-model="selectedIds" :options="assignmentOptions" />
      <template #buttons>
        <ButtonBasic :disabled="loading" @click="showAssignmentDialog = false">
          {{ Translator.trans('u2.cancel') }}
        </ButtonBasic>

        <ButtonBasic :disabled="loading" button-style="solid" @click="save">
          {{ Translator.trans('u2_core.save_groups') }}
        </ButtonBasic>
      </template>
    </AppDialog>
  </AsideSection>
</template>
