import PopupCard from '@js/components/PopupCard.vue'
import AppChip from '@js/components/AppChip.vue'
import AppPlaceholder from './AppPlaceholder.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof PopupCard> = {
  title: 'Card',
  component: PopupCard,
  argTypes: {},
  args: {},
}
export default meta

export const Default: StoryObj<typeof PopupCard> = {
  render: (args) => ({
    components: { PopupCard, AppPlaceholder, AppChip },
    setup() {
      return { args }
    },
    template: `
      <PopupCard v-bind="args">
        <template #header-title>Hi</template>
        <template #header-chips>
          <AppChip>Chip 1</AppChip>
          <AppChip>Chip 2</AppChip>
          <AppChip>Chip 3</AppChip>
          <AppChip>Chip 4</AppChip>
          <AppChip>Chip 5</AppChip>
          <AppChip>Chip 6</AppChip>
          <AppChip>Chip 7</AppChip>
          <AppChip>Chip 8</AppChip>
          <AppChip>Chip 9</AppChip>
          <AppChip>Chip 10</AppChip>
          <AppChip>Chip 11</AppChip>
          <AppChip>Chip 12</AppChip>
        </template>
        <template #default>
          <AppPlaceholder size="medium"/>
        </template>
      </PopupCard>
    `,
  }),
}
