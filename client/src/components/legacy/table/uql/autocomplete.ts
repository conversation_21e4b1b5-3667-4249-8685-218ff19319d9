import { z } from 'zod'
import Parser from '@js/Parser'
import Operators from '@js/components/legacy/table/uql/operators.js'
import type { useTaskListStore } from '@js/stores/task-list'
import type SuggestContext from './SuggestContext'

declare module '@js/Parser' {
  const parse: (uql: string) => void
}

const expectedErrorSchema = z.object({
  expected: z
    .union([
      z.record(z.string(), z.unknown()),
      z.record(z.literal('description'), z.string()).optional(),
    ])
    .array()
    .optional(),
})

export default class Autocomplete {
  private fields: ReturnType<typeof useTaskListStore>['fields']
  private functions: ReturnType<typeof useTaskListStore>['functions']

  constructor(
    columns: ReturnType<typeof useTaskListStore>['fields'],
    functions: ReturnType<typeof useTaskListStore>['functions']
  ) {
    this.fields = columns
    this.functions = functions
  }

  suggest(suggestContext: SuggestContext) {
    return (
      this.resolveTokenTypes(suggestContext)
        .map((tokenType) =>
          this.getSuggestionsByType(tokenType, suggestContext).filter((suggestion) => {
            if (typeof suggestion !== 'undefined') {
              return suggestion.toLowerCase().indexOf(suggestContext.partial.toLowerCase()) !== -1
            }
            return false
          })
        )
        .reduce((accumulator, value) => accumulator.concat(value), [])
        // Remove duplicates
        .filter((suggestion, index, inputArray) => inputArray.indexOf(suggestion) === index)
    )
  }

  private resolveTokenTypes(suggestContext: SuggestContext): Array<string> {
    try {
      Parser.parse(suggestContext.prev)
    } catch (error) {
      const zodResult = expectedErrorSchema.safeParse(error)
      if (zodResult.success) {
        if (zodResult.data.expected === undefined) {
          return []
        }
        return zodResult.data.expected
          .map((event) => event?.description)
          .filter((token): token is string => {
            return token !== undefined
          })
      }
    }
    return ['AC_LOGIC', 'AC_OPEN_GROUP']
  }

  private getSuggestionsByType(tokenType: string, suggestContext: SuggestContext) {
    switch (tokenType) {
      case 'AC_IDENTIFIER':
        return this.identifierSuggestions()
      case 'AC_OPERATOR':
        return this.operatorSuggestions(suggestContext)
      case 'AC_LOGIC':
        return ['and', 'or', 'xor']
      case 'AC_OPEN_GROUP':
        return ['(']
      case 'AC_CLOSE_GROUP':
        return [')']
      case 'AC_LITERAL':
        return this.literalChoicesSuggestions(suggestContext).concat(
          this.literalFunctionsSuggestions()
        )
    }
    return []
  }

  private identifierSuggestions() {
    return this.fields
      .filter((column) => column.metadata.filterable)
      .map((column) => column.uniqueName)
  }

  private literalChoicesSuggestions(suggestContext: SuggestContext) {
    if (!suggestContext.assocIdentifier) {
      return []
    }

    const matchingColumns = this.fields.filter(
      (column) => column.uniqueName === suggestContext.assocIdentifier
    )
    if (!matchingColumns.length) {
      return []
    }

    if (matchingColumns[0].type.name === 'boolean') {
      return ['TRUE', 'FALSE']
    }

    const choicesList = []

    for (const choice in matchingColumns[0].choices) {
      choicesList.push(`"${choice}"`)
    }

    if (suggestContext.before === 'is' || suggestContext.before === 'not') {
      // TODO: "before" does not recognize "is not" properly
      choicesList.push('EMPTY')
    }

    return choicesList
  }

  private literalFunctionsSuggestions() {
    return Object.keys(this.functions).map((functionName) => `${functionName}()`)
  }

  private operatorSuggestions(suggestContext: SuggestContext) {
    const field = this.fields.find((column) => column.uniqueName === suggestContext.before)

    if (!field) {
      return Operators.all
    }

    return [
      ...new Set(
        field.type.available.flatMap((op) => {
          return Operators.operatorMap[op]
        })
      ),
    ].filter((suggestion, index, inputArray) => {
      return suggestion && inputArray.indexOf(suggestion) === index
    })
  }
}
