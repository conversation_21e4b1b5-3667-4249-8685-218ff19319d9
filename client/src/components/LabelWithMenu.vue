<script setup lang="ts">
import LabelBasic from '@js/components/LabelBasic.vue'
import BasePopover from '@js/components/BasePopover.vue'
import type { Icon, labelColors } from '@js/utilities/name-lists'

defineOptions({ inheritAttrs: false })

withDefaults(
  defineProps<{
    color?: (typeof labelColors)[number]
    rounded?: 'none' | 'half' | 'full'
    text?: string
    disabled?: boolean
    placement?: 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end'
    icon?: Icon
    tooltip?: string
  }>(),
  {
    color: 'white',
    rounded: 'half',
    text: '',
    placement: 'bottom-start',
    disabled: false,
    icon: undefined,
    tooltip: undefined,
  }
)

defineSlots<{
  default: (props: { disabled: boolean }) => unknown
  content: (props: { close: () => void }) => unknown
}>()
</script>

<template>
  <BasePopover :disabled="disabled">
    <template #default="{ disabled: isPopoverDisabled }">
      <button type="button">
        <LabelBasic
          v-tooltip="tooltip"
          :class="[
            'inline transition-colors duration-700 hover:opacity-70',
            isPopoverDisabled ? 'cursor-default' : 'cursor-context-menu',
          ]"
          :disabled="isPopoverDisabled"
          :color="color"
          :text="text"
          :icon="icon"
          :rounded="rounded"
          v-bind="$attrs"
        >
          <slot name="default" :disabled="isPopoverDisabled" />
        </LabelBasic>
      </button>
    </template>

    <template #content="{ close }">
      <slot name="content" :close="close" />
    </template>
  </BasePopover>
</template>
