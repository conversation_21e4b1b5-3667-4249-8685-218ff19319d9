<script setup lang="ts">
import LabelBasic from '@js/components/LabelBasic.vue'
import type { roleLabelColors } from '@js/utilities/name-lists'

withDefaults(
  defineProps<{
    color?: (typeof roleLabelColors)[number]
    title?: string
  }>(),
  {
    color: 'gray',
    title: undefined,
  }
)
</script>

<template>
  <LabelBasic v-tooltip="title" :color="color">
    <slot />
  </LabelBasic>
</template>
