<script setup lang="ts">
import type { Period } from '@js/api/periodApi'
import { computed, ref, toRefs } from 'vue'
import { required } from '@vuelidate/validators'
import { useVuelidate } from '@vuelidate/core'
import { isAxiosError } from 'axios'
import invariant from 'tiny-invariant'
import FormFieldset from '@js/components/form/FormFieldset.vue'
import { useUserSettingsStore } from '@js/stores/user-settings'
import BaseInputNumber from '@js/components/form/BaseInputNumber.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import useCurrenciesAllQuery from '@js/composable/useCurrenciesAllQuery'
import BaseCurrencySelect from '@js/components/form/BaseCurrencySelect.vue'
import useExchangeRateUpdateMutation from '@js/composable/useExchangeRateUpdateMutation'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import Translator from '@js/translator'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import extractApiErrors from '@js/helper/form/extractApiErrors'
import useFormErrors from '@js/composable/useFormErrors'
import { useNotificationsStore } from '@js/stores/notifications'
import useSavePrompt from '@js/composable/useSavePrompt'
import { exchangeRateTypes } from '@js/model/exchangeRate'
import type { ExchangeRate, ExchangeRateType } from '@js/model/exchangeRate'
import type { Currency } from '@js/model/currency'
import useCalculateExchangeRate from '@js/composable/useCalculateExchangeRate'
import { formatFromTo } from '@js/utilities/number-formatter'

const props = defineProps<{
  exchangeRate?: ExchangeRate
  period: Period['@id']
  disabled?: boolean
}>()

const emit = defineEmits<{
  (event: 'saved', payload: ExchangeRate): void
  (event: 'loading', payload: boolean): void
  (event: 'outputCurrencyUpdated', payload: ExchangeRate['indirectRate']): void
  (event: 'inputCurrencyUpdated' | 'directRateUpdated', payload: ExchangeRate['directRate']): void
}>()
const { exchangeRate, period, disabled } = toRefs(props)

type ExchangeRateForm = {
  id?: number
  exchangeRateType: ExchangeRateType
  inputCurrency: Currency['@id']
  outputCurrency: Currency['@id']
  directRate: string
  indirectRate: string
  period: Period['@id']
}
const userSettingsStore = useUserSettingsStore()

const { calculateIndirectRateFromDirectRate, calculateDirectRateFromIndirectRate } =
  useCalculateExchangeRate()

const formData = ref<ExchangeRateForm>({
  exchangeRateType: exchangeRate.value?.exchangeRateType ?? 1,
  inputCurrency: exchangeRate.value?.inputCurrency ?? '',
  outputCurrency: exchangeRate.value?.outputCurrency ?? '',
  directRate: exchangeRate.value ? getFormattedDirectRate(exchangeRate.value.directRate) : '',
  indirectRate: exchangeRate.value
    ? calculateIndirectRateFromDirectRate(getFormattedDirectRate(exchangeRate.value.directRate))
    : '',
  period: exchangeRate.value?.period ?? period.value,
})
const { set: initializeSavePrompt } = useSavePrompt(formData)

const vuelidate = useVuelidate(
  {
    exchangeRateType: {
      required: {
        ...required,
        $message: defaultValidationMessages.required(),
      },
    },
    inputCurrency: {
      required: {
        ...required,
        $message: defaultValidationMessages.required(),
      },
    },
    outputCurrency: {
      required: {
        ...required,
        $message: defaultValidationMessages.required(),
      },
    },
    directRate: {
      required: {
        ...required,
        $message: defaultValidationMessages.required(),
      },
    },
    indirectRate: {
      required: {
        ...required,
        $message: defaultValidationMessages.required(),
      },
    },
  },
  formData
)

const { errors, setServerValidationErrors, resetServerValidationErrors } =
  useFormErrors<ExchangeRateForm>(vuelidate)
const notificationStore = useNotificationsStore()

const { resolveNotification, handle: handleAxiosErrorResponse } = useHandleAxiosErrorResponse()
const { mutate: saveExchangeRate, isPending: isSaving } = useExchangeRateUpdateMutation()
async function save() {
  vuelidate.value.$touch()
  if (!(await vuelidate.value.$validate())) return false

  emit('loading', true)

  const requestData = {
    ...exchangeRate.value,
    ...formData.value,
    directRate:
      userSettingsStore.locale === 'de'
        ? formatFromTo(formData.value.directRate, '0.0000000000', 'de', 'en')
        : formData.value.directRate,
    period: period.value,
  }

  saveExchangeRate(
    {
      exchangeRate: requestData,
    },
    {
      onSuccess: (data) => {
        formData.value = {
          ...data.data,
          directRate: getFormattedDirectRate(data.data.directRate),
          indirectRate: calculateIndirectRateFromDirectRate(
            getFormattedDirectRate(data.data.directRate)
          ),
        }
        notificationStore.addSuccess(Translator.trans('u2_core.success_saved'))
        resetServerValidationErrors()

        initializeSavePrompt()

        emit('saved', data.data)
      },
      async onError(error) {
        await resolveNotification(error, handleAxiosErrorResponse)
        invariant(isAxiosError(error) && error.response)

        setServerValidationErrors(extractApiErrors(error.response))
      },
      onSettled() {
        emit('loading', false)
      },
    }
  )
}

const { items: allCurrencies, isLoading: isCurrenciesLoading } = useCurrenciesAllQuery()
const exchangeRateTypeOptions = Object.keys(exchangeRateTypes).map((key) => {
  return {
    name:
      key === 'current'
        ? Translator.trans('u2.money.exchange_rate.types.current')
        : Translator.trans('u2.money.exchange_rate.types.average'),
    id: exchangeRateTypes[key as keyof typeof exchangeRateTypes],
  }
})

const isFormDisabled = computed(() => disabled.value || isSaving.value || isCurrenciesLoading.value)

function onUpdateIndirectRate(value: string | number | undefined) {
  const directRate = calculateDirectRateFromIndirectRate(value as string)
  formData.value.directRate = directRate
  emit('directRateUpdated', directRate)
}

function onUpdateDirectRate(value: string | number | undefined) {
  formData.value.indirectRate = calculateIndirectRateFromDirectRate(value as string)
  emit('directRateUpdated', value as string)
}

function getFormattedDirectRate(directRate: string) {
  return userSettingsStore.locale === 'de'
    ? formatFromTo(directRate, '0,0.0000000000', 'en', 'de')
    : directRate
}
</script>

<template>
  <form id="exchange_rate_form" name="exchange_rate_form" @submit.prevent="save">
    <FormFieldset :label="Translator.trans('u2_core.currency.plural')">
      <div class="fields-grid sm:grid-cols-2">
        <BaseCurrencySelect
          v-model="formData.inputCurrency"
          name="exchange_rate_form[inputCurrency]"
          :required="true"
          :disabled="isFormDisabled"
          :errors="errors.inputCurrency"
          :currencies="allCurrencies"
          :label="Translator.trans('u2_core.input_currency')"
          @blur="vuelidate.inputCurrency.$touch()"
          @update:model-value="
            emit(
              'inputCurrencyUpdated',
              allCurrencies.find((currency) => currency['@id'] === $event)?.iso4217code ?? ''
            )
          "
        />
        <BaseCurrencySelect
          v-model="formData.outputCurrency"
          name="exchange_rate_form[outputCurrency]"
          :required="true"
          :disabled="isFormDisabled"
          :errors="errors.outputCurrency"
          :currencies="allCurrencies"
          :label="Translator.trans('u2_core.output_currency')"
          @blur="vuelidate.outputCurrency.$touch()"
          @update:model-value="
            emit(
              'outputCurrencyUpdated',
              allCurrencies.find((currency) => currency['@id'] === $event)?.iso4217code ?? ''
            )
          "
        />
      </div>
    </FormFieldset>

    <FormFieldset :label="Translator.trans('u2_core.exchange_rate')">
      <div class="fields-grid sm:grid-cols-2">
        <BaseSelect
          v-model="formData.exchangeRateType"
          name="exchange_rate_form[exchangeRateType]"
          :required="true"
          :disabled="isFormDisabled"
          :errors="errors.exchangeRateType"
          :options="exchangeRateTypeOptions"
          :label="Translator.trans('u2_core.type')"
          class="col-span-full"
          @blur="vuelidate.exchangeRateType.$touch()"
        />

        <BaseInputNumber
          v-model="formData.directRate"
          name="exchange_rate_form[directRate]"
          :required="true"
          :disabled="isFormDisabled"
          :errors="errors.directRate"
          :label="Translator.trans('u2_core.direct_quotation_value')"
          class="max-w-xxs"
          @blur="vuelidate.directRate.$touch()"
          @update:model-value="onUpdateDirectRate"
        />
        <BaseInputNumber
          v-model="formData.indirectRate"
          name="exchange_rate_form[indirectQuotationValue]"
          :required="true"
          :disabled="isFormDisabled"
          :errors="errors.indirectRate"
          :label="Translator.trans('u2_core.indirect_quotation_value')"
          class="max-w-xxs"
          @blur="vuelidate.indirectRate.$touch()"
          @update:model-value="onUpdateIndirectRate"
        />
      </div>
    </FormFieldset>
  </form>
</template>
