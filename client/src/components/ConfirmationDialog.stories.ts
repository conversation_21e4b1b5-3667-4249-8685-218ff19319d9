import { vueRouter } from 'storybook-vue3-router'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof ConfirmationDialog> = {
  title: 'Dialogs/Confirmation',
  decorators: [vueRouter()],
}

export default meta

export const Default: StoryObj<typeof ConfirmationDialog> = {
  render: () => ({
    components: { ConfirmationDialog },
    template: `
    <ConfirmationDialog />
  `,
  }),
}
