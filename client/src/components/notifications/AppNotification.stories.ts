import AppNotification from '@js/components/notifications/AppNotification.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof AppNotification> = {
  title: 'Alerts/Notification',
  argTypes: {
    content: {
      control: {
        type: 'text',
      },
    },
    type: {
      options: ['success', 'notice', 'warning', 'error'],
      control: {
        type: 'select',
      },
    },
  },
  args: {
    content: 'All good. Cannot complain',
    type: 'success',
  },
  parameters: {
    docs: {
      description: {
        component:
          'AppNotification is a message used to give users feedback after taking an action. It is dismissible.',
      },
    },
  },
}

export default meta

export const Default: StoryObj<typeof AppNotification> = {
  render: (args) => ({
    components: { AppNotification },
    setup() {
      return { args }
    },
    template: `
    <div class="p-5">
      <AppNotification v-bind="args"/>
    </div>
  `,
  }),
}
