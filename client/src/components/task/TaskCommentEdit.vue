<script setup lang="ts">
import invariant from 'tiny-invariant'
import { computed, ref, toRefs } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import TaskCommentTextarea from '@js/components/task/TaskCommentTextarea.vue'
import AppDateTime from '@js/components/AppDateTime.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import Translator from '@js/translator'
import { useCommentsStore } from '@js/stores/comments'
import UserAvatar from '@js/components/user/UserAvatar.vue'
import CurrentUserAssignedUserGroupSelect from '@js/components/form/CurrentUserAssignedUserGroupSelect.vue'
import { queries } from '@js/query'
import { getIdFromIri } from '@js/utilities/api-resource'
import type { Comment } from '@js/model/comment'

const props = defineProps<{
  comment: Comment
}>()
const emit = defineEmits<{ (event: 'cancel'): void; (event: 'save', payload: Comment): void }>()

const { comment } = toRefs(props)

const form = ref({
  content: comment.value.content,
  group: comment.value.group,
})

const resetComment = () => emit('cancel')
function updateComment() {
  const updatedComment = { ...props.comment, ...form.value }
  if (updatedComment.group === '') {
    updatedComment.group = null
  }

  emit('save', updatedComment)
}

const commentsStore = useCommentsStore()
const quotedContent = computed(() =>
  props.comment.quote ? commentsStore.getCommentByIri(props.comment.quote)?.content : undefined
)

const authorId = computed(() => {
  invariant(comment.value.author)
  return getIdFromIri(comment.value.author) as number
})
const { data: author } = useQuery(queries.users.single(authorId))
</script>

<template>
  <form class="flex w-full flex-wrap" @submit.prevent="updateComment">
    <UserAvatar />

    <div class="my-auto ml-2 flex-1">
      <!-- header -->
      <div class="flex h-8 items-center justify-between">
        <div class="flex basis-1/2 items-baseline space-x-2">
          <div class="font-medium leading-normal">
            {{ author?.username }}
          </div>
          <div>
            <AppDateTime :date="comment.createdAt" class="text-sm leading-none text-gray-500" />
          </div>
        </div>
        <div class="flex basis-1/2 justify-end">
          <CurrentUserAssignedUserGroupSelect
            v-model="form.group"
            name="user_group"
            :placeholder="Translator.trans('u2_comment.unrestricted')"
          />
        </div>
      </div>

      <div class="flex flex-col">
        <!-- body -->
        <TaskCommentTextarea
          v-model="form.content"
          class="mt-2"
          required
          :rows="1"
          :label="Translator.trans('u2_comment.comment')"
        >
          <template v-if="quotedContent" #quote>
            {{ quotedContent }}
          </template>
        </TaskCommentTextarea>

        <!-- buttons -->
        <div class="mt-2 flex gap-x-1">
          <ButtonSave type="submit" />
          <ButtonBasic button-style="text" @click="resetComment">
            {{ Translator.trans('u2.cancel') }}
          </ButtonBasic>
        </div>
      </div>
    </div>
  </form>
</template>
