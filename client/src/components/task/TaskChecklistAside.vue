<script setup lang="ts">
import invariant from 'tiny-invariant'
import { computed, ref } from 'vue'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import BaseCheckboxGroup from '@js/components/form/BaseCheckboxGroup.vue'
import BaseCheckbox from '@js/components/form/BaseCheckbox.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import { fetchStates } from '@js/types'
import TaskChecklistHistoryDialog from '@js/components/task/TaskChecklistHistoryDialog.vue'
import Translator from '@js/translator'
import { useTaskStore } from '@js/stores/task'
import { useWorkflowStore } from '@js/stores/workflow'
import { vAnimate } from '@js/directives/animate'
import type { CheckState } from '@js/model/checkstate'
import type { CheckWithStatusesAsObjects } from '@js/model/check'

const currentCheck = ref<CheckWithStatusesAsObjects>()
const showAll = ref(false as boolean)
const showHistory = ref(false as boolean)
const loadingChecks = ref([] as Array<number>)

const task = computed(() => taskStore.task)
const taskStore = useTaskStore()

const isInCurrentWorkflowStatus = (checkItem: CheckWithStatusesAsObjects) =>
  checkItem.statuses.find((status) => status['@id'] === task.value?.status)

const workflowStore = useWorkflowStore()
const checklist = computed((): Array<CheckWithStatusesAsObjects & { checked?: boolean }> => {
  const currentCheckStates = taskStore.currentCheckStates

  return workflowStore.checks
    .filter(
      (check) =>
        check.enabled ||
        currentCheckStates.find((checkState: CheckState) => checkState.check === check['@id']) !==
          undefined
    )
    .map((check) => {
      const currentCheckState = currentCheckStates.find(
        (checkState: CheckState) => checkState.check === check['@id']
      ) || { checked: false }
      return {
        ...check,
        checked: currentCheckState.checked,
      }
    })
})

const workflowFetchStates = computed(() => workflowStore.fetchStates)
const visibleChecks = computed(() =>
  checklist.value.filter((checkItem) => showAll.value || isInCurrentWorkflowStatus(checkItem))
)
const canBeChecked = (checkItem: CheckWithStatusesAsObjects) =>
  checkItem.enabled && task.value?.['u2:extra']?.canWrite
const getTooltipForCheck = (checkItem: CheckWithStatusesAsObjects) => {
  if (!checkItem.enabled) {
    return Translator.trans('u2.workflow_check.disabled')
  }
  if (task.value?.['u2:extra']?.canWrite) {
    return undefined
  }

  return Translator.trans('u2.insufficient_permissions')
}
const hasMoreChecks = computed(() =>
  checklist.value.some((check) => !isInCurrentWorkflowStatus(check))
)
const updateCheckState = async (checkItem: CheckWithStatusesAsObjects, isChecked: boolean) => {
  loadingChecks.value.push(checkItem.id)

  invariant(task.value)
  await taskStore.changeCheckState({
    check: checkItem['@id'],
    checked: isChecked,
    task: task.value['@id'],
  })

  loadingChecks.value = loadingChecks.value.filter((id) => id !== checkItem.id)
}

const showCheckItemHistory = (checkItem: CheckWithStatusesAsObjects) => {
  currentCheck.value = checkItem
  showHistory.value = true
}
</script>

<template>
  <AsideSection icon="list" :headline="Translator.trans('u2_core.workflow.checklist')">
    <template #button>
      <ButtonBasic
        :tooltip="
          showAll
            ? Translator.trans('u2.task_checklist.hide_checks')
            : Translator.trans('u2.task_checklist.show_checks')
        "
        :disabled="hasMoreChecks === false"
        class="mt-1"
        @click="showAll = !showAll"
      >
        <template v-if="showAll">{{ Translator.trans('u2_core.hide') }}</template>
        <template v-else>{{ Translator.trans('u2_core.show') }}</template>
      </ButtonBasic>
    </template>
    <div v-animate>
      <AppLoader
        v-if="
          workflowFetchStates.checks === fetchStates.loading ||
          workflowFetchStates.checks === fetchStates.idle
        "
        class="h-24"
      />

      <template
        v-else-if="workflowFetchStates.checks === fetchStates.resolved && checklist.length > 0"
      >
        <AppEmptyState v-if="visibleChecks.length === 0">
          <template #title>
            {{ Translator.trans('u2.task_checklist.no_checks_title') }}
          </template>
          {{ Translator.trans('u2.task_checklist.no_checks_in_current_status') }}
          <template #action>
            <ButtonBasic @click="showAll = true">{{
              Translator.trans('u2.task_checklist.show_checks_other_status')
            }}</ButtonBasic>
          </template>
        </AppEmptyState>

        <div v-else class="flex flex-col">
          <BaseCheckboxGroup>
            <div
              v-for="checkItem in visibleChecks"
              :key="checkItem.id"
              class="inline-flex items-center gap-2"
            >
              <BaseCheckbox
                :id="`check-item-${checkItem.id}`"
                v-model="checkItem.checked"
                :tooltip="getTooltipForCheck(checkItem)"
                :state="
                  loadingChecks.find((id) => id === checkItem.id) ? fetchStates.loading : undefined
                "
                :disabled="!canBeChecked(checkItem)"
                :label="checkItem.title"
                :label-tooltip="checkItem.description"
                @click.prevent="updateCheckState(checkItem, $event.target.checked)"
              />
              <a
                :id="`show-history-${checkItem.id}`"
                v-tooltip="
                  Translator.trans('u2.task_checklist.check_history_title', {
                    title: checkItem.title,
                  })
                "
                class="flex-none leading-none"
                @click="showCheckItemHistory(checkItem)"
              >
                {{ Translator.trans('u2.history') }}
              </a>
            </div>
          </BaseCheckboxGroup>
        </div>
      </template>

      <AppEmptyState v-else>
        <template #title>
          {{ Translator.trans('u2.task_checklist.no_checks_title') }}
        </template>
        {{ Translator.trans('u2.task_checklist.no_checks') }}
      </AppEmptyState>
    </div>

    <TaskChecklistHistoryDialog
      v-if="showHistory && currentCheck"
      :check-item="currentCheck"
      @close="showHistory = false"
    />
  </AsideSection>
</template>
