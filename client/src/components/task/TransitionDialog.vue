<script lang="ts" setup>
import { transitionTask } from '@js/api/taskApi'
import { isAxiosError } from 'axios'
import { ref, toRefs, watch } from 'vue'
import invariant from 'tiny-invariant'
import AppDialog from '@js/components/AppDialog.vue'
import AssignUserForm from '@js/components/task/AssignUserForm.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import extractApiErrors from '@js/helper/form/extractApiErrors'
import FormErrors from '@js/components/form/FormErrors.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { TransitionWithEmbeddedStatus } from '@js/api/workflowTransitionApi'
import type { Task } from '@js/model/task'
import type { FormErrors as FormErrorsType } from '@js/helper/form/mergeErrors'

const emit = defineEmits<(event: 'close' | 'success') => void>()

const props = defineProps<{
  task: Task
  transition: TransitionWithEmbeddedStatus
}>()

const isSaving = ref(false)
const close = () => emit('close')

const serverValidationErrors = ref<
  FormErrorsType<{
    transition: string
    assignee: string | null
    content: string
    group: string | null
  }>
>({})
const { resolveNotification } = useHandleAxiosErrorResponse()
const save = async () => {
  isSaving.value = true

  try {
    await transitionTask(task.value, {
      transition: props.transition['@id'],
      assignee: formData.value.assignee ?? null,
      userGroup: formData.value.content ? (formData.value.group ?? null) : null,
      comment: formData.value.content,
    })

    emit('success')
  } catch (error) {
    await resolveNotification(error)
    invariant(isAxiosError(error) && error.response)
    serverValidationErrors.value = extractApiErrors(error.response)
  } finally {
    isSaving.value = false
  }
}

const { task } = toRefs(props)
const formData = ref<{
  assignee?: string
  content?: string
  group?: string
}>({
  assignee: task.value.assignee ?? undefined,
  content: undefined,
  group: undefined,
})

watch(
  formData,
  (newValue, oldValue) => {
    if (!newValue.assignee || oldValue.assignee !== newValue.assignee) {
      serverValidationErrors.value = {}
    }
  },
  { deep: true }
)
</script>

<template>
  <AppDialog :title="Translator.trans('u2_core.workflow.confirm_status_transition')" @close="close">
    <div class="ajax-content">
      <FormErrors :errors="serverValidationErrors['']" />

      <div>
        {{
          Translator.trans('u2.workflow.transition_confirm', {
            transition_name: transition.name,
          })
        }}
      </div>
      <div>
        <StatusBadge :status="transition.originStatus" />
        <SvgIcon icon="arrow-right" size="small" class="align-middle" />
        <StatusBadge :status="transition.destinationStatus" />
      </div>

      <AssignUserForm
        id="entity_transition_form"
        v-model:form-data="formData"
        :task="task"
        name="entity_transition_form"
        :is-form-disabled="isSaving"
        :errors="serverValidationErrors"
      />
    </div>

    <template #buttons>
      <ButtonBasic @click="close">
        {{ Translator.trans('u2.cancel') }}
      </ButtonBasic>

      <ButtonSave button-style="solid" :state="isSaving ? 'saving' : 'ready'" @click="save">
        {{
          Translator.trans('u2_core.workflow.change_status_to_given_status', {
            destinationStatus: transition.destinationStatus.name.toUpperCase(),
          })
        }}
      </ButtonSave>
    </template>
  </AppDialog>
</template>
