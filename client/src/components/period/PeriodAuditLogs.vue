<script setup lang="ts">
import { fetchPeriodLogsByQuery } from '@js/api/periodApi'
import type { Period } from '@js/api/periodApi'
import AppEmptyState from '@js/components/AppEmptyState.vue'
import AuditLogEntry from '@js/components/AuditLogEntry.vue'
import InfiniteList from '@js/components/InfiniteList.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import type { AuditLog } from '@js/model/auditLog'
import { queries } from '@js/query'
import { getInfiniteQueryDefaultOptions, resetInfiniteQuery } from '@js/query/helpers'
import Translator from '@js/translator'
import { useInfiniteQuery, useQueryClient } from '@tanstack/vue-query'
import { computed, onUnmounted, toRefs } from 'vue'

const props = defineProps<{
  period: Period
}>()
const { period } = toRefs(props)
const auditLogInfiniteQueryKey = queries.periods.single(period.value.id)._ctx.auditLogInfinite
  .queryKey
const query = useInfiniteQuery({
  queryKey: auditLogInfiniteQueryKey,
  queryFn: ({ pageParam }) =>
    fetchPeriodLogsByQuery(period.value.id, {
      page: Number(pageParam),
      itemsPerPage: 10,
    }).then((response) => response.data),
  ...getInfiniteQueryDefaultOptions<AuditLog>(),
})
const isLoading = computed(() => query.isLoading.value)
const items = computed(
  () => query.data.value?.pages.map((page) => page['hydra:member']).flat() ?? []
)
const queryClient = useQueryClient()
onUnmounted(() => {
  resetInfiniteQuery(auditLogInfiniteQueryKey)
  queryClient.invalidateQueries({ queryKey: auditLogInfiniteQueryKey })
})
</script>

<template>
  <div>
    <AppLoader v-if="isLoading" :key="items.length" />
    <AppEmptyState v-else-if="items.length === 0">
      <template #title>{{ Translator.trans('u2.no_changes') }}</template>
      {{ Translator.trans('u2.audit_log.no_changes_description') }}
    </AppEmptyState>
    <InfiniteList v-else :query="query">
      <template #item="{ item }">
        <AuditLogEntry :entry="item" class="py-2" />
      </template>
    </InfiniteList>
  </div>
</template>
