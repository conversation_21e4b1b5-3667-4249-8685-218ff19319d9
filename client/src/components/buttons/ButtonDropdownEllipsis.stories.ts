import flushPromises from 'flush-promises'
import { onMounted } from 'vue'
import { colors } from '@js/utilities/name-lists'
import ButtonDropdownItem from './ButtonDropdownItem.vue'
import ButtonDropdownEllipsis from './ButtonDropdownEllipsis.vue'
import type { Color } from '@js/utilities/name-lists'
import type { ComponentProps } from 'vue-component-type-helpers'
import type { Meta, StoryObj } from '@storybook/vue3'

type PropsAndCustomArgs = ComponentProps<typeof ButtonDropdownEllipsis> & {
  color: Color
}
const meta: Meta<PropsAndCustomArgs> = {
  title: 'Button/Dropdown Ellipsis',
  argTypes: {
    color: {
      options: colors,
      control: {
        type: 'select',
      },
    },
  },
}

export default meta

export const Default: StoryObj<typeof ButtonDropdownEllipsis> = {
  render: (args) => ({
    components: { ButtonDropdownEllipsis, ButtonDropdownItem },
    setup() {
      onMounted(async () => {
        await flushPromises()

        const container = document.querySelector('#container')
        if (container) {
          container.scrollTop = 260
          container.scrollLeft = 260
        }
      })

      const items = [
        { path: '/link/1', text: 'Link 1' },
        { path: '/link/2', text: 'Link 2' },
      ]
      return { args, items }
    },
    template: `
    <div id="container" class="w-[500px] h-[500px] border-2 max-w-fit overscroll-contain overflow-scroll relative" >
      <div class="flex justify-center items-center w-[1000px] h-[1000px]" >
        <ButtonDropdownEllipsis :class="['text-'+args.color]">
          <template #items>
            <ButtonDropdownItem v-for="item in items" :to="item.path">        
              {{ item.text }}
            </ButtonDropdownItem>
          </template>
        </ButtonDropdownEllipsis>
      </div>
    </div>
`,
  }),
}
