<script setup lang="ts">
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
</script>

<template>
  <ButtonDropdown
    placement="bottom-end"
    class="px-1"
    :arrow="false"
    :aria-label="Translator.trans('u2.open_menu')"
  >
    <template #default>
      <div class="inline-flex h-4 items-center overflow-visible text-inherit" tabindex="-1">
        <SvgIcon icon="dots" size="large" />
      </div>
    </template>
    <template #body>
      <slot name="items" />
    </template>
  </ButtonDropdown>
</template>
