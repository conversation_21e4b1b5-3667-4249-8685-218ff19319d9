import { withActions } from '@storybook/addon-actions/decorator'
import { vueRouter } from 'storybook-vue3-router'
import { colors, icons } from '@js/utilities/name-lists'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import type { ComponentProps } from 'vue-component-type-helpers'
import type { Meta, StoryObj } from '@storybook/vue3'

type PropsAndCustomArgs = ComponentProps<typeof ButtonBasic> & {
  content: string
}
const meta: Meta<PropsAndCustomArgs> = {
  title: 'Button/Basic',
  component: ButtonBasic,
  argTypes: {
    icon: {
      options: icons,
    },
    content: {
      type: 'string',
    },
    type: {
      options: ['button', 'reset', 'submit'],
    },
    disabled: {
      control: {
        type: 'boolean',
      },
    },
    buttonStyle: { table: { disable: true } },
    color: { table: { disable: true } },
  },
  args: {
    content: undefined,
    disabled: false,
    grouped: false,
    tooltip: 'I want to be clicked!',
  },
  decorators: [vueRouter(), withActions],
  parameters: {
    actions: {
      handles: ['click button'],
    },
  },
}
export default meta

export const Default: StoryObj<typeof ButtonBasic> = {
  render: (args) => {
    return {
      components: { ButtonBasic },
      setup() {
        return { args, colors }
      },
      template: `
        <div class="grid gap-2 justify-center">
          <span v-for="style in ['text', 'solid', 'outlined']" class="flex" :class="{'gap-2': !args.grouped}">
              <ButtonBasic
                v-for="(color) in colors"
                v-bind="args"
                :button-style="style"
                :color="color"
              >
                  {{ args.content ?? style + ' ' + color }}
              </ButtonBasic>
          </span>
        </div>
      `,
    }
  },
}
