<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useVuelidate } from '@vuelidate/core'
import { required } from '@vuelidate/validators'
import Translator from '@js/translator'
import { defaultValidationMessages } from '@js/helper/form/validation-messages'
import extractVuelidateErrors from '@js/helper/form/extractVuelidateErrors'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import useWidgetConfiguration from '@js/composable/useWidgetConfiguration'
import BaseRadioGroup from '@js/components/form/BaseRadioGroup.vue'
import type { CurrentWeekOverviewWidget } from '@js/model/dashboard'

const modelValue = defineModel<CurrentWeekOverviewWidget>({
  required: true,
})

const formData = ref<CurrentWeekOverviewWidget['parameters']>({
  size: modelValue.value.parameters?.size ?? 1,
  title:
    modelValue.value.parameters?.title ??
    Translator.trans('u2.dashboard.widget.current_week_overview'),
})

const vuelidate = useVuelidate(
  {
    size: { required: { ...required, $message: defaultValidationMessages.required() } },
    title: { required: { ...required, $message: defaultValidationMessages.required() } },
  },
  formData
)

const errors = computed(() =>
  extractVuelidateErrors<CurrentWeekOverviewWidget['parameters']>(vuelidate.value)
)

const { sizeSelectOptions } = useWidgetConfiguration(modelValue)

const updateModelValue = (value: CurrentWeekOverviewWidget['parameters']) => {
  vuelidate.value.$touch()

  modelValue.value = { ...modelValue.value, parameters: value }
}

onMounted(() => {
  // Populate defaults set by the form up the chain
  updateModelValue(formData.value)
})
</script>

<template>
  <div class="grid grid-cols-1 gap-[--app-form-field-spacing]">
    <BaseInputText
      v-model="formData.title"
      :label="Translator.trans('u2.title')"
      :required="true"
      maxlength="120"
      :errors="errors.title"
      @blur="vuelidate.title.$touch()"
      @update:model-value="
        updateModelValue({ ...formData, title: $event ? $event.toString() : '' })
      "
    />

    <BaseRadioGroup
      v-model="formData.size"
      :required="true"
      horizontal
      :label="Translator.trans('u2.size')"
      class="flex max-w-fit flex-col gap-2"
      :options="sizeSelectOptions"
      :errors="errors.size"
      @update:model-value="updateModelValue({ ...formData, size: $event })"
    />
  </div>
</template>
