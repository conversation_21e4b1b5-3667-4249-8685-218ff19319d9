<script setup lang="ts">
import { computed } from 'vue'
import { useAsyncComponent } from '@js/composable/useAsyncComponent'
import BaseWidget from '@js/components/dashboard/BaseWidget.vue'
import InfoBox from '@js/components/InfoBox.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import useWidgetConfiguration from '@js/composable/useWidgetConfiguration'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'
import type { HtmlWidget as HtmlWidgetModel } from '@js/model/dashboard'

const { defineAsyncComponentWithFallback } = useAsyncComponent()
const HtmlWidgetEditor = defineAsyncComponentWithFallback(
  () => import('@js/components/dashboard/HtmlWidgetEditor.vue')
)

withDefaults(
  defineProps<{
    draggable?: boolean
    editable?: boolean
  }>(),
  {
    editable: false,
    draggable: false,
  }
)

const emit = defineEmits<(event: 'delete') => void>()

const widget = defineModel<HtmlWidgetModel>({
  required: true,
})

const { size, title } = useWidgetConfiguration(widget)
const id = computed(() => widget.value.parameters?.id)
const content = computed(() => widget.value.parameters?.content)

const hasError = computed(
  () =>
    widget.value.parameters === undefined ||
    (widget.value.parameters.content === '' && widget.value.parameters.id !== 'welcome') ||
    widget.value.parameters.title === undefined
)
</script>

<template>
  <BaseWidget
    :is-edit="!modelValue.parameters"
    :has-error="hasError"
    :draggable="draggable"
    :editable="editable"
    :size="size"
  >
    <template #title>
      <span :title="title">
        {{ title }}
      </span>
    </template>

    <template #buttons-edit-mode>
      <ButtonDelete :show-text="false" @click="emit('delete')" />
    </template>

    <template #content-edit-mode>
      <HtmlWidgetEditor v-model="widget" />
    </template>

    <template #content>
      <div class="tinymce-html w-full overflow-auto break-words">
        <!-- eslint-disable-next-line vue/no-v-html -- The html is purified on backend -->
        <div v-if="content.length" v-html="content" />

        <div v-else-if="id === 'welcome' && content === ''">
          <div style="align-items: center; display: flex; gap: 2rem">
            <div style="flex-shrink: 0">
              <img width="60" height="60" src="/assets/img/u2-logo.svg?url" alt="U² logo" />
            </div>
            <div>
              <p>{{ Translator.trans('u2.dashboard.help') }}</p>
            </div>
          </div>

          <table style="width: 100%">
            <tbody>
              <tr>
                <th class="w-1/2 text-left">
                  {{ Translator.trans('u2_core.tools') }}
                </th>
                <th class="w-1/2 text-left">{{ Translator.trans('u2_core.help') }}</th>
              </tr>
              <tr>
                <td>
                  <router-link :to="{ name: 'CalendarOverview' }">
                    <SvgIcon icon="calendar" class="relative top-0.5" />
                    {{ Translator.trans('u2_core.calendar') }}
                  </router-link>
                </td>
                <td>
                  <a
                    href="https://universalunits.atlassian.net/wiki/spaces/DOCS/overview"
                    target="_blank"
                  >
                    <SvgIcon icon="help" class="relative top-0.5" />
                    {{ Translator.trans('u2_core.u2_documentation') }}
                  </a>
                </td>
              </tr>
              <tr>
                <td>
                  <router-link :to="{ name: 'SavedFilterList' }">
                    <SvgIcon icon="filter" class="relative top-0.5" />
                    {{ Translator.trans('u2.saved_filters') }}
                  </router-link>
                </td>
                <td>
                  <router-link :to="{ name: 'SupportInfo' }">
                    <SvgIcon icon="info" class="relative top-0.5" />
                    {{ Translator.trans('u2_core.support') }}
                  </router-link>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </template>
    <template #error>
      <InfoBox :title="Translator.trans('u2_core.empty_widget')" icon="document-empty">
        <p>{{ Translator.trans('u2_core.empty_widget.help') }}</p>
      </InfoBox>
    </template>
  </BaseWidget>
</template>
