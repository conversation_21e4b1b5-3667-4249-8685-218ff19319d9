<script setup lang="ts">
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import type { FileAccessType } from '@js/model/file'

defineProps<{
  accessType: FileAccessType
}>()
</script>

<template>
  <SvgIcon
    v-if="accessType === 'public'"
    v-tooltip="Translator.trans('u2.access_type.public')"
    icon="users"
    class="text-gray-500"
  />
  <SvgIcon
    v-else-if="accessType === 'smart'"
    v-tooltip="Translator.trans('u2_core.file.access_type_smart')"
    icon="key"
    class="text-gray-500"
  />
  <SvgIcon
    v-else-if="accessType === 'protected'"
    v-tooltip="Translator.trans('u2_core.file.access_type_protected')"
    icon="lock-closed"
    class="text-gray-500"
  />
</template>
