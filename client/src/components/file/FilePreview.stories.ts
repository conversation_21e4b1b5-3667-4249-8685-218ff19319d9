import { ref } from 'vue'
import FilePreview from '@js/components/file/FilePreview.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import BaseInputFile from '@js/components/form/BaseInputFile.vue'
import type { Meta, StoryObj } from '@storybook/vue3'

const meta: Meta<typeof FilePreview> = {
  title: 'File Preview',
  args: {
    file: new File([], 'vnd.openxmlformats-officedocument.spreadsheetml.sheet.xls', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }),
    width: 200,
  },
}

export default meta

export const Default: StoryObj<typeof FilePreview> = {
  render: (args) => ({
    components: { FilePreview, BaseInputFile, ButtonBasic },
    setup() {
      const file = ref(args.file)

      return { args, file }
    },
    template: `
      <template v-if="file">
        <FilePreview v-bind="args" :file="file" @remove="file = undefined">
          <template #buttons>
            <ButtonBasic @click="file = undefined">Remove File</ButtonBasic>
          </template>
        </FilePreview>
      </template>
      <BaseInputFile v-else label="Please select a file to preview" v-model="file"/>
    `,
  }),
}
