import { updateAuthorizationProfileAssignedUsers } from '@js/api/authorizationProfileApi'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { AuthorizationProfile } from '@js/model/authorization'
import type { User } from '@js/model/user'

export default function useAuthorizationProfileUpdateAssignedUsersMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (variables: {
      authorizationProfile: AuthorizationProfile
      userIds: Array<NonNullable<User['@id']>>
    }) => {
      return updateAuthorizationProfileAssignedUsers(
        variables.authorizationProfile.id,
        variables.userIds
      )
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: queries.authorizationProfiles.single(variables.authorizationProfile.id).queryKey,
      })
    },
  })
}
