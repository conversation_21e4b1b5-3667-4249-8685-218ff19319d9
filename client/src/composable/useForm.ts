import { useForm as useFormInner } from 'vee-validate'
import { computed, ref } from 'vue'
import { flattenObject } from '@js/utilities/flattenObject'
import type { FlattenAndSetPathsType, FormOptions, GenericObject } from 'vee-validate'
import type { HydraErrorResponse } from '@js/types'

export function extractApiErrorMessages(
  response: HydraErrorResponse
): Record<string, Array<string>> {
  const { violations, 'hydra:title': hydraTitle } = response.data

  if (violations?.length) {
    return violations.reduce<Record<string, Array<string>>>((accumulator, violation) => {
      const { propertyPath, message } = violation
      accumulator[propertyPath] = accumulator[propertyPath]
        ? [...accumulator[propertyPath], message]
        : [message]
      return accumulator
    }, {})
  }

  if (hydraTitle) {
    return { '': [hydraTitle] }
  }

  return {}
}
export default function useForm<T extends GenericObject>(options?: FormOptions<T>) {
  const { setErrors, ...veeValidateUseForm } = useFormInner<T>(options)
  const unmappedErrors = ref(new Set<string>())

  function setResponseErrors(
    errorResponse: HydraErrorResponse,
    propertyPathMap: Record<string, string> = {}
  ) {
    const apiErrors = extractApiErrorMessages(errorResponse)
    const validationKeys = new Set(Object.keys(flattenObject(veeValidateUseForm.values)))
    const mappedErrors: Record<string, Array<string>> = {}

    Object.entries(apiErrors).forEach(([key, messages]) => {
      const mappedKey = propertyPathMap[key]

      if (mappedKey === '') {
        messages.forEach((message) => unmappedErrors.value.add(message))
        return
      }

      if (mappedKey !== undefined) {
        mappedErrors[mappedKey] = [...(mappedErrors[mappedKey] || []), ...messages]
        return
      }

      if (!validationKeys.has(key)) {
        messages.forEach((message) => unmappedErrors.value.add(message))
        return
      }

      mappedErrors[key] = messages
    })

    setErrors(mappedErrors as Partial<FlattenAndSetPathsType<T, string | Array<string>>>)
  }

  return {
    ...veeValidateUseForm,
    setResponseErrors,
    unmappedErrors: computed(() => Array.from(unmappedErrors.value)),
  }
}
