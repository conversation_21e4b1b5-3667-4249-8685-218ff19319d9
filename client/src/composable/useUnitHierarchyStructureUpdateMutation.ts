import { updateUnitHierarchyStructure } from '@js/api/unitHierarchyApi'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { UnitHierarchy, UnitHierarchyStructureApiFormat } from '@js/model/unit_hierarchy'

/**
 * Returns a TansStack mutation to update the unit hierarchy structure.
 */
export default function useUnitHierarchyStructureUpdateMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (variables: {
      unitHierarchyStructure: UnitHierarchyStructureApiFormat
      unitHierarchyId: UnitHierarchy['id']
    }) => {
      return updateUnitHierarchyStructure(variables.unitHierarchyStructure).then((response) => {
        return response.data
      })
    },
    onSuccess: (data, variables) => {
      queryClient.setQueryData(
        queries.unitHierarchies
          .single(variables.unitHierarchyId)
          ._ctx.structure(variables.unitHierarchyStructure.date).query<PERSON>ey,
        data
      )
    },
  })
}
