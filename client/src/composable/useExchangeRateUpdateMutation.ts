import { exchangeRateApi } from '@js/api/exchangeRateApi'
import { invalidatePeriodQueries } from '@js/composable/usePeriodUpdateMutation'
import { getIdFromIri } from '@js/utilities/api-resource'
import { useMutation } from '@tanstack/vue-query'
import type { ExchangeRate } from '@js/model/exchangeRate'

export default function useExchangeRateUpdateMutation() {
  return useMutation({
    mutationFn: (variables: { exchangeRate: Partial<Omit<ExchangeRate, 'id'>> }) => {
      return exchangeRateApi.saveExchangeRate(variables.exchangeRate)
    },
    onSuccess: (data) => {
      invalidatePeriodQueries(getIdFromIri(data.data.period))
    },
  })
}
