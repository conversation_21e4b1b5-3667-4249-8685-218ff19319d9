import { updateUserGroupAssignedUsers } from '@js/api/userGroupApi'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { User } from '@js/model/user'
import type { UserGroup } from '@js/model/userGroup'

export default function useUserGroupUpdateAssignedUsersMutation() {
  const queryClient = useQueryClient()
  return useMutation({
    mutationFn: (variables: { userGroup: UserGroup; userIds: Array<NonNullable<User['@id']>> }) => {
      return updateUserGroupAssignedUsers(variables.userGroup.id, variables.userIds)
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: queries.userGroups.single(variables.userGroup.id).queryKey,
      })
    },
  })
}
