import { computed, reactive, watch } from 'vue'
import { mapSectionToNumbering } from '@js/helper/document/mapSectionToNumbering'
import { mapSectionToParentSection } from '@js/helper/document/mapSectionToParentSection'
import { mapSectionToSubSections } from '@js/helper/document/mapSectionToSubSections'
import { transformSectionsToHierarchy } from '@js/helper/document/transformSectionsToHierarchy'
import { newSectionTitleIdentifier } from '@js/model/document'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'
import type { Ref } from 'vue'

export default function useDocumentSections(
  sectionIdToRenderedContentDataArray: Ref<Array<{ id: DocumentSection['id']; content: string }>>,
  sections: Ref<Array<DocumentSection>>
) {
  // Create a reactive data store that handles all section relationships
  const sectionData = reactive({
    sections: [] as Array<DocumentSection>,
    numberingById: new Map<DocumentSection['id'], string>(),
    parentIdById: new Map<DocumentSection['id'], DocumentSection['id'] | undefined>(),
    subsectionIdsByParentId: new Map<DocumentSection['id'], Array<DocumentSection['id']>>(),
    sectionsById: new Map<DocumentSection['id'], DocumentSection>(),

    // Helper methods that work reliably with any section reference
    getNumbering(section: DocumentSection): string | undefined {
      return this.numberingById.get(section.id)
    },

    getParentSection(section: DocumentSection): DocumentSection | undefined {
      const parentId = this.parentIdById.get(section.id)
      return parentId ? this.sectionsById.get(parentId) : undefined
    },

    getSubsections(section: DocumentSection): Array<DocumentSection> {
      const subsectionIds = this.subsectionIdsByParentId.get(section.id) ?? []
      return subsectionIds.map(id => this.sectionsById.get(id)).filter(Boolean) as DocumentSection[]
    },

    hasSubsections(section: DocumentSection): boolean {
      return (this.subsectionIdsByParentId.get(section.id)?.length ?? 0) > 0
    }
  })

  // Watch for changes in sections and update the reactive store
  watch(sections, (newSections) => {
    // Update sections array
    sectionData.sections = [...newSections]

    // Update sections by ID map
    sectionData.sectionsById.clear()
    newSections.forEach(section => {
      sectionData.sectionsById.set(section.id, section)
    })

    // Update numbering
    const numberingMap = mapSectionToNumbering(newSections)
    sectionData.numberingById.clear()
    newSections.forEach(section => {
      const numbering = numberingMap.get(section)
      if (numbering !== undefined) {
        sectionData.numberingById.set(section.id, numbering)
      }
    })

    // Update parent relationships
    const parentMap = mapSectionToParentSection(newSections)
    sectionData.parentIdById.clear()
    newSections.forEach(section => {
      const parent = parentMap.get(section)
      sectionData.parentIdById.set(section.id, parent?.id)
    })

    // Update subsection relationships
    const subsectionsMap = mapSectionToSubSections(newSections)
    sectionData.subsectionIdsByParentId.clear()
    newSections.forEach(section => {
      const subsections = subsectionsMap.get(section) ?? []
      sectionData.subsectionIdsByParentId.set(section.id, subsections.map(s => s.id))
    })
  }, { immediate: true })

  // Legacy computed properties for backward compatibility
  const subsectionsBySection = computed(() => {
    // Create a Map that works with any section object reference by using ID-based lookup
    const legacyMap = new Map<DocumentSection, Array<DocumentSection>>()

    sections.value.forEach(section => {
      const subsectionIds = sectionData.subsectionIdsByParentId.get(section.id) ?? []
      const subsections = subsectionIds.map(id => sectionData.sectionsById.get(id)).filter(Boolean) as DocumentSection[]
      legacyMap.set(section, subsections)

      // Also handle cases where the section object might be a different reference with the same ID
      const canonicalSection = sectionData.sectionsById.get(section.id)
      if (canonicalSection && canonicalSection !== section) {
        legacyMap.set(canonicalSection, subsections)
      }
    })

    return legacyMap
  })

  const numberingBySection = computed(() => {
    // Create a Map that works with any section object reference by using ID-based lookup
    const legacyMap = new Map<DocumentSection, string>()

    sections.value.forEach(section => {
      const numbering = sectionData.numberingById.get(section.id)
      if (numbering !== undefined) {
        legacyMap.set(section, numbering)

        // Also handle cases where the section object might be a different reference with the same ID
        const canonicalSection = sectionData.sectionsById.get(section.id)
        if (canonicalSection && canonicalSection !== section) {
          legacyMap.set(canonicalSection, numbering)
        }
      }
    })

    return legacyMap
  })

  const renderedContentBySection = computed(() => {
    const map = new Map<DocumentSection, string | undefined>()

    for (const section of sections.value ?? []) {
      if (newSectionTitleIdentifier === section.name) {
        map.set(
          section,
          `
          <div class="mceNonEditable new-section-placeholder">
            <span>*** ${Translator.trans('u2_structureddocument.new_section_content')} ***</span>
          </div>`
        )
        continue
      }

      const sectionWithRenderedContent = (sectionIdToRenderedContentDataArray.value ?? []).find(
        (idAndContent) => idAndContent.id === section.id
      )
      map.set(section, sectionWithRenderedContent?.content)
    }

    return map
  })

  const hasSubsections = computed(() => {
    return (section: DocumentSection) => sectionData.hasSubsections(section)
  })

  const buildSectionNameWithNumbering = computed(() => {
    return (section: DocumentSection) => {
      return getSectionNumber(section) + ' ' + section.name
    }
  })

  const hierarchicalSections = computed(() => {
    return transformSectionsToHierarchy(
      sections.value.map((section) => {
        return {
          section,
          renderedContent: renderedContentBySection.value.get(section),
          tocId: getSectionNumber(section),
        }
      })
    )
  })

  function getSectionNumber(section: DocumentSection) {
    return sectionData.getNumbering(section)
  }

  const sectionToParentSection = computed(() => {
    // Create a Map that works with any section object reference by using ID-based lookup
    const legacyMap = new Map<DocumentSection, DocumentSection | undefined>()

    sections.value.forEach(section => {
      const parentId = sectionData.parentIdById.get(section.id)
      const parent = parentId ? sectionData.sectionsById.get(parentId) : undefined
      legacyMap.set(section, parent)

      // Also handle cases where the section object might be a different reference with the same ID
      const canonicalSection = sectionData.sectionsById.get(section.id)
      if (canonicalSection && canonicalSection !== section) {
        legacyMap.set(canonicalSection, parent)
      }
    })

    return legacyMap
  })

  // Enhanced section maps with reactive store integration
  const sectionMaps = computed(() => {
    return {
      // Backward compatibility - original Maps
      numberingBySection: numberingBySection.value,
      sectionToParentSection: sectionToParentSection.value,

      // New reactive helper methods
      getNumbering: (section: DocumentSection) => sectionData.getNumbering(section),
      getParentSection: (section: DocumentSection) => sectionData.getParentSection(section),
      getSubsections: (section: DocumentSection) => sectionData.getSubsections(section),
      hasSubsections: (section: DocumentSection) => sectionData.hasSubsections(section),
    }
  })

  return {
    buildSectionNameWithNumbering,
    getSectionNumber,
    hasSubsections,
    hierarchicalSections,
    numberingBySection,
    renderedContentBySection,
    sectionToParentSection,
    subsectionsBySection,
    sectionMaps,
    // Direct access to reactive store
    sectionData,
  }
}
