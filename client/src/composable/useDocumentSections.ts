import { computed } from 'vue'
import { mapSectionToNumbering } from '@js/helper/document/mapSectionToNumbering'
import { mapSectionToParentSection } from '@js/helper/document/mapSectionToParentSection'
import { mapSectionToSubSections } from '@js/helper/document/mapSectionToSubSections'
import { transformSectionsToHierarchy } from '@js/helper/document/transformSectionsToHierarchy'
import { newSectionTitleIdentifier } from '@js/model/document'
import Translator from '@js/translator'
import type { DocumentSection } from '@js/model/document'
import type { Ref } from 'vue'

export default function useDocumentSections(
  sectionIdToRenderedContentDataArray: Ref<Array<{ id: DocumentSection['id']; content: string }>>,
  sections: Ref<Array<DocumentSection>>
) {
  // ID-based Maps for reliable lookups
  const numberingBySectionId = computed(() => {
    const sectionsArray = sections.value
    const numberingMap = mapSectionToNumbering(sectionsArray)
    const idMap = new Map<DocumentSection['id'], string>()

    sectionsArray.forEach(section => {
      const numbering = numberingMap.get(section)
      if (numbering !== undefined) {
        idMap.set(section.id, numbering)
      }
    })

    return idMap
  })

  const parentSectionIdBySectionId = computed(() => {
    const sectionsArray = sections.value
    const parentMap = mapSectionToParentSection(sectionsArray)
    const idMap = new Map<DocumentSection['id'], DocumentSection['id'] | undefined>()

    sectionsArray.forEach(section => {
      const parent = parentMap.get(section)
      idMap.set(section.id, parent?.id)
    })

    return idMap
  })

  const subsectionIdsBySectionId = computed(() => {
    const sectionsArray = sections.value
    const subsectionsMap = mapSectionToSubSections(sectionsArray)
    const idMap = new Map<DocumentSection['id'], Array<DocumentSection['id']>>()

    sectionsArray.forEach(section => {
      const subsections = subsectionsMap.get(section) ?? []
      idMap.set(section.id, subsections.map(s => s.id))
    })

    return idMap
  })

  // Legacy computed properties for backward compatibility
  const subsectionsBySection = computed(() => {
    return mapSectionToSubSections(sections.value)
  })

  const numberingBySection = computed(() => {
    return mapSectionToNumbering(sections.value)
  })

  const renderedContentBySection = computed(() => {
    const map = new Map<DocumentSection, string | undefined>()

    for (const section of sections.value ?? []) {
      if (newSectionTitleIdentifier === section.name) {
        map.set(
          section,
          `
          <div class="mceNonEditable new-section-placeholder">
            <span>*** ${Translator.trans('u2_structureddocument.new_section_content')} ***</span>
          </div>`
        )
        continue
      }

      const sectionWithRenderedContent = (sectionIdToRenderedContentDataArray.value ?? []).find(
        (idAndContent) => idAndContent.id === section.id
      )
      map.set(section, sectionWithRenderedContent?.content)
    }

    return map
  })

  const hasSubsections = computed(() => {
    return (section: DocumentSection) => (subsectionsBySection.value.get(section)?.length ?? 0) > 0
  })

  const buildSectionNameWithNumbering = computed(() => {
    return (section: DocumentSection) => {
      return getSectionNumber(section) + ' ' + section.name
    }
  })

  const hierarchicalSections = computed(() => {
    return transformSectionsToHierarchy(
      sections.value.map((section) => {
        return {
          section,
          renderedContent: renderedContentBySection.value.get(section),
          tocId: getSectionNumber(section),
        }
      })
    )
  })
  function getSectionNumber(section: DocumentSection) {
    return numberingBySection.value.get(section)
  }

  const sectionToParentSection = computed(() => {
    return mapSectionToParentSection(hierarchicalSections.value)
  })

  return {
    buildSectionNameWithNumbering,
    getSectionNumber,
    hasSubsections,
    hierarchicalSections,
    numberingBySection,
    renderedContentBySection,
    sectionToParentSection,
    subsectionsBySection,
  }
}
