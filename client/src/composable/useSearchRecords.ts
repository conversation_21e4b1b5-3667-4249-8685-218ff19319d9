import { computed, ref, unref } from 'vue'
import { refDebounced } from '@vueuse/core'
import { toSearchTokens } from '@js/utilities/toSearchTokens'
import type { MaybeRef } from '@vueuse/core'

export default function useSearchRecords<T>(
  records: MaybeRef<Array<T>> | Array<T>,
  properties: Array<keyof T>,
  searchQueryDefault?: string
) {
  const searchQuery = ref(searchQueryDefault ?? '')
  const debouncedSearchQuery = refDebounced(searchQuery, 200)

  const tokens = computed(() => {
    return toSearchTokens(debouncedSearchQuery.value)
  })

  return {
    searchQuery,
    filteredRecords: computed(() => {
      if (tokens.value.length === 0) {
        return unref(records)
      }

      return unref(records).filter((record) => {
        return !tokens.value.some((token) => {
          return !properties.some((property) => {
            const value = record[property]
            return typeof value === 'string' && value.toLowerCase().includes(token.toLowerCase())
          })
        })
      })
    }),
  }
}
