import { computed, toRef, toValue, unref } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { TaskParams } from '@js/model/task'
import type { MaybeRefOrGetter } from 'vue'

export default function useDocumentEditDataQuery(
  taskParams: MaybeRefOrGetter<TaskParams>,
  queryOptions?: {
    enabled?: MaybeRefOrGetter<boolean>
  }
) {
  const taskParamsRef = toRef(taskParams)

  return useQuery({
    ...queries.document.editDocumentData(taskParamsRef),
    ...queryOptions,
    enabled: computed(() =>
      toValue(queryOptions?.enabled) === false ? false : !!unref(taskParamsRef)
    ),
  })
}
