import { format, formatDuration, formatRelative, intervalToDuration, lightFormat } from 'date-fns'
import { de, enGB } from 'date-fns/locale'
import { computed } from 'vue'
import { useUserSettingsStore } from '@js/stores/user-settings'
import Translator from '@js/translator'

export default function useDateFormat() {
  const userSettings = useUserSettingsStore()

  const locale = computed(() => (userSettings.locale === 'de' ? de : enGB))

  function getLocale() {
    return locale.value
  }

  function getDashedDate(date: Date): string {
    return lightFormat(date, 'yyyy-MM-dd')
  }

  function getDate(date: Date): string {
    if (date.toUTCString() === 'Invalid Date') {
      return Translator.trans('u2.unknown')
    }

    return format(date, 'P', { locale: locale.value })
  }

  function getDateTime(date: Date): string {
    if (date.toUTCString() === 'Invalid Date') {
      return Translator.trans('u2.unknown')
    }

    return format(date, 'Pp', { locale: locale.value })
  }

  function getLongDate(date: Date): string {
    if (date.toUTCString() === 'Invalid Date') {
      return Translator.trans('u2.unknown')
    }

    return format(date, 'PPPP', { locale: locale.value })
  }

  function getLongDateTime(date: Date): string {
    if (date.toUTCString() === 'Invalid Date') {
      return Translator.trans('u2.unknown')
    }

    return format(date, 'PPPPpppp', { locale: locale.value })
  }

  function getRelativeDate(date: Date): string {
    if (date.toUTCString() === 'Invalid Date') {
      return Translator.trans('u2.unknown')
    }

    // Do not inline this, it's needed for the Translator to work properly
    const day = format(date, 'eeee', { locale: locale.value })

    const relativeDateFormat = {
      lastWeek: `'${Translator.trans('u2.date.last_day', { day })}'`,
      yesterday: `'${Translator.trans('u2.date.yesterday')}'`,
      today: `'${Translator.trans('u2.date.today')}'`,
      tomorrow: `'${Translator.trans('u2.date.tomorrow')}'`,
      nextWeek: 'eeee',
      other: 'PPP',
    }

    const relativeDateLocale = {
      ...locale.value,
      formatRelative: (
        token: 'lastWeek' | 'yesterday' | 'today' | 'tomorrow' | 'nextWeek' | 'other'
      ) => relativeDateFormat[token],
    }

    return formatRelative(date, Date.now(), { locale: relativeDateLocale })
  }

  function getRelativeDateTime(date: Date): string {
    if (date.toUTCString() === 'Invalid Date') {
      return Translator.trans('u2.unknown')
    }

    // Do not inline this, it's needed for the Translator to work properly
    const day = format(date, 'eeee', { locale: locale.value })
    const time = format(date, 'p', { locale: locale.value })

    const relativeDateFormat = {
      lastWeek: `'${Translator.trans('u2.datetime.last_day', { day, time })}'`,
      yesterday: `'${Translator.trans('u2.datetime.yesterday', { time })}'`,
      today: `'${Translator.trans('u2.datetime.now', { time })}'`,
      tomorrow: `'${Translator.trans('u2.datetime.tomorrow', { time })}'`,
      nextWeek: `'${Translator.trans('u2.datetime.next_week', { day, time })}'`,
      other: 'PPPp',
    }

    const relativeDateLocale = {
      ...locale.value,
      formatRelative: (
        token: 'lastWeek' | 'yesterday' | 'today' | 'tomorrow' | 'nextWeek' | 'other'
      ) => relativeDateFormat[token],
    }

    return formatRelative(date, Date.now(), { locale: relativeDateLocale })
  }

  function duration(start: Date, end: Date) {
    return formatDuration(intervalToDuration({ start, end }), { locale: locale.value })
  }

  return {
    getLocale,
    getDashedDate,
    getDate,
    getDateTime,
    getLongDate,
    getLongDateTime,
    getRelativeDate,
    getRelativeDateTime,
    duration,
  }
}
