import { computed } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { SavedFilter } from '@js/model/saved-filter'

/**
 * Returns a TansStack query with the users assigned to a saved filter via their group membership.
 */
export default function useSavedFilterAssignedUsersByUserGroupsQuery(id: SavedFilter['id']) {
  const query = useQuery(queries.savedFilters.single(id)._ctx.inheritedUsers)
  return {
    ...query,
    users: computed(() => query.data.value?.['hydra:member'] ?? []),
  }
}
