import { computed } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { queries } from '@js/query'
import type { Country } from '@js/model/country'

export default function useCountriesAllQuery() {
  const query = useQuery(queries.countries.all)
  const items = computed(() => query.data.value?.['hydra:member'] ?? [])
  const countriesByIri = computed(() => {
    return new Map(items.value.map((country: Country) => [country['@id'], country]))
  })
  return { ...query, items, countriesByIri }
}
