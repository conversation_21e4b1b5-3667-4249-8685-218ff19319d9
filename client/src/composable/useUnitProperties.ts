import useUnitPropertiesAllQuery from '@js/composable/useUnitPropertiesAllQuery'
import type {
  LegalUnit,
  OrganisationalGroup,
  PermanentEstablishment,
  Unit,
  UnitProperty,
} from '@js/model/unit'

type UnitPropertName =
  | keyof Unit
  | keyof LegalUnit
  | keyof PermanentEstablishment
  | keyof OrganisationalGroup

export default function useUnitProperties() {
  const unitProperties = useUnitPropertiesAllQuery()

  return {
    unitProperties: unitProperties.unitProperties,
    isPropertyEditable: (property: UnitPropertName) => {
      if (unitProperties.isLoading.value) {
        return false
      }

      const foundProperty = unitProperties.unitProperties.value.find(
        (unitProperty: UnitProperty) => unitProperty.id === property
      )
      return foundProperty?.editable ?? false
    },
  }
}
