import { fetchFieldConfigurationStatusSetByIri } from '@js/api/fieldConfigurationApi'
import { acceptHMRUpdate, defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { fetchStates as allFetchStates } from '@js/types'
import { useStatusStore } from '@js/stores/status'
import * as WorkflowApi from '@js/api/workflowApi'
import * as WorkflowTransitionApi from '@js/api/workflowTransitionApi'
import type { Workflow } from '@js/api/workflowApi'
import type { Check, CheckWithStatusesAsObjects } from '@js/model/check'
import type { Transition, TransitionWithEmbeddedStatus } from '@js/api/workflowTransitionApi'
import type { FieldConfigurationStatusSet } from '@js/model/fieldConfigurationStatusSet'
import type { FetchState } from '@js/types'

export type WorkflowFetchStates = {
  checks: FetchState
  fieldConfigurationStatuses: FetchState
  transitions: FetchState
  workflow: FetchState
}

export const useWorkflowStore = defineStore('workflow', () => {
  const checks = ref<Array<CheckWithStatusesAsObjects>>([])
  const fetchStates = ref<WorkflowFetchStates>({
    checks: allFetchStates.idle,
    fieldConfigurationStatuses: allFetchStates.idle,
    transitions: allFetchStates.idle,
    workflow: allFetchStates.idle,
  })
  const fieldConfigurationStatuses = ref<Array<FieldConfigurationStatusSet>>([])
  const transitions = ref<Array<TransitionWithEmbeddedStatus>>([])
  const workflow = ref<Workflow>()

  async function fetchWorkflowByIri(iri: NonNullable<Workflow['@id']>) {
    fetchStates.value.workflow = allFetchStates.loading

    const { data } = await WorkflowApi.fetchWorkflowByIri(iri)
    workflow.value = data

    await Promise.all([
      getTransitions(workflow.value.transitions),
      fetchChecks(workflow.value.checks),
      fetchFieldConfigurationStatuses(workflow.value.fieldConfigurationStatuses),
    ])

    fetchStates.value.workflow = allFetchStates.resolved
  }

  async function fetchWorkflowById(id: NonNullable<Workflow['id']>) {
    fetchStates.value.workflow = allFetchStates.loading

    const { data } = await WorkflowApi.fetchWorkflowById(id)
    workflow.value = data

    await Promise.all([
      getTransitions(workflow.value.transitions),
      fetchChecks(workflow.value.checks),
      fetchFieldConfigurationStatuses(workflow.value.fieldConfigurationStatuses),
    ])

    fetchStates.value.workflow = allFetchStates.resolved
  }

  async function fetchWorkflowByBindingId(bindingId: string) {
    fetchStates.value.workflow = allFetchStates.loading

    const { data: workflows } = await WorkflowApi.fetchWorkflowByBindingId(bindingId)
    workflow.value = workflows['hydra:member'][0]

    await Promise.all([
      getTransitions(workflow.value.transitions),
      fetchChecks(workflow.value.checks),
      fetchFieldConfigurationStatuses(workflow.value.fieldConfigurationStatuses),
    ])

    fetchStates.value.workflow = allFetchStates.resolved
  }

  async function fetchChecks(checkIris: Array<NonNullable<Check['@id']>>) {
    fetchStates.value.checks = allFetchStates.loading
    const checksPromises = checkIris.map((checkIri) =>
      WorkflowApi.fetchWorkflowCheckByIri(checkIri)
    )
    const promises = await Promise.all(checksPromises)
    checks.value = promises.map(({ data }) => data)
    fetchStates.value.checks = allFetchStates.resolved
  }

  async function fetchFieldConfigurationStatuses(
    fieldConfigurationStatusesIri: Array<NonNullable<FieldConfigurationStatusSet['@id']>>
  ) {
    fetchStates.value.fieldConfigurationStatuses = allFetchStates.loading

    const fieldConfigurationStatusesPromises = fieldConfigurationStatusesIri.map((url) =>
      fetchFieldConfigurationStatusSetByIri(url)
    )
    const responses = await Promise.all(fieldConfigurationStatusesPromises)
    fieldConfigurationStatuses.value = responses.map(({ data }) => data)

    fetchStates.value.fieldConfigurationStatuses = allFetchStates.resolved
  }

  async function fetchCheck(checkIri: NonNullable<Check['@id']>) {
    const { data } = await WorkflowApi.fetchWorkflowCheckByIri(checkIri)
    return data
  }

  async function getTransitions(iriTransitions: Array<NonNullable<Transition['@id']>>) {
    fetchStates.value.transitions = allFetchStates.loading

    const transitionPromises = iriTransitions.map((url) =>
      WorkflowTransitionApi.fetchWorkflowTransitionByIri(url)
    )
    const responses = await Promise.all(transitionPromises)
    transitions.value = responses.map(({ data }) => data)

    fetchStates.value.transitions = allFetchStates.resolved
  }

  async function deleteCheck(check: Pick<Check, '@id'>) {
    if (!check['@id']) {
      throw new Error('The Check IRI is not set')
    }
    await WorkflowApi.deleteWorkflowCheckByIri(check['@id'])
    checks.value = checks.value.filter((item) => item['@id'] !== check['@id'])
  }

  async function deleteTransitionItem(id: NonNullable<Transition['id']>) {
    try {
      await WorkflowTransitionApi.deleteWorkflowTransitionById(id)
    } finally {
      transitions.value = transitions.value.filter((transition) => transition.id !== id)
    }
  }

  async function save(updatedWorkflow: Workflow) {
    const response = updatedWorkflow.id
      ? await WorkflowApi.updateWorkflow(updatedWorkflow)
      : await WorkflowApi.createWorkflow(updatedWorkflow)
    workflow.value = response.data
    getTransitions(response.data.transitions)
    fetchChecks(response.data.checks)

    return response
  }

  const availableTransitions = computed(() => {
    const statusStore = useStatusStore()
    const status = statusStore.status
    if (!status) {
      return []
    }
    return transitions.value.filter(
      (transition) => transition.originStatus['@id'] === status['@id']
    )
  })

  return {
    checks,
    fetchStates,
    fieldConfigurationStatuses,
    transitions,
    workflow,
    fetchWorkflowByIri,
    fetchWorkflowById,
    fetchWorkflowByBindingId,
    fetchChecks,
    fetchFieldConfigurationStatuses,
    fetchCheck,
    getTransitions,
    deleteCheck,
    deleteTransitionItem,
    save,
    availableTransitions,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useWorkflowStore, import.meta.hot))
}
