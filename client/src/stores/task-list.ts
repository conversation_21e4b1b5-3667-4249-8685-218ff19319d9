import urlToShortName from '@js/assets/router/urlToShortName'
import { acceptHMRUpdate, defineStore } from 'pinia'
import invariant from 'tiny-invariant'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { taskType<PERSON><PERSON> } from '@js/api/taskTypeApi'
import Url from '@js/components/legacy/table/components/url'
import { useNotificationsStore } from '@js/stores/notifications'
import { useTaskListInfoStore } from '@js/stores/task-list-info'
import type { TableDomData, TableField } from '@js/api/taskTypeApi'
import type { RouteLocation } from 'vue-router'
import type { Pagination } from '@js/model/tablePagination'
import type Operators from '@js/components/legacy/table/uql/operators'
import type { tableContentTypes } from '@js/types'

export const defaultFieldNames = ['Period', 'SearchText'] as Array<TableField['uniqueName']>
export const modes = ['basic', 'advanced'] as const
export type Mode = (typeof modes)[number]

export type TableColumn = {
  id: string
  name: string
  label: string
  type: (typeof tableContentTypes)[number]
  filterable: boolean
  sortable: boolean
  hidden: boolean
  vars?: {
    unit?: string
  }
}

type Logic = 'AND' | 'OR'
export type FilterElement = {
  field: string
  method: keyof typeof Operators.operatorMap
  value: string
}
export type FilterGroup = {
  elements: Array<FilterElement | FilterGroup>
  logic: Logic
}

export function isFilterElement(filter: FilterGroup | FilterElement): filter is FilterElement {
  return 'field' in filter
}

type AdvancedFilterState = {
  compatible: boolean
  error: string
  filters: FilterGroup
  status: string
  uql: string
  valid: boolean
}

export const useTaskListStore = defineStore('task-list', () => {
  const advancedFilterState = ref<AdvancedFilterState>({
    compatible: true,
    error: '',
    filters: {
      elements: [],
      logic: 'AND',
    },
    status: 'empty',
    uql: '',
    valid: true,
  })
  const name = ref<string>()
  const pagination = ref<Pagination>({
    totalItems: undefined,
    currentPage: 0,
    itemsPerPage: 10,
    maxVisiblePages: 5,
  })
  const savedFilterId = ref<string>()
  const selectedColumns = ref<Array<string>>([])
  const selectedRecordIds = ref<Array<number>>([])
  const sort = ref<
    Array<{
      column_id: string
      direction: string
    }>
  >([])
  const tableDomData = ref<TableDomData>()
  const functions = ref<TableDomData['functions']>({})
  const fields = ref<Array<TableField>>([])
  const columns = ref<TableDomData['columns']>([])
  const records = ref<TableDomData['records']>([])

  const queryIsTooComplex = computed(() => !advancedFilterState.value.compatible)

  const selectedRecordsCount = computed(() => selectedRecordIds.value.length)

  const hasData = computed<boolean>(() => records.value.length > 0)

  const visibleColumns = computed(() => columns.value.filter((column) => !column.hidden))

  const router = useRouter()

  async function legacyTableSubmit(urlObject?: URL) {
    urlObject = urlObject ?? Url.build()
    const url = urlObject.href

    await router.push(url.substring(url.indexOf(urlObject.pathname)))
  }

  function changeSort(columnId: string, direction?: string) {
    sort.value = direction
      ? [
          {
            column_id: columnId,
            direction,
          },
        ]
      : []
    pagination.value.currentPage = 0
    const url = Url.build()
    legacyTableSubmit(url)
  }

  function changePage(pageNumber: number) {
    pagination.value.currentPage = pageNumber
    const url = Url.build()
    legacyTableSubmit(url)
  }

  function changeItemsPerPage(count: number) {
    pagination.value.itemsPerPage = count
    pagination.value.currentPage = 0
    const url = Url.build()
    legacyTableSubmit(url)
  }

  function changeSelectedColumns(selectedColumnIds: Array<string>) {
    selectedColumns.value = selectedColumnIds
    const url = Url.build()
    legacyTableSubmit(url)
  }

  function setTableDomData(newTableDomData: TableDomData) {
    advancedFilterState.value.uql = newTableDomData.state.uql
    columns.value = newTableDomData.columns
    fields.value = newTableDomData.fields
    functions.value = newTableDomData.functions
    name.value = newTableDomData.name
    pagination.value = {
      itemsPerPage: newTableDomData.state.pag[1],
      currentPage: newTableDomData.state.pag[0],
    }
    records.value = newTableDomData.records
    savedFilterId.value = newTableDomData.state.filter
    selectedColumns.value = newTableDomData.state.clmns
    selectedRecordIds.value = newTableDomData.state.sel.map((value) => Number(value))
    sort.value = newTableDomData.state.sor.map(([columnId, direction]) => ({
      column_id: columnId,
      direction,
    }))

    tableDomData.value = newTableDomData
  }

  const notificationsStore = useNotificationsStore()
  const taskListInfoStore = useTaskListInfoStore()

  async function fetchData(route: RouteLocation) {
    await taskListInfoStore.fetchTaskListInfo(urlToShortName(route.path), route.query)
    const shortName = taskListInfoStore.taskListInfo?.shortName
    invariant(shortName)
    const { data } = await taskTypeApi.fetchTaskTableByQuery(shortName, route.query)
    notificationsStore.addByType(data.messages)
    setTableDomData(data)
  }

  return {
    advancedFilterState,
    changeItemsPerPage,
    changePage,
    changeSelectedColumns,
    changeSort,
    columns,
    fields,
    functions,
    fetchData,
    hasData,
    legacyTableSubmit,
    name,
    pagination,
    queryIsTooComplex,
    records,
    savedFilterId,
    selectedColumns,
    selectedRecordIds,
    selectedRecordsCount,
    setTableDomData,
    sort,
    visibleColumns,
    /**
     * @deprecated This is the raw data from the backend. Use other store properties instead.
     */
    tableDomData,
  }
})

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useTaskListStore, import.meta.hot))
}
