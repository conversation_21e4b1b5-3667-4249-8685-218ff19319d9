import { taskTypeUrlPatternToShortNameMap } from '@js/model/task'
import type { RouteLocation } from 'vue-router'

export default function (to: RouteLocation | RouteLocation['fullPath']) {
  const url = typeof to === 'string' ? to : to.path

  for (const pattern in taskTypeUrlPatternToShortNameMap) {
    if (url.includes(pattern)) {
      return taskTypeUrlPatternToShortNameMap[pattern]
    }
  }

  throw new Error(`No short name found for URL ${url}`)
}
