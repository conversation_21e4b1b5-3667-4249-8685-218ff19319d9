import axios from 'axios'
import type { Document } from '@js/model/document'
import type { ElmaFile } from '@js/model/elmaFile'

export function downloadElmaFile(payload: {
  countryByCountryReportId: Document['id']
  accountId: string
  bzstNumber: string
}) {
  return axios.post<ElmaFile>('/api/elma-files', {
    countryByCountryReport: `/api/country-by-country-reports/${payload.countryByCountryReportId}`,
    accountId: payload.accountId,
    bzstNumber: payload.bzstNumber,
  })
}
export const elmaFileApi = {
  downloadElmaFile,
}
