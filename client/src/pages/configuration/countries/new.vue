<script lang="ts" setup>
import { useHead } from '@vueuse/head'
import { useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import CountryEditor from '@js/components/CountryEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import type { Country } from '@js/model/country'

const router = useRouter()
useHead({ title: Translator.trans('u2.new_entity_type_name', { entity_type_name: 'Country' }) })

const onSave = (country: Country) => {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
  router.push({ name: 'CountryEdit', params: { id: country.id } })
}
const countryEditor = useTemplateRef<typeof CountryEditor>('countryEditor')
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2.country')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'CountryList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: Translator.trans('u2.country'),
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="country" :state="countryEditor?.state" />
      </PageHeader>
    </template>
    <CountryEditor ref="countryEditor" @saved="onSave" />
  </AppPageWithAside>
</template>
