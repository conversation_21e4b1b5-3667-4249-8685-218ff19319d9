<script lang="ts" setup>
import { useHead } from '@vueuse/head'
import { useTemplateRef } from 'vue'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AuditorEditor from '@js/components/AuditorEditor.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import type { Auditor } from '@js/model/auditor'

const router = useRouter()
useHead({ title: Translator.trans('u2.new_entity_type_name', { entity_type_name: 'Auditor' }) })
const auditorEditor = useTemplateRef<typeof AuditorEditor>('auditorEditor')
const onSave = (auditor: Auditor) => {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
  router.push({ name: 'AuditorEdit', params: { id: auditor.id } })
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.auditor')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'AuditorList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: Translator.trans('u2_core.auditor'),
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="auditor" :state="auditorEditor?.state" />
      </PageHeader>
    </template>
    <AuditorEditor ref="auditorEditor" @saved="onSave" />
  </AppPageWithAside>
</template>
