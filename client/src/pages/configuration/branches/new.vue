<script lang="ts" setup>
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import BranchEditor from '@js/components/BranchEditor.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useNotificationsStore } from '@js/stores/notifications'
import type { Branch } from '@js/model/branch'

const router = useRouter()
useHead({ title: Translator.trans('u2.new_entity_type_name', { entity_type_name: 'Branch' }) })
const onSave = (branch: Branch) => {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
  router.push({ name: 'BranchEdit', params: { id: branch.id } })
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.branch')">
        <ButtonBasic
          icon="list"
          :to="{ name: 'BranchList' }"
          :tooltip="
            Translator.trans('u2_core.given_entity_type_list', {
              entity_type_name: Translator.trans('u2_core.branch'),
            })
          "
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonSave form="branch_form" />
      </PageHeader>
    </template>

    <BranchEditor @saved="onSave" />
  </AppPageWithAside>
</template>
