<script setup lang="ts">
import { keepPreviousData, useQuery } from '@tanstack/vue-query'
import { computed } from 'vue'
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router'
import { useHead } from '@vueuse/head'
import { watchDebounced } from '@vueuse/core'
import AppLoader from '@js/components/loader/AppLoader.vue'
import AppSkeleton from '@js/components/AppSkeleton.vue'
import { queries } from '@js/query'
import TableControls from '@js/components/table/TableControls.vue'
import AppPageWide from '@js/components/page-structure/AppPageWide.vue'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import ButtonNew from '@js/components/buttons/ButtonNew.vue'
import { flattenObject } from '@js/utilities/flattenObject'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import useCountriesAllQuery from '@js/composable/useCountriesAllQuery'
import useTable from '@js/composable/useTable'
import { getIdFromIri } from '@js/utilities/api-resource'
import { roleNameMap } from '@js/model/role'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const isUserGroupAdmin = computed(() => authStore.hasRole('ROLE_USER_GROUP_ADMIN'))
useHead({ title: Translator.trans('u2_core.users') })

const { data: userGroupsData } = useQuery(queries.userGroups.list({ pagination: false }))
const userGroups = computed(() => userGroupsData.value?.['hydra:member'] ?? [])
const { countriesByIri, isLoading: countriesByIriIsLoading } = useCountriesAllQuery()
const {
  columns,
  query,
  apiQuery,
  fromLocationQuery,
  toLocationQuery,
  sort,
  setSelectedColumns,
  changePage,
  changePageSize,
} = useTable(
  [
    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.id'),
      id: 'id',
      required: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.username'),
      id: 'username',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.name_first'),
      id: 'contact.nameFirst',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.name_last'),
      id: 'contact.nameLast',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.email'),
      id: 'contact.email',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.locked'),
      id: 'locked',
      type: 'boolean',
      selectedByDefault: true,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.parent_user'),
      id: 'parentUser.username',
      type: 'user',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.account_expires'),
      id: 'accountExpires',
      type: 'date',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.password_expires'),
      id: 'passwordExpires',
      type: 'date',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.last_login'),
      id: 'lastLogin',
      type: 'datetime',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.last_activity'),
      id: 'lastActivity',
      type: 'datetime',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.company'),
      id: 'contact.company',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2.country'),
      id: 'contact.country.nameShort',
      type: 'country',
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.telephone'),
      id: 'contact.telephone',
      wrap: false,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.mobile'),
      id: 'contact.mobile',
      wrap: false,
    },

    {
      align: 'left',
      filter: false,
      isSortable: true,
      name: Translator.trans('u2_core.fax'),
      id: 'contact.fax',
      wrap: false,
    },

    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.user_groups'),
      id: 'groups',
      type: 'list',
    },

    {
      align: 'left',
      filter: false,
      isSortable: false,
      name: Translator.trans('u2_core.roles'),
      id: 'userRoles',
      type: 'list',
    },

    {
      name: '',
      id: 'actions',
      required: true,
    },
  ],
  { filter: { search: '' } },
  { cacheKey: 'user' }
)

const { data: usersData, isLoading } = useQuery({
  ...queries.users.list(apiQuery),
  placeholderData: keepPreviousData,
})
const users = computed(() => usersData.value?.['hydra:member'] ?? [])
const totalItems = computed(() => usersData.value?.['hydra:totalItems'] ?? 0)

const items = computed(() => {
  return users.value
    .map((user) => flattenObject(user))
    .map((flatUser) => {
      return {
        ...flatUser,
        'contact.country.nameShort': flatUser['contact.country']
          ? countriesByIri.value.get(flatUser['contact.country'])
          : null,
        'parentUser.username': flatUser.parentUser ? getIdFromIri(flatUser.parentUser) : undefined,
        userRoles: flatUser.roles?.map((role) => roleNameMap[role]()),
        groups: flatUser.groups?.map(
          (group) => userGroups.value.find((userGroup) => userGroup['@id'] === group)?.name
        ),
      }
    })
})

watchDebounced(
  query,
  (newValue) => {
    router.push({ query: toLocationQuery(newValue) })
  },
  { deep: true, debounce: 400 }
)
const newQuery = fromLocationQuery(route.query)
if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
  query.value = newQuery
}

onBeforeRouteUpdate(async (to) => {
  const newQuery = fromLocationQuery(to.query)
  if (JSON.stringify(newQuery) !== JSON.stringify(query.value)) {
    query.value = newQuery
  }
})
</script>

<template>
  <AppPageWide>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.users')">
        <ButtonNew
          :to="{ name: 'UserNew' }"
          :disabled="!isUserGroupAdmin"
          :tooltip="Translator.trans('u2_core.add_new_user')"
        />
      </PageHeader>
    </template>

    <template #beforeWideContent>
      <AppSearch
        v-model="query.filter.search"
        class="w-full"
        :placeholder="Translator.trans('u2.search')"
      />

      <TableControls
        v-if="!isLoading"
        :headers="columns"
        :query="query"
        :total-items="totalItems"
        :selected="query.selectedColumns"
        @page-change="changePage"
        @page-size-change="changePageSize"
        @new-column-selection="setSelectedColumns"
      />
    </template>

    <AppLoader v-if="isLoading" class="mt-10" />

    <AppTable
      v-else
      :headers="columns"
      :query="query"
      :items="items"
      :total-items="totalItems"
      :selected="query.selectedColumns"
      sticky-header
      :has-controls="false"
      @sort="sort"
    >
      <template #item-id="{ item }">
        <router-link :to="{ name: 'UserEdit', params: { id: item.id } }">
          {{ item.id }}
        </router-link>
      </template>
      <template #item-username="{ item }">
        <span class="font-bold">{{ item.username }}</span>
      </template>
      <template #itemtype-country="{ value }">
        <AppSkeleton v-if="countriesByIriIsLoading" />
        <span v-else-if="value">{{ value.nameShort }}</span>
      </template>
      <template #item-actions="{ item }">
        <span class="flex justify-end">
          <ButtonEdit :to="{ name: 'UserEdit', params: { id: item.id } }" />
        </span>
      </template>
    </AppTable>
  </AppPageWide>
</template>
