<script setup lang="ts">
import { deleteDashboardById, fetchDashboardById } from '@js/api/dashboardApi'
import { useQueryClient } from '@tanstack/vue-query'
import { onBeforeRouteUpdate, useRouter } from 'vue-router'
import { useConfirmDialog, useLocalStorage } from '@vueuse/core'
import { useHead } from '@vueuse/head'
import { computed, ref, toRefs } from 'vue'
import invariant from 'tiny-invariant'
import { isAxiosError } from 'axios'
import { roles } from '@js/model/role'
import AppPage from '@js/components/page-structure/AppPage.vue'
import Translator from '@js/translator'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import { useAuthStore } from '@js/stores/auth'
import { queries } from '@js/query'
import useDashboardQuery from '@js/composable/useDashboardQuery'
import DashboardWidgetList from '@js/components/dashboard/DashboardWidgetList.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import { useNotificationsStore } from '@js/stores/notifications'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonDropdownEllipsis from '@js/components/buttons/ButtonDropdownEllipsis.vue'
import ButtonDropdownItem from '@js/components/buttons/ButtonDropdownItem.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import { vAnimate } from '@js/directives/animate'
import useDashboardForm from '@js/composable/useDashboardForm'
import SelectNewDashboardWidgetButton from '@js/components/dashboard/SelectNewDashboardWidgetButton.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { Dashboard, Widget as DashboardWidget } from '@js/model/dashboard'
import type { RouteLocation } from 'vue-router'

const props = defineProps<{ dashboard: Dashboard }>()

const { dashboard } = toRefs(props)

const dashboardId = computed(() => dashboard.value.id)
const dashboardQuery = useDashboardQuery(dashboardId)
const currentDashboard = computed(() => dashboardQuery.data.value ?? dashboard.value)

useHead({
  title: () => `${currentDashboard.value?.title} - ${Translator.trans('u2.dashboard')}`,
})

const isEditable = ref(false)
const authStore = useAuthStore()
const canEdit = computed(() => authStore.hasRole(roles[1]))

const queryClient = useQueryClient()

const { saveDashboard: save, savePrompt, formData, isSaving } = useDashboardForm(currentDashboard)
async function saveDashboard() {
  if (await save()) {
    await queryClient.invalidateQueries({
      queryKey: queries.dashboards.single(dashboardId).queryKey,
    })
    isEditable.value = false
  }
}

const router = useRouter()

const {
  isRevealed: isConfirmDeleteRevealed,
  reveal: revealConfirmDelete,
  confirm: confirmDelete,
  cancel: cancelDelete,
} = useConfirmDialog()
const { resolveNotification } = useHandleAxiosErrorResponse()

async function deleteDashboard() {
  const { isCanceled } = await revealConfirmDelete()
  if (!isCanceled) {
    try {
      invariant(typeof dashboard.value?.id === 'string', 'Expected IRI')

      await deleteDashboardById(dashboard.value.id)
      useNotificationsStore().addSuccess(Translator.trans('u2.success_removed'))

      queryClient.invalidateQueries({ queryKey: queries.menu.mainMenuJson.queryKey })

      await router.push({ name: 'DashboardList' })
    } catch (error) {
      resolveNotification(error)
    }
  }
}

function onDashboardWidgetAdded(widget: Pick<DashboardWidget, 'id' | 'name'>) {
  formData.value.widgets = [widget as DashboardWidget, ...(formData.value.widgets ?? [])]
}

function onAbort() {
  if (savePrompt.changed.value && !savePrompt.confirm()) {
    return
  }

  savePrompt.revert()
  isEditable.value = false
}

onBeforeRouteUpdate(async (to: RouteLocation) => {
  const dashboardId = to.params.id

  invariant(typeof dashboardId === 'string', 'Expected a string')

  const redirectToDashboard = await queryClient.ensureQueryData({
    queryKey: queries.dashboards.single(ref(dashboardId)).queryKey,
    queryFn: () =>
      fetchDashboardById(dashboardId)
        .then((response) => response.data)
        .catch((error) => {
          // Ignore 404. We will redirect to AppDefaultDashboard
          if (!isAxiosError(error) || error.response?.status !== 404) {
            throw error
          }
        }),
  })

  invariant(authStore.user)
  const lastUsedDashboardId = useLocalStorage<Dashboard['id']>(
    'dashboard:last:' + authStore.user.id,
    null
  )
  if (redirectToDashboard === undefined) {
    lastUsedDashboardId.value = null
    return {
      name: 'AppDefaultDashboard',
    }
  }

  lastUsedDashboardId.value = redirectToDashboard['id']

  to.meta.dashboard = redirectToDashboard
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="currentDashboard?.title">
        <template v-if="canEdit">
          <ButtonBasic v-if="!isEditable" @click="isEditable = true">
            {{ Translator.trans('u2.dashboard.edit_widgets') }}
          </ButtonBasic>

          <template v-else>
            <ButtonBasic @click="onAbort">
              {{ Translator.trans('u2.cancel') }}
            </ButtonBasic>

            <ButtonSpacer />

            <SelectNewDashboardWidgetButton @widget-selected="onDashboardWidgetAdded" />

            <ButtonSpacer />

            <ButtonSave :state="isSaving ? 'saving' : 'ready'" @click="saveDashboard" />
          </template>

          <ButtonDropdownEllipsis>
            <template #items>
              <ButtonDropdownItem
                icon="edit"
                :text="Translator.trans('u2.edit')"
                :to="{ name: 'DashboardEdit', params: { id: dashboardId } }"
              />

              <ButtonDropdownItem
                icon="delete"
                :text="Translator.trans('u2.delete')"
                :tooltip="
                  Translator.trans('u2_core.delete_given_entity_type_with_given_name', {
                    entity_type_name: Translator.trans('u2.dashboard'),
                    entity_name: dashboard.title,
                  })
                "
                @click="deleteDashboard"
              />

              <ButtonDropdownItem
                icon="add"
                :text="Translator.trans('u2.new')"
                :to="{ name: 'DashboardNew' }"
              />
            </template>
          </ButtonDropdownEllipsis>
        </template>
      </PageHeader>
    </template>

    <div v-animate class="flex flex-wrap content-start items-start">
      <DashboardWidgetList
        v-if="currentDashboard"
        v-model="formData.widgets"
        :draggable="isEditable"
        :editable="isEditable"
      />
    </div>

    <ConfirmationDialog
      v-if="isConfirmDeleteRevealed"
      @confirm="confirmDelete"
      @close="cancelDelete"
    >
      {{ Translator.trans('u2_core.delete_entry.confirmation') }}
    </ConfirmationDialog>
  </AppPage>
</template>
