<script setup lang="ts">
import { ref } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import AppPage from '@js/components/page-structure/AppPage.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import Translator from '@js/translator'
import { fetchSupportInfo, updateSupportInfo } from '@js/api/supportInfoApi'
import useForm from '@js/composable/useForm'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import FormErrors from '@js/components/form/FormErrors.vue'
import FieldTextareaWysiwyg from '@js/components/form/FieldTextareaWysiwyg.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import type { EditorState } from '@js/types'

useHead({ title: Translator.trans('u2_core.edit_support_information') })

const router = useRouter()
const { handleSubmit, setResponseErrors, unmappedErrors, setFieldValue } = useForm({
  validationSchema: toTypedSchema(z.object({ html: z.string() })),
  initialValues: { html: '' },
})

const { resolveNotification } = useHandleAxiosErrorResponse()
const state = ref<EditorState>('loading')
const save = handleSubmit(async (values) => {
  state.value = 'saving'

  updateSupportInfo(values.html)
    .then(() => {
      router.push({ name: 'SupportInfo' })
    })
    .catch((error) => {
      resolveNotification(error)
      setResponseErrors(error.response)
    })
    .finally(() => {
      state.value = 'ready'
    })
})

async function loadData() {
  try {
    const { data } = await fetchSupportInfo()
    setFieldValue('html', data.html)
  } finally {
    state.value = 'ready'
  }
}

await loadData()
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.edit_support_information')">
        <ButtonBasic :to="{ name: 'SupportInfo' }" icon="cross">
          {{ Translator.trans('u2.cancel') }}
        </ButtonBasic>

        <ButtonSave form="support_form" :state="state" @click.prevent="save" />
      </PageHeader>
    </template>

    <form id="support_edit" name="support_edit">
      <FormErrors :errors="unmappedErrors" />
      <FieldTextareaWysiwyg name="html" />
    </form>
  </AppPage>
</template>
