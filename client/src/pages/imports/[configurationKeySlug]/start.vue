<script setup lang="ts">
import { computed, ref, toRef } from 'vue'
import { useHead } from '@vueuse/head'
import { useRouter } from 'vue-router'
import orderBy from 'lodash/orderBy'
import AppPageWithAside from '@js/components/page-structure/AppPageWithAside.vue'
import AsideSection from '@js/components/AsideSection.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import HelpPanel from '@js/components/help/HelpPanel.vue'
import HelpPanelToggler from '@js/components/help/HelpPanelToggler.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import SvgIcon from '@js/components/SvgIcon.vue'
import Translator from '@js/translator'
import AppLoader from '@js/components/loader/AppLoader.vue'
import { importApi } from '@js/api/importApi'
import BaseInputFile from '@js/components/form/BaseInputFile.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import ConfirmationDialog from '@js/components/ConfirmationDialog.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'
import useImportTypeQuery from '@js/composable/useImportTypeQuery'
import { supportedCsvTypes, supportedExcelTypes } from '@js/model/import'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'
import { extractImportDataFromFile } from '@js/pages/imports/extractImportDataFromFile'
import { prepareDataForImport } from '@js/pages/imports/prepareDataForImport'
import type { ImportRequest } from '@js/model/import'

const showConfirmationDialog = ref(false)
const loading = ref(false)

const props = defineProps<{
  configurationKeySlug: string
}>()

const configurationKeySlug = toRef(props, 'configurationKeySlug')
const { data: importConfiguration, isLoading: isConfigurationLoading } = useImportTypeQuery(
  configurationKeySlug.value
)

const configurationFields = computed(() =>
  orderBy(importConfiguration.value?.configuration?.fields ?? [], ['label'], ['asc'])
)

const entityName = computed(() => importConfiguration.value?.readableName)
useHead({
  title: computed(() => {
    return (
      Translator.trans('u2.import.importing_given_item', { item: entityName.value }) +
      ' - Universal Units'
    )
  }),
})

const router = useRouter()

const disabled = computed(() => loading.value || isConfigurationLoading.value)

const fileErrors = ref<Array<string>>([])
const fileInput = ref()
const form = ref<ImportRequest>({
  configurationKey: configurationKeySlug.value.replaceAll(/-/g, '_'),
  dryRun: false,
  reference: '',
  data: [],
})

const isSubmitEnabled = computed(() => fileErrors.value.length !== 0 || !fileInput.value)

const processFile = async (file?: File) => {
  fileErrors.value = []
  form.value.data = []
  fileInput.value = file

  if (!file) {
    return
  }

  const fileData = await extractImportDataFromFile(file)

  // We do not need the parsed extra. Only the data below a header is relevant.
  fileData.forEach((entry) => {
    delete entry.__parsed_extra
  })

  if (fileData.length === 0) {
    fileErrors.value.push(Translator.trans('u2_core.import.no_data_found', {}, 'validators'))
    return
  }

  const fieldNames = configurationFields.value.map((field) => field.label)
  const invalidHeaders = Object.keys(fileData[0]).filter((key) => !fieldNames.includes(key))

  if (invalidHeaders.length > 0) {
    fileErrors.value.push(
      Translator.trans('u2_core.import.invalid_headers', {
        invalidHeaders: invalidHeaders.join(', '),
        validHeaders: fieldNames.join(', '),
      })
    )
    return
  }

  fileInput.value = file
  form.value.reference = file.name

  form.value.data = fileData.map((entry) => {
    const newEntry = {} as Record<string, string>
    for (const [key, value] of Object.entries(entry)) {
      const property = configurationFields.value.find((field) => field.label === key)
      if (!property) {
        throw new Error(`Unable to identify field for import header ${key}`)
      }

      newEntry[property.id] = value
    }
    return newEntry
  })
}

const { resolveNotification } = useHandleAxiosErrorResponse()
async function save() {
  loading.value = true

  if (fileErrors.value.length > 0) {
    return
  }

  try {
    const response = await importApi.startImport({
      configurationKey: form.value.configurationKey,
      dryRun: form.value.dryRun,
      reference: form.value.reference,
      data: prepareDataForImport(form.value.data),
    })

    router.push({ name: 'ImportResult', params: { id: response.data.id } })
  } catch (error) {
    await resolveNotification(error)
  }
  showConfirmationDialog.value = false
  loading.value = false
}

function submit() {
  if (!form.value.dryRun) {
    showConfirmationDialog.value = true
    return
  }

  save()
}
</script>

<template>
  <AppPageWithAside>
    <template #header>
      <PageHeader>
        <template #title>
          <div v-if="entityName" class="flex items-baseline gap-2">
            <PageHeaderTitle
              :title="Translator.trans('u2.import.importing_given_item', { item: entityName })"
            />
            <HelpPanelToggler help-panel-id="import-start-help" />
          </div>
        </template>
        <ButtonBasic
          icon="list"
          :to="{ name: 'ImportList' }"
          :tooltip="Translator.trans('u2_core.import.list')"
        >
          {{ Translator.trans('u2.list') }}
        </ButtonBasic>

        <ButtonSpacer />

        <ButtonBasic
          :disabled="isSubmitEnabled"
          button-style="solid"
          color="good"
          icon="upload"
          @click.prevent="submit"
        >
          {{ Translator.trans('u2.import.import') }}
        </ButtonBasic>
      </PageHeader>
    </template>

    <template #default>
      <HelpPanel id="import-start-help" :title="Translator.trans('u2_core.help')">
        <!-- eslint-disable-next-line vue/no-v-html -->
        <span v-html="Translator.trans('u2_core.import.help')"></span>
        <hr class="mx-0 my-3 border-blue-200" />
        <!-- eslint-disable-next-line vue/no-v-html -->
        <span v-html="Translator.trans('u2_core.import.help_values_contain_commas')"></span>
      </HelpPanel>

      <BaseInputFile
        required
        :label="Translator.trans('u2_core.import.select_a_file_to_import')"
        :disabled="disabled"
        :help-tooltip="Translator.trans('u2.import.file.help')"
        :model-value="fileInput"
        :errors="fileErrors"
        :progress="undefined"
        :supported-mime-types="[...supportedCsvTypes, ...supportedExcelTypes]"
        @update:model-value="processFile"
      />

      <BaseToggle
        v-model="form.dryRun"
        :disabled="disabled"
        :label="Translator.trans('u2.simulate')"
        :help-tooltip="Translator.trans('u2.import.simulate.help')"
        :required="true"
      />

      <ConfirmationDialog
        v-if="showConfirmationDialog"
        :title="Translator.trans('u2_core.import.confirm')"
        :accept-text="Translator.trans('u2.import.import')"
        @close="showConfirmationDialog = false"
        @confirm="save"
      >
        {{ Translator.trans('u2_core.import.not_simulated_confirmation') }}
      </ConfirmationDialog>
    </template>
    <template #asideAfter>
      <AsideSection icon="info" :headline="Translator.trans('u2_core.configuration_information')">
        <template v-if="isConfigurationLoading">
          <AppLoader />
        </template>
        <template v-else>
          <template v-if="importConfiguration?.configuration?.help">
            {{ importConfiguration.configuration.help }}
          </template>

          <p>{{ Translator.trans('u2_core.import.possible_headers_data_import') }}</p>
          <ul v-if="configurationFields">
            <li v-for="field in configurationFields" :key="field.label">
              <strong>{{ field.label }} </strong>

              <template v-if="field['@type'] === 'LookupFieldConfiguration'">
                (<em>{{ field.class.substring(field.class.lastIndexOf('\\') + 1) }}</em
                >: <kbd>{{ field.lookupField }}</kbd
                >)
              </template>

              <template
                v-if="
                  field['@type'] === 'DateTimeFieldConfiguration' ||
                  field['@type'] === 'DateFieldConfiguration'
                "
              >
                (<kbd>{{ field.format }}</kbd
                >)
              </template>

              <VTooltip v-if="field.help" class="inline">
                <SvgIcon icon="help" size="small" class="text-action" />
                <template #popper>
                  <!-- eslint-disable-next-line vue/no-v-html -->
                  <span v-html="field.help" />
                </template>
              </VTooltip>
            </li>
          </ul>
        </template>
      </AsideSection>
    </template>
  </AppPageWithAside>
</template>
