<script setup lang="ts">
import { fetchUserSettings } from '@js/api/userApi'
import invariant from 'tiny-invariant'
import { computed, onMounted, ref } from 'vue'
import { useHead } from '@vueuse/head'
import ApiKeyList from '@js/components/ApiKeyList.vue'
import AppPage from '@js/components/page-structure/AppPage.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import ButtonSpacer from '@js/components/buttons/ButtonSpacer.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import ResetTrustedDevicesButton from '@js/components/user/ResetTrustedDevicesButton.vue'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import { useNotificationsStore } from '@js/stores/notifications'
import UserSettingsEditor from '@js/components/user/UserSettingsEditor.vue'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import FeatureToggles from '@js/components/FeatureToggles.vue'
import useHandleAxiosErrorResponse from '@js/composable/useHandleAxiosErrorResponse'

const loading = ref(true)

const userSettings = ref()

const authStore = useAuthStore()
const currentUser = computed(() => authStore.user)
const isCurrentUserAdmin = computed(() => authStore.hasRole('ROLE_ADMIN'))
const hasApiRole = computed(() => authStore.hasRole('ROLE_API'))
useHead({ title: Translator.trans('u2_core.edit_user_settings') })

const systemSettingsQuery = useSystemSettingsAllQuery()
const isTwoFactorEnforced = computed(
  () => !!systemSettingsQuery.systemSettings.value?.securityTwoFactorIsEnforced
)

const { resolveNotification } = useHandleAxiosErrorResponse()
async function loadData() {
  try {
    invariant(authStore.user)
    const { data } = await fetchUserSettings(authStore.user.id)

    userSettings.value = data
  } catch (error) {
    await resolveNotification(error)
  }
}

function onSave() {
  useNotificationsStore().addSuccess(Translator.trans('u2_core.success'))
}

const allowSave = computed(
  () => loading.value === false && systemSettingsQuery.isLoading.value === false
)

onMounted(async () => {
  await Promise.all([loadData()])
  loading.value = false
})
</script>

<template>
  <AppPage>
    <template #header>
      <PageHeader :title="Translator.trans('u2_core.edit_user_settings')">
        <ResetTrustedDevicesButton v-if="userSettings && currentUser" :user="currentUser" />

        <ButtonSpacer />

        <ButtonSave :disabled="!allowSave" form="user_setting" />
      </PageHeader>
    </template>

    <UserSettingsEditor
      v-if="userSettings"
      :settings="userSettings"
      :is-two-factor-enforced="isTwoFactorEnforced"
      @saved="onSave"
    />

    <ApiKeyList v-if="hasApiRole" class="mt-10" />
    <FeatureToggles v-if="isCurrentUserAdmin" class="mt-10" />
  </AppPage>
</template>
