<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { computed, watch } from 'vue'
import DatasheetCollectionGroupView from '@js/components/datasheet/DatasheetCollectionGroupView.vue'
import DatasheetCollectionUnitView from '@js/components/datasheet/DatasheetCollectionUnitView.vue'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import useImmediateRouteQuery from '@js/composable/useImmediateRouteQuery'
import { numberToString, stringToNumber } from '@js/utilities/data-transformers'
import useLayoutCollectionQuery from '@js/composable/useLayoutCollectionQuery'
import invariant from 'tiny-invariant'
import { useFieldInspectorStore } from '@js/stores/field-inspector'
import { useRouteQuery } from '@vueuse/router'

const route = useRoute()

const datasheetParametersStore = useDatasheetParametersStore()
datasheetParametersStore.updateFromLocationQuery(route.query)
watch(
  () => route.query,
  () => {
    datasheetParametersStore.updateFromLocationQuery(route.query)
  }
)

const sheetId = computed(() => Number(route.params.sheetId))
datasheetParametersStore.parameters.layout = sheetId.value

const datasheetCollectionId = computed(() => route.params.id as string)
datasheetParametersStore.parameters.layoutCollection = String(datasheetCollectionId.value)

const { data: sheetCollection, suspense } = useLayoutCollectionQuery(datasheetCollectionId)

await suspense()
if (sheetCollection.value === undefined) {
  useRouter().push({
    name: 'Error404',
    params: { pathMatch: route.path.substring(1).split('/') },
    query: route.query,
    hash: route.hash,
  })
}

invariant(sheetCollection.value)
watch(sheetId, (newValue) => {
  datasheetParametersStore.parameters.layout = newValue
})

useImmediateRouteQuery('period', datasheetParametersStore.parameters.period, {
  get: stringToNumber,
  set: numberToString,
})

const showUnitView = computed(() => {
  if ('unit' in route.query) {
    return true
  }

  if ('unitHierarchy' in route.query) {
    return false
  }

  return datasheetParametersStore.parameters.selectedView === 'unit'
})

const fieldId = useRouteQuery<string, number | null | undefined>('field', undefined, {
  transform(value) {
    return value ? Number(value) : null
  },
})

const fieldInspector = useFieldInspectorStore()

watch(
  fieldId,
  (newFieldId) => {
    fieldInspector.setFieldId(newFieldId)
  },
  { immediate: true }
)

watch(
  () => fieldInspector.field,
  (newField) => {
    if (newField?.id === fieldId.value) {
      return
    }

    fieldId.value = newField?.id
  },
  { immediate: true }
)
</script>

<template>
  <DatasheetCollectionUnitView v-if="showUnitView" />
  <DatasheetCollectionGroupView v-else />
</template>
