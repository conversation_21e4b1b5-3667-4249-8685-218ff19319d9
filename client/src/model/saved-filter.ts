import type { ApiResource, ApiResourceId } from '@js/types'
import type { TaskShortName } from '@js/model/task'

export type SavedFilter = ApiResource & {
  id: number
  '@type': 'SavedFilter'
  name: string
  owner: ApiResourceId
  public: boolean
  favourite: boolean
  description: string
  uql: string
  taskShortName: TaskShortName
  createdAt?: string
  updatedAt?: string
  updatedBy?: ApiResourceId
  createdBy?: ApiResourceId
  canEdit: boolean
  canDelete: boolean
}

export type SavedFilterSubscription = ApiResource & {
  id: number
  name: string
  active: boolean
  description: string
  frequency: string
  lastRun: string | null
  nextRun: string
  savedFilter: SavedFilter
  createdAt: string
  updatedAt: string
  updatedBy?: ApiResourceId
  createdBy?: ApiResourceId
  userCount: number
  groupCount: number
}

export type SavedFilterInformation = {
  changed: boolean
  savedFilter: null | SavedFilter
}

export function isSavedFilter(entity: ApiResource): entity is SavedFilter {
  return entity['@type'] === 'SavedFilter'
}

export function isSavedFilterSubscription(entity: ApiResource): entity is SavedFilterSubscription {
  return entity['@type'] === 'SavedFilterSubscription'
}
