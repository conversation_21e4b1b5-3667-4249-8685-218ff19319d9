import type { Icon } from '@js/utilities/name-lists'

export type MultiSelectSlots<TOption> = {
  selectedValue?: (props: {
    option: TOption
    activeOption: TOption | undefined
    clear: (option: TOption) => void
  }) => unknown
  option?: (props: {
    option: TOption
    disabled: boolean
    icon: Icon | undefined
    label: string
  }) => unknown
  optionLabel?: (props: { option: TOption; disabled: boolean; label: string }) => unknown
  optionIcon?: (props: {
    option: TOption | undefined
    disabled: boolean
    icon: Icon | undefined
  }) => unknown
}

export type SelectSlots<TOption> = {
  selectedValue?: (props: { option: TOption; isHighlighted: boolean }) => unknown
  option?: (props: {
    option: TOption
    disabled: boolean
    icon: Icon | undefined
    label: string
  }) => unknown
  optionLabel?: (props: { option: TOption; disabled: boolean; label: string }) => unknown
  optionIcon?: (props: {
    option: TOption | undefined
    disabled: boolean
    icon: Icon | undefined
  }) => unknown
}

export type SelectOptionGroup<TOption> = {
  label: string
  options: Array<TOption>
}

export type SelectIcon<TOption> = keyof TOption | ((option: TOption) => Icon | undefined)
export type SelectLabel<TOption> = keyof TOption | ((option: TOption) => string | undefined)
export type SelectSort<TOption> = keyof TOption | ((a: TOption, b: TOption) => number) | undefined
export type SelectGroupConfigurations<TOption> = Record<string, (option: TOption) => boolean>
