import type { Period } from '@js/api/periodApi'
import type { AnyBasicUnit } from '@js/model/unit'
import type { ApiResource, ApiResourceId } from '@js/types'
import type { User } from '@js/model/user'

export type AuditLogChangeType = 'change' | 'addition' | 'removal'

export type AuditLogChange = {
  type: AuditLogChangeType
  field: string
  addedElement?: string
  removedElement?: string
  from?: string
  to?: string
}

export type AuditLog = ApiResource & {
  id: number
  auditedEntity: string
  timestamp: string
  auditUser?: ApiResourceId
  changes: Array<AuditLogChange>
  username: string | null
  user: User['@id'] | null
}
export const auditLogTypes = [
  'legal-unit',
  'organisational-group',
  'period',
  'permanent-establishment',
  'unit',
  'user',
] as const

export type AuditLogType = (typeof auditLogTypes)[number]

export type AuditLogTypeId = AnyBasicUnit['id'] | Period['id'] | User['id']
