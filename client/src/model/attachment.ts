import type { ApiResource, ApiResourceId } from '@js/types'
import type { FileEntity } from '@js/model/file'

export type UnrestrictedAttachment = ApiResource &
  Omit<FileEntity, '@type' | 'downloadPath'> & {
    '@type': 'Attachment'
    file: FileEntity['@id']
    entity: ApiResourceId
    links: {
      unattachPath: string | null
      fileEditPath: string | null
      downloadPath: string
    }
  }

export type RestrictedAttachment = ApiResource &
  Omit<FileEntity, 'name' | '@type'> & {
    '@type': 'Attachment'
    file: FileEntity['@id']
    entity: ApiResourceId
    name: null
    links: {
      unattachPath: null
      fileEditPath: null
      downloadPath: null
    }
  }

export type Attachment = UnrestrictedAttachment | RestrictedAttachment

export function isUnrestrictedAttachment(entity: ApiResource): entity is UnrestrictedAttachment {
  return (
    entity['@type'] === 'Attachment' && ('name' in entity ? typeof entity.name === 'string' : false)
  )
}

export function isRestrictedAttachment(entity: ApiResource): entity is RestrictedAttachment {
  return entity['@type'] === 'Attachment' && ('name' in entity ? entity.name === null : false)
}
