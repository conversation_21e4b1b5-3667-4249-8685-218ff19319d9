import { fetchDatasheetGroupPermissions } from '@js/api/datasheetApi'
import { datasheetCollectionApi } from '@js/api/datasheetCollectionApi'
import { fetchDocumentGroupPermissions } from '@js/api/documentApi'
import { fetchDocumentTemplateGroupPermissions } from '@js/api/documentTemplateApi'
import type { PermissionableTask } from '@js/model/task'

export default function fetchGroupPermissions(permissionableTask: PermissionableTask) {
  switch (permissionableTask['@type']) {
    case 'DocumentTemplate':
      return fetchDocumentTemplateGroupPermissions(permissionableTask)
    case 'Layout':
      return fetchDatasheetGroupPermissions(permissionableTask)
    case 'LayoutCollection':
      return datasheetCollectionApi.fetchDatasheetCollectionGroupPermissions(permissionableTask)
    case 'Task':
      return fetchDocumentGroupPermissions(permissionableTask)
    default:
      throw new Error(
        'Resource with type ' + permissionableTask['@type'] + ' is not a permissionable entity.'
      )
  }
}
