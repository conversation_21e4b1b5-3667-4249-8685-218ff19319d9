import type { DocumentSection } from '@js/model/document'

function buildNumbering(currentNumber: Array<number>) {
  return currentNumber.join('.')
}

export function mapSectionToNumbering(sections: Array<DocumentSection>) {
  const numberingBySectionId = new Map<DocumentSection['id'], string>()
  let currentNumber: Array<number> = []
  let disabledLevel: number = Number.MAX_SAFE_INTEGER

  sections.forEach((section) => {
    const currentLevel = currentNumber.length
    const level = section.level
    if (level > disabledLevel) {
      numberingBySectionId.set(section.id, buildNumbering(Array(level).fill('-')))
      return
    }

    if (!section.include) {
      disabledLevel = level
      numberingBySectionId.set(section.id, buildNumbering(Array(level).fill('-')))
      return
    }

    disabledLevel = Number.MAX_SAFE_INTEGER

    if (level > currentLevel) {
      currentNumber = [...currentNumber, 1]
      numberingBySectionId.set(section.id, buildNumbering([...currentNumber]))
      return
    }

    if (level < currentLevel) {
      currentNumber = currentNumber.slice(0, level)
    }

    currentNumber[currentNumber.length - 1]++

    numberingBySectionId.set(section.id, buildNumbering([...currentNumber]))
  })

  return numberingBySectionId
}
