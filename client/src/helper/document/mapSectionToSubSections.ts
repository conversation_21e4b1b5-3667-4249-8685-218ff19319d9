import type { DocumentSection } from '@js/model/document'

function findSubsectionsAndTheirSubSections(
  section: DocumentSection,
  sections: Array<DocumentSection>
) {
  const subSectionIds = []
  for (const subSection of sections) {
    if (subSection.level <= section.level) {
      break
    }

    subSectionIds.push(subSection.id)
  }

  return subSectionIds
}

export function mapSectionToSubSections(
  sections: Array<DocumentSection>
): Map<DocumentSection['id'], Array<DocumentSection['id']>> {
  const subsectionsBySectionId = new Map()
  for (const [index, section] of sections.entries()) {
    subsectionsBySectionId.set(
      section.id,
      findSubsectionsAndTheirSubSections(section, sections.slice(index + 1))
    )
  }

  return subsectionsBySectionId
}
