import type { DocumentSection } from '@js/model/document'

function findSubsectionsAndTheirSubSections(
  section: DocumentSection,
  sections: Array<DocumentSection>
) {
  const subSections = []
  for (const subSection of sections) {
    if (subSection.level <= section.level) {
      break
    }

    subSections.push(subSection.id)
  }

  return subSections
}

export function mapSectionToSubSections(
  sections: Array<DocumentSection>
): Map<DocumentSection['id'], Array<DocumentSection['id']>> {
  const subsectionsBySection = new Map()
  for (const [index, section] of sections.entries()) {
    subsectionsBySection.set(
      section.id,
      findSubsectionsAndTheirSubSections(section, sections.slice(index + 1))
    )
  }

  return subsectionsBySection
}
