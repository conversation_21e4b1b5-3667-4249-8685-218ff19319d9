import type { DocumentSection } from '@js/model/document'
import type { HierarchicalSection } from './transformSectionsToHierarchy'

export function mapSectionToParentSection(hierarchicalSections: Array<HierarchicalSection>) {
  const parentMap = new Map<DocumentSection['id'], DocumentSection['id'] | undefined>()
  for (const topSection of hierarchicalSections) {
    parentMap.set(topSection.section.id, undefined)

    processSubsections(topSection, parentMap)
  }

  return parentMap
}

function processSubsections(
  parentSection: HierarchicalSection,
  parentMap: Map<DocumentSection['id'], DocumentSection['id'] | undefined>
) {
  for (const subSection of parentSection.subHierarchicalSections) {
    parentMap.set(subSection.section.id, parentSection.section.id)
    processSubsections(subSection, parentMap)
  }
}
