import type { DocumentSection } from '@js/model/document'

function findParent(section: DocumentSection, sections: Array<DocumentSection>) {
  // Find parent by iterating backwards through the sections array
  for (let i = sections.length - 1; i >= 0; i--) {
    const potentialParent = sections[i]
    if (potentialParent.level < section.level) {
      return potentialParent
    }
  }
  return undefined
}

export function mapSectionToParentSection(
  sections: Array<DocumentSection>
): Map<DocumentSection, DocumentSection | undefined> {
  return new Map(
    sections.map((section, index) => {
      return [section, findParent(section, sections.slice(0, index))]
    })
  )
}
