import { onUnmounted, ref } from 'vue'
import Translator from '@js/translator'

export function useDetectReadyToEdit() {
  const preventClick = ref(false)

  document.addEventListener('mousedown', resolvePreventClick)
  document.addEventListener('keydown', resolvePreventClick)

  function getCurrentSelection() {
    return window && window.getSelection()
  }

  function getHasActiveSelection() {
    const currentSelection = getCurrentSelection()

    return (
      (currentSelection && currentSelection.rangeCount > 0 && !currentSelection.isCollapsed) ??
      false
    )
  }

  // Event handler to prevent going to edit mode if a selection is detected or a link is clicked
  function resolvePreventClick(event: MouseEvent | KeyboardEvent) {
    const hasActiveSelection = getHasActiveSelection()

    const target = event.target as HTMLElement
    const isEditButton =
      target.getAttribute('aria-label')?.includes(`${Translator.trans('u2.edit_content')}`) ||
      target.getAttribute('aria-label')?.includes(`${Translator.trans('u2.edit')}`)

    if (
      target.tagName.toLowerCase() === 'a' ||
      (target.tagName.toLowerCase() === 'button' && !isEditButton)
    ) {
      preventClick.value = true
      return
    }

    if (preventClick.value && !hasActiveSelection) {
      preventClick.value = false
      return
    }
    if (hasActiveSelection) {
      preventClick.value = true
    }
  }

  function isReadyToEdit() {
    if (preventClick.value) {
      return false
    }
    return !getHasActiveSelection()
  }

  onUnmounted(() => {
    document.removeEventListener('mousedown', resolvePreventClick)
  })

  return isReadyToEdit
}
