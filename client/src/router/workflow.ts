import { computed } from 'vue'
import ConfigurationDataLayout from '@js/layouts/ConfigurationDataLayout.vue'
import Translator from '@js/translator'
import type { RouteLocation, RouteRecordRaw } from 'vue-router'

export default [
  {
    component: () => import('@js/pages/configuration/workflows/new.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
    },
    name: 'WorkflowNew',
    path: '/configuration/workflows/new',
  },

  {
    component: () => import('@js/pages/configuration/workflows/index.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      globalSearch: {
        icon: 'config',
        name: () => Translator.trans('u2_core.workflows'),
      },
    },
    name: 'WorkflowList',
    path: '/configuration/workflows',
  },

  {
    component: () => import('@js/pages/configuration/workflows/[id]/index.vue'),
    meta: {
      auth: 'R<PERSON><PERSON>_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { id: number }) => {
        return `${Translator.trans('u2_core.workflow.edit_workflow')} #${params.id}`
      },
    },
    name: 'WorkflowEdit',
    path: '/configuration/workflows/:id',
    props: (route: RouteLocation) => ({ id: +route.params.id }),
  },

  {
    component: () => import('@js/pages/configuration/workflows/[id]/transitions/new.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
    },
    name: 'WorkflowTransitionNew',
    path: '/configuration/workflows/:id/transitions/new',
    props: (route: RouteLocation) => ({ id: +route.params.id }),
  },

  {
    component: () =>
      import('@js/pages/configuration/workflows/[id]/transitions/[transitionId]/index.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { id: number }) => {
        return `${Translator.trans('u2.transition')} #${params.id}`
      },
    },
    name: 'WorkflowTransitionEdit',
    path: '/configuration/workflows/:id/transitions/:transitionId',
    props: (route: RouteLocation) => ({
      transitionId: +route.params.transitionId,
      id: +route.params.id,
    }),
  },

  {
    component: () =>
      import(
        '@js/pages/configuration/workflows/[id]/transitions/[transitionId]/conditions/new/[conditionTypeId].vue'
      ),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: () => {
        return Translator.trans('u2_core.workflow.adding_condition_to_transition')
      },
    },
    name: 'WorkflowConditionNew',
    path: '/configuration/workflows/:id/transitions/:transitionId/conditions/new/:conditionTypeId',
    props: (route: RouteLocation) => ({
      transitionId: route.params.transitionId,
      conditionTypeId: route.params.conditionTypeId,
    }),
  },
  {
    component: () => import('@js/pages/configuration/workflows/[id]/checks/new.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
    },
    name: 'WorkflowCheckNew',
    path: '/configuration/workflows/:id/checks/new',
    props: (route: RouteLocation) => ({ id: +route.params.id }),
  },

  {
    component: () => import('@js/pages/configuration/workflows/[id]/checks/[checkId].vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { id: number }) => {
        return `${Translator.trans('u2_core.workflow.check')} #${params.id}`
      },
    },
    name: 'WorkflowCheckEdit',
    path: '/configuration/workflows/:id/checks/:checkId',
    props: (route: RouteLocation) => ({ id: +route.params.id, checkId: +route.params.checkId }),
  },
  {
    component: () => import('@js/pages/configuration/workflows/workflow-assignments/index.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      globalSearch: {
        icon: 'config',
        name: () => Translator.trans('u2_core.workflow_assignments'),
      },
    },
    name: 'WorkflowBindingList',
    path: '/configuration/workflows/workflow-assignments',
  },
  {
    component: () =>
      import('@js/pages/configuration/workflows/workflow-assignments/[bindingId].vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { workflowBinding: { readableName: string } }) =>
        computed(() => {
          return Translator.trans('u2_core.workflow.edit_workflow_assignment_with_given_name', {
            workflow_name: params.workflowBinding?.readableName || '',
          })
        }),
    },
    name: 'WorkflowBindingEdit',
    path: '/configuration/workflows/workflow-assignments/:bindingId',
    props: (to: RouteLocation) => ({ bindingId: to.params.bindingId }),
  },
] as Array<RouteRecordRaw>
