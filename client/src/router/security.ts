import BaseLayout from '@js/layouts/BaseLayout.vue'
import OutsideAppLayout from '@js/layouts/OutsideAppLayout.vue'
import { logDeprecatedRouteUsage } from '@js/router/helpers'
import Translator from '@js/translator'
import { useAuthStore } from '@js/stores/auth'
import type { RouteLocation, RouteRecordRaw } from 'vue-router'

export default [
  {
    component: () => import('@js/pages/login-confirm.vue'),
    meta: {
      auth: false,
      layout: OutsideAppLayout,
    },
    name: 'ConfirmTwoFactor',
    path: '/login-confirm',
    beforeEnter: async () => {
      const authStore = useAuthStore()
      if (authStore.isTokenValid()) {
        return { name: 'AppDefaultDashboard' }
      }
    },
  },
  {
    path: '/logout',
    name: 'AppLogout',
    component: { name: 'Dummy' },
    meta: {
      auth: false,
      globalSearch: {
        icon: 'arrow-right',
        name: () => Translator.trans('u2_core.logout'),
      },
    },
    beforeEnter: async () => {
      const authStore = useAuthStore()
      await authStore.logout()
      return { name: 'AppLogin' }
    },
  },
  {
    component: () => import('@js/pages/login.vue'),
    meta: {
      auth: false,
      layout: OutsideAppLayout,
    },
    name: 'AppLogin',
    path: '/login',
    beforeEnter: async () => {
      const authStore = useAuthStore()
      if (authStore.isTokenValid()) {
        return { name: 'AppDefaultDashboard' }
      }
    },
  },
  {
    component: () => import('@js/pages/password-reset/index.vue'),
    meta: {
      auth: false,
      layout: OutsideAppLayout,
    },
    name: 'PasswordResetRequest',
    path: '/password-reset',
  },
  {
    component: () => import('@js/pages/password-reset/[token].vue'),
    meta: {
      auth: false,
      layout: OutsideAppLayout,
    },
    name: 'PasswordReset',
    path: '/password-reset/:token',
    props: (route: RouteLocation) => ({ token: route.params.token }),
  },
  {
    component: () => import('@js/pages/me/change-password.vue'),
    meta: {
      auth: true,
      layout: BaseLayout,
    },
    name: 'ChangePassword',
    path: '/me/change-password',
  },
  {
    path: '/security/two-factor/confirm',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'ConfirmTwoFactor',
      }
    },
  },
  {
    path: '/user/resetting/request',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'PasswordResetRequest',
      }
    },
  },
  {
    path: '/user/resetting/reset/:token',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'PasswordReset',
        params: {
          token: to.params.token,
        },
      }
    },
  },
  {
    name: 'AppLoginLegacy',
    path: '/security/login',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'AppLogin',
      }
    },
  },
  {
    name: 'AppLogoutLegacy',
    path: '/security/logout',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'AppLogout',
      }
    },
  },
  {
    name: 'ChangePasswordLegacy',
    path: '/user/change-password',
    redirect: (to) => {
      logDeprecatedRouteUsage(to)
      return {
        name: 'ChangePassword',
      }
    },
  },
] as Array<RouteRecordRaw>
