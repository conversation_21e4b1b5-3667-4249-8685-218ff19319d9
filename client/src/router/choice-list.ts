import ConfigurationDataLayout from '@js/layouts/ConfigurationDataLayout.vue'
import { choices } from '@js/model/choice'
import type { RouteLocation, RouteRecordRaw } from 'vue-router'

export const getCurrentChoice = (slug: string) => {
  return choices.find((choice) => choice.slug === slug)
}
export default [
  {
    name: 'ChoiceList',
    path: `/configuration/tasks/fields/:slug`,
    component: () => import('@js/pages/configuration/tasks/fields/[slug].vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { slug: string }) => getCurrentChoice(params.slug)?.title,
      globalSearch: {
        icon: 'config',
        name: () => '',
      },
    },
    props: (to: RouteLocation) => {
      const currentChoice = getCurrentChoice(to.params.slug as string)
      return {
        fieldId: to.params.slug,
        readableName: currentChoice?.readableName(),
        resourceCollectionEndpoint: currentChoice?.collectionEndpoint,
      }
    },
  },
] as Array<RouteRecordRaw>
