import { fetchDocumentTemplateById } from '@js/api/documentTemplateApi'
import ConfigurationDataLayout from '@js/layouts/ConfigurationDataLayout.vue'
import Translator from '@js/translator'
import type { DocumentTemplate } from '@js/model/document-template'
import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    component: () => import('@js/pages/configuration/document/templates/index.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      globalSearch: {
        icon: 'config',
        name: () => Translator.trans('u2_structureddocument.document_templates'),
      },
    },
    name: 'TemplateList',
    path: '/configuration/document/templates',
  },
  {
    component: () => import('@js/pages/configuration/document/templates/import.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
    },
    name: 'TemplateImport',
    path: '/configuration/document/templates/import',
  },
  {
    component: () => import('@js/pages/configuration/document/templates/[id]/preview.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { documentTemplate: DocumentTemplate }) =>
        Translator.trans('u2_structureddocument.preview_given_document_template', {
          document_template_name: params.documentTemplate.name,
        }),
    },
    name: 'TemplatePreview',
    path: '/configuration/document/templates/:id/preview',
    props: (to) => ({
      documentTemplate: to.meta.props?.documentTemplate,
    }),
    beforeEnter: async (to) => {
      to.meta.props = {
        documentTemplate: (await fetchDocumentTemplateById(Number(to.params.id))).data,
      }
    },
  },
  {
    component: () => import('@js/pages/configuration/document/templates/[id]/configure.vue'),
    meta: {
      auth: 'ROLE_ADMIN',
      layout: ConfigurationDataLayout,
      title: (params: { documentTemplate: DocumentTemplate }) =>
        Translator.trans('u2_structureddocument.document_template') +
        ': ' +
        params.documentTemplate.name,
    },
    name: 'TemplateConfigure',
    path: '/configuration/document/templates/:id/configure',
    props: true,
  },
] as Array<RouteRecordRaw>
