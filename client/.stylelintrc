{"extends": ["stylelint-config-standard", "stylelint-config-recommended-vue"], "plugins": ["stylelint-order"], "ignoreFiles": ["**/*.(js|ts|svg|xsd)", "patches/**/*"], "rules": {"at-rule-no-deprecated": [true, {"ignoreAtRules": ["apply"]}], "at-rule-no-unknown": [true, {"ignoreAtRules": ["tailwind", "apply", "layer", "variants", "responsive", "screen"]}], "declaration-property-value-no-unknown": null, "function-no-unknown": [true, {"ignoreFunctions": ["theme", "v-bind"]}], "import-notation": "string", "media-query-no-invalid": null, "no-descending-specificity": null, "order/properties-alphabetical-order": true, "selector-class-pattern": null, "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["v-deep"]}]}}