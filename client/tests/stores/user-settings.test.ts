import { createP<PERSON>, setActiveP<PERSON> } from 'pinia'
import flushPromises from 'flush-promises'
import queryClient from '@js/queryClient'
import { useUserSettingsStore } from '@js/stores/user-settings'

describe('User Settings Store', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    setActivePinia(createPinia())
  })

  describe('actions', () => {
    it('invalidates menu queries on locale change', async () => {
      const queryClientInvalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries')

      // Given
      const statusStore = useUserSettingsStore()

      // When
      statusStore.locale = 'de'

      await flushPromises()

      // Then
      expect(queryClientInvalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['menu', 'userMenuJson'],
      })
      expect(queryClientInvalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['menu', 'mainMenuJson'],
      })
    })
  })
})
