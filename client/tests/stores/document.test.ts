import { VueQueryPlugin } from '@tanstack/vue-query'
import { waitFor } from '@testing-library/vue'
import { createDocumentSection } from '@tests/__factories__/createDocumentSection'
import { createTask } from '@tests/__factories__/createTask'
<<<<<<< HEAD
import { setupServer } from '@tests/utils'
=======
import { createUnrestrictedAttachment } from '@tests/__factories__/createUnrestrictedAttachment'
import { ensureDefined, setupServer } from '@tests/utils'
>>>>>>> 51167e2778... Fixup undefined section number
import flushPromises from 'flush-promises'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { createPinia, setActivePinia } from 'pinia'
import { beforeAll, expect } from 'vitest'
import { createApp } from 'vue'
import { useTaskStore } from '@js/stores/task'
import { useNotificationsStore } from '@js/stores/notifications'
import { useDocumentStore } from '@js/stores/document'
import queryClient from '@js/queryClient'
import * as DocumentApi from '@js/api/documentApi'
import type { DocumentSection } from '@js/model/document'

describe('Document Store Module', () => {
  const server = setupServer(
    http.get('/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data', async () => {
      return HttpResponse.json(
        {
          sections: [
            createDocumentSection({
              '@type': 'CountryByCountryReportSection',
              id: 1,
              level: 1,
              include: true,
              editable: true,
              name: 'Section name',
            }),
            createDocumentSection({
              '@type': 'CountryByCountryReportSection',
              id: 2,
              level: 2,
              include: true,
              editable: true,
              name: 'Subsection name',
            }),
          ],
          userCanEditContent: true,
        },
        { status: StatusCodes.OK }
      )
    }),
    http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
      return HttpResponse.json([{ id: 1, content: 'Section content' }], { status: StatusCodes.OK })
    })
  )

  const task = createTask({
    '@id': '/api/tasks/my-task-id',
    '@type': 'Task',
    id: 'my-task-id',
    taskType: 'tpm_country_by_country_report',
    'u2:extra': {
      taskTypeId: 3,
      shortName: 'tpm-country-by-country-report',
    },
  })
  beforeAll(() => {
    vi.clearAllMocks()
    server.listen()
  })

  beforeEach(() => {
    const app = createApp({})
    const pinia = createPinia()
    app.use(pinia)
    app.use(VueQueryPlugin, { queryClient, enableDevtoolsV6Plugin: true })
    setActivePinia(pinia)

    const taskStore = useTaskStore()
    taskStore.$patch({
      task,
    })
  })

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('adds a section', async () => {
    // Given
    server.use(
      http.post(
        `/legacy/structured-document/section/tpm-country-by-country-report-section/new/before/1`,
        async () => {
          return HttpResponse.json([], { status: StatusCodes.OK })
        }
      )
    )
    const expectedNewSection = {
      '@type': 'CountryByCountryReportSection',
      name: '***new-section***',
    }
    const documentApiSpy = vi.spyOn(DocumentApi, 'addDocumentSection')
    const documentStore = useDocumentStore()
    documentStore.$patch({ enabled: true })

    await flushPromises()

    // When
    const response = await documentStore.createNewSection('before', 1)

    // Then
    expect(documentApiSpy).toHaveBeenCalledWith(expectedNewSection, {
      placement: 'before',
      referenceSectionId: 1,
    })
    expect(response).toBe(true)

    // Given
    server.use(
      http.post(
        `/legacy/structured-document/section/tpm-country-by-country-report-section/new/after/1`,
        async () => {
          return HttpResponse.json([], { status: StatusCodes.OK })
        }
      )
    )

    // When
    await documentStore.createNewSection('after', 1)

    // Then
    expect(documentApiSpy).toHaveBeenCalledWith(expectedNewSection, {
      placement: 'after',
      referenceSectionId: 1,
    })
    expect(response).toBe(true)

    // Given
    server.use(
      http.post(
        `/legacy/structured-document/section/tpm-country-by-country-report-section/new/subsection-of/1`,
        async () => {
          return HttpResponse.json([], { status: StatusCodes.OK })
        }
      )
    )

    // When
    await documentStore.createNewSection('subsection-of', 1)

    // Then
    expect(documentApiSpy).toHaveBeenCalledWith(expectedNewSection, {
      placement: 'subsection-of',
      referenceSectionId: 1,
    })
  })

  it('shows error notification when adding a section fails', async () => {
    // Given
    server.use(
      http.post(
        `/legacy/structured-document/section/tpm-country-by-country-report-section/new/before/1`,
        async () => {
          return HttpResponse.json({}, { status: StatusCodes.INTERNAL_SERVER_ERROR })
        }
      )
    )
    const documentStore = useDocumentStore()
    documentStore.$patch({ enabled: true })
    const notificationStore = useNotificationsStore()

    await flushPromises()

    // When
    const response = await documentStore.createNewSection('before', 1)

    // Then
    expect(response).toBe(false)
    expect(notificationStore.notifications[0]).toEqual({
      message: 'u2.http_500',
      type: 'error',
    })
  })

  it('adds initial section', async () => {
    // Given
    server.use(
      http.post(
        '/legacy/structured-document/section/tpm-country-by-country-report/create-initial-section-for-document/:documentId',
        async () => {
          return HttpResponse.json([], { status: StatusCodes.OK })
        }
      )
    )

    const documentApiSpy = vi.spyOn(DocumentApi, 'addDocumentInitialSection')
    const documentStore = useDocumentStore()
    documentStore.$patch({ enabled: true })

    await flushPromises()

    // When
    const response = await documentStore.createInitialSection()

    // Then
    expect(documentApiSpy).toHaveBeenCalledWith({
      shortName: task['u2:extra'].shortName,
      documentId: task['u2:extra'].taskTypeId,
      name: '***new-section***',
    })
    expect(response).toBe(true)
  })

  describe('getting new section configuration', () => {
    const sections = {
      _: undefined,
      _1: createDocumentSection({
        level: 1,
        include: true,
        id: 1,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_1: createDocumentSection({
        level: 2,
        include: true,
        id: 2,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_1_1: createDocumentSection({
        level: 3,
        include: true,
        id: 3,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_2: createDocumentSection({
        level: 2,
        include: true,
        id: 4,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_2_1: createDocumentSection({
        level: 3,
        include: true,
        id: 5,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_2_1_1: createDocumentSection({
        level: 4,
        include: true,
        id: 6,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_2_1_2: createDocumentSection({
        level: 4,
        include: true,
        id: 7,
        '@type': 'CountryByCountryReportSection',
      }),
      _1_2_1_3: createDocumentSection({
        level: 4,
        include: true,
        id: 8,
        '@type': 'CountryByCountryReportSection',
      }),
      _2: createDocumentSection({
        level: 1,
        include: true,
        id: 9,
        '@type': 'CountryByCountryReportSection',
      }),
      _2_1: createDocumentSection({
        level: 2,
        include: true,
        id: 10,
        '@type': 'CountryByCountryReportSection',
      }),
      _2_2: createDocumentSection({
        level: 2,
        include: true,
        id: 11,
        '@type': 'CountryByCountryReportSection',
      }),
      _2_3: createDocumentSection({
        level: 2,
        include: true,
        id: 12,
        '@type': 'CountryByCountryReportSection',
      }),
      _2_4: createDocumentSection({
        level: 2,
        include: true,
        id: 13,
        '@type': 'CountryByCountryReportSection',
      }),
      _2_4_1: createDocumentSection({
        level: 3,
        include: true,
        id: 14,
        '@type': 'CountryByCountryReportSection',
      }),
      _2_4_2: createDocumentSection({
        level: 3,
        include: true,
        id: 15,
        '@type': 'CountryByCountryReportSection',
      }),
      _3: createDocumentSection({
        level: 1,
        include: true,
        id: 16,
        '@type': 'CountryByCountryReportSection',
      }),
      excluded: createDocumentSection({
        level: 2,
        include: false,
        id: 17,
        '@type': 'CountryByCountryReportSection',
      }),
      _3_1: createDocumentSection({
        level: 2,
        include: true,
        id: 18,
        '@type': 'CountryByCountryReportSection',
      }),
      _3_1_1: createDocumentSection({
        level: 3,
        include: true,
        id: 19,
        '@type': 'CountryByCountryReportSection',
      }),
      _3_2: createDocumentSection({
        level: 2,
        include: true,
        id: 20,
        '@type': 'CountryByCountryReportSection',
      }),
      _4: createDocumentSection({
        level: 1,
        include: true,
        id: 21,
        '@type': 'CountryByCountryReportSection',
      }),
    }

    function getSection(
      sections: Array<DocumentSection>,
      sectionId?: DocumentSection['id']
    ): DocumentSection {
      const section = sections.find((section) => section.id === sectionId)
      if (!section) {
        throw new Error(`Section with ID ${sectionId} not found`)
      }
      return section
    }

    beforeEach(() => {
      server.use(
        http.get(
          '/legacy/tasktype/tpm-country-by-country-report/3/edit-document/data',
          async () => {
            return HttpResponse.json(
              {
                sections: Object.values(sections).filter((section) => section !== undefined),
                userCanEditContent: true,
              },
              { status: StatusCodes.OK }
            )
          }
        ),
        http.get('/legacy/tasktype/tpm-country-by-country-report/3/content', async () => {
          return HttpResponse.json(
            [
              { id: 1, content: 'Section content' },
              { id: 2, content: '' },
              { id: 3, content: 'Section content' },
              { id: 4, content: 'Section content' },
              { id: 5, content: 'Section content' },
            ],
            {
              status: StatusCodes.OK,
            }
          )
        })
      )
    })

    it.each([
      [sections['_'], [{ placement: 'before', referenceSectionId: sections['_1'].id }]],
      [sections['_1'].id, [{ placement: 'before', referenceSectionId: sections['_1_1'].id }]],
      [sections['_1_1'].id, [{ placement: 'before', referenceSectionId: sections['_1_1_1'].id }]],
      [
        sections['_1_1_1'].id,
        [
          { placement: 'after', referenceSectionId: sections['_1_1_1'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_1_1_1'].id },
          { placement: 'after', referenceSectionId: sections['_1_1'].id },
        ],
      ],
      [sections['_1_2'].id, [{ placement: 'before', referenceSectionId: sections['_1_2_1'].id }]],
      [
        sections['_1_2_1_1'].id,
        [
          { placement: 'after', referenceSectionId: sections['_1_2_1_1'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_1_2_1_1'].id },
        ],
      ],
      [
        sections['_1_2_1_2'].id,
        [
          { placement: 'after', referenceSectionId: sections['_1_2_1_2'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_1_2_1_2'].id },
        ],
      ],
      [
        sections['_1_2_1_3'].id,
        [
          { placement: 'after', referenceSectionId: sections['_1_2_1_3'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_1_2_1_3'].id },
          { placement: 'after', referenceSectionId: sections['_1_2_1'].id },
          { placement: 'after', referenceSectionId: sections['_1_2'].id },
          { placement: 'after', referenceSectionId: sections['_1'].id },
        ],
      ],
      [
        sections['_2_4_1'].id,
        [
          { placement: 'after', referenceSectionId: sections['_2_4_1'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_2_4_1'].id },
        ],
      ],
      [
        sections['_2_4_2'].id,
        [
          { placement: 'after', referenceSectionId: sections['_2_4_2'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_2_4_2'].id },
          { placement: 'after', referenceSectionId: sections['_2_4'].id },
          { placement: 'after', referenceSectionId: sections['_2'].id },
        ],
      ],
      [
        sections.excluded.id,
        [
          { placement: 'after', referenceSectionId: sections.excluded.id },
          { placement: 'subsection-of', referenceSectionId: sections.excluded.id },
        ],
      ],
      [
        sections['_3_1_1'].id,
        [
          { placement: 'after', referenceSectionId: sections['_3_1_1'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_3_1_1'].id },
          { placement: 'after', referenceSectionId: sections['_3_1'].id },
        ],
      ],
      [
        sections['_3_2'].id,
        [
          { placement: 'after', referenceSectionId: sections['_3_2'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_3_2'].id },
          { placement: 'after', referenceSectionId: sections['_3'].id },
        ],
      ],
      [
        sections['_4'].id,
        [
          { placement: 'after', referenceSectionId: sections['_4'].id },
          { placement: 'subsection-of', referenceSectionId: sections['_4'].id },
        ],
      ],
    ])(
      'returns configuration to create new sections for section id %s',
      async (sectionId, expectedConfigs) => {
        const documentStore = useDocumentStore()
        documentStore.$patch({ enabled: true })

        await waitFor(() => {
          expect(documentStore.sections?.length).not.toBe(0)
        })

        // When
        const configs = documentStore.getPossibleNewSectionConfigs(
          sectionId ? getSection(documentStore.sections, sectionId) : undefined
        )

        // Then
        expect(configs).toEqual(expectedConfigs)
      }
    )
  })

  it('gets parent section numbering', async () => {
    // Given
    const documentStore = useDocumentStore()
    documentStore.$patch({ enabled: true })

    await flushPromises()

    const referenceSection = ensureDefined(
      documentStore.sections.find((section) => section.id === 2)
    )

    // When
    const referenceSectionParent = documentStore.sectionToParentSection.get(referenceSection)

    // Then
    expect(documentStore.numberingBySection.get(ensureDefined(referenceSectionParent))).toBe('1')
  })
})
