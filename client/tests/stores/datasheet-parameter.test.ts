import { createP<PERSON>, setActive<PERSON><PERSON> } from 'pinia'
import { useDatasheetParametersStore } from '@js/stores/datasheet-parameters'
import { expect } from 'vitest'

vi.unmock('@vueuse/core')

describe('Datasheet parameter store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    sessionStorage.clear()
    localStorage.clear()
  })

  it('updates store values from route location for unit view', () => {
    // Given
    const datasheetParametersStore = useDatasheetParametersStore()
    expect(datasheetParametersStore.parameters).toStrictEqual({
      field: undefined,
      layout: undefined,
      layoutCollection: undefined,
      period: undefined,
      selectedView: 'unit',
      unit: undefined,
      unitHierarchy: undefined,
    })

    // When
    datasheetParametersStore.updateFromLocationQuery({
      period: '1',
      field: '2',
      unit: '3',
    })

    // Then
    expect(datasheetParametersStore.parameters).toStrictEqual({
      field: 2,
      layout: undefined,
      layoutCollection: undefined,
      period: 1,
      selectedView: 'unit',
      unit: 3,
      unitHierarchy: undefined,
    })
  })

  it('updates store values from route location for hierarchy view', () => {
    // Given
    const datasheetParametersStore = useDatasheetParametersStore()
    expect(datasheetParametersStore.parameters).toStrictEqual({
      field: undefined,
      layout: undefined,
      layoutCollection: undefined,
      period: undefined,
      selectedView: 'unit',
      unit: undefined,
      unitHierarchy: undefined,
    })

    // When
    datasheetParametersStore.updateFromLocationQuery({
      period: '1',
      field: '2',
      unitHierarchy: '3',
    })

    // Then
    expect(datasheetParametersStore.parameters).toStrictEqual({
      field: 2,
      layout: undefined,
      layoutCollection: undefined,
      period: 1,
      selectedView: 'group',
      unitHierarchy: 3,
      unit: undefined,
    })
  })

  it('sets parameters as undefined if they are empty in location query', () => {
    // Given
    const datasheetParametersStore = useDatasheetParametersStore()
    expect(datasheetParametersStore.parameters).toStrictEqual({
      field: undefined,
      layout: undefined,
      layoutCollection: undefined,
      period: undefined,
      selectedView: 'unit',
      unit: undefined,
      unitHierarchy: undefined,
    })

    // When
    datasheetParametersStore.updateFromLocationQuery({
      period: '',
      field: '',
      unitHierarchy: '',
    })

    // Then
    expect(datasheetParametersStore.parameters).toStrictEqual({
      field: undefined,
      layout: undefined,
      layoutCollection: undefined,
      period: undefined,
      selectedView: 'group',
      unitHierarchy: undefined,
      unit: undefined,
    })
  })
})
