import { mount } from '@vue/test-utils'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'

describe('PageHeaderTitle', () => {
  it('renders the page header title without subtitles and image', () => {
    const vueWrapper = mount(PageHeaderTitle, {
      shallow: true,
      props: {
        image: undefined,
        subtitle: undefined,
        title: 'AAA',
      },
    })

    expect(vueWrapper.find('img').exists()).toBeFalsy()
    expect(vueWrapper.find('span').find('span').exists()).toBeFalsy()
    expect(vueWrapper.find('h1').exists()).toBe(true)
  })

  it('renders the page header title with subtitle but without image', () => {
    const vueWrapper = mount(PageHeaderTitle, {
      shallow: true,
      props: {
        image: undefined,
        subtitle: 'BBB',
        title: 'AAA',
      },
    })

    expect(vueWrapper.find('img').exists()).toBeFalsy()
    expect(vueWrapper.find('span').exists()).toBe(true)
    expect(vueWrapper.find('h1').exists()).toBe(true)
  })

  it('renders the page header title with image', () => {
    const vueWrapper = mount(PageHeaderTitle, {
      shallow: true,
      props: {
        subtitle: undefined,
        title: 'AAA',
      },
      slots: {
        default: '<img src="domain/image.svg" />',
      },
    })
    expect(vueWrapper.find('span').find('span').exists()).toBeFalsy()
    expect(vueWrapper.find('img').exists()).toBe(true)
    expect(vueWrapper.find('h1').exists()).toBe(true)
  })

  it('renders the page header title, subtitle and image', () => {
    const vueWrapper = mount(PageHeaderTitle, {
      shallow: true,
      props: {
        subtitle: 'BBB',
        title: 'AAA',
      },
      slots: {
        default: '<img src="domain/image.svg" />',
      },
    })
    expect(vueWrapper.find('span').exists()).toBe(true)
    expect(vueWrapper.find('img').exists()).toBe(true)
    expect(vueWrapper.find('h1').exists()).toBe(true)
  })
})
