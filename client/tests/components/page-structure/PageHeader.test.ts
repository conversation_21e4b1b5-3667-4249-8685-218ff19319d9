import { createTestingPinia } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import PageHeaderTitle from '@js/components/page-structure/PageHeaderTitle.vue'

describe('PageHeader', () => {
  it('renders the page header without controls', () => {
    const wrapper = mount(PageHeader, {
      global: {
        plugins: [createTestingPinia()],
      },
    })
    expect(wrapper.find('#page-header').exists()).toBe(true)
    expect(wrapper.find('#page-controls').exists()).toBe(false)
    expect(wrapper.findComponent(PageHeaderTitle).exists()).toBe(true)
  })

  it('renders the page header with controls', () => {
    const wrapper = mount(PageHeader, {
      global: {
        plugins: [createTestingPinia()],
      },
      slots: {
        default: '<div>Test</div>',
      },
    })

    expect(wrapper.find('#page-controls').text()).toBe('Test')
  })
})
