import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import BranchEditor from '@js/components/BranchEditor.vue'

describe('Branch editor', () => {
  it('renders', () => {
    // Given
    const component = mount(BranchEditor, {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(component.findComponent(BaseInputText).props('label')).toBe('u2_core.name')
    expect(component.findComponent(BaseToggle).props('label')).toBe('u2.enabled')
  })
})
