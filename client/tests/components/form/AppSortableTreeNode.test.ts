import { render } from '@testing-library/vue'
import { expect } from 'vitest'
import AppSortableTreeNode from '@js/components/AppSortableTreeNode.vue'
import type { TreeNode } from '@js/types'

const treeNode: TreeNode = {
  label: '<PERSON>',
  id: 6,
  children: [{ label: '<PERSON>', id: 9, children: [], icon: 'organisational-group' }],
  icon: 'unit',
}

describe('AppSortableTreeNode', () => {
  it('renders', () => {
    const ui = render(AppSortableTreeNode, {
      props: {
        item: treeNode,
        group: 'test',
      },
    })

    const item = ui.getByRole('treeitem', { name: '<PERSON>' })
    expect(item.querySelectorAll('.js-sortable-drag-handler')).toHaveLength(2)

    const childItem = ui.getByRole('treeitem', { name: '<PERSON>' })
    expect(childItem.querySelectorAll('.js-sortable-drag-handler')).toHaveLength(1)
  })

  it('can be readonly', () => {
    const ui = render(AppSortableTreeNode, {
      props: {
        readonly: true,
        item: treeNode,
        group: 'test',
      },
    })

    const item = ui.getByRole('treeitem', { name: 'Edgar' })
    // There should be no draggable elements inside a tree node when readonly
    expect(item.querySelectorAll('.js-sortable-drag-handler')).toHaveLength(0)
    // item is not disabled
    expect(item.getAttribute('aria-disabled')).toBe('false')
    // childItem is not disabled
    expect(ui.getByRole('treeitem', { name: 'Edgar Edgar' }).getAttribute('aria-disabled')).toBe(
      'false'
    )
  })

  it('can be disabled', () => {
    const ui = render(AppSortableTreeNode, {
      props: {
        disabled: true,
        item: treeNode,
        group: 'test',
      },
    })

    const item = ui.getByRole('treeitem', { name: 'Edgar' })
    // There should be no draggable elements inside a tree node when readonly
    expect(item.querySelectorAll('.js-sortable-drag-handler')).toHaveLength(0)
    // item is disabled
    expect(item.getAttribute('aria-disabled')).toBe('true')
    // childItem is disabled
    expect(ui.getByRole('treeitem', { name: 'Edgar Edgar' }).getAttribute('aria-disabled')).toBe(
      'true'
    )
  })
})
