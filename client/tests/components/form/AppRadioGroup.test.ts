import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render, within } from '@testing-library/vue'
import AppRadioGroup from '@js/components/form/AppRadioGroup.vue'

describe('AppRadioGroup', () => {
  it('renders', () => {
    const options = [
      {
        id: '1',
        name: '<PERSON>',
      },
      {
        id: '2',
        name: '<PERSON><PERSON>',
        disabled: true,
      },
      {
        id: '3',
        name: '<PERSON>',
      },
    ]
    const ui = render(AppRadioGroup, {
      props: {
        modelValue: '3',
        options,
      },
    })

    expect(ui.getByRole('radio', { name: '<PERSON>' })).toBeChecked()
    expect(ui.getByRole('radio', { name: '<PERSON><PERSON>' })).not.toBeChecked()
    expect(ui.getByRole('radio', { name: '<PERSON><PERSON>' })).toBeDisabled()
    expect(ui.getByRole('radio', { name: '<PERSON>' })).not.toBeChecked()
  })

  it('renders help/warning if provided', async () => {
    const options = [
      {
        id: '1',
        name: '<PERSON>',
        help: 'Help me!',
        warning: '<PERSON> you!',
      },
      {
        id: '2',
        name: '<PERSON>nie',
        warning: 'You cannot trust her!',
      },
      {
        id: '3',
        name: '<PERSON>',
        help: 'Help me!',
      },
    ]
    const ui = render(AppRadioGroup, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          SvgIcon: false,
        },
      },
      props: {
        modelValue: '3',
        options,
      },
    })

    await vi.dynamicImportSettled()

    const mickeyContainer = ui.getByRole('radiogroup').children[0] as HTMLElement
    expect(within(mickeyContainer).getByRole('radio', { name: 'Mickey' })).toBeInTheDocument()
    expect(
      within(mickeyContainer).getByRole('graphics-symbol', {
        name: 'alert',
      })
    ).toBeInTheDocument()
    expect(
      within(mickeyContainer).getByRole('graphics-symbol', {
        name: 'help-outline',
      })
    ).toBeInTheDocument()

    const minnieContainer = ui.getByRole('radiogroup').children[1] as HTMLElement
    expect(within(minnieContainer).getByRole('radio', { name: 'Minnie' })).toBeInTheDocument()
    expect(
      within(minnieContainer).getByRole('graphics-symbol', {
        name: 'alert',
      })
    ).toBeInTheDocument()

    const jerryContainer = ui.getByRole('radiogroup').children[2] as HTMLElement
    expect(within(jerryContainer).getByRole('radio', { name: 'Jerry' })).toBeInTheDocument()
    expect(
      within(jerryContainer).queryByRole('graphics-symbol', {
        name: 'help-outline',
      })
    ).toBeInTheDocument()
  })

  it('checks the clicked option', async () => {
    const user = userEvent.setup()
    const options = [
      {
        id: '1',
        name: 'Mickey',
      },
      {
        id: '2',
        name: 'Minnie',
      },
    ]
    const ui = render(AppRadioGroup, {
      props: {
        modelValue: '3',
        options,
      },
    })

    const mickey = ui.getByRole('radio', { name: 'Mickey' })
    const minnie = ui.getByRole('radio', { name: 'Minnie' })

    expect(mickey).not.toBeChecked()
    expect(minnie).not.toBeChecked()

    await user.click(mickey)

    expect(mickey).toBeChecked()
    expect(minnie).not.toBeChecked()

    await user.click(minnie)

    expect(mickey).not.toBeChecked()
    expect(minnie).toBeChecked()
  })

  it('disables the radio group when disabled is true', async () => {
    const options = [
      {
        id: '1',
        name: 'Mickey',
      },
      {
        id: '2',
        name: 'Minnie',
      },
    ]
    const ui = render(AppRadioGroup, {
      props: {
        modelValue: '3',
        options,
        disabled: true,
      },
    })

    const mickey = ui.getByRole('radio', { name: 'Mickey' })
    const minnie = ui.getByRole('radio', { name: 'Minnie' })

    expect(mickey).toBeDisabled()
    expect(minnie).toBeDisabled()
  })
})
