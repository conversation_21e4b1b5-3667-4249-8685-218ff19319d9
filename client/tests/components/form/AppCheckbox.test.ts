import { render } from '@testing-library/vue'
import { mount } from '@vue/test-utils'
import AppCheckbox from '@js/components/form/AppCheckbox.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import { fetchStates } from '@js/types'

describe('AppCheckbox', () => {
  it('renders', () => {
    const ui = render(AppCheckbox, {
      props: {
        modelValue: true,
      },
    })

    // Then
    const checkbox = ui.getByRole('checkbox')
    expect(checkbox).toHaveAttribute('aria-readonly', 'false')
    expect(checkbox).not.toHaveClass('cursor-default')
    expect(checkbox).toBeChecked()
  })

  it('emits its value on change', async () => {
    // Given
    const wrapper = mount(AppCheckbox, {
      shallow: true,
      props: {
        modelValue: false,
      },
    })

    // When
    await wrapper.find('input[type=checkbox]').setValue(true)

    // Then
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual([true])
  })

  it('shows a loader in loading state', async () => {
    // Given
    const wrapper = mount(AppCheckbox, {
      shallow: true,
      props: {
        modelValue: false,
        state: fetchStates.loading,
      },
    })

    // Then
    expect(wrapper.findComponent(AppLoader).exists()).toBe(true)
    expect(wrapper.find('input[type=checkbox]').exists()).toBe(false)
  })

  it('does not change value when readonly', async () => {
    // Given
    const ui = render(AppCheckbox, {
      props: {
        modelValue: false,
        readonly: true,
      },
    })

    const checkbox = ui.getByRole('checkbox')
    expect(checkbox).toHaveAttribute('aria-readonly', 'true')
    expect(checkbox).toHaveClass('cursor-default')

    // When
    checkbox.click()

    // Then
    expect(checkbox).not.toBeChecked()
  })
})
