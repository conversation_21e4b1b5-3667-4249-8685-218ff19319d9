import { mount } from '@vue/test-utils'
import AppInputText from '@js/components/form/AppInputText.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import FormLabel from '@js/components/form/FormLabel.vue'

describe('BaseInputText', () => {
  it('renders', () => {
    const wrapper = mount(BaseInputText, {
      props: {
        modelValue: 'Hello',
        label: 'Label',
      },
    })
    expect(wrapper.findComponent(AppInputText).props('modelValue')).toBe('Hello')
    expect(wrapper.findComponent(FormLabel).exists()).toBe(true)
  })
})
