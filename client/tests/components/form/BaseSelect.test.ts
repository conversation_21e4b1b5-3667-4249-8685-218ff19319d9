import { mount } from '@vue/test-utils'
import AppSelect from '@js/components/form/AppSelect.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import FormLabel from '@js/components/form/FormLabel.vue'

describe('BaseSelect', () => {
  it('renders', () => {
    const wrapper = mount(BaseSelect, {
      global: {
        stubs: {
          AppSelect: true,
        },
      },
      props: {
        modelValue: '2',
        label: 'Label',
        options: [
          {
            id: '1',
            name: 'Option 1',
            disabled: false,
          },
          {
            id: '2',
            name: 'Option 2',
            disabled: false,
          },
        ],
      },
    })
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(wrapper.findComponent(AppSelect).props('modelValue')).toBe('2')
    expect(wrapper.findComponent(FormLabel).exists()).toBe(true)
  })

  it('renders errors', () => {
    const wrapper = mount(BaseSelect, {
      global: {
        stubs: {
          AppSelect: true,
        },
      },
      props: {
        errors: ['Something went wrong. Panic!'],
        modelValue: '2',
        label: 'Label',
        options: [
          {
            id: '1',
            name: 'Option 1',
            disabled: false,
          },
          {
            id: '2',
            name: 'Option 2',
            disabled: false,
          },
        ],
      },
    })
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(wrapper.findComponent(AppSelect).props('modelValue')).toBe('2')
    expect(wrapper.findComponent(FormLabel).exists()).toBe(true)
  })
})
