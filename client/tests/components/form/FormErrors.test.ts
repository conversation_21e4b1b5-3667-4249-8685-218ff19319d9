import { render } from '@testing-library/vue'
import { mount } from '@vue/test-utils'
import FormErrors from '@js/components/form/FormErrors.vue'

describe('FormErrors', () => {
  it('renders a block with error messages', () => {
    const ui = render(FormErrors, {
      props: {
        errors: ['Test error message 1', 'Test error message 2'],
      },
    })
    expect(ui.getAllByRole('alert')).toHaveLength(2)
  })

  it('does not error if no errors are provided', () => {
    const wrapper = mount(FormErrors)
    expect(wrapper.html()).toBe('<!--v-if-->')
  })
})
