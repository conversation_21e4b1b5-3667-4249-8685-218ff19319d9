import { mount } from '@vue/test-utils'
import AppInputText from '@js/components/form/AppInputText.vue'
import { fetchStates } from '@js/types'
import StateIndicator from '@js/components/StateIndicator.vue'

describe('AppInputText', () => {
  it('renders', () => {
    const wrapper = mount(AppInputText, {
      shallow: true,
      props: {
        modelValue: 'text',
      },
    })

    const textField = wrapper.find('input[type="text"]').element as HTMLInputElement

    expect(textField.value).toBe('text')
  })

  it('emits its value on input', async () => {
    // Given
    const wrapper = mount(AppInputText)

    // When
    const input = wrapper.find('input')
    await input.setValue('text')

    // Then
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['text'])
  })

  it('shows a state indicator', async () => {
    // Given
    const wrapper = mount(AppInputText, {
      shallow: true,
      props: {
        state: fetchStates.loading,
      },
    })

    // Then
    expect(wrapper.findComponent(StateIndicator).props('state')).toBe(fetchStates.loading)
  })
})
