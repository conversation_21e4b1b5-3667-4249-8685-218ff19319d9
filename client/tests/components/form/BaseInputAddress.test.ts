import { createAddress } from '@tests/__factories__/createAddress'
import axios from 'axios'
import { mount } from '@vue/test-utils'
import BaseInputAddress from '@js/components/form/BaseInputAddress.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import FormLabel from '@js/components/form/FormLabel.vue'

vi.mock('axios')

describe('BaseInputAddress', () => {
  beforeAll(() => {
    vi.mocked(axios.get).mockResolvedValue({
      data: {
        'hydra:member': [],
      },
    })
  })

  it('renders', async () => {
    const wrapper = mount(BaseInputAddress, {
      global: {
        stubs: {
          BaseSelect: true,
        },
      },
      props: {
        modelValue: createAddress({
          line1: '221B Baker Street',
          line2: 'HH',
          line3: '3OG',
          city: 'London',
          state: '',
          postcode: '0001',
          country: '/api/countries/1',
        }),
        label: 'Label',
      },
    })

    expect(wrapper.findAllComponents(BaseInputText)).toHaveLength(6)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(wrapper.findComponent(BaseSelect).exists()).toBe(true)
    expect(wrapper.findComponent(FormLabel).exists()).toBe(true)
  })
})
