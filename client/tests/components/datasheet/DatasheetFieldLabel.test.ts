import { periodApiBasePath } from '@js/api/periodApi'
import { unitApiBasePath } from '@js/api/unitApi'
import { createTestingPinia } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render } from '@testing-library/vue'
import { createDatasheet } from '@tests/__factories__/createDatasheet'
import { createDatasheetField } from '@tests/__factories__/createDatasheetField'
import { createPeriod } from '@tests/__factories__/createPeriod'
import { createUnit } from '@tests/__factories__/createUnit'
import { createHydraCollection, findResourceById, setupServer, wrapInSuspense } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { mockIntersectionObserver, mockResizeObserver } from 'jsdom-testing-mocks'
import { HttpResponse, http } from 'msw'
import { expect } from 'vitest'
import DatasheetFieldLabel from '@js/components/datasheet/DatasheetFieldLabel.vue'
import { createItem } from '@tests/__factories__/createItem'
import { itemApi } from '@js/api/itemApi'
import flushPromises from 'flush-promises'

const server = setupServer()
const datasheet = createDatasheet()

describe('DatasheetFieldLabel', () => {
  beforeAll(() => {
    mockIntersectionObserver()
    mockResizeObserver()
    server.listen()
  })
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  const item = createItem()
  const field = createDatasheetField({}, datasheet, item)

  beforeEach(() => {
    server.use(
      http.get('/api/layouts/:layoutId/fields/:id', ({ params }) => {
        if (field.id === Number(params.id)) {
          return HttpResponse.json(field, {
            status: StatusCodes.OK,
          })
        }

        return HttpResponse.json('Not Found', { status: StatusCodes.NOT_FOUND })
      })
    )
  })

  it('renders a field', async () => {
    const ui = render(DatasheetFieldLabel, {
      props: {
        field: field,
        datasheet: datasheet,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(await ui.findByText(field.name)).toBeInTheDocument()
  })

  it('renders a disabled field', async () => {
    const user = userEvent.setup()

    // Given
    field.disabled = true

    // When
    const ui = render(DatasheetFieldLabel, {
      props: {
        field: field,
        datasheet: datasheet,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    // Then
    const label = ui.getByText(field.name)
    expect(label).toBeInTheDocument()
    expect(label.parentElement?.parentElement).toHaveClass('bg-gray-200')

    await user.hover(label)

    expect(await ui.findByText('u2.datasheets.field_disabled')).toBeInTheDocument()
  })

  it('renders fallback if no field is provided', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get('/api/items', () => {
        return HttpResponse.json(createHydraCollection([item]), {
          status: StatusCodes.OK,
        })
      })
    )

    // When
    const ui = render(
      wrapInSuspense(DatasheetFieldLabel, {
        fallback: '1337',
        datasheet: datasheet,
      }),
      {
        global: {
          plugins: [createTestingPinia()],
        },
      }
    )

    await flushPromises()

    // Then
    const label = ui.getByText('1337')
    expect(label).toBeInTheDocument()

    // When
    await user.tab()

    // Then
    expect(label.parentElement).toHaveFocus()

    // When
    await user.hover(label)

    // Then
    expect(await ui.findByText('u2.field.click_to_create')).toBeInTheDocument()

    // When
    await user.click(label)

    // Then
    expect(await ui.findByRole('heading', { name: /u2.new_entity_type_name/ })).toBeInTheDocument()

    expect(ui.getByLabelText(`item-combobox`)).toBeInTheDocument()
    expect(ui.getByLabelText('u2.help_text')).toBeInTheDocument()
    expect(ui.getByText('u2.disabled')).toBeInTheDocument()
  })

  describe('Popup Card', () => {
    it('renders a popup card', async () => {
      // Given
      const user = userEvent.setup()
      server.use(
        http.get(itemApi.basePath + '/:id', ({ params }) =>
          HttpResponse.json(findResourceById(params.id, [item]), {
            status: StatusCodes.OK,
          })
        )
      )
      const ui = render(DatasheetFieldLabel, {
        props: {
          field: field,
          datasheet: datasheet,
        },
        global: {
          plugins: [createTestingPinia()],
        },
      })

      await user.click(ui.getByText(field.name))

      // Then
      expect(ui.getByLabelText('u2.edit')).toBeInTheDocument()
      expect(ui.getByLabelText('u2.delete')).toBeInTheDocument()
      expect(ui.getByText('u2.datasheets.formula')).toBeInTheDocument()
    })

    it('renders a popup card with context', async () => {
      const unit = createUnit()
      const period = createPeriod({
        name: '2020',
      })
      const field = createDatasheetField({}, datasheet, item)

      server.use(
        http.get(itemApi.basePath + '/:id', ({ params }) =>
          HttpResponse.json(findResourceById(params.id, [item]), {
            status: StatusCodes.OK,
          })
        ),
        http.get(periodApiBasePath + '/:id', ({ params }) =>
          HttpResponse.json(findResourceById(params.id, [period]), {
            status: StatusCodes.OK,
          })
        ),
        http.get(unitApiBasePath + '/:id', ({ params }) => {
          if (unit.id === Number(params.id)) {
            return HttpResponse.json(unit, {
              status: StatusCodes.OK,
            })
          }

          return HttpResponse.json('Not Found', { status: StatusCodes.NOT_FOUND })
        }),
        http.get('/api/layouts/:layoutId/fields/:id', ({ params }) => {
          if (field.id === Number(params.id)) {
            return HttpResponse.json(field, {
              status: StatusCodes.OK,
            })
          }

          return HttpResponse.json('Not Found', { status: StatusCodes.NOT_FOUND })
        }),
        http.get('/api/layouts/:layoutId/fields/:id/layouts', ({ params }) => {
          if (field.id === Number(params.id)) {
            return HttpResponse.json(createHydraCollection([]), { status: StatusCodes.OK })
          }

          return HttpResponse.json('Not Found', { status: StatusCodes.NOT_FOUND })
        })
      )

      const user = userEvent.setup()

      const ui = render(DatasheetFieldLabel, {
        props: {
          field,
          datasheet: datasheet,
          context: {
            unitId: unit.id,
            periodId: period.id,
          },
        },
        global: {
          plugins: [createTestingPinia()],
        },
      })

      expect(ui.getByText(field.name)).toBeInTheDocument()

      await user.click(ui.getByText(field.name))

      expect(ui.getByText(unit.refId + ' - ' + unit.name)).toBeInTheDocument()
      expect(ui.getByText(period.name)).toBeInTheDocument()
    })
  })
})
