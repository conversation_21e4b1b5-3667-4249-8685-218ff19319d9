import { createTestingPinia } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import AppDate from '@js/components/AppDate.vue'

describe('AppDate', () => {
  beforeEach(() => {
    // Mock the current date
    vi.useFakeTimers().setSystemTime(new Date('2020-01-07T09:11:18+00:00').getTime())
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
  })

  it.each([
    [undefined, 'Tuesday, 7 January 2020', '07/01/2020'],
    ['2021-04-21T09:10:14+00:00', 'Wednesday, 21 April 2021', '21/04/2021'],
  ])('renders %s', (input, tooltip, text) => {
    const component = mount(AppDate, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        date: input,
      },
    })
    // @ts-expect-error: we cannot detect the types inside a component. We should find another way to test this
    expect(component.vm.tooltip).toBe(tooltip)
    expect(component.find('span').text()).toBe(text)
  })

  it.each([
    [undefined, 'Tuesday, 7 January 2020', 'u2.date.today'],
    ['2021-04-21T09:10:14+00:00', 'Wednesday, 21 April 2021', '21 April 2021'],
  ])('renders a relative date for %s', (input, tooltip, text) => {
    const component = mount(AppDate, {
      props: {
        date: input,
        relative: true,
      },
    })
    // @ts-expect-error: we cannot detect the types inside a component. We should find another way to test this
    expect(component.vm.tooltip).toBe(tooltip)
    expect(component.find('span').text()).toBe(text)
  })
})
