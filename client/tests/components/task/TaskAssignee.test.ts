import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { render } from '@testing-library/vue'
import { setupServer } from '@tests/utils'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { useTaskInfoStore } from '@js/stores/task-info'
import TaskAssignee from '@js/components/task/TaskAssignee.vue'
import type { User } from '@js/model/user'

const user1 = fromPartial<User>({
  '@id': '/api/users/1',
  id: 1,
  username: 'user1',
})

const server = setupServer(
  http.get(user1['@id'], async () => {
    return HttpResponse.json({ ...user1 }, { status: StatusCodes.OK })
  })
)

describe('TaskAssignee', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('Renders', async () => {
    // Given
    const ui = render(TaskAssignee, {
      global: {
        plugins: [createTestingPinia()],
      },
    })
    const taskInfoStore = useTaskInfoStore()

    taskInfoStore.$patch({
      assignee: {
        id: 1,
        valid: true,
      },
      id: 1,
      readableName: 'my_readable_name',
      shortName: 'unit-period',
    })

    await flushPromises()

    // Then
    expect(ui.getByText('user1')).toBeInTheDocument()
  })
})
