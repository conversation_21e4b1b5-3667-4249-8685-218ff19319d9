import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { render } from '@testing-library/vue'
import { createComment } from '@tests/__factories__/createComment'
import { createUser } from '@tests/__factories__/createUser'
import flushPromises from 'flush-promises'
import { setupServer } from '@tests/utils'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import { createUserGroup } from '@tests/__factories__/createUserGroup'
import { useCommentsStore } from '@js/stores/comments'
import { useAuthStore } from '@js/stores/auth'
import TaskCommentNew from '@js/components/task/TaskCommentNew.vue'

const scrollTo = vi.fn()

const user = createUser({ id: 1, username: 'user 1' })
const userGroup = createUserGroup()

const server = setupServer(
  http.get('/api/users/' + user.id + '/groups', async () => {
    return HttpResponse.json({ 'hydra:member': [userGroup] }, { status: StatusCodes.OK })
  })
)

describe('Task Comment New', () => {
  afterEach(() => server.resetHandlers())

  beforeEach(() => {
    vi.stubGlobal('scrollTo', scrollTo)
    vi.useFakeTimers().setSystemTime(new Date(2020, 3, 1))
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
  })

  beforeAll(() => {
    server.listen()
  })

  afterAll(() => {
    server.close()
  })

  it('renders', async () => {
    // Given
    const ui = render(TaskCommentNew, {
      props: {
        modelValue: {
          group: '',
          quote: '/api/comments/1',
          content: '',
        },
        openNewCommentPanel: true,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    useAuthStore().$patch({ user })
    useCommentsStore().$patch({
      comments: [createComment({ id: 1, content: 'quoted comment content', deleted: false })],
    })

    await flushPromises()

    // Then
    expect(ui.getByText('user 1')).toBeInTheDocument()
    expect(ui.getByText('quoted comment content')).toBeInTheDocument()
    expect(ui.getByPlaceholderText('u2.comment.add_comment_field_placeholder')).toBeInTheDocument()
    expect(ui.getByRole('button', { name: /u2.post_comment/ })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'u2.cancel' })).toBeInTheDocument()
  })

  it('adds new comment', async () => {
    // Given
    const comment = createComment({
      group: null,
      quote: '/api/comments/1',
      content: 'my-textarea-content',
    })

    const ui = render(TaskCommentNew, {
      props: {
        modelValue: comment,
        openNewCommentPanel: true,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    useAuthStore().$patch({ user })

    expect(await ui.findByPlaceholderText('u2_comment.unrestricted')).toBeInTheDocument()

    // When
    ui.getByRole('button', { name: /u2.post_comment/ }).click()

    // Then
    expect(ui.emitted().save).toStrictEqual([[comment]])
  })
})
