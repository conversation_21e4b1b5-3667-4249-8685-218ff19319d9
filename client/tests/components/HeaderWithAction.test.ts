import { mount } from '@vue/test-utils'
import HeaderWithAction from '@js/components/HeaderWithAction.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

describe('HeaderWithAction', () => {
  it('renders the header', () => {
    const wrapper = mount(HeaderWithAction, {
      slots: {
        default: 'header content',
        button: 'button content',
      },
    })
    expect(wrapper.text()).toContain('header content')
    expect(wrapper.text()).toContain('button content')
    expect(wrapper.findComponent(SvgIcon).exists()).toBe(false)
  })

  it('renders the header with icon', () => {
    const wrapper = mount(HeaderWithAction, {
      shallow: true,
      props: {
        icon: 'add',
      },
    })

    expect(wrapper.findComponent(SvgIcon).props('icon')).toBe('add')
  })
})
