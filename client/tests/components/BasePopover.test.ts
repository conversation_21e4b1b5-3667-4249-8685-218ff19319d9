import { render, waitFor } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import BasePopover from '@js/components/BasePopover.vue'
import { mockResizeObserver } from 'jsdom-testing-mocks'
import { h } from 'vue'

describe('BasePopover', () => {
  beforeEach(() => {
    mockResizeObserver()
  })

  it('opens and closes popover when enabled', async () => {
    const user = userEvent.setup()
    const ui = render(BasePopover, {
      props: {
        disabled: false,
      },
      slots: {
        default: () => h('button', { type: 'button' }, 'Trigger'),
        content: () => 'Popover Content',
      },
    })

    expect(ui.queryByText('Popover Content')).not.toBeInTheDocument()

    await user.click(ui.getByText('Trigger'))
    expect(ui.getByText('Popover Content')).toBeInTheDocument()

    await user.click(ui.getByText('Trigger'))
    expect(ui.queryByText('Popover Content')).not.toBeInTheDocument()
  })

  it('stays open when disabled and open', async () => {
    const user = userEvent.setup()
    const ui = render(BasePopover, {
      props: {
        disabled: true,
        open: true,
      },
      slots: {
        default: () => h('button', { type: 'button' }, 'Trigger'),
        content: () => `Popover Content`,
      },
    })

    await waitFor(() => {
      expect(ui.getByText('Popover Content')).toBeInTheDocument()
    })

    // When
    await user.click(ui.getByText('Trigger'))

    // Then
    expect(ui.getByText('Popover Content')).toBeInTheDocument()

    // When
    await user.keyboard('{Escape}')

    // Then
    expect(ui.getByText('Popover Content')).toBeInTheDocument()
  })

  it('stays closed when disabled and closed', async () => {
    const user = userEvent.setup()
    const ui = render(BasePopover, {
      props: {
        disabled: true,
        open: false,
      },
      slots: {
        default: () => h('button', { type: 'button' }, 'Trigger'),
        content: () => `Popover Content`,
      },
    })

    expect(ui.queryByText('Popover Content')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByText('Trigger'))

    // Then
    expect(ui.queryByText('Popover Content')).not.toBeInTheDocument()
  })
})
