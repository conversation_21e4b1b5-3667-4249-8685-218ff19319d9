import { render } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import { createTestingPinia } from '@pinia/testing'
import { enableScroll } from '@tests/utils'
import UqlFieldSelector from '@js/components/table/UqlFieldSelector.vue'
import { expect } from 'vitest'
import { mockResizeObserver } from 'jsdom-testing-mocks'

describe('UqlFieldSelector', () => {
  beforeAll(() => {
    mockResizeObserver()
  })

  it('renders inside a popover fields that can be selected and deselected', async () => {
    const user = userEvent.setup()
    const ui = render(UqlFieldSelector, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              'task-list': {
                defaultFieldNames: ['id', 'name'],
                fields: [
                  {
                    uniqueName: 'id',
                    metadata: { name: 'ID', filterable: true },
                  },
                  {
                    uniqueName: 'name',
                    metadata: { name: 'Name', filterable: true },
                  },
                  {
                    uniqueName: 'description',
                    metadata: { name: 'Description', filterable: true },
                  },
                  {
                    uniqueName: 'status',
                    metadata: { name: 'Status', filterable: true },
                  },
                  {
                    uniqueName: 'priority',
                    metadata: { name: 'Priority', filterable: true },
                  },
                  {
                    uniqueName: 'assignee',
                    metadata: { name: 'Assignee', filterable: true },
                  },
                  {
                    uniqueName: 'dueDate',
                    metadata: { name: 'Due Date', filterable: true },
                  },
                  {
                    uniqueName: 'createdAt',
                    metadata: { name: 'Created At', filterable: true },
                  },
                  {
                    uniqueName: 'updatedAt',
                    metadata: { name: 'Updated At', filterable: true },
                  },
                  {
                    uniqueName: 'tags',
                    metadata: { name: 'Tags', filterable: true },
                  },
                ],
              },
            },
          }),
        ],
      },
    })

    expect(ui.queryByRole('group')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByText('u2_table.filters'))

    // Then
    const scrollContainer = ui.getByRole('group')

    expect(scrollContainer).not.toBeNull()
    const checkboxes = ui.getAllByRole('checkbox')
    expect(checkboxes.length).toBeGreaterThan(0)

    // When
    enableScroll(scrollContainer as HTMLElement)
    expect(scrollContainer.scrollTop).toBe(0)
    scrollContainer.scrollTop = 50

    // Then
    expect(scrollContainer.scrollTop).toBe(50)

    // When
    checkboxes.forEach((checkbox) => {
      expect(checkbox).not.toBeChecked()
    })
    await user.click(checkboxes[0])

    // Then
    expect(checkboxes[0]).toBeChecked()

    // When
    await user.click(checkboxes[0])

    // Then
    expect(checkboxes[0]).not.toBeChecked()
  })
})
