import { mount } from '@vue/test-utils'
import SvgIcon from '@js/components/SvgIcon.vue'
import YesNo from '@js/components/YesNo.vue'

describe('Yes No', () => {
  it('renders', () => {
    const wrapper = mount(YesNo, {
      props: {
        value: true,
      },
    })

    expect(wrapper.findComponent(SvgIcon).props('icon')).toBe('yes-ok')
  })

  it('renders with text', () => {
    const wrapper = mount(YesNo, {
      props: {
        value: false,
        withText: true,
      },
    })
    expect(wrapper.text()).toBe('u2.no')
  })

  it('renders empty', () => {
    const wrapper = mount(YesNo, {
      props: {
        value: null,
        withText: true,
      },
    })

    expect(wrapper.findComponent(SvgIcon).exists()).toBe(false)
    expect(wrapper.find('span').text()).toBe('')
  })
})
