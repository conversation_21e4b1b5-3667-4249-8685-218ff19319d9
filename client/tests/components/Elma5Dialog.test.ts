import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render } from '@testing-library/vue'
import { saveAs } from 'file-saver'
import flushPromises from 'flush-promises'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { setupServer } from 'msw/node'
import { expect } from 'vitest'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import Elma5Dialog from '@js/components/Elma5Dialog.vue'
import type { ApiViolation } from '@js/types'

vi.mock('file-saver', () => ({
  saveAs: vi.fn(),
}))

const server = setupServer()

describe('Elma 5 dialog', () => {
  const AppDialog = {
    name: 'AppDialog',
    template: '<div><slot name="default" /><slot name="buttons" /></div>',
  }

  beforeAll(() => server.listen())

  beforeEach(() => {
    mockIntersectionObserver()
  })

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('renders a form', async () => {
    // When
    const ui = render(Elma5Dialog, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppDialog,
        },
      },
      props: {
        countryByCountryReportId: 2,
      },
    })
    await flushPromises()

    expect(ui.queryByRole('alert')).not.toBeInTheDocument()

    expect(ui.getByRole('textbox', { name: /u2_core.bzst_number/ })).toBeInTheDocument()

    expect(ui.getByRole('textbox', { name: /u2_core.account_number/ })).toBeInTheDocument()

    expect(ui.getByRole('button', { name: /u2_core.download/ })).toBeInTheDocument()
  })

  it('shows server validation', async () => {
    // Given
    const violations = [
      {
        message: 'Form level error',
        propertyPath: '',
      },
      {
        message: 'bzst error',
        propertyPath: 'bzstNumber',
      },
      {
        message: 'account id error',
        propertyPath: 'accountId',
      },
    ]

    server.use(
      http.post('/api/elma-files', async () => {
        return HttpResponse.json(
          {
            'hydra:title': 'Api error title',
            violations,
          },
          { status: StatusCodes.UNPROCESSABLE_ENTITY }
        )
      })
    )
    const user = userEvent.setup()
    const ui = render(Elma5Dialog, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppDialog,
        },
      },
      props: {
        countryByCountryReportId: 2,
      },
    })

    // When
    await user.type(ui.getByRole('textbox', { name: /u2_core.bzst_number/ }), 'AB********9')
    await user.type(ui.getByRole('textbox', { name: /u2_core.account_number/ }), '**********')
    await user.click(ui.getByRole('button', { name: /u2_core.download/ }))

    // Then
    expect(ui.getAllByRole('alert')).toHaveLength(3)
    violations.forEach((violation: ApiViolation) => {
      expect(ui.getByText(violation.message)).toBeInTheDocument()
    })
  })

  it('closes when "cancel" is clicked', async () => {
    // Given
    const user = userEvent.setup()
    const ui = render(Elma5Dialog, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          AppDialog,
        },
      },
      props: {
        countryByCountryReportId: 2,
      },
    })

    // When
    await user.click(ui.getByRole('button', { name: 'u2.cancel' }))

    // Then
    expect(ui.emitted().close).toHaveLength(1)
  })

  describe('Form validation', () => {
    it.each([
      ['1', true],
      ['1a********9', true],
      ['***********', true],
      ['ab12345abcd', true],
      ['ab***********3456789', true],
      ['AB********9', false],
    ])('validates the BZSt number %s: error = %s', async (value, result) => {
      // Given
      const ui = render(Elma5Dialog, {
        global: {
          stubs: {
            AppDialog,
          },
        },
        props: {
          countryByCountryReportId: 2,
        },
      })

      // When
      const user = userEvent.setup()
      await user.type(ui.getByRole('textbox', { name: /u2_core.bzst_number/ }), value)
      await user.tab()

      // Then
      expect(ui.queryAllByText('u2.invalid_bzst_number {}').length).toBe(result ? 1 : 0)
    })

    it('validates the BZSt number when it is removed', async () => {
      // Given
      const user = userEvent.setup()
      const ui = render(Elma5Dialog, {
        global: {
          stubs: {
            AppDialog,
          },
        },
        props: {
          countryByCountryReportId: 2,
        },
      })
      expect(ui.queryByText(/u2.required/)).not.toBeInTheDocument()

      // When
      await user.click(ui.getByRole('textbox', { name: /u2_core.bzst_number/ }))
      await user.tab()

      // Then
      expect(ui.queryByText(/u2.required/)).toBeInTheDocument()
    })

    it.each([
      ['**********', false, 'contains only digits'],
      ['********9A', true, 'contains letters'],
      ['********', true, 'contains less than 10 characters'],
    ])('validates the account number %s: error = %s because it %s', async (value, result) => {
      // Given
      const ui = render(Elma5Dialog, {
        global: {
          stubs: {
            AppDialog,
          },
        },
        props: {
          countryByCountryReportId: 2,
        },
      })
      await flushPromises()

      // When
      const user = userEvent.setup()
      await user.type(ui.getByRole('textbox', { name: /u2_core.account_number/ }), value)
      await user.tab()

      // Then
      expect(ui.queryAllByText('u2.invalid_bop_account_number {}').length).toBe(result ? 1 : 0)
    })
  })

  it('allows a maximum of 10 chars for the account number', async () => {
    // Given
    const ui = render(Elma5Dialog, {
      global: {
        stubs: {
          AppDialog,
        },
      },
      props: {
        countryByCountryReportId: 2,
      },
    })
    await flushPromises()

    // When
    const user = userEvent.setup()
    await user.type(
      ui.getByRole('textbox', { name: /u2_core.account_number/ }),
      '0******************9'
    )
    await user.tab()

    // Then
    expect(ui.getByRole('textbox', { name: /u2_core.account_number/ })).toHaveValue('0********9')
  })

  it('enables Submit button when the form does not have validation errors', async () => {
    // Given
    const ui = render(Elma5Dialog, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: {
        countryByCountryReportId: 2,
      },
    })
    await flushPromises()

    // Then
    expect(ui.getByRole('button', { name: /u2_core.download/ })).toBeDisabled()

    // When
    const user = userEvent.setup()
    await user.type(ui.getByRole('textbox', { name: /u2_core.bzst_number/ }), 'AB********9')
    await user.type(ui.getByRole('textbox', { name: /u2_core.account_number/ }), '**********')

    // Then
    expect(ui.getByRole('button', { name: /u2_core.download/ })).not.toBeDisabled()
  })

  it('downloads an ELMA file', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.post('/api/elma-files', async () => {
        return HttpResponse.json(
          { name: 'File name', xml: 'lots of xml' },
          { status: StatusCodes.OK }
        )
      })
    )
    const ui = render(Elma5Dialog, {
      global: {
        stubs: {
          AppDialog,
        },
      },
      props: {
        countryByCountryReportId: 2,
      },
    })

    // When
    await user.type(ui.getByRole('textbox', { name: /u2_core.bzst_number/ }), 'AB********9')
    await user.type(ui.getByRole('textbox', { name: /u2_core.account_number/ }), '**********')
    await user.click(ui.getByRole('button', { name: /u2_core.download/ }))

    // Then
    expect(ui.emitted()).toHaveProperty('close')
    expect(vi.mocked(saveAs)).toHaveBeenCalledWith(new Blob(['lots of xml']), 'File name')
  })
})
