import { mount } from '@vue/test-utils'
import InfoBox from '@js/components/InfoBox.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

describe('Info Box', () => {
  it('does not renders the icon if non is defined', () => {
    const wrapper = mount(InfoBox, {
      props: {
        title: 'Title',
      },
    })

    expect(wrapper.findComponent(SvgIcon).exists()).toBe(false)
  })

  it('renders the defined icon', () => {
    const wrapper = mount(InfoBox, {
      props: {
        icon: 'add',
        title: 'Title',
      },
    })
    expect(wrapper.findAllComponents(SvgIcon)).toHaveLength(1)
    expect(wrapper.findComponent(SvgIcon).props('icon')).toBe('add')
  })

  it('renders provided content', () => {
    const wrapper = mount(InfoBox, {
      props: {
        icon: 'add',
        title: 'Title',
      },
      slots: {
        default: '<div>Test</div>',
      },
    })

    expect(wrapper.text()).toContain('Test')
  })
})
