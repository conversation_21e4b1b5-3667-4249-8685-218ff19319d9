import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import UnitEditor from '@js/components/unit/UnitEditor.vue'
import BaseToggle from '@js/components/form/BaseToggle.vue'
import BaseTextarea from '@js/components/form/BaseTextarea.vue'
import BaseSelect from '@js/components/form/BaseSelect.vue'
import BaseInputText from '@js/components/form/BaseInputText.vue'
import BaseCurrencySelect from '@js/components/form/BaseCurrencySelect.vue'
import type { Unit } from '@js/model/unit'

const server = setupServer(
  http.get('/api/currencies', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/countries', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('UnitEditor', () => {
  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  const unit = fromPartial<Unit>({
    '@id': 'unit-iri-1',
    '@type': 'Unit',
    id: 1,
    refId: 'refId',
    name: 'name',
    taxNumbers: [],
    country: 'country-1',
    currency: 'currency-1',
    description: 'description',
    verified: false,
  })

  it('renders', async () => {
    server.use(
      http.get('/api/unit-properties', async () => {
        return HttpResponse.json(
          {
            'hydra:member': Object.keys(unit).map((property) => ({
              id: property,
              editable: true,
            })),
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const component = mount(UnitEditor, {
      global: {
        stubs: {
          AppSelect: true,
          TaxNumbers: true,
        },
        plugins: [createTestingPinia()],
      },
      props: {
        unit,
      },
    })

    await flushPromises()

    const textInputs = component.findAllComponents(BaseInputText)
    const refId = textInputs[0]
    expect(refId.props().label).toBe('u2_core.ref_id')
    expect(refId.props().modelValue).toBe('refId')
    expect(refId.props().disabled).toBe(false)

    const name = textInputs[1]
    expect(name.props().label).toBe('u2_core.name')
    expect(name.props().modelValue).toBe('name')
    expect(name.props().disabled).toBe(false)

    const description = component.findComponent(BaseTextarea)
    expect(description.props().modelValue).toBe('description')
    expect(description.props().disabled).toBe(false)

    const verified = component.findComponent(BaseToggle)
    expect(verified.props().label).toBe('u2_core.verified')
    expect(verified.props().modelValue).toBe(false)
    expect(verified.props().disabled).toBe(false)

    const currencySelect = component.findComponent(BaseCurrencySelect)
    expect(currencySelect.props().label).toBe('u2_core.currency')
    expect(currencySelect.props().modelValue).toBe('currency-1')
    expect(currencySelect.props().disabled).toBe(false)

    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    const countrySelect = component.findComponent(BaseSelect)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(countrySelect.props().label).toBe('u2.country')
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(countrySelect.props().modelValue).toBe('country-1')
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(countrySelect.props().disabled).toBe(false)
  })

  it('renders field as disabled if unit properties returns that the field is disabled', async () => {
    server.use(
      http.get('/api/unit-properties', async () => {
        return HttpResponse.json(
          {
            'hydra:member': Object.keys(unit).map((property) => ({
              id: property,
              editable: false,
            })),
          },
          { status: StatusCodes.OK }
        )
      })
    )

    const component = mount(UnitEditor, {
      global: {
        stubs: {
          AppSelect: true,
          TaxNumbers: true,
        },
        plugins: [createTestingPinia()],
      },
      props: {
        unit,
      },
    })

    await flushPromises()

    const textInputs = component.findAllComponents(BaseInputText)

    const refId = textInputs[0]
    expect(refId.props().disabled).toBe(true)

    const name = textInputs[1]
    expect(name.props().disabled).toBe(true)

    const description = component.findComponent(BaseTextarea)
    expect(description.props().disabled).toBe(true)

    const verified = component.findComponent(BaseToggle)
    expect(verified.props().disabled).toBe(true)

    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    const countrySelect = component.findComponent(BaseSelect)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(countrySelect.props().disabled).toBe(true)

    const currencySelect = component.findComponent(BaseCurrencySelect)
    expect(currencySelect.props().disabled).toBe(true)
  })
})
