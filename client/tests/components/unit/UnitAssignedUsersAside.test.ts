import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { render } from '@testing-library/vue'
import flushPromises from 'flush-promises'
import { fromPartial } from '@total-typescript/shoehorn'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import UnitAssignedUsersAside from '@js/components/unit/UnitAssignedUsersAside.vue'
import UserLabel from '@js/components/UserLabel.vue'
import ButtonEdit from '@js/components/buttons/ButtonEdit.vue'
import type { User } from '@js/model/user'
import type { Unit } from '@js/model/unit'

const unit = fromPartial<Unit>({
  '@id': '/api/units/1',
  id: 1,
  name: 'unit 1',
})

const user1 = fromPartial<User>({
  '@id': '/api/users/1',
  id: 1,
  username: 'user1',
})

const user2 = fromPartial<User>({
  '@id': '/api/users/2',
  id: 2,
  username: 'user2',
})

const server = setupServer(
  http.get('/api/users', async () => {
    return HttpResponse.json({ 'hydra:member': [user1] }, { status: StatusCodes.OK })
  }),
  http.get(user1['@id'], async () => {
    return HttpResponse.json({ ...user1 }, { status: StatusCodes.OK })
  }),
  http.get(user2['@id'], async () => {
    return HttpResponse.json({ ...user2 }, { status: StatusCodes.OK })
  })
)

describe('UnitAssignedUsersAside', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('Renders with non inherited users', async () => {
    // Given
    server.use(
      http.get('/api/units/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [user1] }, { status: StatusCodes.OK })
      }),
      http.get('/api/units/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )
    const wrapper = mount(UnitAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit },
    })

    await flushPromises()

    // Then
    const userLabel = wrapper.findComponent(UserLabel)
    expect(userLabel.exists()).toBe(true)
    expect(userLabel.html()).toContain(user1.id)
    expect(userLabel.props('color')).toContain('white')

    const editButton = wrapper.findComponent(ButtonEdit)
    expect(editButton.exists()).toBe(true)
  })

  it('Renders with edit button disabled', async () => {
    // Given
    server.use(
      http.get('/api/units/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      }),
      http.get('/api/units/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )
    const ui = render(UnitAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit, disabled: true },
    })

    await flushPromises()

    // Then
    expect(ui.getByRole('button', { name: 'u2.edit' })).toBeDisabled()
    expect(ui.getByText('u2.no_users')).toBeInTheDocument()
    expect(
      ui.getByText('u2.unit.no_users_assigned_description', { exact: false })
    ).toBeInTheDocument()
    expect(ui.getByText('u2.contact_admin', { exact: false })).toBeInTheDocument()
    expect(ui.queryByRole('button', { name: 'u2.assign_users' })).not.toBeInTheDocument()
  })

  it('Renders with inherited users', async () => {
    // Given
    server.use(
      http.get('/api/units/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      }),
      http.get('/api/units/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [user2] }, { status: StatusCodes.OK })
      })
    )
    const wrapper = mount(UnitAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit },
    })

    await flushPromises()

    // Then
    const userLabel = wrapper.findComponent(UserLabel)
    expect(userLabel.exists()).toBe(true)
    expect(userLabel.html()).toContain(user2.username)
    expect(userLabel.props('color')).toContain('gray')
  })

  it('Renders without users', async () => {
    server.use(
      http.get('/api/units/1/direct-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      }),
      http.get('/api/units/1/inherited-users', async () => {
        return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      })
    )

    // Given
    const ui = render(UnitAssignedUsersAside, {
      global: {
        plugins: [createTestingPinia()],
      },
      props: { unit },
    })

    await flushPromises()

    // Then
    expect(ui.getByText('u2.no_users')).toBeInTheDocument()
    expect(
      ui.getByText('u2.unit.no_users_assigned_description', { exact: false })
    ).toBeInTheDocument()
    expect(ui.getByText('u2.unit.no_users_assigned_admin', { exact: false })).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'u2.assign_users' })).toBeInTheDocument()
  })
})
