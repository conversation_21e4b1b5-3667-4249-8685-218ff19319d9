import queryClient from '@js/queryClient'
import { createTestingPinia } from '@pinia/testing'
import { userEvent } from '@testing-library/user-event'
import { render, within } from '@testing-library/vue'
import { createCountry } from '@tests/__factories__/createCountry'
import { createTaxNumber } from '@tests/__factories__/createTaxNumber'
import { createUnit } from '@tests/__factories__/createUnit'
import { chooseOption, createHydraCollection } from '@tests/utils'
import { flushPromises } from '@vue/test-utils'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { setupServer } from 'msw/node'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { useNotificationsStore } from '@js/stores/notifications'
import TaxNumbers from '@js/components/unit/TaxNumbers.vue'

describe('TaxNumbers', () => {
  const server = setupServer()
  beforeEach(async () => {
    mockIntersectionObserver()
    window.HTMLElement.prototype.scrollIntoView = vi.fn()
  })
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  const countries = [createCountry({ id: 1 }), createCountry({ id: 2 })]
  const taxNumbers = [
    createTaxNumber({ country: countries[0]['@id'] }),
    createTaxNumber({ country: countries[1]['@id'] }),
  ]
  const unit = createUnit({ taxNumbers: [taxNumbers[0], taxNumbers[1]] })

  it('renders', async () => {
    // Given
    server.use(
      http.get('/api/countries', async () => {
        return HttpResponse.json(createHydraCollection(countries), { status: StatusCodes.OK })
      })
    )
    const ui = render(TaxNumbers, {
      props: {
        unit,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(
      await ui.findByText(`${countries[0].iso3166code} ${taxNumbers[0].value}`)
    ).toBeInTheDocument()
    expect(ui.getByText(`${countries[1].iso3166code} ${taxNumbers[1].value}`)).toBeInTheDocument()
    expect(ui.getByRole('button', { name: 'add u2.add' })).toBeInTheDocument()
  })

  it('adds a new TaxNumber', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get('/api/countries', async () => {
        return HttpResponse.json(createHydraCollection(countries), { status: StatusCodes.OK })
      }),
      http.post('/api/tax-numbers', async () => {
        return HttpResponse.json(newTaxNumber, {
          status: StatusCodes.CREATED,
        })
      })
    )
    const newTaxNumber = createTaxNumber({
      '@id': '/api/tax-number/2',
      id: 'tax-number-3',
      country: countries[1]['@id'],
      value: 'newTaxNumberValue',
    })
    const ui = render(TaxNumbers, {
      props: {
        unit,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    const queryClientSpy = vi.spyOn(queryClient, 'invalidateQueries')
    const notificationStore = useNotificationsStore()
    await flushPromises()

    // When
    await user.click(ui.getByRole('button', { name: 'add u2.add' }))

    await chooseOption(ui, 'country', countries[1].nameShort)
    await user.type(ui.getByLabelText('u2_core.tax_number'), newTaxNumber.value)

    await user.click(ui.getByRole('button', { name: 'save u2.save' }))

    // Then
    expect(notificationStore.addSuccess).toHaveBeenCalled()
    expect(ui.getByText(`${countries[1].iso3166code} ${newTaxNumber.value}`)).toBeInTheDocument()
    expect(queryClientSpy).toHaveBeenCalledWith({ queryKey: ['units', 'single', unit.id] })
  })

  it('deletes TaxNumber', async () => {
    // Given
    const user = userEvent.setup()
    server.use(
      http.get('/api/countries', async () => {
        return HttpResponse.json(createHydraCollection(countries), { status: StatusCodes.OK })
      }),
      http.delete('/api/tax-numbers/:id', async () => {
        return new Response(null, { status: StatusCodes.NO_CONTENT })
      })
    )
    const ui = render(TaxNumbers, {
      props: {
        unit,
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    const queryClientSpy = vi.spyOn(queryClient, 'invalidateQueries')
    const notificationStore = useNotificationsStore()

    expect(
      await ui.findByText(`${countries[1].iso3166code} ${taxNumbers[1].value}`)
    ).toBeInTheDocument()

    // When
    await user.click(
      within(
        ui.getByText(`${countries[1].iso3166code} ${taxNumbers[1].value}`)
          .parentElement as HTMLElement
      ).getByRole('button', { name: 'u2.clear' })
    )
    expect(
      await ui.findByText(
        `u2_core.delete_given_entity_type_with_given_name.confirmation {"entity_type_name":"u2_core.tax_number","entity_name":"${countries[1].iso3166code} ${taxNumbers[1].value}"}`
      )
    ).toBeInTheDocument()
    await user.click(ui.getByTestId('confirm-delete-button'))

    // Then
    expect(
      ui.queryByText(`${countries[1].iso3166code} ${taxNumbers[1].value}`)
    ).not.toBeInTheDocument()
    expect(notificationStore.addSuccess).toHaveBeenCalled()
    expect(queryClientSpy).toHaveBeenCalledWith({ queryKey: ['units', 'single', unit.id] })
  })
})
