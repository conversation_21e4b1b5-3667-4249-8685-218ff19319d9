import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { render, waitFor } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDelete from '@js/components/buttons/ButtonDelete.vue'

describe('ButtonDelete', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  it('renders', () => {
    const wrapper = mount(ButtonDelete, {
      global: {
        plugins: [createTestingPinia()],
      },
      shallow: true,
    })

    const buttonBasic = wrapper.findComponent(ButtonBasic)
    expect(buttonBasic.exists()).toBe(true)
    expect(buttonBasic.props('buttonStyle')).toBe('text')
    expect(buttonBasic.props('color')).toBe('action')
    expect(buttonBasic.props('disabled')).toBe(false)
    expect(buttonBasic.props('icon')).toBe('delete')
    expect(buttonBasic.props('grouped')).toBe(false)
    expect(buttonBasic.props('tooltip')).toBeUndefined()
  })

  it('has a "Delete" tooltip when there is no text', () => {
    const ui = render(ButtonDelete, {
      props: {
        showText: false,
      },
    })
    expect(ui.getByRole('button', { name: 'u2.delete' })).toBeInTheDocument()
  })

  it('has a custom tooltip when it is set and the button has no text', () => {
    const ui = render(ButtonDelete, {
      props: {
        showText: false,
        tooltip: 'Hey!',
      },
    })

    expect(ui.getByRole('button', { name: 'Hey!' })).toBeInTheDocument()
    expect(ui.queryByText('u2.delete')).not.toBeInTheDocument()
  })

  it('triggers click on delete button when deletion is confirmed', async () => {
    // Given
    const user = userEvent.setup()
    const onClick = vi.fn()
    const ui = render(ButtonDelete, {
      attrs: {
        click: onClick,
      },
      props: {
        confirm: true,
        confirmationText: 'Confirm!',
      },
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByRole('button', { name: /delete/ }))

    await vi.dynamicImportSettled()

    // Then
    await waitFor(() => {
      expect(ui.getByRole('dialog')).toBeInTheDocument()
    })
    expect(onClick).toHaveBeenCalledTimes(0)
    expect(ui.queryByText('u2_core.delete_entry.confirmation')).not.toBeInTheDocument()
    expect(ui.getByText('Confirm!')).toBeInTheDocument()
    expect(ui.getByRole('dialog')).toBeInTheDocument()

    // When
    await user.click(ui.getByTestId('confirm-delete-button'))

    // Then
    expect(onClick).toHaveBeenCalledTimes(1)
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })
})
