import { mount } from '@vue/test-utils'
import { render } from '@testing-library/vue'
import { userEvent } from '@testing-library/user-event'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import ButtonDropdown from '@js/components/buttons/ButtonDropdown.vue'
import SvgIcon from '@js/components/SvgIcon.vue'

describe('ButtonDropdown', () => {
  it('renders a dropdown', () => {
    const wrapper = mount(ButtonDropdown, {
      slots: {
        default: 'button name',
      },
    })
    expect(wrapper.findComponent(ButtonBasic).text()).toBe('button name')
    expect(wrapper.findComponent(SvgIcon).props('icon')).toBe('arrow-up')
  })

  it('opens when clicked', async () => {
    const ui = render(ButtonDropdown, {
      slots: {
        body: '<a>Drop Entry 1</a><a>Drop Entry 2</a>',
        default: 'button name',
      },
    })
    expect(ui.queryByText('Drop Entry 1')).not.toBeInTheDocument()
    await userEvent.setup().click(ui.getByText('button name'))
    expect(ui.getByText('Drop Entry 1')).toBeInTheDocument()
  })
})
