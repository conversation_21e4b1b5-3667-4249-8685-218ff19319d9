import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { createTransitionWithEmbeddedStatus } from '@tests/__factories__/createTransition'
import { createWorkflow } from '@tests/__factories__/createWorkflow'
import { mount } from '@vue/test-utils'
import AppTable from '@js/components/table/AppTable.vue'
import AppLoader from '@js/components/loader/AppLoader.vue'
import TransitionsList from '@js/components/workflow/TransitionList.vue'

describe('TransitionList', () => {
  it('shows a loader', async () => {
    const wrapper = mount(TransitionsList, {
      shallow: true,
      props: {
        transitions: [],
        loading: true,
        workflow: createWorkflow({ id: 1 }),
      },
    })
    expect(wrapper.findComponent(AppLoader).exists()).toBe(true)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(wrapper.findComponent(AppTable).exists()).toBe(false)
  })

  it('renders with data', () => {
    const transitions = [createTransitionWithEmbeddedStatus()]
    const wrapper = mount(TransitionsList, {
      props: {
        global: {
          plugins: [createTestingPinia()],
        },
        transitions,
        loading: false,
        workflow: createWorkflow({ id: 1 }),
      },
    })
    expect(wrapper.findComponent(AppLoader).exists()).toBe(false)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    const table = wrapper.findComponent(AppTable)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(table.props().headers).toHaveLength(6)
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(table.props().items).toStrictEqual(transitions)
  })
})
