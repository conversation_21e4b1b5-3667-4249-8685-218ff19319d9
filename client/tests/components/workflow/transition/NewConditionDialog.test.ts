import { createStatus } from '@tests/__factories__/createStatus'
import { createTransitionWithEmbeddedStatus } from '@tests/__factories__/createTransition'
import axios from 'axios'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import AppDialog from '@js/components/AppDialog.vue'
import NewConditionDialog from '@js/components/workflow/transition/NewConditionDialog.vue'
import StatusBadge from '@js/components/workflow/StatusBadge.vue'

vi.mock('axios')

describe('NewConditionDialog', () => {
  beforeEach(() => {
    mockIntersectionObserver()
  })

  beforeAll(() => {
    vi.mocked(axios.get).mockResolvedValue({
      data: {
        'hydra:member': [
          {
            '@id': '/api/workflow/transition/condition-types/current-user-has-not-reviewed',
            '@type': 'ConditionType',
            id: 'current-user-has-not-reviewed',
            help: 'This transition can only be performed by a user who has not reviewed the entity.',
            name: 'User has not reviewed',
          },
          {
            '@id': '/api/workflow/transition/condition-types/current-user-has-reviewed',
            '@type': 'ConditionType',
            id: 'current-user-has-reviewed',
            help: 'This transition can only be performed by a user who has reviewed the entity.',
            name: 'User has reviewed',
          },
        ],
      },
    })
  })

  it('Renders', async () => {
    // When
    const wrapper = mount(NewConditionDialog, {
      props: {
        transition: createTransitionWithEmbeddedStatus({
          name: 'Transition from A to B',
          originStatus: createStatus({
            name: 'A',
            type: 'IN_PROGRESS',
          }),
          destinationStatus: createStatus({
            name: 'B',
            type: 'COMPLETE',
          }),
        }),
      },
    })
    await flushPromises()

    // Then
    expect(wrapper.findAllComponents(StatusBadge)).toHaveLength(2)
  })

  it('emits "close" event', async () => {
    // Given
    const wrapper = mount(NewConditionDialog, {
      props: {
        transition: createTransitionWithEmbeddedStatus({
          name: 'Transition from A to B',
          originStatus: createStatus({
            name: 'A',
            type: 'IN_PROGRESS',
          }),
          destinationStatus: createStatus({
            name: 'B',
            type: 'COMPLETE',
          }),
        }),
      },
    })
    await flushPromises()

    const dialog = wrapper.findComponent(AppDialog)

    // When
    dialog.vm.$emit('close')

    // Then
    expect(wrapper.emitted('close')).toBeTruthy()
  })
})
