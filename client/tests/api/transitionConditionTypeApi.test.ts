import { HttpMethods, HttpResponse, http } from 'msw'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import {
  transitionConditionTypeApi,
  transitionConditionTypeApiBasePath,
} from '@js/api/transitionConditionTypeApi'

describe('transition condition types', () => {
  const server = setupServer()
  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('fetches transition condition types', async function () {
    const conditionType1 = {
      '@id': `${transitionConditionTypeApiBasePath}/currentuserhasnotreviewedcondition`,
      id: 'currentuserhasnotreviewedcondition',
    }
    const conditionType2 = {
      '@id': `${transitionConditionTypeApiBasePath}/itemvalueequalscondition`,
      id: 'itemvalueequalscondition',
    }
    server.use(
      http.get(transitionConditionTypeApiBasePath, async () => {
        return HttpResponse.json(
          { 'hydra:member': [conditionType1, conditionType2] },
          { status: StatusCodes.OK }
        )
      })
    )
    expect((await transitionConditionTypeApi.fetchAllTaskTransitionConditionTypes()).data).toEqual({
      'hydra:member': [conditionType1, conditionType2],
    })
  })

  it('fetches a user by id', async function () {
    const conditionType1 = {
      '@id': `${transitionConditionTypeApiBasePath}/currentuserhasnotreviewedcondition`,
      id: 'currentuserhasnotreviewedcondition',
    }
    server.use(
      http.get(conditionType1['@id'], async () => {
        return HttpResponse.json(conditionType1, { status: StatusCodes.OK })
      })
    )
    const response = await transitionConditionTypeApi.fetchTaskTransitionConditionTypeById(
      conditionType1.id
    )
    expect(response.data).toEqual(conditionType1)
    expect(response.config.url).toBe(conditionType1['@id'])
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches a user by IRI', async function () {
    const conditionType2 = {
      '@id': `${transitionConditionTypeApiBasePath}/itemvalueequalscondition`,
      id: 'itemvalueequalscondition',
    }
    server.use(
      http.get(conditionType2['@id'], async () => {
        return HttpResponse.json(conditionType2, { status: StatusCodes.OK })
      })
    )
    const response = await transitionConditionTypeApi.fetchTaskTransitionConditionTypeByIri(
      conditionType2['@id']
    )
    expect(response.data).toEqual(conditionType2)
    expect(response.config.url).toBe(conditionType2['@id'])
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })
})
