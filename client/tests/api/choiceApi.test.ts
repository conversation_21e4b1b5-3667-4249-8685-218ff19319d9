import { createChoice } from '@tests/__factories__/createChoice'
import { HttpMethods, HttpResponse, http } from 'msw'
import { createHydraCollection, setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { choiceTypesToResourceCollectionEndpoint } from '@js/model/choice'
import { choiceApi } from '@js/api/choiceApi'

describe('choice api', () => {
  const server = setupServer()
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('fetches a collection of records by query', async function () {
    const collectionPath = '/api/configuration/field/advice-types'
    const choiceCollection = createHydraCollection([createChoice({ '@type': 'AdviceType' })])
    server.use(
      http.get(collectionPath, async () =>
        HttpResponse.json(choiceCollection, {
          status: StatusCodes.OK,
        })
      )
    )
    const response = await choiceApi.fetchChoiceByQuery(collectionPath, { pagination: false })
    expect(response.config.url).toBe(collectionPath)
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
    expect(response.config.params).toEqual({ pagination: false })
    expect(response.data).toEqual(choiceCollection)
  })

  it('creates a new record', async function () {
    const choice = createChoice({ '@type': 'AdviceType', id: undefined })
    server.use(
      http.post(choiceTypesToResourceCollectionEndpoint[choice['@type']], async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      })
    )
    const response = await choiceApi.createChoice(choice)
    expect(response.config.url).toBe('/api/configuration/field/advice-types')
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(response.data).toEqual(choice)
  })

  it('updates an existing record', async function () {
    const choice = createChoice({ '@type': 'BillingType' })
    server.use(
      http.patch(`/api/configuration/field/billing-types/${choice.id}`, async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      })
    )
    const response = await choiceApi.updateChoice(choice)
    expect(response.config.url).toBe(`/api/configuration/field/billing-types/${choice.id}`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(response.data).toEqual(choice)
  })

  it('deletes an existing record', async function () {
    const choice = createChoice({ '@type': 'BillingType' })
    server.use(
      http.delete(`/api/configuration/field/billing-types/${choice.id}`, async () => {
        return HttpResponse.json(undefined, { status: StatusCodes.NO_CONTENT })
      })
    )
    const response = await choiceApi.deleteChoice(choice)
    expect(response.config.url).toBe(`/api/configuration/field/billing-types/${choice.id}`)
    expect(response.config.method).toBe(HttpMethods.DELETE.toLowerCase())
    expect(response.data).toBe('')
  })
})
