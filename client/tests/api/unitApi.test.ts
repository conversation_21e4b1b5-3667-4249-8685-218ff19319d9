import { createLegalUnit } from '@tests/__factories__/createLegalUnit'
import { createOrganisationalGroup } from '@tests/__factories__/createOrganisationalGroup'
import { createPermanentEstablishment } from '@tests/__factories__/createPermanentEstablishment'
import { createUnit } from '@tests/__factories__/createUnit'
import { HttpMethods, HttpResponse, http } from 'msw'
import { findResourceById, setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import * as UnitApi from '@js/api/unitApi'
import { basicUnitApiBasePath, unitApiBasePath } from '@js/api/unitApi'
import { createUserGroup } from '@tests/__factories__/createUserGroup'
import { createUser } from '@tests/__factories__/createUser'
import { createBasicUnit } from '@tests/__factories__/createBasicUnit'

const server = setupServer()

describe('unit', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())

  it('fetches units', async function () {
    const unit = createUnit()
    server.use(
      http.get(basicUnitApiBasePath, async () => {
        return HttpResponse.json({ 'hydra:member': [unit] }, { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.fetchAllUnits()
    expect(response.data).toEqual({ 'hydra:member': [unit] })
    expect(response.config.url).toBe(basicUnitApiBasePath)
    expect(response.config.params).toStrictEqual({
      pagination: false,
      'sort[name]': 'ASC',
    })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches unit assigned groups', async function () {
    const group = createUserGroup()
    server.use(
      http.get(`${unitApiBasePath}/1/groups`, async () => {
        return HttpResponse.json({ 'hydra:member': [group] }, { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.fetchUnitAssignedUserGroups(1)
    expect(response.data).toEqual({ 'hydra:member': [group] })
    expect(response.config.url).toBe(`${unitApiBasePath}/1/groups`)
    expect(response.config.params).toStrictEqual({
      pagination: false,
    })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('updates unit assigned groups', async function () {
    server.use(
      http.patch(`${unitApiBasePath}/1/groups`, async () => {
        return HttpResponse.json(
          { '@id': `${unitApiBasePath}/1/groups` },
          { status: StatusCodes.OK }
        )
      })
    )
    const response = await UnitApi.updateUnitAssignedUserGroups(1, ['/api/user-group/1'])
    expect(response.data).toEqual({
      '@id': `${unitApiBasePath}/1/groups`,
    })
    expect(response.config.url).toBe(`${unitApiBasePath}/1/groups`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
  })

  it('fetches unit assigned users', async function () {
    const user = createUser()
    server.use(
      http.get(`${unitApiBasePath}/1/direct-users`, async () => {
        return HttpResponse.json({ 'hydra:member': [user] }, { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.fetchUnitAssignedUsers(1)
    expect(response.data).toEqual({ 'hydra:member': [user] })
    expect(response.config.url).toBe(`${unitApiBasePath}/1/direct-users`)
    expect(response.config.params).toStrictEqual({
      pagination: false,
    })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('updates unit assigned users', async function () {
    server.use(
      http.patch(`${unitApiBasePath}/1/direct-users`, async () => {
        return HttpResponse.json(
          { '@id': `${unitApiBasePath}/1/direct-users` },
          { status: StatusCodes.OK }
        )
      })
    )
    const response = await UnitApi.updateUnitAssignedUsers(1, ['/api/user/1'])
    expect(response.data).toEqual({
      '@id': `${unitApiBasePath}/1/direct-users`,
    })
    expect(response.config.url).toBe(`${unitApiBasePath}/1/direct-users`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
  })

  it('fetches unit assigned inherited users', async function () {
    const user = createUser()
    server.use(
      http.get(`${unitApiBasePath}/1/inherited-users`, async () => {
        return HttpResponse.json({ 'hydra:member': [user] }, { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.fetchUnitInheritedAssignedUsers(1)
    expect(response.data).toEqual({ 'hydra:member': [user] })
    expect(response.config.url).toBe(`${unitApiBasePath}/1/inherited-users`)
    expect(response.config.params).toStrictEqual({
      pagination: false,
    })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches a basic unit by id', async function () {
    const basicUnit = createBasicUnit()
    server.use(
      http.get(basicUnitApiBasePath + '/:id', async ({ params }) => {
        return HttpResponse.json(findResourceById(params.id, [basicUnit]), {
          status: StatusCodes.OK,
        })
      })
    )
    const response = await UnitApi.fetchBasicUnitById(basicUnit.id)
    expect(response.data).toEqual(basicUnit)
    expect(response.config.url).toBe(basicUnit['@id'])
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches basic units by query', async function () {
    const basicUnit = createBasicUnit()
    server.use(
      http.get(basicUnitApiBasePath, async () => {
        return HttpResponse.json({ 'hydra:member': [basicUnit] }, { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.fetchBasicUnitsByQuery({ page: 1 })
    expect(response.data).toEqual({ 'hydra:member': [basicUnit] })
    expect(response.config.url).toBe(basicUnitApiBasePath)
    expect(response.config.params).toStrictEqual({ page: 1 })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches a unit by id', async function () {
    const unit = createUnit()
    server.use(
      http.get(unitApiBasePath + '/:id', async ({ params }) => {
        return HttpResponse.json(findResourceById(params.id, [unit]), {
          status: StatusCodes.OK,
        })
      })
    )
    const response = await UnitApi.fetchUnitById(unit.id)
    expect(response.data).toEqual(unit)
    expect(response.config.url).toBe(unit['@id'])
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('fetches units by query', async function () {
    const unit = createUnit()
    server.use(
      http.get(unitApiBasePath, async () => {
        return HttpResponse.json({ 'hydra:member': [unit] }, { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.fetchUnitsByQuery({ page: 1 })
    expect(response.data).toEqual({ 'hydra:member': [unit] })
    expect(response.config.url).toBe(unitApiBasePath)
    expect(response.config.params).toStrictEqual({ page: 1 })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('creates a new record', async function () {
    const unit = createUnit({ id: undefined })
    server.use(
      http.post(unitApiBasePath, async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      })
    )
    const response = await UnitApi.createUnit(unit)
    expect(response.config.url).toBe(unitApiBasePath)
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(response.data).toEqual(unit)
  })

  it('updates an existing record', async function () {
    const unit = createUnit()
    server.use(
      http.patch(`${unitApiBasePath}/${unit.id}`, async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.updateUnit(unit)
    expect(response.config.url).toBe(`${unitApiBasePath}/${unit.id}`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(response.data).toEqual(unit)
  })

  it('downloads xls', async function () {
    const params = { page: 1 }
    server.use(
      http.get(unitApiBasePath, () => {
        return HttpResponse.json('', { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.downloadUnitXls(params)
    expect(response.data).toEqual(new Blob())
    expect(response.config.url).toBe(unitApiBasePath)
    expect(response.config.params).toEqual({ page: 1, pagination: false })
  })

  it('deletes a record', async function () {
    const unit = createUnit()
    server.use(
      http.delete(unit['@id'], () => {
        return HttpResponse.json('', { status: StatusCodes.NO_CONTENT })
      })
    )
    const response = await UnitApi.deleteUnit(unit)
    expect(response.config.url).toBe(unit['@id'])
    expect(response.config.method).toBe(HttpMethods.DELETE.toLowerCase())
  })

  it('fetches unit audit logs by query', async function () {
    server.use(
      http.get(`${unitApiBasePath}/1/logs`, () =>
        HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
      )
    )
    const response = await UnitApi.fetchUnitLogsByQuery(1, { page: 2 })
    expect(response.data).toEqual({ 'hydra:member': [] })
    expect(response.config.url).toBe(`${unitApiBasePath}/1/logs`)
    expect(response.config.params).toStrictEqual({ page: 2 })
    expect(response.config.method).toBe(HttpMethods.GET.toLowerCase())
  })

  it('creates a new legal unit', async function () {
    const legalUnit = createLegalUnit({ id: undefined })
    server.use(
      http.post('/api/legal-units', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      })
    )
    const response = await UnitApi.createLegalUnit(legalUnit)
    expect(response.config.url).toBe('/api/legal-units')
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(response.data).toEqual(legalUnit)
  })

  it('updates an existing legal unit', async function () {
    const legalUnit = createLegalUnit()
    server.use(
      http.patch(`/api/legal-units/${legalUnit.id}`, async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.updateLegalUnit(legalUnit)
    expect(response.config.url).toBe(`/api/legal-units/${legalUnit.id}`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(response.data).toEqual(legalUnit)
  })

  it('creates a new organisational group', async function () {
    const organisationalGroup = createOrganisationalGroup({ id: undefined })
    server.use(
      http.post('/api/organisational-groups', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      })
    )
    const response = await UnitApi.createOrganisationalGroup(organisationalGroup)
    expect(response.config.url).toBe('/api/organisational-groups')
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(response.data).toEqual(organisationalGroup)
  })

  it('updates an existing organisational group', async function () {
    const organisationalGroup = createOrganisationalGroup()
    server.use(
      http.patch(`/api/organisational-groups/${organisationalGroup.id}`, async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      })
    )
    const response = await UnitApi.updateOrganisationalGroup(organisationalGroup)
    expect(response.config.url).toBe(`/api/organisational-groups/${organisationalGroup.id}`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(response.data).toEqual(organisationalGroup)
  })

  it('creates a new permanent establishment', async function () {
    const permanentEstablishment = createPermanentEstablishment({ id: undefined })
    server.use(
      http.post('/api/permanent-establishments', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      })
    )
    const response = await UnitApi.createPermanentEstablishment(permanentEstablishment)
    expect(response.config.url).toBe('/api/permanent-establishments')
    expect(response.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(response.data).toEqual(permanentEstablishment)
  })

  it('updates an existing permanent establishment', async function () {
    const permanentEstablishment = createPermanentEstablishment()
    server.use(
      http.patch(
        `/api/permanent-establishments/${permanentEstablishment.id}`,
        async ({ request }) => {
          return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
        }
      )
    )
    const response = await UnitApi.updatePermanentEstablishment(permanentEstablishment)
    expect(response.config.url).toBe(`/api/permanent-establishments/${permanentEstablishment.id}`)
    expect(response.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(response.data).toEqual(permanentEstablishment)
  })

  it('creates any new unit', async function () {
    server.use(
      http.post('/api/legal-units', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      }),
      http.post('/api/organisational-groups', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      }),
      http.post('/api/permanent-establishments', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      }),
      http.post('/api/units', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.CREATED })
      })
    )
    const legalUnit = createLegalUnit({ id: undefined, '@type': 'LegalUnit' })
    const legalUnitResponse = await UnitApi.createAnyUnit(legalUnit)
    expect(legalUnitResponse.config.url).toBe('/api/legal-units')
    expect(legalUnitResponse.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(legalUnitResponse.data).toEqual(legalUnit)

    const organisationalGroup = createOrganisationalGroup({
      id: undefined,
      '@type': 'OrganisationalGroup',
    })
    const organisationalGroupResponse = await UnitApi.createAnyUnit(organisationalGroup)
    expect(organisationalGroupResponse.config.url).toBe('/api/organisational-groups')
    expect(organisationalGroupResponse.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(organisationalGroupResponse.data).toEqual(organisationalGroup)

    const permanentEstablishment = createPermanentEstablishment({
      id: undefined,
      '@type': 'PermanentEstablishment',
    })
    const permanentEstablishmentResponse = await UnitApi.createAnyUnit(permanentEstablishment)
    expect(permanentEstablishmentResponse.config.url).toBe('/api/permanent-establishments')
    expect(permanentEstablishmentResponse.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(permanentEstablishmentResponse.data).toEqual(permanentEstablishment)

    const unit = createUnit({ id: undefined, '@type': 'Unit' })
    const unitResponse = await UnitApi.createAnyUnit(unit)
    expect(unitResponse.config.url).toBe('/api/units')
    expect(unitResponse.config.method).toBe(HttpMethods.POST.toLowerCase())
    expect(unitResponse.data).toEqual(unit)
  })

  it('updates any existing unit', async function () {
    server.use(
      http.patch('/api/legal-units/:id', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      }),
      http.patch('/api/organisational-groups/:id', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      }),
      http.patch('/api/permanent-establishments/:id', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      }),
      http.patch('/api/units/:id', async ({ request }) => {
        return HttpResponse.json(await request.json(), { status: StatusCodes.OK })
      })
    )
    const legalUnit = createLegalUnit({ id: 1, '@type': 'LegalUnit' })
    const legalUnitResponse = await UnitApi.updateAnyUnit(legalUnit)
    expect(legalUnitResponse.config.url).toBe(`/api/legal-units/${legalUnit.id}`)
    expect(legalUnitResponse.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(legalUnitResponse.data).toEqual(legalUnit)

    const organisationalGroup = createOrganisationalGroup({
      id: 1,
      '@type': 'OrganisationalGroup',
    })
    const organisationalGroupResponse = await UnitApi.updateAnyUnit(organisationalGroup)
    expect(organisationalGroupResponse.config.url).toBe(`/api/organisational-groups/${1}`)
    expect(organisationalGroupResponse.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(organisationalGroupResponse.data).toEqual(organisationalGroup)

    const permanentEstablishment = createPermanentEstablishment({
      id: 1,
      '@type': 'PermanentEstablishment',
    })
    const permanentEstablishmentResponse = await UnitApi.updateAnyUnit(permanentEstablishment)
    expect(permanentEstablishmentResponse.config.url).toBe(`/api/permanent-establishments/${1}`)
    expect(permanentEstablishmentResponse.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(permanentEstablishmentResponse.data).toEqual(permanentEstablishment)

    const unit = createUnit({ id: 1, '@type': 'Unit' })
    const unitResponse = await UnitApi.updateAnyUnit(unit)
    expect(unitResponse.config.url).toBe(`/api/units/${unit.id}`)
    expect(unitResponse.config.method).toBe(HttpMethods.PATCH.toLowerCase())
    expect(unitResponse.data).toEqual(unit)
  })
})
