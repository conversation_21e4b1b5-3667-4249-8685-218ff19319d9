import { createFile } from '@tests/__factories__/createFile'
import axios from 'axios'
import { createTestingPinia } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { render } from '@testing-library/vue'
import { createUser } from '@tests/__factories__/createUser'
import { mockIntersectionObserver } from 'jsdom-testing-mocks'
import { userEvent } from '@testing-library/user-event'
import FilePermissions from '@js/components/file/FilePermissions.vue'
import ButtonSave from '@js/components/buttons/ButtonSave.vue'
import { CompositePermissionMasks } from '@js/model/permission'
import FileEdit from '@js/pages/files/[id].vue'
import FileEditor from '@js/components/file/FileEditor.vue'
import LabelBasic from '@js/components/LabelBasic.vue'
import type ButtonDelete from '@js/components/buttons/ButtonDelete.vue'

vi.mock('axios')

describe('FileEdit Page', () => {
  beforeEach(async () => {
    mockIntersectionObserver()
  })

  const file = createFile({
    '@id': '/api/file/1',
    id: 1,
    name: 'File',
    userPermissions: [],
    groupPermissions: [],
    accessType: 'smart',
    types: [],
    downloadPath: 'path-to-file',
  })
  const userResource = createUser()

  it('renders', async function () {
    const ui = render(FileEdit, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          FileEditor: {
            template:
              '<form id="file_form" name="file_form"><label for="file">File</label><input id="file" type="file"/></form>',
          },
        },
      },
      props: {
        file,
      },
    })

    expect(ui.getByRole('heading', { name: 'u2.file: File' })).toBeInTheDocument()
    expect(ui.getByLabelText('File')).toBeInTheDocument()
    expect(ui.getByRole('link', { name: /u2.list/ })).toBeInTheDocument()
    expect(ui.getByRole('link', { name: /u2.new/ })).toBeInTheDocument()
    expect(
      ui.getByRole('button', { name: 'u2_core.show_entities_linked_with_this_file' })
    ).toBeInTheDocument()
  })

  it('deletes a file', async function () {
    const page = mount(FileEdit, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user: userResource,
              },
            },
          }),
        ],
        stubs: {
          FileEditor: true,
        },
      },
      props: {
        file: {
          ...file,
          userPermissions: [
            {
              '@id': '/api/permission-id/1',
              id: 1,
              user: userResource['@id'],
              mask: CompositePermissionMasks.DELETE,
            },
          ],
        },
      },
    })
    await flushPromises()

    await page.findComponent('#button-delete-file').trigger('click')

    await vi.dynamicImportSettled()

    await page.findComponent('[data-testId="confirm-delete-button"]').trigger('click')

    expect(vi.mocked(axios.delete)).toHaveBeenCalledWith('/api/files/1')
  })

  it('opens the file linked entities dialog when clicking the "linked entities" button', async () => {
    // Given
    vi.mocked(axios.get).mockResolvedValue({ data: { 'hydra:member': [] } })
    const user = userEvent.setup()
    const ui = render(FileEdit, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user,
              },
            },
          }),
        ],
        stubs: {
          InfoBox: true,
          FileEditor: true,
          LinkedEntities: true,
          InformationAsideSection: true,
        },
      },
      props: {
        file: {
          ...file,
          userPermissions: [
            {
              '@id': '/api/permission-id/1',
              id: 1,
              user: userResource['@id'],
              mask: CompositePermissionMasks.DELETE,
            },
          ],
        },
      },
    })
    await flushPromises()

    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()

    // When
    await user.click(ui.getByText('u2_core.linked_entities'))

    // Then
    expect(ui.getByRole('dialog')).toBeInTheDocument()
    expect(ui.getByTestId('linked-entities')).toBeInTheDocument()

    // When
    await user.click(ui.getByLabelText('cross'))

    // Then
    expect(ui.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('renders when user permissions are Read', async function () {
    const page = mount(FileEdit, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user: userResource,
              },
            },
          }),
        ],
        stubs: {
          FileEditor: true,
        },
      },
      props: {
        file: {
          ...file,
          userPermissions: [
            {
              '@id': '/api/permission-id/1',
              id: 1,
              user: userResource['@id'],
              mask: CompositePermissionMasks.VIEW,
            },
          ],
        },
      },
    })
    await flushPromises()
    await page.findComponent(FileEditor).trigger('loaded')

    expect(page.findComponent<typeof ButtonDelete>('#button-delete-file').props('disabled')).toBe(
      true
    )
    expect(page.findComponent(ButtonSave).props('disabled')).toBe(true)
    const filePermissions = page.findComponent(FilePermissions)
    const permissionLabels = filePermissions.findAllComponents(LabelBasic)
    expect(permissionLabels).toHaveLength(1)
    expect(permissionLabels[0].text()).toBe('u2_core.read')
  })

  it('renders when user permissions are Write', async function () {
    const page = mount(FileEdit, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user: userResource,
              },
            },
          }),
        ],
        stubs: {
          FileEditor: true,
        },
      },
      props: {
        file: {
          ...file,
          userPermissions: [
            {
              '@id': '/api/permission-id/1',
              id: 1,
              user: userResource['@id'],
              mask: CompositePermissionMasks.EDIT,
            },
          ],
        },
      },
    })
    await flushPromises()
    await page.findComponent(FileEditor).trigger('loaded')

    expect(page.findComponent<typeof ButtonDelete>('#button-delete-file').props('disabled')).toBe(
      true
    )
    expect(page.findComponent(ButtonSave).props('disabled')).toBe(false)
    const filePermissions = page.findComponent(FilePermissions)
    const permissionLabels = filePermissions.findAllComponents(LabelBasic)
    expect(permissionLabels).toHaveLength(2)
    expect(permissionLabels[0].text()).toBe('u2_core.read')
    expect(permissionLabels[1].text()).toBe('u2_core.write')
  })

  it('renders when user permissions are Delete', async function () {
    const page = mount(FileEdit, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user: userResource,
              },
            },
          }),
        ],
        stubs: {
          FileEditor: true,
        },
      },
      props: {
        file: {
          ...file,
          userPermissions: [
            {
              '@id': '/api/permission-id/1',
              id: 1,
              user: userResource['@id'],
              mask: CompositePermissionMasks.DELETE,
            },
          ],
        },
      },
    })
    await flushPromises()
    await page.findComponent(FileEditor).trigger('loaded')

    expect(page.findComponent<typeof ButtonDelete>('#button-delete-file').props('disabled')).toBe(
      false
    )
    const filePermissions = page.findComponent(FilePermissions)
    const permissionLabels = filePermissions.findAllComponents(LabelBasic)
    expect(permissionLabels).toHaveLength(3)
    expect(permissionLabels[0].text()).toBe('u2_core.read')
    expect(permissionLabels[1].text()).toBe('u2_core.write')
    expect(permissionLabels[2].text()).toBe('u2.delete')
  })

  it('renders when user permissions are Manage', async function () {
    const page = mount(FileEdit, {
      global: {
        plugins: [
          createTestingPinia({
            initialState: {
              auth: {
                user: userResource,
              },
            },
          }),
        ],
        stubs: {
          FileEditor: true,
        },
      },
      props: {
        file: {
          ...file,
          userPermissions: [
            {
              '@id': '/api/permission-id/1',
              id: 1,
              user: userResource['@id'],
              mask: CompositePermissionMasks.MANAGE,
            },
          ],
        },
      },
    })
    await flushPromises()
    await page.findComponent(FileEditor).trigger('loaded')

    expect(page.findComponent<typeof ButtonDelete>('#button-delete-file').props('disabled')).toBe(
      false
    )
    expect(page.findComponent(ButtonSave).props('disabled')).toBe(false)

    const filePermissions = page.findComponent(FilePermissions)
    const permissionLabels = filePermissions.findAllComponents(LabelBasic)
    expect(permissionLabels).toHaveLength(4)
    expect(permissionLabels[0].text()).toBe('u2_core.read')
    expect(permissionLabels[1].text()).toBe('u2_core.write')
    expect(permissionLabels[2].text()).toBe('u2.delete')
    expect(permissionLabels[3].text()).toBe('u2_core.manage')
  })
})
