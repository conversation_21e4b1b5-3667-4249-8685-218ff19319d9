import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { StatusCodes } from 'http-status-codes'
import { HttpResponse, http } from 'msw'
import { setupServer, wrapInSuspense } from '@tests/utils'
import { mount } from '@vue/test-utils'
import flushPromises from 'flush-promises'
import AppSearch from '@js/components/form/AppSearch.vue'
import AppTable from '@js/components/table/AppTable.vue'
import ButtonBasic from '@js/components/buttons/ButtonBasic.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import WorkflowBindingList from '@js/pages/configuration/workflows/workflow-assignments/index.vue'

const server = setupServer(
  http.get('/api/workflow-bindings', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  }),
  http.get('/api/workflows', async () => {
    return HttpResponse.json(
      { 'hydra:member': [], 'hydra:totalItems': 0 },
      { status: StatusCodes.OK }
    )
  })
)

describe('WorkflowBindingList Page', () => {
  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('renders', async function () {
    const page = await mount(wrapInSuspense(WorkflowBindingList), {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBe(true)
    expect(page.findComponent(ButtonBasic).props('icon')).toBe('list')
    // @ts-expect-error: See https://github.com/vuejs/test-utils/pull/2242.
    expect(page.findComponent(AppTable).exists()).toBe(true)
    expect(page.findComponent(AppSearch).exists()).toBe(true)
  })
})
