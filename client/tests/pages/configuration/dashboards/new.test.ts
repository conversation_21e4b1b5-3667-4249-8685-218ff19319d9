import { flushPromises, mount } from '@vue/test-utils'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import DashboardForm from '@js/components/dashboard/DashboardForm.vue'
import DashboardNew from '@js/pages/configuration/dashboards/new.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'

describe('DashboardNew Page', () => {
  it('renders', async function () {
    const page = mount(DashboardNew, {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          PageHeader: true,
        },
      },
    })
    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBe(true)
    expect(page.findComponent(DashboardForm).exists()).toBe(true)
  })
})
