import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import AuditorNew from '@js/pages/configuration/auditors/new.vue'

const server = setupServer(
  http.get('/api/countries', async () => {
    return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
  })
)

describe('AuditorNew Page', () => {
  beforeAll(() => server.listen())

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('renders', function () {
    const page = mount(AuditorNew, {
      global: {
        plugins: [createTestingPinia()],
      },
    })

    expect(page.findComponent(PageHeader).exists()).toBe(true)
  })
})
