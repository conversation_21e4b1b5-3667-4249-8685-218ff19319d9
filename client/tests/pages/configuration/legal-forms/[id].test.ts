import axios from 'axios'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { wrapInSuspense } from '@tests/utils'
import LegalFormEdit from '@js/pages/configuration/legal-forms/[id].vue'
import LegalFormEditor from '@js/components/LegalFormEditor.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'

vi.mock('axios')

describe('LegalFormEdit Page', () => {
  beforeEach(() => {
    vi.mocked(axios.get).mockResolvedValue({
      data: {
        id: 1,
        enabled: true,
        name: 'Legal Form 1',
        address: null,
      },
    })
  })

  it('renders', async function () {
    const page = await mount(wrapInSuspense(LegalFormEdit, { id: 1 }), {
      global: {
        plugins: [createTestingPinia()],
        stubs: {
          BaseInputAddress: true,
        },
      },
    })

    await flushPromises()
    expect(page.findComponent(PageHeader).exists()).toBe(true)
    expect(page.findComponent(LegalFormEditor).exists()).toBe(true)
  })
})
