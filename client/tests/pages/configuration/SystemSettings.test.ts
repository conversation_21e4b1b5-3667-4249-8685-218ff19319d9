import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import { setupServer } from 'msw/node'
import { HttpResponse, http } from 'msw'
import { StatusCodes } from 'http-status-codes'
import flushPromises from 'flush-promises'
import { wrapInSuspense } from '@tests/utils'
import { render } from '@testing-library/vue'
import { expect } from 'vitest'
import SystemSettingsEdit from '@js/pages/configuration/system-settings.vue'
import type { SystemSettings } from '@js/model/system_setting'

describe('SystemSettingsEdit Page', () => {
  const systemSettings = {
    '@id': '/api/system-settings',
    '@type': 'SystemSettings',
    securityPasswordRulesResetHoursValid: 5,
    u2Locale: 'string',
    loginColor: 'string',
    securityMaxLoginAttempts: 5,
    securityPasswordRulesUniqueHistoryCount: 5,
    securityPasswordRulesMinLength: 5,
    securityPasswordRulesMaxAgeInDays: 5,
    securityFileUploadWhitelist: [],
    securityUnitEditFieldWhitelist: [],
    securityTwoFactorIsEnforced: false,
    securityPasswordRulesRequireNumber: false,
    securityPasswordRulesRequireNonAlphanumericCharacter: false,
    securityPasswordRulesRequireUppercaseLetter: false,
    securityPasswordRulesRequireLowercaseLetter: false,
    taxAssessmentOpenBoy: false,
    maxUploadSize: 5,
    applicationCurrency: '/api/currencies/1',
  } satisfies SystemSettings

  const server = setupServer(
    http.get('/api/system-settings', async () => {
      return HttpResponse.json(systemSettings, { status: StatusCodes.OK })
    }),
    http.get('/api/unit-properties', async () => {
      return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
    }),
    http.get('/api/currencies', async () => {
      return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
    })
  )
  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  beforeAll(() => server.listen())

  it('renders', async function () {
    const ui = render(wrapInSuspense(SystemSettingsEdit), {
      global: {
        stubs: {
          PageHeader: true,
          BaseColorPicker: true,
          BaseCurrencySelect: true,
        },
        plugins: [createTestingPinia()],
      },
    })

    await flushPromises()

    const nameField = ui.getByRole('textbox', { name: 'u2.max_upload_size' })
    expect(nameField).toBeInTheDocument()

    const requireUpperCaseLetter = ui.getByLabelText(
      'u2.password_requires_at_least_one_uppercase_letter'
    ).parentElement
    expect(requireUpperCaseLetter?.role).toBe('switch')

    const requireLowerCaseLetter = ui.getByLabelText(
      'u2.password_requires_at_least_one_lowercase_letter'
    ).parentElement
    expect(requireLowerCaseLetter?.role).toBe('switch')

    const securityPasswordRulesRequireNumber = ui.getByLabelText(
      'u2.password_requires_at_least_one_number'
    ).parentElement
    expect(securityPasswordRulesRequireNumber?.role).toBe('switch')

    const securityPasswordRulesRequireNonAlphanumericCharacter = ui.getByLabelText(
      'u2.password_requires_at_least_one_non_alphanumeric_character'
    ).parentElement
    expect(securityPasswordRulesRequireNonAlphanumericCharacter?.role).toBe('switch')

    const securityTwoFactorIsEnforced = ui.getByLabelText('u2.security.two_factor').parentElement
    expect(securityTwoFactorIsEnforced?.role).toBe('switch')

    const taxAssessmentOpenBoy = ui.getByLabelText('u2_core.open_beginning_of_year')
    expect(taxAssessmentOpenBoy).toBeInTheDocument()

    const securityPasswordRulesMinLength = ui.getByRole('textbox', {
      name: 'u2.password_min_length',
    })
    expect(securityPasswordRulesMinLength).toBeInTheDocument()

    const securityPasswordRulesUniqueHistoryCount = ui.getByRole('textbox', {
      name: 'u2.password_history_length',
    })
    expect(securityPasswordRulesUniqueHistoryCount).toBeInTheDocument()

    const securityPasswordRulesMaxAgeInDays = ui.getByRole('textbox', {
      name: 'u2.password_max_age_in_days',
    })
    expect(securityPasswordRulesMaxAgeInDays).toBeInTheDocument()

    const securityPasswordRulesResetHoursValid = ui.getByRole('textbox', {
      name: 'u2.password_reset_hours_valid',
    })
    expect(securityPasswordRulesResetHoursValid).toBeInTheDocument()

    const securityMaxLoginAttempts = ui.getByRole('textbox', {
      name: 'u2_core.max_login_attempts',
    })
    expect(securityMaxLoginAttempts).toBeInTheDocument()

    const applicationCurrencySelect = ui.getByPlaceholderText('u2.select_currency')
    expect(applicationCurrencySelect).toBeInTheDocument()
  })
})
