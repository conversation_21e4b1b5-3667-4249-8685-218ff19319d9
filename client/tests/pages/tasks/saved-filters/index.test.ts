import { userApi<PERSON>asePath } from '@js/api/userApi'
import { createTesting<PERSON><PERSON> } from '@pinia/testing'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { findResourceById, setupServer, wrapInSuspense } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import { createUser } from '@tests/__factories__/createUser'
import HelpPanel from '@js/components/help/HelpPanel.vue'
import PageHeader from '@js/components/page-structure/PageHeader.vue'
import SavedFilterList from '@js/pages/tasks/saved-filters/index.vue'
import SavedFilterShareDialog from '@js/components/task/saved-filter/SavedFilterShareDialog.vue'
import type { SavedFilter } from '@js/model/saved-filter'

const user = createUser()

const savedFilter = {
  '@id': '/api/saved-filters/1',
  '@type': 'SavedFilter',
  id: 1,
  name: 'Test Filter',
  createdBy: user['@id'],
  description: 'filter description',
  taskShortName: 'apm-transaction',
  owner: user['@id'],
  favourite: false,
  public: true,
  uql: 'id = 1',
  updatedBy: user['@id'],
  canEdit: true,
  canDelete: true,
} satisfies SavedFilter

const server = setupServer(
  http.get(`${userApiBasePath}/:id/favourite-saved-filters`, async ({ params }) => {
    if (!findResourceById(params.id, [user])) throw new Error('User not found')

    return HttpResponse.json({ 'hydra:member': [savedFilter] }, { status: StatusCodes.OK })
  }),
  http.get('/api/saved-filters', async () => {
    return HttpResponse.json({ 'hydra:member': [savedFilter] }, { status: StatusCodes.OK })
  }),
  http.get(`${userApiBasePath}/:id`, ({ params }) =>
    HttpResponse.json(findResourceById(params.id, [user]), { status: StatusCodes.OK })
  ),
  http.get('/api/saved-filters/1/saved-filter-subscriptions', async () => {
    return HttpResponse.json({ 'hydra:member': [] }, { status: StatusCodes.OK })
  })
)

describe('Saved filter list page', () => {
  beforeAll(() => {
    server.listen()
    createTestingPinia({
      initialState: {
        auth: {
          user,
        },
      },
    })
  })

  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  it('renders', async function () {
    const page = await mount(wrapInSuspense(SavedFilterList), {})
    await flushPromises()

    expect(page.findComponent(PageHeader).exists()).toBe(true)
    expect(page.findComponent(HelpPanel).exists()).toBe(true)
  })

  it('shows and hides share dialog', async () => {
    const page = await mount(wrapInSuspense(SavedFilterList), {})
    await flushPromises()
    expect(page.findComponent(SavedFilterShareDialog).exists()).toBe(false)

    // When
    await page.findComponent('[data-test="share-button"]').trigger('click')

    // Then
    const dialog = page.findComponent(SavedFilterShareDialog)
    expect(dialog.exists()).toBe(true)

    await dialog.vm.$emit('close')
    expect(page.findComponent(SavedFilterShareDialog).exists()).toBe(false)
  })
})
