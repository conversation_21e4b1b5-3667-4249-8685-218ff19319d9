import useSearchRecords from '@js/composable/useSearchRecords'

describe('useSearchRecords', () => {
  test('returns all for an empty query', () => {
    const { filteredRecords } = useSearchRecords(
      [
        { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
        { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
      ],
      ['id', 'property', 'anotherProperty']
    )

    // empty query
    expect(filteredRecords.value).toEqual([
      { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
      { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
    ])
  })

  test('single token not present', () => {
    const { filteredRecords } = useSearchRecords(
      [
        { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
        { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
      ],
      ['id', 'property', 'anotherProperty'],
      'Z'
    )

    expect(filteredRecords.value).toEqual([])
  })

  test('single token', () => {
    const { filteredRecords } = useSearchRecords(
      [
        { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
        { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
      ],
      ['id', 'property', 'anotherProperty'],
      'A'
    )

    expect(filteredRecords.value).toEqual([
      { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
      { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
    ])
  })

  test('two tokens', () => {
    const { filteredRecords } = useSearchRecords(
      [
        { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
        { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
      ],
      ['id', 'property', 'anotherProperty'],
      'A D'
    )

    expect(filteredRecords.value).toEqual([
      { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
      { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
    ])
  })

  test('token and quoted token', () => {
    const { filteredRecords } = useSearchRecords(
      [
        { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
        { id: 2, property: 'D C', anotherProperty: 'B A', unknown: 'Z' },
      ],
      ['id', 'property', 'anotherProperty'],
      'A "C D"'
    )

    expect(filteredRecords.value).toEqual([
      { id: 1, property: 'A B', anotherProperty: 'C D', unknown: 'Z' },
    ])
  })

  test('two quoted tokens', () => {
    const { filteredRecords } = useSearchRecords(
      [
        { id: 1, property: 'A B', anotherProperty: 'C  D', unknown: 'Z' },
        { id: 2, property: 'C D', anotherProperty: 'B A', unknown: 'Z' },
      ],
      ['id', 'property', 'anotherProperty'],
      '"C D" "B A"'
    )

    expect(filteredRecords.value).toEqual([
      { id: 2, property: 'C D', anotherProperty: 'B A', unknown: 'Z' },
    ])
  })
})
