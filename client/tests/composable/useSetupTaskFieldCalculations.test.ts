import { beforeEach } from 'vitest'
import { createTestingPinia } from '@pinia/testing'
import useSetupTaskFieldCalculations from '@js/composable/useSetupTaskFieldCalculations'

describe('useSetupTaskFieldCalculations', () => {
  beforeEach(() => {
    createTestingPinia()
  })
  afterEach(() => {
    document.body.innerHTML = ''
  })

  it('set total revenue with unrelated party', function () {
    const totalRevenueElement = document.createElement('input')
    totalRevenueElement.setAttribute('data-js-calc-total-revenue', '')

    const totalRevenueWithRelatedPartyElement = document.createElement('input')
    totalRevenueWithRelatedPartyElement.setAttribute(
      'data-js-calc-total-revenue-with-related-party',
      ''
    )
    totalRevenueWithRelatedPartyElement.value = '5'

    const totalRevenueWithUnrelatedPartyElement = document.createElement('input')
    totalRevenueWithUnrelatedPartyElement.setAttribute(
      'data-js-calc-total-revenue-with-unrelated-party',
      ''
    )
    totalRevenueWithUnrelatedPartyElement.value = '5'

    document.body.append(totalRevenueElement)
    document.body.append(totalRevenueWithRelatedPartyElement)
    document.body.append(totalRevenueWithUnrelatedPartyElement)

    const setupTaskFieldCalculations = useSetupTaskFieldCalculations()
    setupTaskFieldCalculations()

    totalRevenueWithUnrelatedPartyElement.dispatchEvent(new Event('input'))

    expect(totalRevenueElement.value).toBe('10')
  })

  it('set total revenue with related party', function () {
    const totalRevenueElement = document.createElement('input')
    totalRevenueElement.setAttribute('data-js-calc-total-revenue', '')

    const totalRevenueWithRelatedPartyElement = document.createElement('input')
    totalRevenueWithRelatedPartyElement.setAttribute(
      'data-js-calc-total-revenue-with-related-party',
      ''
    )
    totalRevenueWithRelatedPartyElement.value = '5'

    const totalRevenueWithUnrelatedPartyElement = document.createElement('input')
    totalRevenueWithUnrelatedPartyElement.setAttribute(
      'data-js-calc-total-revenue-with-unrelated-party',
      ''
    )
    totalRevenueWithUnrelatedPartyElement.value = '5'

    document.body.append(totalRevenueElement)
    document.body.append(totalRevenueWithRelatedPartyElement)
    document.body.append(totalRevenueWithUnrelatedPartyElement)

    const setupTaskFieldCalculations = useSetupTaskFieldCalculations()
    setupTaskFieldCalculations()

    totalRevenueWithRelatedPartyElement.dispatchEvent(new Event('input'))

    expect(totalRevenueElement.value).toBe('10')
  })
})
