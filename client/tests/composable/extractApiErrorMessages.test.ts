import { extractApiErrorMessages } from '@js/composable/useForm'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'
import type { HydraErrorResponse } from '@js/types'

const axiosResponse = {
  status: 1,
  statusText: '',
  config: {} as AxiosRequestConfig,
  data: 'some random data',
} as AxiosResponse

describe('Errors Extractor', () => {
  it('returns an empty object for a standard axios response without violations', () => {
    expect(extractApiErrorMessages(axiosResponse)).toEqual({})
  })

  it('extracts the title as an error when the violations are empty', () => {
    const errorResponse = {
      ...axiosResponse,
      data: {
        'hydra:title': 'Api error title',
        'hydra:description': 'Api error description',
        violations: [],
      },
    } as HydraErrorResponse

    expect(extractApiErrorMessages(errorResponse)).toStrictEqual({
      '': ['Api error title'],
    })
  })

  it('extracts errors from responses with a title and a violation', () => {
    const errorResponse = {
      ...axiosResponse,
      data: {
        'hydra:title': 'Api error title',
        'hydra:description': 'Api error description',
        violations: [
          {
            message: 'Test message 1',
            propertyPath: 'testPropertyPath1',
          },
        ],
      },
    } as HydraErrorResponse

    expect(extractApiErrorMessages(errorResponse)).toStrictEqual({
      testPropertyPath1: ['Test message 1'],
    })
  })

  it('extracts multiple errors for multiple properties', () => {
    const errorResponse = {
      ...axiosResponse,
      data: {
        'hydra:title': 'Api error title',
        'hydra:description': 'Api error description',
        violations: [
          {
            message: 'Test message 1',
            propertyPath: 'testPropertyPath1',
          },
          {
            message: 'Test message 2',
            propertyPath: 'testPropertyPath2',
          },
          {
            message: 'Test message 3',
            propertyPath: 'testPropertyPath3',
          },
          {
            message: 'Test message 4',
            propertyPath: 'testPropertyPath1',
          },
          {
            message: 'Test message 5',
            propertyPath: 'testPropertyPath2',
          },
          {
            message: 'Test message 6',
            propertyPath: 'testPropertyPath3',
          },
          {
            message: 'Test message 7',
            propertyPath: '',
          },
          {
            message: 'Test message 8',
            propertyPath: '',
          },
        ],
      },
    } as HydraErrorResponse

    expect(extractApiErrorMessages(errorResponse)).toEqual({
      testPropertyPath1: ['Test message 1', 'Test message 4'],
      testPropertyPath2: ['Test message 2', 'Test message 5'],
      testPropertyPath3: ['Test message 3', 'Test message 6'],
      '': ['Test message 7', 'Test message 8'],
    })
  })

  it('extracts errors for nested properties', () => {
    const errorResponse = {
      ...axiosResponse,
      data: {
        'hydra:title': 'Api error title',
        'hydra:description': 'Api error description',
        violations: [
          {
            message: 'Test message 1',
            propertyPath: 'testPropertyPath1.line1',
          },
          {
            message: 'Test message 2',
            propertyPath: 'testPropertyPath1.line2',
          },
          {
            message: 'Test message 3',
            propertyPath: 'testPropertyPath1.line3',
          },
          {
            message: 'Test message 4',
            propertyPath: 'testPropertyPath2.line1',
          },
          {
            message: 'Test message 5',
            propertyPath: 'testPropertyPath2.line2',
          },
          {
            message: 'Test message 6',
            propertyPath: '',
          },
          {
            message: 'Test message 7',
            propertyPath: '',
          },
          {
            message: 'Test message 8',
            propertyPath: 'testPropertyPath1.line1',
          },
        ],
      },
    } as HydraErrorResponse

    expect(extractApiErrorMessages(errorResponse)).toEqual({
      'testPropertyPath1.line1': ['Test message 1', 'Test message 8'],
      'testPropertyPath1.line2': ['Test message 2'],
      'testPropertyPath1.line3': ['Test message 3'],
      'testPropertyPath2.line1': ['Test message 4'],
      'testPropertyPath2.line2': ['Test message 5'],
      '': ['Test message 6', 'Test message 7'],
    })
  })

  it('extracts errors for properties with nested array values', () => {
    const errorResponse = {
      ...axiosResponse,
      data: {
        'hydra:title': 'Api error title',
        'hydra:description': 'Api error description',
        violations: [
          {
            message: 'Test message 3',
            propertyPath: 'testPropertyPath1',
          },
          {
            message: 'Test message 1',
            propertyPath: 'testPropertyPath1[0].line1',
          },
          {
            message: 'Test message 4',
            propertyPath: 'testPropertyPath1',
          },
          {
            message: 'Test message 2',
            propertyPath: 'testPropertyPath1[0].line2',
          },
        ],
      },
    } as HydraErrorResponse

    expect(extractApiErrorMessages(errorResponse)).toEqual({
      'testPropertyPath1[0].line1': ['Test message 1'],
      'testPropertyPath1[0].line2': ['Test message 2'],
      testPropertyPath1: ['Test message 3', 'Test message 4'],
    })
  })
})
