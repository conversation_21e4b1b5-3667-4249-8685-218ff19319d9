import { defineComponent } from 'vue'
import flushPromises from 'flush-promises'
import { mount } from '@vue/test-utils'
import { HttpResponse, http } from 'msw'
import { setupServer } from '@tests/utils'
import { StatusCodes } from 'http-status-codes'
import useSystemSettingsAllQuery from '@js/composable/useSystemSettingsAllQuery'
import useSystemSettingsUpdateMutation from '@js/composable/useSystemSettingsUpdateMutation'
import type { SystemSettings } from '@js/model/system_setting'

describe('useSystemSettingsUpdateMutation', () => {
  const systemSettings = {
    '@id': '/api/system-settings',
    '@type': 'SystemSettings',
    securityPasswordRulesResetHoursValid: 5,
    u2Locale: 'string',
    loginColor: 'string',
    securityMaxLoginAttempts: 5,
    securityPasswordRulesUniqueHistoryCount: 5,
    securityPasswordRulesMinLength: 5,
    securityPasswordRulesMaxAgeInDays: 5,
    securityFileUploadWhitelist: [],
    securityUnitEditFieldWhitelist: [],
    securityTwoFactorIsEnforced: false,
    securityPasswordRulesRequireNumber: false,
    securityPasswordRulesRequireNonAlphanumericCharacter: false,
    securityPasswordRulesRequireUppercaseLetter: false,
    securityPasswordRulesRequireLowercaseLetter: false,
    taxAssessmentOpenBoy: false,
    maxUploadSize: 5,
    applicationCurrency: '/api/currencies/1',
  } satisfies SystemSettings

  const updatedSystemSettings = {
    ...systemSettings,
  } satisfies SystemSettings

  const server = setupServer(
    http.get('/api/system-settings', async () => {
      return HttpResponse.json(systemSettings, { status: StatusCodes.OK })
    }),
    http.patch('/api/system-settings', async () => {
      return HttpResponse.json(updatedSystemSettings, { status: StatusCodes.OK })
    })
  )

  function createTestComponent() {
    return defineComponent({
      setup() {
        const { data: systemSettings } = useSystemSettingsAllQuery()
        const { mutate } = useSystemSettingsUpdateMutation()
        return {
          mutate,
          systemSettings,
        }
      },
      template: '<div/>',
    })
  }
  afterEach(() => server.resetHandlers())

  afterAll(() => server.close())

  beforeAll(() => server.listen())

  it('updates system settings', async () => {
    // Given
    const component = mount(createTestComponent())
    await flushPromises()
    expect(component.vm.systemSettings).toEqual(systemSettings)

    // When
    component.vm.mutate({
      systemSettings: updatedSystemSettings,
    })
    await flushPromises()

    // Then
    expect(component.vm.systemSettings).toEqual(updatedSystemSettings)
  })
})
