import { faker } from '@faker-js/faker/locale/en'
import type { BasicUnit } from '@js/model/unit'

export function createBasicUnit(overrides: Partial<BasicUnit> = {}): BasicUnit {
  const id = overrides.id ?? faker.number.int()
  return {
    '@id': '/api/basic-units/' + id,
    id,
    '@type': 'Unit',
    country: faker.location.country(),
    currency: `/api/currencies/${faker.number.int()}`,
    description: faker.lorem.text(),
    refId: faker.lorem
      .word({
        length: {
          max: 3,
          min: 6,
        },
      })
      .toUpperCase(),
    name: faker.company.name(),
    ...overrides,
  }
}
