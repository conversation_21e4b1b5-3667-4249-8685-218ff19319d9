import { faker } from '@faker-js/faker/locale/en'
import { createUnit } from '@tests/__factories__/createUnit'
import { taskTypes } from '@js/model/task'
import type { TaskShortName } from '@js/model/task'
import type { SavedFilter } from '@js/model/saved-filter'

export function createSavedFilter(overrides: Partial<SavedFilter> = {}): SavedFilter {
  const id = overrides.id ?? faker.number.int()
  const canEdit = Math.random() < 0.5

  const taskShortNames = Object.values(taskTypes).map((taskType) =>
    taskType.replace(/_/g, '-')
  ) as Array<TaskShortName>

  const taskShortName = taskShortNames[Math.floor(Math.random() * taskShortNames.length)]

  return {
    ...createUnit(),
    '@id': '/api/saved-filters/' + id,
    '@type': 'SavedFilter',
    id: id,
    name: faker.lorem.word(),
    public: Math.random() < 0.5,
    favourite: Math.random() < 0.5,
    description: faker.lorem.words(),
    uql: faker.lorem.word(),
    // get a random taskShortName from TaskShortName const
    taskShortName: taskShortName,
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.past().toISOString(),
    // 50% chance to be true
    canEdit: canEdit,
    canDelete: canEdit,
    owner: '/api/users/1',
    updatedBy: '/api/users/1',
    createdBy: '/api/users/1',
    ...overrides,
  }
}
