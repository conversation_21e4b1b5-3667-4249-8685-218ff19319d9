import { faker } from '@faker-js/faker/locale/en'
import { createItem } from '@tests/__factories__/createItem'
import { itemCountryReportApiBasePath } from '@js/api/itemCountryReportApi'
import type { ItemCountryReport } from '@js/api/itemCountryReportApi'

export const createItemCountryReport = (
  overrides: Partial<ItemCountryReport> = {}
): ItemCountryReport => {
  const id = overrides.id ?? faker.string.uuid()
  return {
    '@id': itemCountryReportApiBasePath + '/' + id,
    '@type': 'ItemCountryReport',
    id,
    name: 'Item Country Report' + id,
    items: [createItem()['@id']],
    createdBy: '/api/users/1',
    updatedBy: '/api/users/1',
    createdAt: '2020-09-28T11:14:10.590Z',
    updatedAt: '2020-09-28T11:14:10.590Z',
    ...overrides,
  }
}
