import { faker } from '@faker-js/faker/locale/en'
import type { Comment } from '@js/model/comment'

export function createComment(overrides: Partial<Comment> = {}): Comment {
  const id = overrides.id ?? faker.number.int()
  return {
    '@id': '/api/comments/' + id,
    '@type': 'Comment',
    id,
    content: faker.lorem.sentence(),
    quote: '/api/comments/' + faker.number.int(),
    task: '/api/tasks/' + faker.string.uuid(),
    author: '/api/users/' + faker.number.int(),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.past().toISOString(),
    group: faker.datatype.boolean() ? `/api/user-groups/${faker.number.int()}` : null,
    deleted: faker.datatype.boolean(),
    'u2:extra': {
      canWrite: faker.datatype.boolean(),
      canDelete: faker.datatype.boolean(),
    },
    ...overrides,
  }
}
