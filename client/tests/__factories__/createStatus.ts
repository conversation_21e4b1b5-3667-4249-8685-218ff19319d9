import { faker } from '@faker-js/faker/locale/en'
import type { Status } from '@js/model/status'

export const createStatus = (overrides: Partial<Status> = {}): Status => {
  const id = overrides.id ?? faker.number.int()
  const type: Status['type'] = faker.helpers.arrayElement(['OPEN', 'IN_PROGRESS', 'COMPLETE'])
  return {
    '@id': '/api/statuses/' + id,
    '@type': 'Status',
    id,
    type,
    name: type.charAt(0).toUpperCase() + type.slice(1),
    ...overrides,
  }
}
