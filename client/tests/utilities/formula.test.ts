import { createItem } from '@tests/__factories__/createItem'
import { splitFormulaIntoElements } from '@js/helper/datasheets/formula'

describe('splitFormulaIntoElements', () => {
  it('splits with elements inside', () => {
    const formulaElement1 = createItem()
    const formulaElement2 = createItem()
    const formulaElement3 = createItem()
    const formulaElement4 = createItem()
    const formulaElement5 = createItem()

    const item = createItem({
      formulaReadable: `{${formulaElement1.refId}}+{P${formulaElement2.refId}}+{${formulaElement3.refId}}+{${formulaElement4.refId}}+{P${formulaElement5.refId}}`,
      formula: `{${formulaElement1.id}}+{P${formulaElement2.id}}+{${formulaElement3.id}}+{${formulaElement4.id}}+{P${formulaElement5.id}}`,
    })

    const formulaElements = splitFormulaIntoElements(item.formula ?? '')

    expect(formulaElements).toEqual([
      `{${formulaElement1.id}}`,
      '+',
      `{P${formulaElement2.id}}`,
      '+',
      `{${formulaElement3.id}}`,
      `+`,
      `{${formulaElement4.id}}`,
      '+',
      `{P${formulaElement5.id}}`,
    ])
  })

  it('splits without elements inside', () => {
    const item = createItem({
      formulaReadable: `0.1+0.2`,
      formula: `0.1+0.2`,
    })

    const formulaElements = splitFormulaIntoElements(item.formula ?? '')

    expect(formulaElements).toEqual(['0.1+0.2'])
  })
})
