import { cleanupWhitespaceInHtmlString } from '@tests/utils'
import { fromPartial } from '@total-typescript/shoehorn'
import buildDocumentWidgetFilePlaceholder from '@js/helper/document/buildDocumentWidgetFilePlaceholder'
import type { Attachment, RestrictedAttachment } from '@js/model/attachment'

vi.mock('@assets/icons/edit.svg?raw', () => ({
  default: 'editButton',
}))

vi.mock('@js/helper/document/encodeDocumentWidget', async () => {
  return {
    encodeDocumentWidget: vi.fn(() => 'encoded string'),
  }
})

describe('buildDocumentWidgetFilePlaceholder', () => {
  it('returns an html string for file widget placeholder', async () => {
    const actual = await buildDocumentWidgetFilePlaceholder(
      {
        isConfigurable: true,
        name: 'file',
        parameters: {
          id: '10',
          text: '',
        },
      },
      [
        fromPartial<Attachment>({
          '@id': '/api/master-file-sections/1/attachments/6',
          '@type': 'Attachment',
          userPermissions: [],
          groupPermissions: [],
          accessType: 'public',
          file: '/api/files/10',
          name: 'Rechnung.pdf',
          links: {
            downloadPath: '/api/master-file-sections/1/attachments/10/download',
          },
        }),
      ]
    )

    const expected = `
    <span class="mceNonEditable" data-inline-document-widget>
      <a href="/api/master-file-sections/1/attachments/10/download" target="_blank">Rechnung.pdf</a>
      <span class="mceNonEditable" data-document-widget-settings>encoded string</span>
      <span class="mceNonEditable" style="height: 1rem; width: 1rem;" data-document-widget-edit>editButton</span>
    </span>`

    expect(cleanupWhitespaceInHtmlString(actual)).toEqual(cleanupWhitespaceInHtmlString(expected))
  })

  it('returns an html string for file widget placeholder with text', async () => {
    const actual = await buildDocumentWidgetFilePlaceholder(
      {
        isConfigurable: true,
        name: 'file',
        parameters: {
          id: '10',
          text: 'Invoice',
        },
      },
      [
        fromPartial<Attachment>({
          '@id': '/api/master-file-sections/1/attachments/6',
          '@type': 'Attachment',
          userPermissions: [],
          groupPermissions: [],
          accessType: 'public',
          file: '/api/files/10',
          name: 'Rechnung.pdf',
          links: {
            downloadPath: '/api/master-file-sections/1/attachments/10/download',
          },
        }),
      ]
    )

    const expected = `
    <span class="mceNonEditable" data-inline-document-widget>
      <a href="/api/master-file-sections/1/attachments/10/download" target="_blank">Invoice</a>
      <span class="mceNonEditable" data-document-widget-settings>encoded string</span>
      <span class="mceNonEditable" style="height: 1rem; width: 1rem;" data-document-widget-edit>editButton</span>
    </span>`

    expect(cleanupWhitespaceInHtmlString(actual)).toEqual(cleanupWhitespaceInHtmlString(expected))
  })

  it('returns an html string for file widget placeholder with restricted file name', async () => {
    const actual = await buildDocumentWidgetFilePlaceholder(
      {
        isConfigurable: true,
        name: 'file',
        parameters: {
          id: '20',
          text: '',
        },
      },
      [
        fromPartial<RestrictedAttachment>({
          '@id': '/api/master-file-sections/1/attachments/6',
          '@type': 'Attachment',
          userPermissions: [],
          groupPermissions: [],
          accessType: 'public',
          file: '/api/files/20',
          name: null,
          links: {
            downloadPath: null,
          },
        }),
      ]
    )

    const expected = `
    <span class="mceNonEditable" data-inline-document-widget>
      u2_core.the_name_of_this_file_is_restricted
      <span class="mceNonEditable" data-document-widget-settings>encoded string</span>
      <span class="mceNonEditable" style="height: 1rem; width: 1rem;" data-document-widget-edit>editButton</span>
    </span>`

    expect(cleanupWhitespaceInHtmlString(actual)).toEqual(cleanupWhitespaceInHtmlString(expected))
  })

  it('returns an html string for widget placeholder of a file that is no longer attached to the section', async () => {
    const actual = await buildDocumentWidgetFilePlaceholder(
      {
        isConfigurable: true,
        name: 'file',
        parameters: {
          id: '10',
          text: 'Invoice',
        },
      },
      []
    )
    const expected = `
    <span class="mceNonEditable" data-inline-document-widget>
      u2_tpm.widget.file.not_attached
      <span class="mceNonEditable" data-document-widget-settings>encoded string</span>
      <span class="mceNonEditable" style="height: 1rem; width: 1rem;" data-document-widget-edit>editButton</span>
    </span>`
    expect(cleanupWhitespaceInHtmlString(actual)).toEqual(cleanupWhitespaceInHtmlString(expected))
  })
})
