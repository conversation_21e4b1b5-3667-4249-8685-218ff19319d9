## Troubleshooting

### Resolving "connect ECONNREFUSED 127.0.0.1:3000" on Continuous Integration (CI)

This error usually indicates that a network request attempted during testing does not have a corresponding handler in Mock Service Worker (MSW). To diagnose and fix this issue, follow these steps:

enable debug; Mode: Turn on the isDebugEnabled flag in the utils.ts file. This action configures MSW to log unmatched network requests to the console, detailing the request path and method.
By identifying unhandled requests, you can ensure that every network interaction during tests is accounted for, reducing the likelihood of encountering the ECONNREFUSED error.

### Ensuring Tests Fail on Post-Completion Console Messages

To catch and fail tests that produce console messages after completion, you can adjust the setup of your test environment as; follows:

modify the `failOnConsole` configuration in [vitest.setup.ts](vitest.setup.ts): set the `aftereachdelay` option to a value greater than 0. this introduces a delay after each test, during which any console messages
triggered will cause the test to fail. implementing this strategy helps in identifying tests that may have asynchronous operations not properly awaited or cleaned up,
ensuring a more robust and reliable test suite.
