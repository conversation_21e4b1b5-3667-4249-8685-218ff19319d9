{{ if .Values.storybook.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: storybook
  labels:
    app.kubernetes.io/name: {{ include "u2.name" . }}-storybook
    helm.sh/chart: {{ include "u2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: web
  selector:
    app.kubernetes.io/name: {{ include "u2.name" . }}-storybook
    app.kubernetes.io/instance: {{ .Release.Name }}
  type: ClusterIP
  sessionAffinity: None
{{ end }}
