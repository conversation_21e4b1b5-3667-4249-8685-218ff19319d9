{{- define "u2.env_vars" -}}
env:
- name: APP_SECRET
  valueFrom:
    secretKeyRef:
      name: app-secret
      key: secret
- name: ENVIRONMENT
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: environment
- name: APP_HOSTNAME
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: tenant_base_hostname
{{- if .Values.appData }}
- name: DATA_STORE
  value: s3
- name: AWS_REGION
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.region
- name: AWS_S3_BUCKET
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.name
- name: AWS_S3_BUCKET_PREFIX
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: s3_bucket.prefix
{{- else }}
- name: DATA_STORE
  value: local
{{- end }}
- name: DATABASE_URL
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: database.url
- name: DATABASE_VERSION
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: database.version
- name: REDIS_URL
  valueFrom:
    configMapKeyRef:
      name: app-config
      key: redis.url
- name: TENANTS_CONFIG
  valueFrom:
    secretKeyRef:
      name: {{ .Values.multiTenancyConfigSecret.name }}
      key: {{ .Values.multiTenancyConfigSecret.tenantsMapDataKey }}
- name: MAILER_URL
  valueFrom:
    secretKeyRef:
      name: mailer-config
      key: url
- name: MAILER_FROM
  valueFrom:
    secretKeyRef:
      name: mailer-config
      key: from
- name: JWT_SECRET_KEY
  valueFrom:
    secretKeyRef:
      name: jwt-config
      key: secret-key
- name: JWT_PUBLIC_KEY
  valueFrom:
    secretKeyRef:
      name: jwt-config
      key: public-key
- name: JWT_PASSPHRASE
  valueFrom:
    secretKeyRef:
      name: jwt-config
      key: passphrase
{{- end -}}
