apiVersion: apps/v1
kind: Deployment
metadata:
  name: consumer
  labels:
    app.kubernetes.io/name: {{ include "u2.name" . }}-consumer
    helm.sh/chart: {{ include "u2.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  replicas: {{ .Values.consumer.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "u2.name" . }}-consumer
      app.kubernetes.io/instance: {{ .Release.Name }}
  strategy:
    rollingUpdate:
      # The value of 100% might not be optimal if the default number of replicas is high. See UU-5025
      maxSurge: 100%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "u2.name" . }}-consumer
        app.kubernetes.io/instance: {{ .Release.Name }}
      {{- if not .Values.appData }}
      annotations:
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      {{- end }}
    spec:
      initContainers:
      {{- if .Values.redis.enabled }}
      - name: wait-for-redis
        image: redis:6.2
        imagePullPolicy: IfNotPresent
        command:
        - bash
        - -c
        - |
          echo "Trying to connect to redis at $REDIS_HOST:$REDIS_PORT"
          i=1
          while ! timeout 1 redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" PING; do
            if [ $(( i++ )) -ge 30 ]; then
              echo "Could not connect to redis"
              exit 1
            fi
            echo "Waiting for redis..."
            sleep 1
          done
          echo "Done waiting for redis"
        env:
        - name: REDIS_HOST
          value: "u2-redis-master"
        - name: REDIS_PORT
          value: "6379"
      {{- end }}
      {{- if .Values.mysql.enabled }}
      - name: wait-for-mysql
        image: mysql:8.0.36
        imagePullPolicy: IfNotPresent
        command:
        - bash
        - -c
        - |
          echo "Trying to connect to mysql at $MYSQL_HOST:$MYSQL_PORT/$MYSQL_DB"
          i=1
          while ! timeout 1 mysql --host="$MYSQL_HOST" --port="$MYSQL_PORT" --user="$MYSQL_USER" --password="$MYSQL_PASS" --execute "select 1" "$MYSQL_DB"; do
            if [ $(( i++ )) -ge 30 ]; then
              echo "Could not connect to mysql"
              exit 1
            fi
            echo "Waiting for mysql..."
            sleep 1
          done
          echo "Done waiting for mysql"
        env:
        - name: MYSQL_HOST
          value: "u2-mysql"
        - name: MYSQL_PORT
          value: "3306"
        - name: MYSQL_USER
          value: {{ .Values.mysql.auth.username | quote }}
        - name: MYSQL_PASS
          value: {{ .Values.mysql.auth.password | quote }}
        - name: MYSQL_DB
          value: {{ .Values.mysql.auth.database | quote }}
      {{- end }}
      terminationGracePeriodSeconds: 300 # Consuming might be slow, allow for 5 minutes graceful shutdown
      containers:
      - name: consumer
        image: "{{ .Values.consumer.image.repository }}:{{ .Values.consumer.image.tag | default .Values.defaultU2ImageTag }}"
        imagePullPolicy: {{ .Values.consumer.image.pullPolicy }}
        lifecycle: # ← this let’s shut down gracefully
          preStop:
            exec:
              command: ["sh", "/pre_stop.sh"]
        resources:
          {{- toYaml .Values.consumer.resources | nindent 10 }}
        {{- include "u2.env_vars" . | nindent 8 }}
        command: ['sh', '-c', 'supervisord -c /etc/supervisord.conf']
        {{- if not .Values.appData }}
        volumeMounts:
        - name: app-data-storage
          mountPath: /app/api/var/data
        {{- end }}
      {{- if not .Values.appData }}
      volumes:
      - name: app-data-storage
      {{- end }}
      {{- if .Values.imageCredentials }}
      imagePullSecrets:
      - name: u2-docker-registry-credentials
      {{- end }}
