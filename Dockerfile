# syntax=docker/dockerfile:1.4

# Versions
FROM nginx:stable-alpine AS nginx_upstream
FROM node:22-alpine AS node_upstream
FROM php:8.4-fpm-alpine3.21 AS php_upstream
FROM composer:latest AS composer_upstream

###################################################################################################
#
# Node Image
#
###################################################################################################
FROM node_upstream AS u2_node_base

EXPOSE 8080

WORKDIR /app/client

RUN corepack enable && \
    corepack prepare --activate pnpm@latest && \
    pnpm config -g set store-dir /.pnpm-store

FROM u2_node_base AS u2_node

CMD ["sh", "-c", "pnpm install; pnpm dev-server"]

COPY --link client/pnpm-lock.yaml ./
COPY --link client/patches ./patches
ENV PATH=/app/client/node_modules/.bin:$PATH
RUN pnpm fetch


COPY --link client .
COPY --link api/templates /app/api/templates
COPY --link api/shared /app/api/shared

RUN mkdir -p var/cache/

ARG VERSION
ENV VERSION=${VERSION:-dev}

RUN	pnpm install --frozen-lockfile --offline && \
    pnpm run build

###################################################################################################
#
# Development PHP Image
#
###################################################################################################
FROM php_upstream AS u2_php_dev

ARG APP_ENV=dev
ENV ENVIRONMENT=development
ENV PATH="/app/api/bin:${PATH}"

RUN \
    # persistent / runtime deps
    apk add --no-cache \
        acl \
        freetype \
        git \
        icu-libs \
        icu-data-full \
        jq \
        libjpeg-turbo \
        libpng \
        libzip-dev \
        make \
        patch \
        supervisor \
        zlib \
    ; \
    # Build dependencies
    set -eux; \
    apk add --no-cache --virtual .build-deps \
        $PHPIZE_DEPS \
        freetype-dev \
        icu-dev \
        libjpeg-turbo-dev \
        libpng-dev \
        libxml2-dev \
        # TODO: remove `linux-headers` when fixed - update linux headers to fix an error "error: rtnetlink.h is required"
        linux-headers \
        sed \
        zlib-dev \
    ; \
    docker-php-ext-configure \
      gd --with-freetype --with-jpeg \
    ; \
    docker-php-ext-configure \
      pcntl --enable-pcntl \
    ; \
    docker-php-ext-install -j$(nproc) \
        bcmath \
        gd \
        intl \
        pcntl \
        pdo_mysql \
    ; \
    pecl install \
        apcu \
        excimer \
        redis \
        xdebug \
        zip \
    ; \
    pecl clear-cache; \
    docker-php-ext-enable \
        apcu \
        excimer \
        opcache \
        redis \
        xdebug \
        zip \
    ; \
    \
    runDeps="$( \
        scanelf --needed --nobanner --format '%n#p' --recursive /usr/local/lib/php/extensions \
            | tr ',' '\n' \
            | sort -u \
            | awk 'system("[ -e /usr/local/lib/" $1 " ]") == 0 { next } { print "so:" $1 }' \
    )"; \
    apk add --no-cache --virtual .api-phpexts-rundeps $runDeps; \
    # Install SPX
    git clone https://github.com/NoiseByNorthwest/php-spx.git \
        && cd php-spx \
        && git checkout release/latest \
        && phpize \
        && ./configure \
        && make \
        && make install \
        && cd .. \
        && rm -rf php-spx; \
    # Remove build dependencies
    apk del .build-deps

# Copy custom PHP ini files
COPY --link docker/php-fpm/php-custom.dev.ini $PHP_INI_DIR/conf.d/php-custom.ini
COPY --link docker/php-fpm/xdebug.ini $PHP_INI_DIR/conf.d/docker-php-ext-xdebug.ini
COPY --link docker/php-fpm/spx.ini $PHP_INI_DIR/conf.d/docker-php-ext-spx.ini

# Copy FPM config
COPY --link docker/php-fpm/zz-docker.conf /usr/local/etc/php-fpm.d/zz-docker.conf

# Install composer
COPY --link --from=composer_upstream /usr/bin/composer /usr/bin/composer
# https://getcomposer.org/doc/03-cli.md#composer-allow-superuser
ENV COMPOSER_ALLOW_SUPERUSER 1
ENV PATH="${PATH}:/root/.composer/vendor/bin"

WORKDIR /app/api

# prevent the reinstallation of vendors at every changes in the source code
COPY --link ./api/composer.json ./api/composer.lock ./
COPY --link ./api/patches ./patches

RUN set -eux; \
    composer install --prefer-dist --no-autoloader --no-scripts --no-progress

COPY --link ./api ./
# The icons are used in twig
COPY --link ./client/assets/icons ../client/assets/icons
# The src is needed for translation exctraction
COPY --link ./client/src ../client/src

RUN set -eux; \
    mkdir -p ./var/data; \
    composer dump-autoload --optimize; \
    composer run-script post-install-cmd; \
    chmod +x ./bin/console

COPY --link docker/php-fpm/entrypoint.sh /usr/local/bin/docker-u2-entrypoint
RUN chmod +x /usr/local/bin/docker-u2-entrypoint

# Supervisord configuration for the consumers
COPY --link docker/php-consumer/supervisord_consumer.conf /etc/supervisord.conf
COPY --link docker/php-consumer/supervisord_message-consumer.sh /supervisord_message-consumer.sh
COPY --link docker/php-consumer/pre_stop.sh /pre_stop.sh

ARG VERSION
ENV VERSION=${VERSION:-dev}

ENTRYPOINT ["docker-u2-entrypoint"]
CMD ["php-fpm"]

###################################################################################################
#
# Nginx Image
#
###################################################################################################
FROM nginx_upstream AS u2_nginx

RUN apk add --no-cache openssl

COPY --link docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY --link docker/nginx/conf.d /etc/nginx/conf.d
RUN mkdir /certs; \
    openssl req -newkey rsa:2048 -nodes -x509 -days 365 -subj '/CN=localhost' -keyout /certs/domain.key -out /certs/domain.crt

COPY --link --from=u2_php_dev /app/api/public /app/api/public
COPY --link --from=u2_node /app/client/public/build /app/client

###################################################################################################
#
# Production PHP Image
#
###################################################################################################
FROM u2_php_dev AS u2_php

ENV ENVIRONMENT=production
ENV APP_ENV=prod

## Remove cache and logs
RUN rm -rf var/cache/* var/logs/*

COPY --link docker/php-fpm/php-custom.prod.ini $PHP_INI_DIR/conf.d/php-custom.ini

## Remove dev dependencies
RUN composer install --no-dev --no-interaction --optimize-autoloader --classmap-authoritative \
    ## Cleanup
    && rm  "$PHP_INI_DIR/conf.d/docker-php-ext-xdebug.ini" \
    && rm  "$PHP_INI_DIR/conf.d/docker-php-ext-spx.ini" \
    && rm /usr/bin/composer \
    && rm -rf ../client/src
