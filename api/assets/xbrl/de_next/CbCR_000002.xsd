<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.itzbund.de/ELAN/CbCR/01" xmlns:cbc="urn:oecd:ties:cbc:v2" xmlns:stm="urn:oecd:ties:cbcstm:v2" targetNamespace="http://www.itzbund.de/ELAN/CbCR/01" elementFormDefault="qualified">
	<xs:import namespace="urn:oecd:ties:cbc:v2" schemaLocation="CbcXML_v2.0.xsd"/>
	<xs:import namespace="urn:oecd:ties:cbcstm:v2" schemaLocation="CbcStatusMessageXML_v2.0.xsd"/>
	<xs:annotation>
		<xs:documentation>
			-	Stand 04/2020 Update Nutzdaten-Version 2.0
		</xs:documentation>
	</xs:annotation>
	<xs:element name="CbCR">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="CbCR_Header">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="AuthSteuernummerMNU">
								<xs:annotation>
									<xs:documentation>Steuernummer des MNU</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="collapse"/>
										<xs:maxLength value="12"/>
										<xs:pattern value="([0-9]{12})"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="NameMNU">
								<xs:annotation>
									<xs:documentation>Name des MNU</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="collapse"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="NameELANNutzer">
								<xs:annotation>
									<xs:documentation>Name des ELAN-Nutzer</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:whiteSpace value="collapse"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:choice>
					<xs:element ref="cbc:CBC_OECD"/>
					<xs:element ref="stm:CbCStatusMessage_OECD"/>
				</xs:choice>
			</xs:sequence>
			<xs:attribute name="CbCRVersion" type="xs:string">
				<xs:annotation>
					<xs:documentation>Version der CbCR Definition</xs:documentation>
				</xs:annotation>
			</xs:attribute>
		</xs:complexType>
	</xs:element>
</xs:schema>
