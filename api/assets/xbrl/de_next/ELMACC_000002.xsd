<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.itzbund.de/ELAN/01" targetNamespace="http://www.itzbund.de/ELAN/01" elementFormDefault="qualified" version="1.0">
	<xs:annotation>
		<xs:documentation xml:lang="de">ELAN - ITZBund 2020 - Stand 04/2020		
		--> 2020-04-17 Nutzdaten-Update und elementFormDefault im ELMA-Umschlag		
		--> 2020-01-30 Verarbeitungslauf im ELMAHeader als Pflichtfeld deklariert 
			und ELMA_Standard_Elemente_000002.xsd eingebunden
		</xs:documentation>
	</xs:annotation>
	<xs:include schemaLocation="./ELMAKOM_CC_000002.xsd"/>
	<xs:include schemaLocation="./ELMA_Protokoll_000001.xsd"/>
	<xs:element name="ELMACC">
		<xs:annotation>
			<xs:documentation>Root Element ELMACC</xs:documentation>
		</xs:annotation>
		<xs:complexType>
			<xs:choice>
				<xs:element ref="ELMAKOM"/>
				<xs:element ref="ELMAProtokoll"/>
			</xs:choice>
		</xs:complexType>
	</xs:element>
</xs:schema>
