<?php

declare(strict_types=1);
namespace Tests\U2;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Tester\ApplicationTester;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use U2\Entity\User;
use U2\MultiTenancy\ApplicationConfigurator;
use U2\MultiTenancy\MultiTenantApplication;
use U2\MultiTenancy\TenantRepository;

use function Zenstruck\Foundry\Persistence\unproxy;

trait ServiceContainerHelpersTrait
{
    /**
     * A helper to get a service from the container by the given class name.
     *
     * This will only work with services that have the class name as the service id.
     * If you need to get a service with a string id, use the getContainer()->get() method directly.
     *
     * @template T of object
     *
     * @param class-string<T> $className
     *
     * @return T
     */
    protected static function getService(string $className): object
    {
        /** @var T|null $service */
        $service = static::getContainer()->get($className);

        return $service ?? throw new \RuntimeException("Service $className not found");
    }

    protected static function getEntityManager(): EntityManagerInterface
    {
        return static::getService(EntityManagerInterface::class);
    }

    protected static function setTenant(string $tenantName): void
    {
        static::getService(ApplicationConfigurator::class)
            ->configure(
                static::getService(TenantRepository::class)->getByName($tenantName)
            );
    }

    protected static function setCurrentUser(User $user): void
    {
        $tokenStorageInterface = self::getService(TokenStorageInterface::class);
        $tokenStorageInterface->setToken(new UsernamePasswordToken(unproxy($user), 'main', $user->getRoles()));
    }

    /**
     * @param array<string, mixed> $params
     */
    protected function runCommand(string $name, array $params = []): ApplicationTester
    {
        $application = new MultiTenantApplication(static::getContainer()->get('kernel'));
        $application->setAutoExit(false);

        $appTester = new ApplicationTester($application);

        $appTester->run(
            [
                'command' => $name,
                ...$params,
            ],
            [
                'tenant' => 'test',
                'interactive' => false,
                'verbosity' => OutputInterface::VERBOSITY_NORMAL,
            ]
        );

        return $appTester;
    }
}
