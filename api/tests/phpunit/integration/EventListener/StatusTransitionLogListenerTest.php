<?php

declare(strict_types=1);
namespace Tests\Integration\U2\EventListener;

use Hautelook\AliceBundle\PhpUnit\RefreshDatabaseTrait;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use U2\DataFixtures\Example\OtherDeadlineFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Security\CurrentUserManipulator;
use U2\Workflow\StatusTypes;
use Zenstruck\Foundry\Test\Factories;

class StatusTransitionLogListenerTest extends KernelTestCase
{
    use Factories;
    use RefreshDatabaseTrait;

    public function test_it_creates_a_transition_log(): void
    {
        // Given
        $user = UserFactory::createOne(['username' => 'status user']);
        self::getContainer()->get(CurrentUserManipulator::class)->change($user->_real());
        $statusOne = StatusFactory::createOne(['name' => 'first status', 'type' => StatusTypes::TYPE_OPEN]);
        $statusTwo = StatusFactory::createOne(['name' => 'second status', 'type' => StatusTypes::TYPE_COMPLETE]);

        // When
        $taskType = OtherDeadlineFactory::createOne(['status' => $statusOne]);

        // Then
        self::assertCount(0, $taskType->getTask()->getStatusTransitionLogs(), 'The first status should not be logged');

        // When
        $taskType->getTask()->setStatus($statusTwo->_real());
        $taskType->_save();
        $logs = $taskType->getTask()->getStatusTransitionLogs();

        // Then
        self::assertCount(1, $logs);
        $logEntry = $logs[0];
        self::assertNotNull($logEntry);
        self::assertSame('first status', $logEntry->getOriginStatusName());
        self::assertSame($statusOne->getType(), $logEntry->getOriginStatusType());
        self::assertSame('second status', $logEntry->getDestinationStatusName());
        self::assertSame($statusTwo->getType(), $logEntry->getDestinationStatusType());
        self::assertSame('status user', $logEntry->getUsername());
        self::assertEquals($taskType->getTask(), $logEntry->getTask());
    }
}
