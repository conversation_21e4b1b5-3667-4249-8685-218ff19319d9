<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Serializer;

use S<PERSON>fony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\KernelTestCase;
use U2\Entity\Dashboard;
use U2\Serializer\DashboardNormalizer;
use U2\Widget\Dashboard\HtmlWidgetContent;
use U2\Widget\Dashboard\HtmlWidgetStorage;

class DashboardNormalizerTest extends KernelTestCase
{
    public function test_normalize_non_html_widget(): void
    {
        $normalizer = self::getContainer()->get(NormalizerInterface::class);

        $dashboardNormalizer = new DashboardNormalizer(
            $this->createMock(HtmlWidgetStorage::class),
            $this->createMock(TranslatorInterface::class)
        );
        $dashboardNormalizer->setNormalizer($normalizer);

        $dashboardNormalizer->normalize(new Dashboard('Hi There', false, [['name' => 'up-next-widget']]));
    }

    public function test_normalize_default_html_widget_with_just_a_name(): void
    {
        $normalizer = self::getContainer()->get(NormalizerInterface::class);

        $translator = $this->createMock(TranslatorInterface::class);
        $translator
            ->method('trans')
            ->with('u2_core.welcome_to_u2')
            ->willReturn('Welcome to U2');

        $dashboardNormalizer = new DashboardNormalizer(
            $this->createMock(HtmlWidgetStorage::class),
            $translator
        );
        $dashboardNormalizer->setNormalizer($normalizer);

        // Keep same format, it's easier to check. We are only interested how the widgets have updated
        /** @var array{widgets: array{name: 'html', parameters: array{id: string, content: string, titel: string}}} $normalizedDashboard */
        $normalizedDashboard = $dashboardNormalizer->normalize(new Dashboard('Hi There', false, [['name' => 'html']]));
        self::assertEquals(
            [['name' => 'html', 'parameters' => ['id' => 'welcome', 'content' => '', 'title' => 'Welcome to U2']]],
            $normalizedDashboard['widgets']
        );
    }

    public function test_normalize_html_widget_with_custom_title(): void
    {
        $normalizer = self::getContainer()->get(NormalizerInterface::class);

        $dashboardNormalizer = new DashboardNormalizer(
            $this->createMock(HtmlWidgetStorage::class),
            $this->createMock(TranslatorInterface::class)
        );
        $dashboardNormalizer->setNormalizer($normalizer);

        // Keep same format, it's easier to check. We are only interested how the widgets have updated
        /** @var array{widgets: array{name: 'html', parameters: array{id: string, content: string, titel: string}}} $normalizedDashboard */
        $normalizedDashboard = $dashboardNormalizer->normalize(
            new Dashboard(
                'Hi There',
                false,
                [
                    [
                        'name' => 'html',
                        'title' => 'Welcome to U3',
                        'parameters' => [],
                    ],
                ])
        );

        self::assertEquals(
            [[
                'name' => 'html',
                'title' => 'Welcome to U3',
                'parameters' => [
                    'id' => 'welcome',
                    'content' => '',
                    'title' => 'Welcome to U3',
                ],
            ],
            ],
            $normalizedDashboard['widgets']
        );
    }

    public function test_normalize_html_widget_with_custom_content(): void
    {
        $normalizer = self::getContainer()->get(NormalizerInterface::class);

        $htmlWidgetStorage = $this->createMock(HtmlWidgetStorage::class);
        $htmlWidgetStorage
            ->method('get')
            ->with('custom')
            ->willReturn(new HtmlWidgetContent('custom', 'Hello World'));

        $dashboardNormalizer = new DashboardNormalizer(
            $htmlWidgetStorage,
            $this->createMock(TranslatorInterface::class)
        );
        $dashboardNormalizer->setNormalizer($normalizer);

        // Keep same format, it's easier to check. We are only interested how the widgets have updated
        /** @var array{widgets: array{name: 'html', parameters: array{id: string, content: string, titel: string}}} $normalizedDashboard */
        $normalizedDashboard = $dashboardNormalizer->normalize(
            new Dashboard(
                'Hi There',
                false,
                [
                    [
                        'name' => 'html',
                        'title' => 'Welcome to U4',
                        'parameters' => [
                            'id' => 'custom',
                        ],
                    ],
                ])
        );

        self::assertEquals(
            [[
                'name' => 'html',
                'title' => 'Welcome to U4',
                'parameters' => [
                    'id' => 'custom',
                    'content' => 'Hello World',
                    'title' => 'Welcome to U4',
                ],
            ],
            ],
            $normalizedDashboard['widgets']
        );
    }
}
