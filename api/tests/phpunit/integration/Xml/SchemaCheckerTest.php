<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Xml;

use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Tests\U2\KernelTestCase;
use U2\Validator\XmlSchema;
use U2\Xml\SchemaChecker;

class SchemaCheckerTest extends KernelTestCase
{
    public function test_validate_returns_no_violations_if_xml_is_valid(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);

        $schemaChecker = new SchemaChecker($validator);

        // Given
        $validator
            ->method('validate')
            ->with(self::isInstanceOf(\DOMDocument::class), self::isInstanceOf(XmlSchema::class))
            ->willReturn($expectedViolationList = new ConstraintViolationList());

        // When
        $actualViolationList = $schemaChecker->validate('<?xml version="1.0" encoding="UTF-8"?><xml></xml>', 'schema.xsd');

        // Then
        self::assertSame($expectedViolationList, $actualViolationList);
    }

    public function test_validate_returns_violations_if_xml_is_invalid(): void
    {
        $validator = $this->createMock(ValidatorInterface::class);

        $schemaChecker = new SchemaChecker($validator);

        // Given
        $validator
            ->method('validate')
            ->with(self::isInstanceOf(\DOMDocument::class), self::isInstanceOf(XmlSchema::class))
            ->willReturn($expectedViolationList = new ConstraintViolationList(
                [
                    new ConstraintViolation('Error', null, [], null, null, 'test'),
                ]
            ));

        // When
        $actualViolationList = $schemaChecker->validate('<?xml version="1.0" encoding="UTF-8"?><xml></xml>', 'schema.xsd');

        // Then
        self::assertSame($expectedViolationList, $actualViolationList);
    }
}
