<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Widget;

use Tests\U2\KernelTestCase;

class WidgetPurifierTest extends KernelTestCase
{
    public function test_does_not_purifies_widget_tags(): void
    {
        $widgetPurifier = self::getContainer()->get('exercise_html_purifier.widget');
        self::assertSame(
            '<widget>Widget tag stays</widget>',
            $widgetPurifier->purify('<widget>Widget tag stays</widget><script>alert(\'Scripts will be removed\')</script>'));
    }
}
