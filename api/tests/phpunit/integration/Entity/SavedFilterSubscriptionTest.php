<?php

declare(strict_types=1);
namespace Tests\Integration\U2\Entity;

use Tests\U2\KernelTestCase;
use U2\DataFixtures\Example\SavedFilterFactory;
use U2\DataFixtures\Example\SavedFilterSubscriptionFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\Frequency;
use U2\Security\CurrentUserManipulator;

class SavedFilterSubscriptionTest extends KernelTestCase
{
    public function test_change_frequency_changes_update_by_and_updated_at(): void
    {
        // Given
        $user = UserFactory::createOne(['username' => 'editor']);
        static::getContainer()->get(CurrentUserManipulator::class)->change($user->_real());
        $otherUser = UserFactory::createOne(['username' => 'other']);

        $createdAt = new \DateTime('-1 year');
        $subscription = SavedFilterSubscriptionFactory::createOne([
            'createdAt' => $createdAt,
            'createdBy' => $user,
            'updatedAt' => $createdAt,
            'updatedBy' => $otherUser,
            'savedFilter' => SavedFilterFactory::createOne(['owner' => $user]),
            'frequency' => new Frequency('5 * * * *'),
        ]);

        // When
        $subscription->setFrequency(new Frequency('10 * * * *'));
        $subscription->_save();

        // Then
        $updatedAt = $subscription->getUpdatedAt();
        self::assertNotNull($updatedAt, 'The updatedAt date should not be null');
        $updatedBy = $subscription->getUpdatedBy();
        self::assertNotNull($updatedBy, 'The `updatedBy` user should not be null');
        self::assertTrue($updatedAt > $createdAt, \sprintf('The updatedAt date should have changed. Old: %s New: %s', $createdAt->format(\DATE_W3C), $updatedAt->format(\DATE_W3C)));
        self::assertSame($updatedBy->getId(), $user->getId(), 'The `updatedBy` user should have changed');
    }
}
