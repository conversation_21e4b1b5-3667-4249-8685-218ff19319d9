<?php

declare(strict_types=1);
namespace Tests\U2;

use Symfony\Component\Uid\Ulid;
use Symfony\Component\Uid\Uuid;
use U2\Entity\Interfaces\Entity;

final class TestUtils
{
    /**
     * @template T of Entity
     *
     * @param T $object
     *
     * @return T
     */
    public static function setId(Entity $object, string|int|Uuid|Ulid $id): Entity
    {
        $reflectionProperty = new \ReflectionProperty($object::class, 'id');
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($object, $id);

        return $object;
    }

    public static function setProperty(Entity $object, string $property, mixed $value): void
    {
        $reflectionProperty = new \ReflectionProperty($object::class, $property);
        $reflectionProperty->setAccessible(true);
        $reflectionProperty->setValue($object, $value);
    }

    public static function getProperty(Entity $object, string $property): mixed
    {
        $reflectionProperty = new \ReflectionProperty($object::class, $property);
        $reflectionProperty->setAccessible(true);

        return $reflectionProperty->getValue($object);
    }

    /**
     * @param array<string, string> $parameters
     */
    public static function mockTranslation(string $transId, array $parameters = [], ?string $domain = null, ?string $locale = null): string
    {
        return \sprintf(
            'Translation: "%s" with %s. Domain: "%s". Locale: "%s"',
            $transId, json_encode($parameters), $domain ?? 'null', $locale ?? 'null'
        );
    }
}
