<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Event\Import;

use Tests\U2\UnitTestCase;
use U2\Event\Import\PostInterpretImportEvent;
use U2\Import\ImportLog;

class PostInterpretImportEventTest extends UnitTestCase
{
    public function test_gives_access_to_the_log(): void
    {
        $log = $this->createMock(ImportLog::class);
        $postInterpretImportEvent = new PostInterpretImportEvent($log);

        self::assertSame($log, $postInterpretImportEvent->getLog());
    }
}
