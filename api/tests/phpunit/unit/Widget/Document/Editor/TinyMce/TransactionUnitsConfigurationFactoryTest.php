<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Widget\Document\Editor\TinyMce;

use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\Document\Section\SectionContext;
use U2\Entity\DocumentSection;
use U2\Widget\Document\Editor\TinyMce\TransactionUnitsConfigurationFactory;
use U2\Widget\Document\TransactionUnits;
use U2\Widget\Editor\TinyMce\Checkbox;
use U2\Widget\Editor\TinyMce\DialogConfig;
use U2\Widget\Editor\TinyMce\FieldSet;
use U2\Widget\Editor\TinyMce\SelectBox;
use U2\Widget\Editor\TinyMce\SelectBoxChoice;

class TransactionUnitsConfigurationFactoryTest extends UnitTestCase
{
    public function test_create(): void
    {
        // Given
        $translator = $this->createMock(TranslatorInterface::class);
        $translator
            ->method('trans')
            ->with(self::anything())
            ->willReturn('');

        $sectionContext = $this->createMock(SectionContext::class);
        $section = $this->createMock(DocumentSection::class);
        $sectionContext
            ->method('getSection')
            ->willReturn($section);

        $configFactory = new TransactionUnitsConfigurationFactory($translator);

        $widget = $this->createMock(TransactionUnits::class);
        $widget
            ->method('getStyle')
            ->willReturn('style');
        $widget
            ->method('isShowRefId')
            ->willReturn(true);
        $widget
            ->method('isShowName')
            ->willReturn(true);
        $widget
            ->method('isShowCurrency')
            ->willReturn(true);
        $widget
            ->method('isShowCountry')
            ->willReturn(true);
        $widget
            ->method('isShowCountryFounded')
            ->willReturn(true);
        $widget
            ->method('isShowLegalName')
            ->willReturn(true);
        $widget
            ->method('isShowLegalForm')
            ->willReturn(true);
        $widget
            ->method('isShowTaxNumber')
            ->willReturn(true);
        $widget
            ->method('isShowRegisterNumber')
            ->willReturn(true);
        $widget
            ->method('isShowRegistryPlace')
            ->willReturn(true);
        $widget
            ->method('isShowPostalAddress')
            ->willReturn(true);

        // When
        $fileConfiguration = $configFactory->create(
            $widget,
            'title'
        );

        // Then
        self::assertEquals(
            new DialogConfig(
                'title',
                [
                    new SelectBox(
                        'style',
                        '',
                        [
                            new SelectBoxChoice(
                                '',
                                'blocks'
                            ),
                            new SelectBoxChoice(
                                '',
                                'table'
                            ),
                        ]
                    ),
                    new FieldSet(
                        '',
                        [
                            new Checkbox(
                                'show_ref_id',
                                ''
                            ),
                            new Checkbox(
                                'show_name',
                                '',
                                false
                            ),
                            new Checkbox(
                                'show_currency',
                                '',
                                false
                            ),
                            new Checkbox(
                                'show_country',
                                '',
                                false
                            ),
                            new Checkbox(
                                'show_country_founded',
                                ''
                            ),
                            new Checkbox(
                                'show_legal_name',
                                ''
                            ),
                            new Checkbox(
                                'show_legal_form',
                                ''
                            ),
                            new Checkbox(
                                'show_tax_number',
                                ''
                            ),
                            new Checkbox(
                                'show_register_number',
                                ''
                            ),
                            new Checkbox(
                                'show_registry_place',
                                ''
                            ),
                            new Checkbox(
                                'show_postal_address',
                                ''
                            ),
                        ]
                    ),
                ],
                [
                    'style' => 'style',
                    'show_ref_id' => true,
                    'show_name' => true,
                    'show_currency' => true,
                    'show_country' => true,
                    'show_country_founded' => true,
                    'show_legal_name' => true,
                    'show_legal_form' => true,
                    'show_tax_number' => true,
                    'show_register_number' => true,
                    'show_registry_place' => true,
                    'show_postal_address' => true,
                ]
            ), $fileConfiguration);
    }
}
