<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Widget\Document;

use Doctrine\Common\Collections\ArrayCollection;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Entity\Configuration\Field\ContractType;
use U2\Entity\Country;
use U2\Entity\DocumentSection;
use U2\Entity\Task\TaskType\Contract;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Repository\ContractRepository;
use U2\Repository\UnitRepository;
use U2\User\CurrentUserProvider;
use U2\Widget\Document\MainContracts;

class MainContractsTest extends UnitTestCase
{
    public function test_render(): void
    {
        $templatingEngine = $this->createMock(Environment::class);
        $unitRepository = $this->createMock(UnitRepository::class);
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $contractRepository = $this->createMock(ContractRepository::class);

        // Given
        $contractType = new ContractType();
        $contractType->setName('contractType');

        $contract = new Contract(new Status());
        $contract->setName('name');
        $contract->setContractType($contractType);

        $unit = new Unit();
        $partnerUnit = new Unit();

        $contract->setUnit($unit);
        $contract->setPartnerUnit($partnerUnit);

        $unitCountry = new Country();
        $unitCountry->setNameShort('unitCountry');

        $partnerUnitCountry = new Country();
        $partnerUnitCountry->setNameShort('partnerUnitCountry');

        $unit->setCountry($unitCountry);
        $unit->setRefId('unitRefId');

        $partnerUnit->setRefId('partnerUnitRefId');
        $partnerUnit->setCountry($partnerUnitCountry);

        $document = $this->createMock(LocalFile::class);
        $document
            ->method('getUnits')
            ->willReturn(new ArrayCollection([$unit]));

        $section = $this->createMock(DocumentSection::class);
        $section
            ->method('getDocument')
            ->willReturn($document);

        $currentUserProvider
            ->method('get')
            ->willReturn($user = new User());

        $unitRepository
            ->method('findUserAssigned')
            ->with($user)
            ->willReturn([$unit]);

        $contractRepository
            ->method('findMainContractsForUnits')
            ->with([$unit])
            ->willReturn([$contract]);

        $contractItem = [
            'unit_country' => 'unitCountry',
            'unit_ref_id' => 'unitRefId',
            'contract_name' => 'name',
            'contract_id' => null,
            'contract_type' => 'contractType',
            'date' => null,
            'partner_unit_ref_id' => 'partnerUnitRefId',
            'partner_unit_country' => 'partnerUnitCountry',
            'show_link' => true,
        ];

        $templatingEngine
            ->method('render')
            ->with(
                'widget/document/main_contracts.widget.html.twig',
                [
                    'contracts' => [$contractItem],
                    'section' => $section,
                    'format' => 'html',
                ]
            )
            ->willReturn('content');

        $widget = new MainContracts(
            $templatingEngine,
            $currentUserProvider,
            $unitRepository,
            $contractRepository
        );

        $widget->setParameters(
            [
                'show_date' => true,
                'show_partner_unit_country' => true,
            ]
        );

        $templatingEngine
            ->expects($this->once())
            ->method('render')
            ->with(
                'widget/document/main_contracts.widget.html.twig',
                [
                    'contracts' => [$contractItem],
                    'section' => $section,
                    'format' => 'html',
                ]
            );

        // Then
        self::assertSame('content', $widget->render(
            [
                'section' => $section,
                'format' => 'html',
            ]
        ));
    }

    public function test_render_contract_name_as_text_if_user_has_insufficient_permissions(): void
    {
        $templatingEngine = $this->createMock(Environment::class);
        $unitRepository = $this->createMock(UnitRepository::class);
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $contractRepository = $this->createMock(ContractRepository::class);

        // Given
        $contract = new Contract(new Status());
        $contract->setName('name');
        $contract->setContractType(new ContractType());

        $unit = new Unit();
        $partnerUnit = new Unit();

        $contract->setUnit($unit);
        $contract->setPartnerUnit($partnerUnit);

        $unitCountry = new Country();
        $unitCountry->setNameShort('unitCountry');

        $partnerUnitCountry = new Country();
        $partnerUnitCountry->setNameShort('partnerUnitCountry');

        $unit->setCountry($unitCountry);
        $unit->setRefId('unitRefId');

        $partnerUnit->setRefId('partnerUnitRefId');
        $partnerUnit->setCountry($partnerUnitCountry);

        $document = $this->createMock(LocalFile::class);
        $document
            ->method('getUnits')
            ->willReturn(new ArrayCollection([$unit]));

        $section = $this->createMock(DocumentSection::class);
        $section
            ->method('getDocument')
            ->willReturn($document);

        $currentUserProvider
            ->method('get')
            ->willReturn($user = new User());

        $unitRepository
            ->method('findUserAssigned')
            ->with($user)
            ->willReturn([]);

        $contractRepository
            ->method('findMainContractsForUnits')
            ->with([$unit])
            ->willReturn([$contract]);

        $widget = new MainContracts(
            $templatingEngine,
            $currentUserProvider,
            $unitRepository,
            $contractRepository
        );

        $contractItem = [
            'unit_country' => 'unitCountry',
            'unit_ref_id' => 'unitRefId',
            'contract_name' => 'name',
            'contract_id' => null,
            'contract_type' => '',
            'partner_unit_ref_id' => 'partnerUnitRefId',
            'show_link' => false,
        ];

        $templatingEngine
            ->method('render')
            ->with(
                'widget/document/main_contracts.widget.html.twig',
                [
                    'contracts' => [$contractItem],
                    'section' => $section,
                    'format' => 'html',
                ]
            )
            ->willReturn('content');

        $templatingEngine
            ->expects($this->once())
            ->method('render')
            ->with(
                'widget/document/main_contracts.widget.html.twig',
                [
                    'contracts' => [$contractItem],
                    'section' => $section,
                    'format' => 'html',
                ]
            );

        // When
        self::assertSame('content', $widget->render(
            [
                'section' => $section,
                'format' => 'html',
            ]
        ));
    }
}
