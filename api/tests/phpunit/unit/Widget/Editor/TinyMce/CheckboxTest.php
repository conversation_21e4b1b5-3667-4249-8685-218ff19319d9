<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Widget\Editor\TinyMce;

use Tests\U2\UnitTestCase;
use U2\Widget\Editor\TinyMce\Checkbox;

class CheckboxTest extends UnitTestCase
{
    private Checkbox $checkbox;

    public function test_initializable(): void
    {
        self::assertInstanceOf(Checkbox::class, $this->checkbox);
        self::assertInstanceOf(\JsonSerializable::class, $this->checkbox);
    }

    public function test_json_serializable(): void
    {
        self::assertSame([
            'type' => 'checkbox',
            'name' => 'test name',
            'label' => 'test label',
            'enabled' => true,
            'tooltip' => '',
        ], $this->checkbox->jsonSerialize());
    }

    protected function setUp(): void
    {
        $this->checkbox = new Checkbox('test name', 'test label', true);
    }
}
