<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Widget\Editor\TinyMce;

use Tests\U2\UnitTestCase;
use U2\Widget\Editor\TinyMce\Input;

class InputTest extends UnitTestCase
{
    private Input $input;

    public function test_initializable(): void
    {
        self::assertInstanceOf(Input::class, $this->input);
        self::assertInstanceOf(\JsonSerializable::class, $this->input);
    }

    public function test_json_serializable(): void
    {
        self::assertSame([
            'type' => 'input',
            'name' => 'test name',
            'label' => 'test label',
            'placeholder' => 'test placeholder',
            'size' => 20,
            'tooltip' => 'test help',
        ], $this->input->jsonSerialize());
    }

    protected function setUp(): void
    {
        $this->input = new Input('test name', 'test label', 'test placeholder', 'test help', 20);
    }
}
