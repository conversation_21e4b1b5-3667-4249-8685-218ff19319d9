<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Widget\Editor\TinyMce;

use Tests\U2\UnitTestCase;
use U2\Widget\Editor\TinyMce\DialogConfig;

class DialogConfigTest extends UnitTestCase
{
    private DialogConfig $dialogConfig;

    public function test_initializable(): void
    {
        self::assertInstanceOf(DialogConfig::class, $this->dialogConfig);
        self::assertInstanceOf(\JsonSerializable::class, $this->dialogConfig);
    }

    public function test_json_serializable(): void
    {
        self::assertSame([
            'title' => 'test title',
            'body' => [
                'type' => 'panel',
                'items' => ['item one', 'item two'],
            ],
            'initialData' => [],
            'buttons' => [
                [
                    'type' => 'cancel',
                    'name' => 'closeButton',
                    'text' => 'Cancel',
                ],
                [
                    'type' => 'submit',
                    'name' => 'submitButton',
                    'text' => 'Ok',
                    'primary' => true,
                ],
            ],
        ], $this->dialogConfig->jsonSerialize());
    }

    protected function setUp(): void
    {
        $this->dialogConfig = new DialogConfig('test title', ['item one', 'item two']);
    }
}
