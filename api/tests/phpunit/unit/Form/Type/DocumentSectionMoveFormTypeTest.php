<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form\Type;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Form\PreloadedExtension;
use Symfony\Component\Form\Test\TypeTestCase;
use U2\Document\Section\EntityMetadata\SectionResolver;
use U2\Document\Structure\HierarchicalBrowser;
use U2\Document\Structure\SectionListManipulation\Movement;
use U2\Document\Structure\SectionListManipulation\SectionPlacement;
use U2\Document\Structure\SectionNumberer;
use U2\Entity\DocumentSection;
use U2\Entity\Task\TaskType\AbstractDocument;
use U2\Entity\Traits\DocumentSectionTrait;
use U2\Entity\Traits\FileAttachableTrait;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Form\Type\DocumentSectionMoveFormType;

class DocumentSectionMoveFormTypeTest extends TypeTestCase
{
    public function test_submit_valid_data(): void
    {
        // Given
        $document = new DocumentSectionMoveFormTypeTestDocument(new Status());
        $document->setSections(
            new ArrayCollection([
                $sectionToMove = new DocumentSectionMoveFormTypeTestSection(1),
                $referenceSection = new DocumentSectionMoveFormTypeTestSection(
                    $referenceSectionId = 2
                ),
            ])
        );

        $sectionResolver = $this->createMock(SectionResolver::class);
        $sectionResolver
            ->method('resolve')
            ->with($referenceSectionId)
            ->willReturn($referenceSection);

        $form = $this->factory->create(
            DocumentSectionMoveFormType::class,
            $actualMovement = new Movement($sectionToMove, new HierarchicalBrowser()),
            [
                'sectionResolver' => $sectionResolver,
            ]
        );

        $formData = [
            'referenceSection' => $referenceSectionId,
            'placement' => SectionPlacement::SUBSECTION_OF,
        ];

        // When
        $form->submit($formData);

        // Then
        $expectedMovement = new Movement($sectionToMove, new HierarchicalBrowser());
        $expectedMovement->setReferenceSection($referenceSection);
        $expectedMovement->setPlacement(SectionPlacement::SUBSECTION_OF);

        self::assertEquals($expectedMovement, $actualMovement, 'Mapping the submitted data to the form failed.');

        self::assertTrue($form->isSynchronized(), 'A data transformer used by the form failed.');

        $children = $form->createView()->children;

        foreach (array_keys($formData) as $key) {
            self::assertArrayHasKey($key, $children, \sprintf("Form field '%s' is missing", $key));
        }
    }

    /**
     * @return array<int, PreloadedExtension>
     */
    protected function getExtensions(): array
    {
        return [
            new PreloadedExtension(
                [
                    new DocumentSectionMoveFormType(
                        new SectionNumberer(),
                        new HierarchicalBrowser()
                    ),
                ],
                []
            ),
        ];
    }
}

class DocumentSectionMoveFormTypeTestSection implements DocumentSection
{
    use DocumentSectionTrait;
    use FileAttachableTrait;

    public function __construct(int $id)
    {
        $this->id = $id;
        $this->include = true;
        $this->level = 1;
        $this->files = new ArrayCollection();
    }

    private ?User $createdBy = null;

    private ?User $updatedBy = null;

    private ?\DateTime $createdAt = null;

    private ?\DateTime $updatedAt = null;

    public function setCreatedAt(?\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }

    public function getDisplayName(): string
    {
        return '';
    }
}

class DocumentSectionMoveFormTypeTestDocument extends AbstractDocument
{
    public static function getSectionClass(): string
    {
        return DocumentSectionMoveFormTypeTestSection::class;
    }

    public static function getTaskType(): string
    {
        return '';
    }

    public static function getWorkflowBindingId(): string
    {
        return '';
    }

    public static function getWorkflowBindingName(): string
    {
        return '';
    }
}
