<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form\Extension;

use Symfony\Component\Form\FormBuilder;
use Tests\U2\UnitTestCase;
use U2\EventListener\UploadFileFieldSubscriber;
use U2\Form\Extension\FileTypeExtension;

class FileTypeExtensionTest extends UnitTestCase
{
    public function test_adds_an_event_subscriber(): void
    {
        $uploadFileFieldSubscriber = $this->createMock(UploadFileFieldSubscriber::class);
        $fileTypeExtension = new FileTypeExtension($uploadFileFieldSubscriber);

        $formBuilder = $this->createMock(FormBuilder::class);
        $formBuilder->expects($this->atLeastOnce())->method('addEventSubscriber')->with($uploadFileFieldSubscriber);

        $fileTypeExtension->buildForm($formBuilder, []);
    }
}
