<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form\DataTransformer;

use Tests\U2\UnitTestCase;
use U2\Form\DataTransformer\MoneyStringToLocalizedStringTransformer;

class MoneyStringToLocalizedStringTransformerTest extends UnitTestCase
{
    private MoneyStringToLocalizedStringTransformer $moneyToLocalizedStringTransformer;

    public function test_reverse_transforms_an_empty_string(): void
    {
        self::assertNull($this->moneyToLocalizedStringTransformer->reverseTransform(''));
    }

    public function test_reverse_transforms_a_null_value(): void
    {
        self::assertNull($this->moneyToLocalizedStringTransformer->reverseTransform(null));
    }

    protected function setUp(): void
    {
        $this->moneyToLocalizedStringTransformer = new MoneyStringToLocalizedStringTransformer(2, true, 6, 1);
    }
}
