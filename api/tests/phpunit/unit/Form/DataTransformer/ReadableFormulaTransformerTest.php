<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form\DataTransformer;

use ApiPlatform\Validator\Exception\ValidationException;
use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\ItemFactory;
use U2\Datasheets\Item\Formula\ElementExtractor;
use U2\Datasheets\Item\ItemRefIdToIdConvertor;
use U2\Entity\Item;
use U2\Exception\ItemNotFoundException;
use U2\Form\DataTransformer\ReadableFormulaTransformer;
use U2\Repository\CachedItemRepository;

class ReadableFormulaTransformerTest extends UnitTestCase
{
    public function test_handles_transform_exceptions_caused_by_missing_items(): void
    {
        $itemRepository = $this->createMock(CachedItemRepository::class);
        $itemRepository->expects($this->once())->method('findById')->with(99)->willThrowException(new ItemNotFoundException('property', 'value'));
        $systemReadableFormula = 'system readable formula';
        $elementExtractor = $this->createMock(ElementExtractor::class);
        $elementExtractor->expects($this->once())->method('extractElementIds')->with($systemReadableFormula)->willReturn(['99']);
        $readableFormulaTransformer = new ReadableFormulaTransformer($elementExtractor, $itemRepository, $this->createMock(ItemRefIdToIdConvertor::class));

        $this->expectException(ValidationException::class);
        $readableFormulaTransformer->transform($systemReadableFormula);
    }

    public function test_handles_revers_transform_exceptions_caused_by_missing_items(): void
    {
        $itemRefIdToIdConvertor = $this->createMock(ItemRefIdToIdConvertor::class);
        $itemRefIdToIdConvertor->expects($this->once())->method('convert')->with(99)->willThrowException(new ItemNotFoundException('property', 'value'));
        $formula = 'human readable formula';
        $elementExtractor = $this->createMock(ElementExtractor::class);
        $elementExtractor->expects($this->once())->method('extractElementIds')->with($formula)->willReturn(['99']);
        $readableFormulaTransformer = new ReadableFormulaTransformer($elementExtractor, $this->createMock(CachedItemRepository::class), $itemRefIdToIdConvertor);

        $this->expectException(ValidationException::class);
        $readableFormulaTransformer->reverseTransform($formula);
    }

    public function test_reverse_transforms_an_formula_into_system_format(): void
    {
        $itemA = ItemFactory::new(['id' => 1])->create();
        $itemB = ItemFactory::new(['id' => 2])->create();
        $itemRefIdToIdConvertor = $this->createMock(ItemRefIdToIdConvertor::class);
        $itemRefIdToIdConvertor
            ->method('convert')
            ->willReturnCallback(
                static fn (string $refId): int => match ($refId) {
                    'A' => $itemA->getId(),
                    'B' => $itemB->getId(),
                    default => throw new \InvalidArgumentException(\sprintf('Unknown value "%s".', $refId)),
                }
            );
        $formula = '{A}+{B}';
        $elementExtractor = $this->createMock(ElementExtractor::class);
        $elementExtractor->expects($this->atLeastOnce())->method('extractElementIds')->with($formula)->willReturn(['A', 'B']);
        $readableFormulaTransformer = new ReadableFormulaTransformer($elementExtractor, $this->createMock(CachedItemRepository::class), $itemRefIdToIdConvertor);

        self::assertSame('{1}+{2}', $readableFormulaTransformer->reverseTransform($formula));
    }

    public function test_transforms_an_formula_into_readable_format(): void
    {
        $itemA = ItemFactory::new(['id' => 1, 'refId' => 'A'])->create();
        $itemB = ItemFactory::new(['id' => 2, 'refId' => 'B'])->create();
        $itemRepository = $this->createMock(CachedItemRepository::class);
        $itemRepository
            ->method('findById')
            ->willReturnCallback(
                static fn (int $id): Item => match ($id) {
                    1 => $itemA,
                    2 => $itemB,
                    default => throw new \InvalidArgumentException(\sprintf('Unknown value "%s".', $id)),
                }
            );
        $elementExtractor = $this->createMock(ElementExtractor::class);
        $elementExtractor->expects($this->atLeastOnce())->method('extractElementIds')->with('{1}+{2}')->willReturn(['1', '2']);
        $readableFormulaTransformer = new ReadableFormulaTransformer($elementExtractor, $itemRepository, $this->createMock(ItemRefIdToIdConvertor::class));

        self::assertSame('{A}+{B}', $readableFormulaTransformer->transform('{1}+{2}'));
    }

    public function test_reverse_transforms_an_formula_containing_previous_period_items_into_system_format(): void
    {
        $itemA = ItemFactory::new(['id' => 1])->create();
        $itemB = ItemFactory::new(['id' => 2])->create();
        $itemRefIdToIdConvertor = $this->createMock(ItemRefIdToIdConvertor::class);
        $itemRefIdToIdConvertor
            ->method('convert')
            ->willReturnCallback(
                static fn (string $refId): int => match ($refId) {
                    'A' => $itemA->getId(),
                    'B' => $itemB->getId(),
                    default => throw new \InvalidArgumentException(\sprintf('Unknown value "%s".', $refId)),
                }
            );
        $elementExtractor = $this->createMock(ElementExtractor::class);
        $elementExtractor->expects($this->atLeastOnce())->method('extractElementIds')->with('{A}+{PB}')->willReturn(['A', 'PB']);
        $readableFormulaTransformer = new ReadableFormulaTransformer($elementExtractor, $this->createMock(CachedItemRepository::class), $itemRefIdToIdConvertor);

        self::assertSame('{1}+{P2}', $readableFormulaTransformer->reverseTransform('{A}+{PB}'));
    }

    public function test_transforms_an_formula_containing_previous_period_items_into_readable_format(): void
    {
        $itemA = ItemFactory::new(['id' => 1, 'refId' => 'A'])->create();
        $itemB = ItemFactory::new(['id' => 2, 'refId' => 'B'])->create();
        $itemRepository = $this->createMock(CachedItemRepository::class);
        $itemRepository
            ->method('findById')
            ->willReturnCallback(
                static fn (int $id): Item => match ($id) {
                    1 => $itemA,
                    2 => $itemB,
                    default => throw new \InvalidArgumentException(\sprintf('Unknown value "%s".', $id)),
                }
            );
        $elementExtractor = $this->createMock(ElementExtractor::class);
        $elementExtractor->expects($this->atLeastOnce())->method('extractElementIds')->with('{1}+{P2}')->willReturn(['1', 'P2']);
        $readableFormulaTransformer = new ReadableFormulaTransformer($elementExtractor, $itemRepository, $this->createMock(ItemRefIdToIdConvertor::class));

        self::assertSame('{A}+{PB}', $readableFormulaTransformer->transform('{1}+{P2}'));
    }
}
