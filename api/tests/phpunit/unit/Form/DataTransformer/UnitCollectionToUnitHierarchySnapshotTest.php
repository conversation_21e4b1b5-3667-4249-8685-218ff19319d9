<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form\DataTransformer;

use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\Unit;
use U2\Form\DataTransformer\UnitCollectionToUnitHierarchySnapshot;
use U2\Repository\UnitRepository;
use U2\Unit\Hierarchy\Snapshot;
use U2\Unit\Hierarchy\SnapshotFactory;

class UnitCollectionToUnitHierarchySnapshotTest extends UnitTestCase
{
    private UnitCollectionToUnitHierarchySnapshot $unitCollectionToUnitHierarchySnapshot;

    /**
     * @var MockObject&UnitRepository
     */
    private MockObject $unitRepository;

    public function test_reverse_transforms_snapshot(): void
    {
        $snapshot = $this->createMock(Snapshot::class);
        $unit = $this->createMock(Unit::class);
        $snapshot->expects($this->atLeastOnce())->method('getSelectedUnitIds')->willReturn([1]);
        $this->unitRepository->expects($this->atLeastOnce())->method('findBy')->with(self::equalTo(['id' => [1]]))->willReturn([$unit]);
        self::assertEquals(
            new ArrayCollection([$unit]),
            $this->unitCollectionToUnitHierarchySnapshot->reverseTransform($snapshot)
        );
    }

    public function test_reverse_transforms_empty_snapshot(): void
    {
        self::assertEquals(
            new ArrayCollection([]),
            $this->unitCollectionToUnitHierarchySnapshot->reverseTransform(null)
        );
    }

    protected function setUp(): void
    {
        $snapshotFactory = $this->createMock(SnapshotFactory::class);
        $this->unitRepository = $this->createMock(UnitRepository::class);
        $this->unitCollectionToUnitHierarchySnapshot = new UnitCollectionToUnitHierarchySnapshot($snapshotFactory, $this->unitRepository);
    }
}
