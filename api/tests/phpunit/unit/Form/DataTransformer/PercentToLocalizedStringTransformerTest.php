<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form\DataTransformer;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\U2\UnitTestCase;
use U2\Form\DataTransformer\PercentToLocalizedStringTransformer;

/**
 * @covers \U2\Form\DataTransformer\PercentToLocalizedStringTransformer
 */
class PercentToLocalizedStringTransformerTest extends UnitTestCase
{
    #[DataProvider('provideTransformerValues')]
    public function test_it_transforms_values_and_remembers_the_decimal_places(string $value, string $transformedValue, string $reversedValue, int $scale): void
    {
        $transformer = new PercentToLocalizedStringTransformer($scale, 'fractional', \NumberFormatter::ROUND_HALFUP);

        self::assertSame($transformedValue, $transformer->transform($value), 'The value did not transform correctly');
        self::assertSame($reversedValue, $transformer->reverseTransform($transformedValue), 'The value did not reverse transform correctly');
    }

    /**
     * @return array<int,array<string,int|string|float>>
     */
    public static function provideTransformerValues(): array
    {
        return [
            [
                'value' => '0.0010',
                'transformedValue' => '0.1000',
                'reversedValue' => '0.0010',
                'scale' => 4,
            ],
            [
                'value' => '1.0',
                'transformedValue' => '100.0000',
                'reversedValue' => '1.0',
                'scale' => 4,
            ],
            [
                'value' => '-0.05',
                'transformedValue' => '-5.0000',
                'reversedValue' => '-0.05',
                'scale' => 4,
            ],
            [
                'value' => '0.000000001',
                'transformedValue' => '0.0000',
                'reversedValue' => '0.000000000',
                'scale' => 4,
            ],
            [
                'value' => '0.000000001',
                'transformedValue' => '0.0000001000',
                'reversedValue' => '0.000000001',
                'scale' => 10,
            ],
            [
                'value' => '0.0000000000000000001',
                'transformedValue' => '0.00000000000000001000',
                'reversedValue' => '0.0000000000000000001',
                'scale' => 20,
            ],
            [
                'value' => '1.234567890',
                'transformedValue' => '123.4567890000',
                'reversedValue' => '1.234567890',
                'scale' => 10,
            ],
            [
                'value' => '1.234567890',
                'transformedValue' => '123.4568',
                'reversedValue' => '1.234568000',
                'scale' => 4,
            ],
            [
                'value' => '1.2222222222',
                'transformedValue' => '122.2222',
                'reversedValue' => '1.2222220000',
                'scale' => 4,
            ],
        ];
    }

    public function test_transforms_a_null_value(): void
    {
        $transformer = new PercentToLocalizedStringTransformer(4, 'fractional', \NumberFormatter::ROUND_HALFUP);

        self::assertSame('', $transformer->transform(null));
    }

    public function test_reverse_transforms_a_null_value(): void
    {
        $transformer = new PercentToLocalizedStringTransformer(4, 'fractional', \NumberFormatter::ROUND_HALFUP);

        $transformer->transform(null);
        self::assertNull($transformer->reverseTransform(null));
    }

    public function test_reverse_transforms_an_empty_string(): void
    {
        $transformer = new PercentToLocalizedStringTransformer(4, 'fractional', \NumberFormatter::ROUND_HALFUP);

        $transformer->transform(null);
        self::assertNull($transformer->reverseTransform(''));
        $transformer->transform('1.0');
        self::assertNull($transformer->reverseTransform(''));
    }
}
