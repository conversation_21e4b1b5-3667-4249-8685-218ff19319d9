<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Form;

use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormErrorIterator;
use Symfony\Component\Form\FormInterface;
use Tests\U2\UnitTestCase;
use U2\Form\NonAutoAffirmativeDisabledFormValidator;

class NonAutoAffirmativeDisabledFormValidatorTest extends UnitTestCase
{
    private NonAutoAffirmativeDisabledFormValidator $nonAutoAffirmativeDisabledFormValidator;

    public function test_invalid_for_invalid_form(): void
    {
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('isValid')->willReturn(false);
        self::assertFalse($this->nonAutoAffirmativeDisabledFormValidator->isValid($form));
    }

    public function test_invalid_for_valid_form_containing_errors(): void
    {
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('isValid')->willReturn(true);
        $form->expects($this->atLeastOnce())->method('getErrors')->with(self::equalTo(true))->willReturn(new FormErrorIterator($form, [new FormError('error')]));
        self::assertFalse($this->nonAutoAffirmativeDisabledFormValidator->isValid($form));
    }

    protected function setUp(): void
    {
        $this->nonAutoAffirmativeDisabledFormValidator = new NonAutoAffirmativeDisabledFormValidator();
    }
}
