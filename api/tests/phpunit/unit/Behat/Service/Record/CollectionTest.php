<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Behat\Service\Record;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Behat\Service\Record\Collection;
use U2\Behat\Service\Reflection\ObjectReflector;

class CollectionTest extends UnitTestCase
{
    private Collection $collection;

    /**
     * @var ObjectReflector&MockObject
     */
    private MockObject $reflector;

    public function test_support_entity_if_not_initialized(): void
    {
        $rightObject = $this->createMock(\stdClass::class);
        $this->reflector->expects($this->atLeastOnce())
            ->method('getClassLongName')
            ->with(self::equalTo($rightObject))
            ->willReturn('The\\Name\\Space\\TheClass');
        self::assertTrue($this->collection->support($rightObject));
        self::assertSame('The\Name\Space\TheClass', $this->collection->getReferential());
    }

    public function test_set_the_referential_from_string(): void
    {
        $str = "The\\Name\\Space\Of\\The\\Class";

        self::assertSame($this->collection, $this->collection->setReferential($str));
        self::assertSame($str, $this->collection->getReferential());
    }

    public function test_set_the_referential_from_entity(): void
    {
        $rightObject = $this->createMock(\stdClass::class);
        $this->reflector->expects($this->atLeastOnce())
            ->method('getClassLongName')
            ->with(self::equalTo($rightObject))
            ->willReturn('The\\Name\\Space\\TheClass');
        self::assertSame($this->collection, $this->collection->setReferential($rightObject));
        self::assertSame('The\Name\Space\TheClass', $this->collection->getReferential());
    }

    public function test_support_entity_when_corresponding_to_string(): void
    {
        $rightObject = $this->createMock(\stdClass::class);
        $this->reflector->expects($this->atLeastOnce())->method('isInstanceOf')->with(self::equalTo($rightObject))->willReturn(true);
        $str = 'The\\Name\\Space\\TheClass';
        self::assertSame($this->collection, $this->collection->setReferential($str));
        self::assertTrue($this->collection->support($rightObject));
    }

    public function test_support_entity_when_corresponding_to_object(): void
    {
        $rightObject = $this->createMock(\stdClass::class);
        $this->reflector->expects($this->atLeastOnce())
            ->method('getClassLongName')
            ->with(self::equalTo($rightObject))
            ->willReturn('The\\Name\\Space\\TheClass');
        $this->reflector->expects($this->atLeastOnce())->method('isInstanceOf')->with(self::equalTo($rightObject))->willReturn(true);
        self::assertSame($this->collection, $this->collection->setReferential($rightObject));
        self::assertTrue($this->collection->support($rightObject));
    }

    public function test_not_support_entity_when_not_corresponding_to_string(): void
    {
        $wrongObject = $this->createMock(\stdClass::class);
        $this->reflector->expects($this->atLeastOnce())->method('isInstanceOf')->with(self::equalTo($wrongObject))->willReturn(false);
        $str = 'The\\Name\\Space\\TheClass';
        self::assertSame($this->collection, $this->collection->setReferential($str));
        self::assertFalse($this->collection->support($wrongObject));
    }

    public function test_not_support_entity_when_not_corresponding_to_object(): void
    {
        $rightObject = $this->createMock(\stdClass::class);
        $wrongObject = $this->createMock(\stdClass::class);
        $this->reflector->expects($this->atLeastOnce())
            ->method('getClassLongName')
            ->with(self::equalTo($rightObject))
            ->willReturn('The\\Name\\Space\\TheClass');
        $this->reflector->expects($this->atLeastOnce())->method('isInstanceOf')->with(self::equalTo($wrongObject))->willReturn(false);
        self::assertSame($this->collection, $this->collection->setReferential($rightObject));
        self::assertFalse($this->collection->support($wrongObject));
    }

    protected function setUp(): void
    {
        $this->reflector = $this->createMock(ObjectReflector::class);
        $this->collection = new Collection($this->reflector);
    }
}
