<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Behat\Service\Utils;

use Tests\U2\UnitTestCase;
use U2\Behat\Service\Utils\Asserter;
use U2\Behat\Service\Utils\TextFormatter;
use U2\Exception\Exception;

class AsserterTest extends UnitTestCase
{
    private Asserter $asserter;

    public function test_assert_if_equals(): void
    {
        $object = new \stdClass();

        self::assertTrue($this->asserter->assertEquals(0, 0));
        self::assertTrue($this->asserter->assertEquals(1000, 1000));
        self::assertTrue($this->asserter->assertEquals('string', 'string'));
        self::assertTrue($this->asserter->assertEquals($object, $object));
        self::assertTrue($this->asserter->assertEquals([0, 1, 2, 3], [0, 1, 2, 3]));
    }

    public function test_throw_and_exception_when_can_t_assert(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failing to assert equals.');
        $this->asserter->assertEquals(true, false);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failing to assert equals.');
        $this->asserter->assertEquals(0, 1);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failing to assert equals.');
        $this->asserter->assertEquals('string', 'STRING');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failing to assert equals.');
        $this->asserter->assertEquals(0, '0');

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Failing to assert equals.');
        $this->asserter->assertEquals(new \stdClass(), new \stdClass());
    }

    public function test_display_the_array_when_display_the_error(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The given array\r\n\r\n| 10 | test | 1 |\r\nis not equals to expected\r\n\r\n| 10 | text |  |");

        $this->asserter->assertArrayEquals([10, 'text', false], [10, 'test', true]);
    }

    public function test_assert_that_an_array_contains_an_other(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage("The given array\r\n\r\n| 10 | test | 1 |\r\ndoes not contain the following rows\r\n\r\n| 10 | text |  |");

        $this->asserter->assertArrayContains([10, 'text', false], [10, 'test', true]);
    }

    public function test_detect_array_into_table(): void
    {
        $real = [
            [1, 2, 3, 4],
            ['a', 'b', 'c', 'd'],
            ['d', 'b', 'a', 'b'],
            [1, 2, 3, 4],
        ];

        $expected = [
            ['b', 'a'],
            [2, 3],
        ];

        self::assertTrue($this->asserter->assertArrayContains($expected, $real));

        $expected = [
            [2, 3],
            ['b', 'c'],
        ];

        self::assertTrue($this->asserter->assertArrayContains($expected, $real));
    }

    public function test_throw_when_array_doesnt_contain(): void
    {
        $real = [
            [1, 2, 3, 4],
            ['a', 'b', 'c', 'd'],
            ['d', 'b', 'a', 'b'],
            [1, 2, 3, 4],
        ];

        $expected = [
            [1, 2],
            ['a', 'd'],
        ];

        $this->expectException(Exception::class);

        $this->asserter->assertArrayContains($expected, $real);
    }

    protected function setUp(): void
    {
        $this->asserter = new Asserter(new TextFormatter());
    }
}
