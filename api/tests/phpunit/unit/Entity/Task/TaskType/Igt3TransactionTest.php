<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity\Task\TaskType;

use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Task\Field\LinkedBaseLocalGroupMoneyCommonTrait;
use U2\Util\ClassInspector;

class Igt3TransactionTest extends UnitTestCase
{
    public function test_it_uses_base_local_group_transaction_trait(): void
    {
        self::assertTrue(ClassInspector::usesTrait(Igt3Transaction::class, LinkedBaseLocalGroupMoneyCommonTrait::class));
    }
}
