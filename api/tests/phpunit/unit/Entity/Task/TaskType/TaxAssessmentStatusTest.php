<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity\Task\TaskType;

use Tests\U2\UnitTestCase;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\TaxAssessmentTaskType;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Task\TaskType\TaxAssessmentStatus;
use U2\Entity\Workflow\Status;

class TaxAssessmentStatusTest extends UnitTestCase
{
    public function test_interface_implementations(): void
    {
        self::assertInstanceOf(TaxAssessmentTaskType::class, new TaxAssessmentStatus(new Status()));
        self::assertInstanceOf(Transferable::class, new TaxAssessmentStatus(new Status()));
        self::assertInstanceOf(Periodable::class, new TaxAssessmentStatus(new Status()));
    }
}
