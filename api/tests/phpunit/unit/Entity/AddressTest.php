<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity;

use Tests\U2\UnitTestCase;
use U2\Entity\Address;

class AddressTest extends UnitTestCase
{
    private Address $address;

    public function test_knows_if_it_is_empty(): void
    {
        self::assertTrue($this->address->isEmpty());
    }

    public function test_knows_if_it_is_not_empty(): void
    {
        $this->address->setCity('City');
        self::assertFalse($this->address->isEmpty());
    }

    protected function setUp(): void
    {
        $this->address = new Address();
    }
}
