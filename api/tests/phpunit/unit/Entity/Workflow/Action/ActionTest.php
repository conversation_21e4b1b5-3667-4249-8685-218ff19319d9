<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity\Workflow\Action;

use Tests\U2\UnitTestCase;
use U2\Entity\Workflow\Action\Action;

class ActionTest extends UnitTestCase
{
    public function test_ensure_action_type_to_class_map_does_not_change(): void
    {
        self::assertSame(
            [
                'addreviewaction' => 'U2\Entity\Workflow\Action\AddReviewAction',
                'setdefaultitemvaluessaction' => 'U2\Entity\Workflow\Action\SetDefaultItemValuesAction',
                'recalculateitemvaluessaction' => 'U2\Entity\Workflow\Action\RecalculateItemValuesAction',
                'resetreviewsaction' => 'U2\Entity\Workflow\Action\ResetReviewsAction',
            ],
            Action::actionTypeToClassMap
        );
    }
}
