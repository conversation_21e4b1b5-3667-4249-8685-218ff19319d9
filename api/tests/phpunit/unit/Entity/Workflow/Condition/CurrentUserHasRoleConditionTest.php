<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Entity\Workflow\Condition;

use Tests\U2\UnitTestCase;
use U2\Entity\Workflow\Condition\CurrentUserHasRoleCondition;
use U2\Entity\Workflow\Transition;

class CurrentUserHasRoleConditionTest extends UnitTestCase
{
    /**
     * @covers \U2\Entity\Workflow\Condition\CurrentUserHasRoleCondition::areUserRolesAllowed
     */
    public function test_are_user_roles_allowed(): void
    {
        $roles = [
            'TEST_ROLE_1',
            'TEST_ROLE_2',
        ];

        $condition = new CurrentUserHasRoleCondition(new Transition());
        $condition->setRoles($roles);

        $testRolesToExpectedResultPairs = [
            [
                'roles' => [
                    'TEST_ROLE_1',
                    'SOME_OTHER_ROLE',
                ],
                'expected' => true,
            ],
            [
                'roles' => [
                    'A_THIRD_ROLE',
                ],
                'expected' => false,
            ],
        ];

        foreach ($testRolesToExpectedResultPairs as $testRoleToExpectedResultPair) {
            $testRoles = $testRoleToExpectedResultPair['roles'];
            $expectedResult = $testRoleToExpectedResultPair['expected'];

            self::assertSame($expectedResult, $condition->areUserRolesAllowed($testRoles));
            self::assertSame($expectedResult, $condition->areUserRolesAllowed($testRoles));
        }
    }
}
