<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Api\Filter\Task;

use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\GetCollection;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\NativeQuery;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Tests\U2\UnitTestCase;
use U2\Api\Filter\Task\CheckStateCurrentFilter;
use U2\Entity\Task\CheckState;

class CheckStateCurrentFilterTest extends UnitTestCase
{
    protected CheckStateCurrentFilter $filter;

    public function test_unsupported_resource_class(): void
    {
        // Given
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $queryBuilder->expects($this->never())->method('andWhere');
        $filter = new CheckStateCurrentFilter($this->createMock(ManagerRegistry::class));

        // When
        $filter->apply(
            $queryBuilder,
            $this->createMock(QueryNameGeneratorInterface::class),
            \stdClass::class,
            new GetCollection(),
            [
                'filters' => [
                    'current' => true,
                ],
                'uri_variables' => ['id' => 'task-id'],
            ],
        );
    }

    public function test_filter_value_is_false(): void
    {
        // Given
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $queryBuilder->expects($this->never())->method('andWhere');
        $filter = new CheckStateCurrentFilter($this->createMock(ManagerRegistry::class));

        // When
        $filter->apply(
            $queryBuilder,
            $this->createMock(QueryNameGeneratorInterface::class),
            CheckState::class,
            new GetCollection(),
            [
                'filters' => [
                    'current' => false,
                ],
                'uri_variables' => ['id' => 'task-id'],
            ],
        );
    }

    public function test_filter_property(): void
    {
        // Given
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $em = $this->createMock(EntityManagerInterface::class);

        $metadata = $this->createMock(ClassMetadata::class);
        $metadata->method('getTableName')->willReturn('');
        $em->expects($this->once())->method('getClassMetadata')->willReturn($metadata);
        $query = $this->createMock(NativeQuery::class);
        $query->method('getArrayResult')->willReturn([
            [
                'check_id' => 1,
                'created_at' => 'today',
            ],
            [
                'check_id' => 2,
                'created_at' => 'tomorrow',
            ],
        ]);
        $em->expects($this->once())->method('createNativeQuery')->willReturn($query);
        $query->expects($this->once())->method('setParameter')->with('taskId', 'task-id', 'uuid')->willReturn($query);
        $queryBuilder->expects($this->once())->method('getEntityManager')->willReturn($em);
        $queryBuilder->expects($this->once())->method('getRootAliases')->willReturn(['r']);
        $queryBuilder->expects($this->once())->method('andWhere')
            ->with("(r.check = 1 and r.createdAt = 'today') OR (r.check = 2 and r.createdAt = 'tomorrow')");
        $filter = new CheckStateCurrentFilter($this->createMock(ManagerRegistry::class));

        // When
        $filter->apply(
            $queryBuilder,
            $this->createMock(QueryNameGeneratorInterface::class),
            CheckState::class,
            new GetCollection(),
            [
                'filters' => [
                    'current' => true,
                ],
                'uri_variables' => ['id' => 'task-id'],
            ],
        );
    }

    public function test_filter_property_on_empty_result_set(): void
    {
        // Given
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $em = $this->createMock(EntityManagerInterface::class);

        $metadata = $this->createMock(ClassMetadata::class);
        $metadata->method('getTableName')->willReturn('');
        $em->expects($this->once())->method('getClassMetadata')->willReturn($metadata);
        $query = $this->createMock(NativeQuery::class);
        $query->method('getArrayResult')->willReturn([]);
        $em->expects($this->once())->method('createNativeQuery')->willReturn($query);
        $query->expects($this->once())->method('setParameter')->with('taskId', 'task-id', 'uuid')->willReturn($query);
        $queryBuilder->expects($this->once())->method('getEntityManager')->willReturn($em);
        $queryBuilder->expects($this->once())->method('getRootAliases')->willReturn(['r']);
        $queryBuilder->expects($this->never())->method('andWhere');

        $filter = new CheckStateCurrentFilter($this->createMock(ManagerRegistry::class));

        // When
        $filter->apply(
            $queryBuilder,
            $this->createMock(QueryNameGeneratorInterface::class),
            CheckState::class,
            new GetCollection(),
            [
                'filters' => [
                    'current' => true,
                ],
                'uri_variables' => ['id' => 'task-id'],
            ],
        );
    }
}
