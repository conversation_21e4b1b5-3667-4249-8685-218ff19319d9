<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Api\Filter;

use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Get;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Tests\U2\UnitTestCase;
use U2\Api\Filter\UqlFilter;
use U2\DataSourcery\DataSource\Configuration\Field;
use U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder\Filterer;
use U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder\JoinAliases;
use U2\DataSourcery\DataType\SearchTextDataType;
use U2\DataSourcery\DataType\StringDataType;
use U2\DataSourcery\Query\Filter;
use U2\DataSourcery\Query\FilterCondition;
use U2\DataSourcery\Query\InvalidFilter;
use U2\DataSourcery\Query\SearchTextFieldHandler;
use U2\DataSourcery\UQL\Interpreter;
use U2\Util\ImmutableCollection;

class UqlFilterTest extends UnitTestCase
{
    public function test_apply_with_uql(): void
    {
        // Given
        $filterer = $this->createMock(Filterer::class);
        $searchTextFieldHandler = $this->createMock(SearchTextFieldHandler::class);
        $interpreter = $this->createMock(Interpreter::class);

        $uqlFilter = new UqlFilter($filterer, $searchTextFieldHandler, $interpreter, [
            'propertyOne' => ['type' => StringDataType::class, 'name' => 'PropertyOne'],
            'propertyTwo' => ['type' => StringDataType::class, 'name' => 'PropertyTwo'],
        ]);
        $queryBuilder = $this->createMock(QueryBuilder::class);
        $queryBuilder
            ->method('getRootAliases')
            ->willReturn(['root_alias']);

        $filter = new Filter(
            [
                new FilterCondition('searchText', FilterCondition::METHOD_STRING_EQ, 'searched text', ''),
                new FilterCondition('PropertyOne', FilterCondition::METHOD_STRING_EQ, 'test', ''),
            ],
            Filter::CONDITION_TYPE_OR);

        $searchTextField = new Field('SearchText', 'search_text', '', new SearchTextDataType());

        $interpreter
            ->method('interpret')
            ->with(
                '(searchText = "searched text") or PropertyOne = "test"',
                self::isInstanceOf(ImmutableCollection::class),
                \stdClass::class
            )
            ->willReturn($filter);

        $searchTextFieldHandler
            ->method('handle')
            ->with($filter, self::containsEqual($searchTextField))
            ->willReturn($filter);

        // Then
        $filterer
            ->expects($this->once())
            ->method('filter')
            ->with(
                $queryBuilder,
                $filter,
                new JoinAliases(
                    'root_alias',
                    [
                        new Field('PropertyOne', 'PropertyOne', '', new StringDataType(), 'propertyOne'),
                        new Field('PropertyTwo', 'PropertyTwo', '', new StringDataType(), 'propertyTwo'),
                    ],
                    []
                )
            )
            ->willReturn($queryBuilder);

        // When
        $uqlFilter->apply(
            $queryBuilder,
            $this->createMock(QueryNameGeneratorInterface::class),
            \stdClass::class,
            new Get('uriTemplate'),
            [
                'filters' => [
                    'page' => 1,
                    'q' => '(searchText = "searched text") or PropertyOne = "test"',
                ],
            ]
        );
    }

    public function test_apply_without_uql(): void
    {
        // Given
        $filterer = $this->createMock(Filterer::class);
        $searchTextFieldHandler = $this->createMock(SearchTextFieldHandler::class);
        $interpreter = $this->createMock(Interpreter::class);

        $uqlFilter = new UqlFilter($filterer, $searchTextFieldHandler, $interpreter, [
            'propertyOne' => ['type' => StringDataType::class, 'name' => 'PropertyOne'],
            'propertyTwo' => ['type' => StringDataType::class, 'name' => 'PropertyTwo'],
        ]);

        // Then
        $filterer
            ->expects($this->never())
            ->method('filter');
        $searchTextFieldHandler
            ->expects($this->never())
            ->method('handle');
        $interpreter
            ->expects($this->never())
            ->method('interpret');

        // When
        $uqlFilter->apply(
            $this->createMock(QueryBuilder::class),
            $this->createMock(QueryNameGeneratorInterface::class),
            \stdClass::class,
            new Get('uriTemplate'),
            [
                'filters' => [
                    'page' => 1,
                ],
            ]
        );
    }

    public function test_apply_with_invalid_uql(): void
    {
        $filterer = $this->createMock(Filterer::class);
        $searchTextFieldHandler = $this->createMock(SearchTextFieldHandler::class);
        $interpreter = $this->createMock(Interpreter::class);

        $uqlFilter = new UqlFilter($filterer, $searchTextFieldHandler, $interpreter, [
            'propertyOne' => ['type' => StringDataType::class, 'name' => 'PropertyOne'],
            'propertyTwo' => ['type' => StringDataType::class, 'name' => 'PropertyTwo'],
        ]);
        $invalidFilter = new InvalidFilter('this uql is invalid', 'error');

        $interpreter
            ->method('interpret')
            ->with(
                'this uql is invalid',
                self::isInstanceOf(ImmutableCollection::class),
                \stdClass::class
            )
            ->willReturn(
                $invalidFilter
            );

        $searchTextFieldHandler
            ->expects($this->once())
            ->method('handle')
            ->with(
                $invalidFilter,
                self::anything()
            )
            ->willReturn($invalidFilter);

        // TODO: Find a way that this can be always be handled within the interpretation so we dont forget it
        // Then
        $this->expectException(BadRequestHttpException::class);

        // When
        $uqlFilter->apply(
            $this->createMock(QueryBuilder::class),
            $this->createMock(QueryNameGeneratorInterface::class),
            \stdClass::class,
            new Get('uriTemplate'),
            [
                'filters' => [
                    'q' => 'this uql is invalid',
                ],
            ]
        );
    }
}
