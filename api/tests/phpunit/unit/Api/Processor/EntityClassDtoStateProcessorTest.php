<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Api\Processor;

use ApiPlatform\Doctrine\Orm\State\Options;
use ApiPlatform\Metadata\DeleteOperationInterface;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use ApiPlatform\Validator\ValidatorInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Symfonycasts\MicroMapper\MicroMapperInterface;
use Tests\U2\UnitTestCase;
use U2\Api\Processor\EntityClassDtoStateProcessor;
use U2\Api\Resource\EntityDto;
use U2\Entity\Interfaces\Entity;

class EntityClassDtoStateProcessorTest extends UnitTestCase
{
    public function test_processes_deletion_of_an_entity(): void
    {
        $persistProcessor = $this->createMock(ProcessorInterface::class);
        $removeProcessor = $this->createMock(ProcessorInterface::class);
        $microMapper = $this->createMock(MicroMapperInterface::class);
        $validator = $this->createMock(ValidatorInterface::class);

        $processor = new EntityClassDtoStateProcessor(
            $persistProcessor,
            $removeProcessor,
            $microMapper,
            $validator
        );

        $entity = $this->createMock(Entity::class);
        $entityDto = $this->createMock(EntityDto::class);

        $stateOptions = new Options(entityClass: $entity::class);

        $operation = $this->createMock(TestDeleteOperation::class);
        $operation->method('getStateOptions')->willReturn($stateOptions);

        $microMapper
            ->expects($this->once())
            ->method('map')
            ->with($entityDto, $entity::class)
            ->willReturn($entity);

        $removeProcessor
            ->expects($this->once())
            ->method('process')
            ->with($entity, $operation, [], []);

        self::assertNull($processor->process($entityDto, $operation));
    }

    public function test_persist_of_an_entity(): void
    {
        $persistProcessor = $this->createMock(ProcessorInterface::class);
        $removeProcessor = $this->createMock(ProcessorInterface::class);
        $microMapper = $this->createMock(MicroMapperInterface::class);

        $validator = $this->createMock(ValidatorInterface::class);
        $validator
            ->expects($this->once())
            ->method('validate');

        $processor = new EntityClassDtoStateProcessor(
            $persistProcessor,
            $removeProcessor,
            $microMapper,
            $validator
        );

        $entity = $this->createMock(Entity::class);
        $entityDto = $this->createMock(EntityDto::class);

        $stateOptions = new Options(entityClass: Entity::class);

        $operation = $this->createMock(Operation::class);
        $operation->method('getStateOptions')->willReturn($stateOptions);

        $microMapper
            ->method('map')
            ->willReturnCallback(
                function ($from, $to) use ($entityDto, $entity): MockObject {
                    if (is_a($to, EntityDto::class, true)) {
                        return $entityDto;
                    }

                    return $entity;
                }
            );

        $persistProcessor
            ->expects($this->once())
            ->method('process')
            ->with($entity, $operation, [], [])
            ->willReturn($entity);

        self::assertSame($entityDto, $processor->process($entityDto, $operation));
    }
}

class TestDeleteOperation extends Operation implements DeleteOperationInterface
{
    public function getMethod(): string
    {
        return 'GET';
    }

    /**
     * @return array<mixed>|null
     */
    public function getExtraProperties(): ?array
    {
        return [];
    }
}
