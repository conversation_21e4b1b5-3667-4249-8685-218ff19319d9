<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Api\ContextProvider;

use ApiPlatform\State\SerializerContextBuilderInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Api\ContextProvider\UserContextBuilder;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Security\UserRoles;
use U2\User\CurrentUserProvider;

class UserContextBuilderTest extends UnitTestCase
{
    public function test_exists_early_for_non_user_resources(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);

        $serializerContextBuilder = $this->createMock(SerializerContextBuilderInterface::class);
        $serializerContextBuilder
            ->expects($this->once())
            ->method('createFromRequest')
            ->with(self::anything())
            ->willReturn([
                'resource_class' => Unit::class,
            ]);

        $userContextBuilder = new UserContextBuilder($serializerContextBuilder, $currentUserProvider, $authorizationChecker);

        self::assertEquals(
            [
                'resource_class' => Unit::class,
            ],
            $userContextBuilder->createFromRequest(new Request(), true)
        );
    }

    public function test_user_group_admin_read_rights_are_attached_if_user_is_user_group_admin(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);

        $user = new User();
        TestUtils::setId($user, 1);

        $serializerContextBuilder = $this->createMock(SerializerContextBuilderInterface::class);
        $serializerContextBuilder
            ->expects($this->once())
            ->method('createFromRequest')
            ->with(self::anything())
            ->willReturn([
                'resource_class' => User::class,
                'groups' => [],
                'operation_type' => 'collection',
            ]);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->expects($this->once())
            ->method('isGranted')
            ->with(UserRoles::UserGroupAdmin->value)
            ->willReturn(true);

        $userContextBuilder = new UserContextBuilder($serializerContextBuilder, $currentUserProvider, $authorizationChecker);

        self::assertEquals(
            [
                'resource_class' => User::class,
                'groups' => ['user-group-admin:read'],
                'operation_type' => 'collection',
            ],
            $userContextBuilder->createFromRequest(new Request(), true)
        );
    }

    public function test_user_group_admin_write_rights_are_attached_if_user_is_user_group_admin(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);

        $user = new User();
        TestUtils::setId($user, 1);

        $serializerContextBuilder = $this->createMock(SerializerContextBuilderInterface::class);
        $serializerContextBuilder
            ->expects($this->once())
            ->method('createFromRequest')
            ->with(self::anything())
            ->willReturn([
                'resource_class' => User::class,
                'groups' => [],
                'operation_type' => 'collection',
            ]);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->expects($this->once())
            ->method('isGranted')
            ->with(UserRoles::UserGroupAdmin->value)
            ->willReturn(true);

        $userContextBuilder = new UserContextBuilder($serializerContextBuilder, $currentUserProvider, $authorizationChecker);

        self::assertEquals(
            [
                'resource_class' => User::class,
                'groups' => ['user-group-admin:write'],
                'operation_type' => 'collection',
            ],
            $userContextBuilder->createFromRequest(new Request(), false)
        );
    }

    public function test_current_user_write_rights_are_attached_if_user_is_current_user(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);

        $user = new User();
        TestUtils::setId($user, 1);

        $currentUserProvider
            ->expects($this->once())
            ->method('get')
            ->willReturn($user);

        $serializerContextBuilder = $this->createMock(SerializerContextBuilderInterface::class);
        $serializerContextBuilder
            ->expects($this->once())
            ->method('createFromRequest')
            ->with(self::anything())
            ->willReturn([
                'resource_class' => User::class,
                'groups' => [],
                'operation_type' => 'item',
            ]);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $userContextBuilder = new UserContextBuilder($serializerContextBuilder, $currentUserProvider, $authorizationChecker);

        self::assertEquals(
            [
                'resource_class' => User::class,
                'groups' => ['current-user:write'],
                'operation_type' => 'item',
            ],
            $userContextBuilder->createFromRequest(new Request(attributes: ['id' => '1']), false)
        );
    }

    public function test_current_user_rights_are_not_attached_if_no_id_param_is_given(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);

        $user = new User();
        TestUtils::setId($user, 1);

        $currentUserProvider
            ->expects($this->never())
            ->method('get');

        $serializerContextBuilder = $this->createMock(SerializerContextBuilderInterface::class);
        $serializerContextBuilder
            ->expects($this->once())
            ->method('createFromRequest')
            ->with(self::anything())
            ->willReturn([
                'resource_class' => User::class,
                'groups' => [],
                'operation_type' => 'collection',
            ]);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $userContextBuilder = new UserContextBuilder($serializerContextBuilder, $currentUserProvider, $authorizationChecker);

        self::assertEquals(
            [
                'resource_class' => User::class,
                'groups' => [],
                'operation_type' => 'collection',
            ],
            $userContextBuilder->createFromRequest(new Request(), false)
        );
    }

    public function test_current_user_read_rights_are_attached_if_user_is_current_user(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);

        $user = new User();
        TestUtils::setId($user, 1);

        $currentUserProvider
            ->expects($this->once())
            ->method('get')
            ->willReturn($user);

        $serializerContextBuilder = $this->createMock(SerializerContextBuilderInterface::class);
        $serializerContextBuilder
            ->expects($this->once())
            ->method('createFromRequest')
            ->with(self::anything())
            ->willReturn([
                'resource_class' => User::class,
                'groups' => [],
                'operation_type' => 'item',
            ]);

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $userContextBuilder = new UserContextBuilder($serializerContextBuilder, $currentUserProvider, $authorizationChecker);

        self::assertEquals(
            [
                'resource_class' => User::class,
                'groups' => ['current-user:read'],
                'operation_type' => 'item',
            ],
            $userContextBuilder->createFromRequest(new Request(attributes: ['id' => '1']), true)
        );
    }
}
