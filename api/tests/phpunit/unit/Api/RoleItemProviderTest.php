<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Api;

use ApiPlatform\Metadata\Operation;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use Tests\U2\UnitTestCase;
use U2\Api\Provider\RoleItemProvider;
use U2\Api\Resource\Role;
use U2\Security\UserRoles;

class RoleItemProviderTest extends UnitTestCase
{
    public function test_provide(): void
    {
        $roleHierarchyInterface = $this->createMock(RoleHierarchyInterface::class);
        $roleItemProvider = new RoleItemProvider($roleHierarchyInterface);

        $operation = $this->createMock(Operation::class);
        $uriVariables = ['name' => UserRoles::User->value];

        $roleHierarchyInterface
            ->expects($this->once())
            ->method('getReachableRoleNames')
            ->willReturn([UserRoles::User->value, UserRoles::Admin->value]);

        $role = $roleItemProvider->provide($operation, $uriVariables);

        self::assertInstanceOf(Role::class, $role);
        self::assertSame(UserRoles::User->value, $role->name);
    }

    public function test_provide_with_invalid_role(): void
    {
        $roleHierarchyInterface = $this->createMock(RoleHierarchyInterface::class);
        $roleItemProvider = new RoleItemProvider($roleHierarchyInterface);

        $this->expectException(NotFoundHttpException::class);
        $this->expectExceptionMessage('Role with name "ROLE_INVALID" not found');

        $operation = $this->createMock(Operation::class);
        $uriVariables = ['name' => 'ROLE_INVALID'];

        $roleHierarchyInterface
            ->expects($this->once())
            ->method('getReachableRoleNames')
            ->willReturn([UserRoles::User->value, UserRoles::Admin->value]);

        $roleItemProvider->provide($operation, $uriVariables);
    }
}
