<?php

declare(strict_types=1);
namespace Tests\Unit\U2\User;

use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\User;
use U2\Exception\Exception;
use U2\User\CurrentUserProvider;

class CurrentUserProviderTest extends UnitTestCase
{
    public function test_get_returns_the_current_user(): void
    {
        $user = $this->createMock(User::class);
        $token = $this->createMock(TokenInterface::class);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn($user);
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $tokenStorage->expects($this->atLeastOnce())->method('getToken')->willReturn($token);
        $currentUserProvider = new CurrentUserProvider($tokenStorage);

        self::assertSame($user, $currentUserProvider->get());
    }

    public function test_get_throws_an_exception_if_there_is_no_user_token(): void
    {
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $tokenStorage->expects($this->atLeastOnce())->method('getToken')->willReturn(null);
        $currentUserProvider = new CurrentUserProvider($tokenStorage);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('There is no current user');

        $currentUserProvider->get();
    }

    public function test_get_throws_an_exception_if_there_is_no_user_in_the_token(): void
    {
        $user = $this->createMock(User::class);
        $token = $this->createMock(TokenInterface::class);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn(null);
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $tokenStorage->expects($this->atLeastOnce())->method('getToken')->willReturn($token);
        $currentUserProvider = new CurrentUserProvider($tokenStorage);

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('There is no current user');

        $currentUserProvider->get();

        self::assertSame($user, $currentUserProvider->get());
    }

    public function test_has_user_returns_true_if_there_is_a_user(): void
    {
        $token = $this->createMock(TokenInterface::class);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn($this->createMock(User::class));
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $tokenStorage->expects($this->atLeastOnce())->method('getToken')->willReturn($token);
        $currentUserProvider = new CurrentUserProvider($tokenStorage);

        self::assertTrue($currentUserProvider->hasUser());
    }

    public function test_has_user_returns_false_if_there_is_a_user(): void
    {
        $token = $this->createMock(TokenInterface::class);
        $token->expects($this->atLeastOnce())->method('getUser')->willReturn(null);
        $tokenStorage = $this->createMock(TokenStorageInterface::class);
        $tokenStorage->expects($this->atLeastOnce())->method('getToken')->willReturn($token);
        $currentUserProvider = new CurrentUserProvider($tokenStorage);

        self::assertFalse($currentUserProvider->hasUser());
    }
}
