<?php

declare(strict_types=1);
namespace Tests\Unit\U2\User;

use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\DataFixtures\Example\ApiKeyFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Email\EmailHelper;
use U2\User\NotifyUserAboutToExpireApiKeysMailer;

class NotifyUserAboutToExpireApiKeysMailerTest extends UnitTestCase
{
    public function test_notify_about_to_expire_api_keys(): void
    {
        // Given
        $translator = $this->createMock(TranslatorInterface::class);
        $translator->method('trans')->willReturnCallback(fn ($id): string => "Translated $id");

        $user = UserFactory::createOne();
        $apiKey = ApiKeyFactory::createOne(['createdBy' => $user]);

        $templateEngine = $this->createMock(Environment::class);
        $templateEngine
            ->method('render')
            ->with(
                'email/api_key_about_to_expire.email.html.twig',
                [
                    'email' => $user->getContact()?->getEmail(),
                    'apiKey' => [
                        'name' => $apiKey->getName(),
                        'expiresOn' => $apiKey->getExpiresAt()?->format('Y-m-d'),
                    ],
                    'locale' => 'DE',
                    'username' => $user->getUsername(),
                ]
            )
            ->willReturn('email content');

        // Then
        $emailHelper = $this->createMock(EmailHelper::class);
        $emailHelper
            ->expects($this->once())
            ->method('sendEmail')
            ->with(
                '<EMAIL>',
                $user->getContact()?->getEmail(),
                'email content',
                'Translated u2.email.api_key.subject.about_to_expire'
            );

        // When
        $mailer = new NotifyUserAboutToExpireApiKeysMailer(
            $emailHelper,
            $translator,
            $templateEngine,
            'DE',
            '<EMAIL>'
        );
        $mailer->__invoke($apiKey->_real());
    }
}
