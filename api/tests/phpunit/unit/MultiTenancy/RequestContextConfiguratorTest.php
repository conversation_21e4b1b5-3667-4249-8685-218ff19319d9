<?php

declare(strict_types=1);
namespace Tests\Unit\U2\MultiTenancy;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Routing\RequestContext;
use Symfony\Component\Routing\RouterInterface;
use Tests\U2\UnitTestCase;
use U2\MultiTenancy\RequestContextConfigurator;
use U2\MultiTenancy\Tenant;

class RequestContextConfiguratorTest extends UnitTestCase
{
    private RequestContextConfigurator $requestContextConfigurator;

    /**
     * @var MockObject&RouterInterface
     */
    private MockObject $router;

    protected function setUp(): void
    {
        $this->router = $this->createMock(RouterInterface::class);
        $this->requestContextConfigurator = new RequestContextConfigurator($this->router);
    }

    public function test_updates_request_context_hostname(): void
    {
        $requestContext = $this->createMock(RequestContext::class);
        $tenant = $this->createMock(Tenant::class);
        $this->router->expects($this->atLeastOnce())->method('getContext')->willReturn($requestContext);
        $tenant->expects($this->atLeastOnce())->method('getHostname')->willReturn('tenant.domain.com');
        $requestContext->expects($this->atLeastOnce())->method('setHost')->with('tenant.domain.com');
        $this->requestContextConfigurator->configure($tenant);
    }
}
