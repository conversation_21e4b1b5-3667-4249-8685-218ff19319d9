<?php

declare(strict_types=1);
namespace Tests\Unit\U2\MultiTenancy;

use Tests\U2\UnitTestCase;
use U2\Exception\TenantException;
use U2\MultiTenancy\CurrentTenantProvider;
use U2\MultiTenancy\Tenant;
use U2\MultiTenancy\TenantRedisAdapter;

class TenantRedisAdapterTest extends UnitTestCase
{
    public function test_initializable_if_multi_tenancy_is_enabled(): void
    {
        $redisClient = $this->createMock(\Redis::class);
        $redisClient->method('getOption')->with(\Redis::OPT_COMPRESSION)->willReturn(\Redis::COMPRESSION_NONE);

        $currentTenantProvider = $this->createMock(CurrentTenantProvider::class);
        $tenant = $this->createMock(Tenant::class);
        $currentTenantProvider->expects($this->atLeastOnce())->method('getCurrentTenant')->willReturn($tenant);
        $tenant->expects($this->atLeastOnce())->method('getName')->willReturn('');

        new TenantRedisAdapter($redisClient, '', 0, $currentTenantProvider);
    }

    public function test_not_initializable_if_multi_tenancy_is_enabled_but_the_tenant_is_not_set(): void
    {
        $redisClient = $this->createMock(\Redis::class);
        $redisClient->method('getOption')->with(\Redis::OPT_COMPRESSION)->willReturn(\Redis::COMPRESSION_NONE);

        $currentTenantProvider = $this->createMock(CurrentTenantProvider::class);
        $currentTenantProvider->expects($this->atLeastOnce())->method('getCurrentTenant')->willReturn(null);
        $this->expectException(TenantException::class);
        new TenantRedisAdapter($redisClient, '', 0, $currentTenantProvider);
    }

    public function test_prefixes_cache_keys_with_tenant_name(): void
    {
        $redisClient = $this->createMock(\Redis::class);
        $redisClient->method('getOption')->with(\Redis::OPT_COMPRESSION)->willReturn(\Redis::COMPRESSION_NONE);

        $currentTenantProvider = $this->createMock(CurrentTenantProvider::class);
        $tenant = $this->createMock(Tenant::class);
        $currentTenantProvider->expects($this->atLeastOnce())->method('getCurrentTenant')->willReturn($tenant);
        $tenant->expects($this->atLeastOnce())->method('getName')->willReturn('tenant');
        $redisClient
            ->method('exists')
            ->willReturnCallback(
                static fn (string $key): bool => match ($key) {
                    'tenant.namespace:existing-key' => true,
                    'tenant.namespace:not-existing-key' => false,
                    default => throw new \Exception('Unexpected key: ' . $key),
                }
            );
        $tenantRedisAdapter = new TenantRedisAdapter($redisClient, 'namespace', 0, $currentTenantProvider);
        self::assertTrue($tenantRedisAdapter->hasItem('existing-key'));
        self::assertFalse($tenantRedisAdapter->hasItem('not-existing-key'));
    }
}
