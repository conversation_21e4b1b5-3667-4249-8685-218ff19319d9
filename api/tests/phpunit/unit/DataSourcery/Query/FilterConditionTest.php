<?php

declare(strict_types=1);
namespace Tests\Unit\U2\DataSourcery\Query;

use Tests\U2\UnitTestCase;
use U2\DataSourcery\Query\FilterCondition;

class FilterConditionTest extends UnitTestCase
{
    public function test_initializable(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_STRING_EQ, 'value', 'valueInDatabase');

        self::assertInstanceOf(FilterCondition::class, $filterCondition);
    }

    public function test_can_generate_uql_for_in_filter_method(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_IN, [1, 2, 3], [1, 2, 3]);

        self::assertSame('columnIdentifier in ["1", "2", "3"]', $filterCondition->getUql());
    }

    public function test_can_generate_uql_for_not_in_filter_method(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_NIN, [1, 2, 3], [1, 2, 3]);

        self::assertSame('columnIdentifier not in ["1", "2", "3"]', $filterCondition->getUql());
    }

    public function test_can_generate_uql_for_boolean_filter_methods(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_BOOLEAN, true, true);

        self::assertSame('columnIdentifier is true', $filterCondition->getUql());
    }

    public function test_can_generate_uql_for_string_filter_methods(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_STRING_EQ, 'Test', 'Test');

        self::assertSame('columnIdentifier = "Test"', $filterCondition->getUql());
    }

    public function test_can_generate_uql_for_numeric_filter_methods(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_NUMERIC_EQ, '5', '5');

        self::assertSame('columnIdentifier = 5', $filterCondition->getUql());
    }

    public function test_can_generate_uql_when_filtering_numeric_fields_by_empty(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_NUMERIC_EQ, null, null);

        self::assertSame('columnIdentifier = EMPTY', $filterCondition->getUql());
    }

    public function test_can_generate_uql_when_filtering_string_fields_by_empty(): void
    {
        $filterCondition = new FilterCondition('columnIdentifier', FilterCondition::METHOD_NUMERIC_EQ, null, null);

        self::assertSame('columnIdentifier = EMPTY', $filterCondition->getUql());
    }
}
