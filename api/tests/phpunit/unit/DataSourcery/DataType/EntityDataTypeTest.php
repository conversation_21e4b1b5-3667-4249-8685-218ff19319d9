<?php

declare(strict_types=1);
namespace Tests\Unit\U2\DataSourcery\DataType;

use Tests\U2\UnitTestCase;
use U2\DataSourcery\DataType\EntityDataType;
use U2\DataSourcery\Query\FilterCondition;

class EntityDataTypeTest extends UnitTestCase
{
    private EntityDataType $dataType;

    protected function setUp(): void
    {
        $this->dataType = new EntityDataType();
    }

    public function test_expected_available_filter_methods(): void
    {
        $expectedAvailableMethods = [
            FilterCondition::METHOD_NUMERIC_EQ,
            FilterCondition::METHOD_NUMERIC_NEQ,
        ];

        self::assertSame($expectedAvailableMethods, $this->dataType->getAvailableFilterMethods());
    }

    public function test_expected_default_filter_method(): void
    {
        $expectedDefaultMethod = FilterCondition::METHOD_NUMERIC_EQ;

        self::assertSame($expectedDefaultMethod, $this->dataType->getDefaultFilterMethod());
    }
}
