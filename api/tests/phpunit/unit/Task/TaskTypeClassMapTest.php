<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Entity\Task\TaskType\Contract;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Task\TaskType\Igt1Transaction;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use U2\Entity\Task\TaskType\TaxAssessmentStatus;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Entity\Task\TaskType\TaxAuthorityAuditObjection;
use U2\Entity\Task\TaskType\TaxConsultingFee;
use U2\Entity\Task\TaskType\TaxCredit;
use U2\Entity\Task\TaskType\TaxFilingMonitor;
use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Entity\Task\TaskType\TaxRate;
use U2\Entity\Task\TaskType\TaxRelevantRestriction;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Task\TaskType\TransferPricing;
use U2\Task\TaskTypeClassMap;

class TaskTypeClassMapTest extends UnitTestCase
{
    #[DataProvider('provideTaskTypes')]
    public function test_get_class(string $taskType, string $expectedIssueClass): void
    {
        self::assertSame(
            $expectedIssueClass,
            TaskTypeClassMap::getClass($taskType)
        );
    }

    /**
     * @return array<string, array<string, string>>
     */
    public static function provideTaskTypes(): array
    {
        return [
            'Ensure class ' . ApmTransaction::class . ' is returned for ' . ApmTransaction::getTaskType() => [
                'taskType' => ApmTransaction::getTaskType(),
                'expectedIssueClass' => ApmTransaction::class,
            ],
            'Ensure class ' . Contract::class . ' is returned for ' . Contract::getTaskType() => [
                'taskType' => Contract::getTaskType(),
                'expectedIssueClass' => Contract::class,
            ],
            'Ensure class ' . FinancialData::class . ' is returned for ' . FinancialData::getTaskType() => [
                'taskType' => FinancialData::getTaskType(),
                'expectedIssueClass' => FinancialData::class,
            ],
            'Ensure class ' . IncomeTaxPlanning::class . ' is returned for ' . IncomeTaxPlanning::getTaskType() => [
                'taskType' => IncomeTaxPlanning::getTaskType(),
                'expectedIssueClass' => IncomeTaxPlanning::class,
            ],
            'Ensure class ' . LossCarryForward::class . ' is returned for ' . LossCarryForward::getTaskType() => [
                'taskType' => LossCarryForward::getTaskType(),
                'expectedIssueClass' => LossCarryForward::class,
            ],
            'Ensure class ' . MainBusinessActivity::class . ' is returned for ' . MainBusinessActivity::getTaskType() => [
                'taskType' => MainBusinessActivity::getTaskType(),
                'expectedIssueClass' => MainBusinessActivity::class,
            ],
            'Ensure class ' . OtherDeadline::class . ' is returned for ' . OtherDeadline::getTaskType() => [
                'taskType' => OtherDeadline::getTaskType(),
                'expectedIssueClass' => OtherDeadline::class,
            ],
            'Ensure class ' . TaxAssessmentMonitor::class . ' is returned for ' . TaxAssessmentMonitor::getTaskType() => [
                'taskType' => TaxAssessmentMonitor::getTaskType(),
                'expectedIssueClass' => TaxAssessmentMonitor::class,
            ],
            'Ensure class ' . TaxAssessmentStatus::class . ' is returned for ' . TaxAssessmentStatus::getTaskType() => [
                'taskType' => TaxAssessmentStatus::getTaskType(),
                'expectedIssueClass' => TaxAssessmentStatus::class,
            ],
            'Ensure class ' . TaxAuditRisk::class . ' is returned for ' . TaxAuditRisk::getTaskType() => [
                'taskType' => TaxAuditRisk::getTaskType(),
                'expectedIssueClass' => TaxAuditRisk::class,
            ],
            'Ensure class ' . TaxAuthorityAuditObjection::class . ' is returned for ' . TaxAuthorityAuditObjection::getTaskType() => [
                'taskType' => TaxAuthorityAuditObjection::getTaskType(),
                'expectedIssueClass' => TaxAuthorityAuditObjection::class,
            ],
            'Ensure class ' . TaxConsultingFee::class . ' is returned for ' . TaxConsultingFee::getTaskType() => [
                'taskType' => TaxConsultingFee::getTaskType(),
                'expectedIssueClass' => TaxConsultingFee::class,
            ],
            'Ensure class ' . TaxCredit::class . ' is returned for ' . TaxCredit::getTaskType() => [
                'taskType' => TaxCredit::getTaskType(),
                'expectedIssueClass' => TaxCredit::class,
            ],
            'Ensure class ' . TaxFilingMonitor::class . ' is returned for ' . TaxFilingMonitor::getTaskType() => [
                'taskType' => TaxFilingMonitor::getTaskType(),
                'expectedIssueClass' => TaxFilingMonitor::class,
            ],
            'Ensure class ' . TaxLitigation::class . ' is returned for ' . TaxLitigation::getTaskType() => [
                'taskType' => TaxLitigation::getTaskType(),
                'expectedIssueClass' => TaxLitigation::class,
            ],
            'Ensure class ' . TaxRate::class . ' is returned for ' . TaxRate::getTaskType() => [
                'taskType' => TaxRate::getTaskType(),
                'expectedIssueClass' => TaxRate::class,
            ],
            'Ensure class ' . TaxRelevantRestriction::class . ' is returned for ' . TaxRelevantRestriction::getTaskType() => [
                'taskType' => TaxRelevantRestriction::getTaskType(),
                'expectedIssueClass' => TaxRelevantRestriction::class,
            ],
            'Ensure class ' . Transaction::class . ' is returned for ' . Transaction::getTaskType() => [
                'taskType' => Transaction::getTaskType(),
                'expectedIssueClass' => Transaction::class,
            ],
            'Ensure class ' . TransferPricing::class . ' is returned for ' . TransferPricing::getTaskType() => [
                'taskType' => TransferPricing::getTaskType(),
                'expectedIssueClass' => TransferPricing::class,
            ],
            'Ensure class ' . Igt1Transaction::class . ' is returned for ' . Igt1Transaction::getTaskType() => [
                'taskType' => Igt1Transaction::getTaskType(),
                'expectedIssueClass' => Igt1Transaction::class,
            ],
            'Ensure class ' . Igt2Transaction::class . ' is returned for ' . Igt2Transaction::getTaskType() => [
                'taskType' => Igt2Transaction::getTaskType(),
                'expectedIssueClass' => Igt2Transaction::class,
            ],
            'Ensure class ' . Igt3Transaction::class . ' is returned for ' . Igt3Transaction::getTaskType() => [
                'taskType' => Igt3Transaction::getTaskType(),
                'expectedIssueClass' => Igt3Transaction::class,
            ],
            'Ensure class ' . Igt4Transaction::class . ' is returned for ' . Igt4Transaction::getTaskType() => [
                'taskType' => Igt4Transaction::getTaskType(),
                'expectedIssueClass' => Igt4Transaction::class,
            ],
            'Ensure class ' . Igt5Transaction::class . ' is returned for ' . Igt5Transaction::getTaskType() => [
                'taskType' => Igt5Transaction::getTaskType(),
                'expectedIssueClass' => Igt5Transaction::class,
            ],
            'Ensure class ' . MasterFile::class . ' is returned for ' . MasterFile::getTaskType() => [
                'taskType' => MasterFile::getTaskType(),
                'expectedIssueClass' => MasterFile::class,
            ],
            'Ensure class ' . CountryByCountryReport::class . ' is returned for ' . CountryByCountryReport::getTaskType() => [
                'taskType' => CountryByCountryReport::getTaskType(),
                'expectedIssueClass' => CountryByCountryReport::class,
            ],
            'Ensure class ' . LocalFile::class . ' is returned for ' . LocalFile::getTaskType() => [
                'taskType' => LocalFile::getTaskType(),
                'expectedIssueClass' => LocalFile::class,
            ],
        ];
    }
}
