<?php

declare(strict_types=1);
namespace Tests\U2\unit\Task;

use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\User;
use U2\Entity\UserPermission;
use U2\Entity\Workflow\Status;
use U2\Task\TaskTypeFactory;
use U2\User\CurrentUserProvider;
use U2\Workflow\WorkflowManager;

class TaskTypeFactoryTest extends UnitTestCase
{
    public function test_create_masterfile_with_defaults_(): void
    {
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $workflowManager = $this->createMock(WorkflowManager::class);
        $status = new Status();
        $workflowManager->method('getInitialStatus')->willReturn($status);
        $factory = new TaskTypeFactory($currentUserProvider, $workflowManager);

        $user = $this->createMock(User::class);
        $currentUserProvider->expects($this->atLeastOnce())->method('hasUser')->willReturn(true);
        $currentUserProvider->expects($this->atLeastOnce())->method('get')->willReturn($user);

        $task = $factory->createWithDefaults(MasterFile::class);
        self::assertInstanceOf(MasterFile::class, $task);
        self::assertSame($status, $task->getStatus());
        $userPermissions = $task->getPermissions()->getUserPermissions();
        self::assertCount(1, $userPermissions);
        $first = $userPermissions->first();
        self::assertInstanceOf(UserPermission::class, $first);
        self::assertSame($user, $first->getUser());
        self::assertSame(141, $first->getMask());
    }
}
