<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Edit;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Form\FormConfigInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\User;
use U2\Task\BulkAction\Edit\FormRecordValidator;

class FormRecordValidatorTest extends UnitTestCase
{
    private FormRecordValidator $formRecordValidator;

    /**
     * @var MockObject&ValidatorInterface
     */
    private MockObject $validator;

    /**
     * @var MockObject&TranslatorInterface
     */
    private MockObject $translator;

    protected function setUp(): void
    {
        $this->validator = $this->createMock(ValidatorInterface::class);
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->formRecordValidator = new FormRecordValidator($this->validator, $this->translator);
    }

    public function test_maps_record_errors_onto_their_form_fields(): void
    {
        $formConfig = $this->createMock(FormConfigInterface::class);
        $form = $this->createMock(FormInterface::class);
        $childForm = $this->createMock(FormInterface::class);
        $subChildForm = $this->createMock(FormInterface::class);
        $user = $this->createMock(User::class);
        $this->translator->expects($this->atLeastOnce())->method('trans')->with(self::anything())->willReturn('error message');
        $this->validator->expects($this->atLeastOnce())->method('validate')->with(self::equalTo($user))->willReturn(
            new ConstraintViolationList(
                [
                    new ConstraintViolation(
                        'message',
                        'message template',
                        [],
                        $user,
                        'dataProperty',
                        'invalid value'
                    ),
                ]
            )
        );
        $form->expects($this->atLeastOnce())->method('getConfig')->willReturn($formConfig);
        $formConfig
            ->expects($this->atLeastOnce())
            ->method('getOption')->with(self::equalTo('error_mapping'))->willReturn(
                [
                    'dataProperty' => 'childFormId.subChildFormId',
                ]
            );
        $form->expects($this->atLeastOnce())->method('get')->with(self::equalTo('childFormId'))->willReturn($childForm);
        $childForm->expects($this->atLeastOnce())->method('get')->with(self::equalTo('subChildFormId'))->willReturn($subChildForm);
        $subChildForm->expects($this->atLeastOnce())->method('addError')->with(new FormError('error message'));
        $this->formRecordValidator->validate($form, [$user]);
    }
}
