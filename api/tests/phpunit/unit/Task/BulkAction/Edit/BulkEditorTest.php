<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Edit;

use Symfony\Component\EventDispatcher\EventDispatcher;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Workflow\Status;
use U2\Exception\BulkEditException;
use U2\Task\BulkAction\Edit\BulkEditor;
use U2\Task\BulkAction\Edit\Change;
use U2\Task\BulkAction\Edit\ChangeSet;

class BulkEditorTest extends UnitTestCase
{
    public function test_bulk_editor(): void
    {
        $changeSet = new ChangeSet([new Change('name', 'here is a value')]);
        $testEntities = [
            new OtherDeadline(new Status()),
            new OtherDeadline(new Status()),
            new OtherDeadline(new Status()),
        ];

        $bulkEditor = new BulkEditor(PropertyAccess::createPropertyAccessor(), new EventDispatcher());
        $bulkEditor->bulkEdit($changeSet, $testEntities);

        foreach ($testEntities as $entity) {
            self::assertSame('here is a value', $entity->getName());
        }
    }

    public function test_bulk_editor_with_invalid_property(): void
    {
        $this->expectException(BulkEditException::class);
        $this->expectExceptionMessage('The property "invalidProperty" does not exist');

        $changeSet = new ChangeSet([new Change('invalidProperty', 'here is a value')]);
        $testEntities = [
            new OtherDeadline(new Status()),
            new OtherDeadline(new Status()),
            new OtherDeadline(new Status()),
        ];

        $bulkEditor = new BulkEditor(PropertyAccess::createPropertyAccessor(), new EventDispatcher());

        try {
            $bulkEditor->bulkEdit($changeSet, $testEntities);
        } catch (\Exception $e) {
            foreach ($testEntities as $entity) {
                self::assertNull($entity->getName());
            }
            throw $e;
        }
    }
}
