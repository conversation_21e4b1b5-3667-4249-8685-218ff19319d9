<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Edit;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Task\BulkAction\Edit\BulkChange;
use U2\Task\BulkAction\Edit\Change;
use U2\Task\BulkAction\Edit\ChangeFactory;
use U2\Task\BulkAction\Edit\ChangeSet;
use U2\Util\EntityPropertyMappingHelper;

class BulkChangeTest extends UnitTestCase
{
    private BulkChange $bulkChange;

    /**
     * @var EntityPropertyMappingHelper&MockObject
     */
    private MockObject $propertyMappingHelper;

    /**
     * @var ChangeFactory&MockObject
     */
    private MockObject $changeFactory;

    protected function setUp(): void
    {
        $this->propertyMappingHelper = $this->createMock(EntityPropertyMappingHelper::class);
        $changeSet = $this->createMock(ChangeSet::class);
        $this->changeFactory = $this->createMock(ChangeFactory::class);
        $this->bulkChange = new BulkChange(OtherDeadline::class, $this->propertyMappingHelper, $changeSet, $this->changeFactory);
    }

    public function test_will_set_entities_for_associative_properties(): void
    {
        $change = $this->createMock(Change::class);
        $testEntity = $this->createMock(\stdClass::class);
        $this->propertyMappingHelper->expects($this->atLeastOnce())->method('isAssociativeProperty')->with(self::equalTo('TestEntity'))->willReturn(true);
        $this->propertyMappingHelper->expects($this->atLeastOnce())
            ->method('findAssociatedEntityByIdentifier')
            ->with(self::equalTo('TestEntity'))
            ->willReturn($testEntity);
        $this->changeFactory->expects($this->atLeastOnce())->method('create')->with(self::equalTo('TestEntity'))->willReturn($change);
        $this->bulkChange->__set('TestEntity', 1);
    }

    public function test_will_set_values_for_not_associative_properties(): void
    {
        $change = $this->createMock(Change::class);
        $this->propertyMappingHelper->expects($this->atLeastOnce())->method('isAssociativeProperty')->with(self::equalTo('test'))->willReturn(false);
        $this->changeFactory->expects($this->atLeastOnce())->method('create')->with(self::equalTo('test'))->willReturn($change);
        $this->propertyMappingHelper->expects($this->never())->method('findAssociatedEntityByIdentifier')->with('test', 1, 'Test');
        $this->bulkChange->__set('test', 1);
    }
}
