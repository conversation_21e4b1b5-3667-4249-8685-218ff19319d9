<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Transition;

use Doctrine\Common\Collections\ArrayCollection;
use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\OtherDeadlineFactory;
use U2\DataFixtures\Example\StatusFactory;
use U2\DataFixtures\Example\TransitionFactory;
use U2\DataFixtures\Example\WorkflowFactory;
use U2\Entity\BulkTransitionSession;
use U2\Task\BulkAction\Transition\BulkTransition;
use U2\Task\BulkAction\Transition\BulkTransitionMapping;
use U2\Workflow\Action\WorkflowTransitionActionExecutor;

class BulkTransitionTest extends UnitTestCase
{
    public function test_perform_transition(): void
    {
        $statusOpen = StatusFactory::createOne(['id' => 1, 'name' => 'Open']);
        $statusClosed = StatusFactory::createOne(['id' => 2, 'name' => 'Closed']);
        $transition = TransitionFactory::createOne(['name' => 'Close', 'originStatus' => $statusOpen, 'destinationStatus' => $statusClosed]);
        $workflow = WorkflowFactory::createOne(['initialStatus' => $statusOpen, 'transitions' => new ArrayCollection([$transition])]);

        $testEntity = OtherDeadlineFactory::createOne(['status' => $statusOpen]);

        $mapping = new BulkTransitionMapping($workflow, $statusOpen, [$testEntity]);
        $mapping->setTransition($transition);

        $bulkTransitionSession = new BulkTransitionSession([$testEntity], [$mapping]);

        $bulkTransition = new BulkTransition($this->createMock(WorkflowTransitionActionExecutor::class));

        $bulkTransition->perform($bulkTransitionSession);

        self::assertSame(1, $bulkTransition->getSuccessCount());
        self::assertSame(0, $bulkTransition->getCannotBeTransitionedCount());
        self::assertEquals($statusClosed->_real(), $testEntity->getStatus(), 'The Entity should have been transitioned.');
    }

    public function test_perform_transition_warns_when_no_transition_is_available(): void
    {
        $statusOpen = StatusFactory::createOne(['id' => 1, 'name' => 'Open']);
        $statusClosed = StatusFactory::createOne(['id' => 2, 'name' => 'Closed']);
        $transition = TransitionFactory::createOne(['name' => 'Close', 'originStatus' => $statusOpen, 'destinationStatus' => $statusClosed]);
        $workflow = WorkflowFactory::createOne(['initialStatus' => $statusOpen, 'transitions' => new ArrayCollection([$transition])]);

        $testEntity = OtherDeadlineFactory::createOne(['status' => $statusOpen]);

        $mapping = new BulkTransitionMapping($workflow, $statusOpen, [$testEntity]);

        $bulkTransitionSession = new BulkTransitionSession([$testEntity], [$mapping]);

        $bulkTransition = new BulkTransition($this->createMock(WorkflowTransitionActionExecutor::class));
        $bulkTransition->perform($bulkTransitionSession);

        self::assertSame(0, $bulkTransition->getSuccessCount());
        self::assertEquals($statusOpen->_real(), $testEntity->getStatus(), 'The Entity should not be transitioned, because there is no transition available.');
    }

    public function test_perform_transition_warns_when_the_status_of_a_mapped_entity_has_been_changed(): void
    {
        $statusOpen = StatusFactory::createOne(['id' => 1, 'name' => 'Open']);
        $statusClosed = StatusFactory::createOne(['id' => 2, 'name' => 'Closed']);
        $statusInProgress = StatusFactory::createOne(['id' => 3, 'name' => 'InProgress']);
        $transition = TransitionFactory::createOne(['name' => 'Close', 'originStatus' => $statusOpen, 'destinationStatus' => $statusClosed]);
        $workflow = WorkflowFactory::createOne(['initialStatus' => $statusOpen, 'transitions' => new ArrayCollection([$transition])]);

        $testEntityWithChangedStatus = OtherDeadlineFactory::createOne(['status' => $statusOpen]);

        $testEntity = OtherDeadlineFactory::createOne(['status' => $statusOpen]);

        $mapping = new BulkTransitionMapping($workflow, $statusOpen, [$testEntityWithChangedStatus, $testEntity]);
        $mapping->setTransition($transition);

        $bulkTransition = new BulkTransition($this->createMock(WorkflowTransitionActionExecutor::class));

        $bulkTransitionSession = new BulkTransitionSession([$testEntityWithChangedStatus, $testEntity], [$mapping]);

        $testEntityWithChangedStatus->setStatus($statusInProgress);

        $bulkTransition->perform($bulkTransitionSession);

        self::assertSame(1, $bulkTransition->getSuccessCount());
        self::assertSame(1, $bulkTransition->getCannotBeTransitionedCount());
        self::assertNotEquals($statusClosed, $testEntityWithChangedStatus->getStatus(), 'The Entity should not be transitioned, because the status has been already changed.');
        self::assertEquals($statusClosed->_real(), $testEntity->getStatus(), 'The Entity should not be transitioned, because the status has been already changed.');
    }
}
