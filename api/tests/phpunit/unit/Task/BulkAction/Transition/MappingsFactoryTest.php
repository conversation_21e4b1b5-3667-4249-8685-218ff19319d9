<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Transition;

use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Workflow\Status;
use U2\Entity\Workflow\Workflow;
use U2\Task\BulkAction\Transition\MappingsFactory;
use U2\Workflow\WorkflowManager;

class MappingsFactoryTest extends UnitTestCase
{
    public function test_creates_empty_list_of_mappings_if_bulk_transition_session_does_not_contain_any_entities(): void
    {
        $workflowManager = $this->createMock(WorkflowManager::class);

        $mappingsFactory = new MappingsFactory($workflowManager);

        $mappings = $mappingsFactory->createMappingsForBulkTransitionSessionEntities([]);
        self::assertCount(0, $mappings);
    }

    public function test_creates_list_of_mappings_for_bulk_transition_session(): void
    {
        $workflowManager = $this->createMock(WorkflowManager::class);

        $status1 = new Status();
        TestUtils::setId($status1, 111);
        $status2 = new Status();
        TestUtils::setId($status2, 222);
        $status3 = new Status();
        TestUtils::setId($status3, 333);

        $entity1 = new OtherDeadline($status1);

        $entity2 = new OtherDeadline($status1);

        $entity3 = new OtherDeadline($status1);

        $entity4 = new OtherDeadline($status2);

        $entity5 = new OtherDeadline($status2);

        $entity6 = new OtherDeadline($status3);

        $workflow = new Workflow();

        $workflowManager
            ->method('getWorkflowForEntity')
            ->with(OtherDeadline::class)
            ->willReturn($workflow);

        $mappingsFactory = new MappingsFactory($workflowManager);

        $entities = [$entity1, $entity2, $entity3, $entity4, $entity5, $entity6];

        $mappings = $mappingsFactory->createMappingsForBulkTransitionSessionEntities($entities);
        self::assertCount(3, $mappings);
        self::assertSame($workflow, $mappings[0]->getWorkflow());
        self::assertSame($workflow, $mappings[1]->getWorkflow());
        self::assertSame($workflow, $mappings[2]->getWorkflow());
        self::assertSame($status1, $mappings[0]->getOriginStatus());
        self::assertSame($status2, $mappings[1]->getOriginStatus());
        self::assertSame($status3, $mappings[2]->getOriginStatus());
        self::assertSame([$entity1, $entity2, $entity3], $mappings[0]->getEntities());
        self::assertSame([$entity4, $entity5], $mappings[1]->getEntities());
        self::assertSame([$entity6], $mappings[2]->getEntities());
    }
}
