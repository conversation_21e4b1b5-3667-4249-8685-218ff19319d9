<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Transition;

use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Workflow\Status;
use U2\Task\BulkAction\Transition\BulkTransitionMapping;
use U2\Task\BulkAction\Transition\MappingsFactory;
use U2\Task\BulkAction\Transition\SessionFactory;

class SessionFactoryTest extends UnitTestCase
{
    public function test_creates_session_from_entities(): void
    {
        $status1 = new Status();
        TestUtils::setId($status1, 111);
        $entity1 = new OtherDeadline($status1);

        $status2 = new Status();
        TestUtils::setId($status2, 222);
        $entity2 = new OtherDeadline($status2);

        $status3 = new Status();
        TestUtils::setId($status3, 333);
        $entity3 = new OtherDeadline($status3);

        $entities = [$entity1, $entity2, $entity3];

        $mappings = [
            $this->createMock(BulkTransitionMapping::class),
            $this->createMock(BulkTransitionMapping::class),
            $this->createMock(BulkTransitionMapping::class),
        ];
        $mappingFactory = $this->createMock(MappingsFactory::class);
        $mappingFactory
            ->expects($this->once())
            ->method('createMappingsForBulkTransitionSessionEntities')
            ->with($entities)
            ->willReturn($mappings);

        $sessionFactory = new SessionFactory($mappingFactory);

        $bulkTransitionSession = $sessionFactory->create($entities);

        self::assertSame($mappings, $bulkTransitionSession->getMappings());
    }
}
