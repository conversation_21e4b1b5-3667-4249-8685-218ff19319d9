<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task\BulkAction\Delete;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Security\Voter\VoterAttributes;
use U2\Task\BulkAction\Delete\BulkDeleter;
use U2\Util\FlashMessageHandler;

class BulkDeleterTest extends UnitTestCase
{
    public function test_deleting_empty_list_of_records(): void
    {
        // Given
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $translator = $this->createMock(TranslatorInterface::class);

        $bulkDeleter = new BulkDeleter(
            $authorizationChecker,
            $entityManager,
            $flashMessageHandler,
            $translator
        );

        $translator
            ->expects($this->once())
            ->method('trans')
            ->with('u2.no_records_selected_delete')
            ->willReturn('No records selected to delete.');

        // Then
        $this->expectExceptionObject(new HttpException(Response::HTTP_BAD_REQUEST, 'No records selected to delete.'));

        $entityManager
            ->expects($this->never())
            ->method('flush');

        // When
        $bulkDeleter->deleteById(OtherDeadline::class, []);
    }

    public function test_delete_record_without_permission(): void
    {
        // Given
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $translator = $this->createMock(TranslatorInterface::class);

        $bulkDeleter = new BulkDeleter(
            $authorizationChecker,
            $entityManager,
            $flashMessageHandler,
            $translator
        );

        $record = new \stdClass();
        $recordId = 1;

        $repository = $this->createMock(EntityRepository::class);
        $repository
            ->method('findBy')
            ->with(['id' => [$recordId]])
            ->willReturn([$record]);

        $entityManager
            ->method('getRepository')
            ->with(OtherDeadline::class)
            ->willReturn($repository);
        $authorizationChecker
            ->method('isGranted')
            ->with(VoterAttributes::delete, $record)
            ->willReturn(false);

        $translator
            ->expects($this->once())
            ->method('trans')
            ->with('u2_core.bulk_delete.error_no_permission')
            ->willReturn('No permission.');

        // Then
        $this->expectExceptionObject(new HttpException(Response::HTTP_BAD_REQUEST, 'No permission.'));

        $entityManager
            ->expects($this->never())
            ->method('remove')
            ->with($record);

        $entityManager
            ->expects($this->never())
            ->method('flush');

        // When
        $bulkDeleter->deleteById(OtherDeadline::class, [$recordId]);
    }

    public function test_delete_records(): void
    {
        // Given
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $translator = $this->createMock(TranslatorInterface::class);

        $bulkDeleter = new BulkDeleter(
            $authorizationChecker,
            $entityManager,
            $flashMessageHandler,
            $translator
        );

        $recordOne = new \stdClass();
        $recordOne->id = 1;
        $recordOneId = 1;
        $recordTwo = new \stdClass();
        $recordTwoId = 2;
        $recordTwo->id = 2;

        $repository = $this->createMock(EntityRepository::class);
        $repository
            ->method('findBy')
            ->with(['id' => [$recordOneId, $recordTwoId]])
            ->willReturn([$recordOne, $recordTwo]);

        $entityManager
            ->method('getRepository')
            ->with(OtherDeadline::class)
            ->willReturn($repository);

        $authorizationChecker
            ->method('isGranted')
            ->with(VoterAttributes::delete, self::callback(static fn (\stdClass $value): true => match ($value->id) {
                1, 2 => true,
                default => throw new \Exception('Unexpected value ' . $value->id),
            }))
            ->willReturn(true);

        // Then
        $translator
            ->expects($this->once())
            ->method('trans')
            ->with('u2_core.bulk_delete.success_for_given_amount_of_entries', ['%count%' => 2])
            ->willReturn('Entries removed successfully.');
        $flashMessageHandler
            ->expects($this->once())
            ->method('addSuccess')
            ->with('Entries removed successfully.');

        $entityManager
            ->expects($this->atLeast(2))
            ->method('remove')
            ->with(self::callback(static fn ($value): true => match ($value->id) {
                1, 2 => true,
                default => throw new \Exception('Unexpected value ' . $value->id),
            }));

        // When
        $bulkDeleter->deleteById(OtherDeadline::class, [$recordOneId, $recordTwoId]);
    }
}
