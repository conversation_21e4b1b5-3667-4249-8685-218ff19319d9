<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task;

use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Entity\Task\TaskType\Igt1Transaction;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Task\TaskTypeFieldConfigMap;

class TaskTypeFieldConfigMapTest extends UnitTestCase
{
    public function test_all_form_field_adders_exist(): void
    {
        foreach (TaskTypeFieldConfigMap::igtApmPropertiesToConfigurationMap as $fieldName => $config) {
            $class = \sprintf("\U2\Form\Type\FieldAdder\%sFieldAdder", ucfirst($fieldName));
            self::assertTrue(class_exists($class), \sprintf('The class "%s" does not exist', $class));
        }
    }

    public function test_all_table_column_adders_exist(): void
    {
        $moneyFields = $this->getMoneyFields();
        foreach (TaskTypeFieldConfigMap::igtApmPropertiesToConfigurationMap as $fieldName => $config) {
            if (\in_array($fieldName, $moneyFields, true)) {
                continue;
            }
            $class = \sprintf("\U2\Task\TableType\%sColumnAdder", ucfirst($fieldName));
            self::assertTrue(class_exists($class), \sprintf('The column adder "%s" does not exist', $class));
        }
    }

    public function test_all_datasource_field_adders_exist(): void
    {
        $moneyFields = $this->getMoneyFields();
        foreach (TaskTypeFieldConfigMap::igtApmPropertiesToConfigurationMap as $fieldName => $config) {
            if (\in_array($fieldName, $moneyFields, true)) {
                continue;
            }
            $class = \sprintf("\U2\Task\DataSource\%sFieldAdder", ucfirst($fieldName));
            self::assertTrue(class_exists($class), \sprintf('The column adder "%s" does not exist', $class));
        }
    }

    /**
     * @return array<int, string>
     */
    private function getMoneyFields(): array
    {
        return array_unique(
            array_merge(
                ApmTransaction::getLinkedBaseLocalGroupMoneyFields(),
                Igt1Transaction::getLinkedBaseLocalGroupMoneyFields(),
                Igt2Transaction::getLinkedBaseLocalGroupMoneyFields(),
                Igt3Transaction::getLinkedBaseLocalGroupMoneyFields(),
                Igt4Transaction::getLinkedBaseLocalGroupMoneyFields(),
                Igt5Transaction::getLinkedBaseLocalGroupMoneyFields(),
            )
        );
    }
}
