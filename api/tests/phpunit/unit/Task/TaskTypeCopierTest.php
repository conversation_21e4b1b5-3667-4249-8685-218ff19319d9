<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Task\TaskTypeCopier;
use U2\Workflow\WorkflowManager;

class TaskTypeCopierTest extends UnitTestCase
{
    private TaskTypeCopier $taskTypeCopier;

    /**
     * @var WorkflowManager&MockObject
     */
    private MockObject $workflowManager;

    public function test_copies_a_workflowable_document_and_sets_its_initial_status(): void
    {
        $initialStatus = new Status();
        $document = new OtherDeadline($initialStatus);
        TestUtils::setId($document, 1);
        TestUtils::setProperty($document->getTask(), 'createdBy', new User());
        $this->workflowManager
            ->expects($this->atLeastOnce())
            ->method('getInitialStatus')
            ->with(OtherDeadline::class)
            ->willReturn($initialStatus);

        $copiedDocument = $this->taskTypeCopier->copy($document);
        \assert($copiedDocument instanceof OtherDeadline);

        $status = $copiedDocument->getStatus();
        self::assertSame($initialStatus, $status);
    }

    protected function setUp(): void
    {
        $this->workflowManager = $this->createMock(WorkflowManager::class);
        $this->taskTypeCopier = new TaskTypeCopier($this->workflowManager);
    }
}
