<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Task;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Workflow\Binding;
use U2\Entity\Workflow\FieldConfiguration;
use U2\Entity\Workflow\FieldConfigurationStatuses;
use U2\Entity\Workflow\FieldState;
use U2\Entity\Workflow\Status;
use U2\Entity\Workflow\Workflow;
use U2\Repository\WorkflowBindingRepository;
use U2\Task\FieldStateResolver;

class FieldStateResolverTest extends UnitTestCase
{
    private FieldStateResolver $fieldStateResolver;

    /**
     * @var WorkflowBindingRepository&MockObject
     */
    private MockObject $workflowBindingRepository;

    private OtherDeadline $taskType;

    private Workflow $workflow;

    protected function setUp(): void
    {
        $this->taskType = new OtherDeadline(new Status());
        $this->workflow = new Workflow();

        $this->workflowBindingRepository = $this->createMock(WorkflowBindingRepository::class);

        $this->fieldStateResolver = new FieldStateResolver(
            $this->workflowBindingRepository
        );
    }

    public function test_is_hidden(): void
    {
        $workflowBinding = new Binding('bindingId', $this->workflow);
        $workflowBinding->setWorkflow($this->workflow);
        $this->workflowBindingRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['bindingId' => $this->taskType::getWorkflowBindingId()])
            ->willReturn($workflowBinding);

        $statusOpen = new Status();
        $this->taskType->setStatus($statusOpen);

        $fieldConfiguration = new FieldConfiguration();

        new FieldState('deadlineType', FieldState::hidden, $fieldConfiguration);

        $fieldConfigurationStatuses = new FieldConfigurationStatuses($this->workflow, [$statusOpen], $fieldConfiguration);

        $this->workflow->addFieldConfigurationStatuses($fieldConfigurationStatuses);

        self::assertTrue($this->fieldStateResolver->isHidden('deadlineType', $this->taskType));
    }

    public function test_is_hidden_will_return_false_if_the_field_is_disabled(): void
    {
        $workflowBinding = new Binding('bindingId', $this->workflow);
        $workflowBinding->setWorkflow($this->workflow);
        $this->workflowBindingRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['bindingId' => $this->taskType::getWorkflowBindingId()])
            ->willReturn($workflowBinding);

        $statusOpen = new Status();
        $this->taskType->setStatus($statusOpen);

        $fieldConfiguration = new FieldConfiguration();

        new FieldState('deadlineType', FieldState::disabled, $fieldConfiguration);

        $fieldConfigurationStatuses = new FieldConfigurationStatuses($this->workflow, [$statusOpen], $fieldConfiguration);

        $this->workflow->addFieldConfigurationStatuses($fieldConfigurationStatuses);

        self::assertFalse($this->fieldStateResolver->isHidden('deadlineType', $this->taskType));
    }

    public function test_is_hidden_will_return_false_if_the_field_is_visible(): void
    {
        $workflowBinding = new Binding('bindingId', $this->workflow);
        $workflowBinding->setWorkflow($this->workflow);
        $this->workflowBindingRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['bindingId' => $this->taskType::getWorkflowBindingId()])
            ->willReturn($workflowBinding);

        $statusOpen = new Status();
        $this->taskType->setStatus($statusOpen);

        self::assertFalse($this->fieldStateResolver->isHidden('deadlineType', $this->taskType));
    }

    public function test_is_disabled(): void
    {
        $workflowBinding = new Binding('bindingId', $this->workflow);
        $workflowBinding->setWorkflow($this->workflow);
        $this->workflowBindingRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['bindingId' => $this->taskType::getWorkflowBindingId()])
            ->willReturn($workflowBinding);

        $statusOpen = new Status();
        $this->taskType->setStatus($statusOpen);

        $fieldConfiguration = new FieldConfiguration();

        new FieldState('deadlineType', FieldState::disabled, $fieldConfiguration);

        $fieldConfigurationStatuses = new FieldConfigurationStatuses($this->workflow, [$statusOpen], $fieldConfiguration);

        $this->workflow->addFieldConfigurationStatuses($fieldConfigurationStatuses);

        self::assertTrue($this->fieldStateResolver->isDisabled('deadlineType', $this->taskType));
    }

    public function test_is_disabled_will_return_false_if_the_field_is_hidden(): void
    {
        $workflowBinding = new Binding('bindingId', $this->workflow);
        $workflowBinding->setWorkflow($this->workflow);
        $this->workflowBindingRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['bindingId' => $this->taskType::getWorkflowBindingId()])
            ->willReturn($workflowBinding);

        $statusOpen = new Status();
        $this->taskType->setStatus($statusOpen);

        $fieldConfiguration = new FieldConfiguration();

        new FieldState('deadlineType', FieldState::hidden, $fieldConfiguration);

        $fieldConfigurationStatuses = new FieldConfigurationStatuses($this->workflow, [$statusOpen], $fieldConfiguration);

        $this->workflow->addFieldConfigurationStatuses($fieldConfigurationStatuses);

        self::assertFalse($this->fieldStateResolver->isDisabled('deadlineType', $this->taskType));
    }

    public function test_is_disabled_will_return_false_if_the_field_is_visible(): void
    {
        $workflowBinding = new Binding('bindingId', $this->workflow);
        $workflowBinding->setWorkflow($this->workflow);
        $this->workflowBindingRepository
            ->expects($this->once())
            ->method('findOneBy')
            ->with(['bindingId' => $this->taskType::getWorkflowBindingId()])
            ->willReturn($workflowBinding);

        $statusOpen = new Status();
        $this->taskType->setStatus($statusOpen);

        self::assertFalse($this->fieldStateResolver->isDisabled('deadlineType', $this->taskType));
    }

    public function test_is_disabled_will_return_false_if_no_workflow_binding_exists_for_the_giving_task_type(): void
    {
        self::assertFalse($this->fieldStateResolver->isDisabled('deadlineType', new Transaction(new Status())));
    }

    public function test_is_hidden_will_return_false_if_no_workflow_binding_exists_for_the_giving_task_type(): void
    {
        self::assertFalse($this->fieldStateResolver->isDisabled('deadlineType', new Transaction(new Status())));
    }
}
