<?php

declare(strict_types=1);
namespace Tests\Unit\U2\SystemSettings;

use Doctrine\DBAL\Exception\SchemaDoesNotExist;
use Doctrine\ORM\EntityManagerInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\SystemSetting;
use U2\Entity\SystemSettingName;
use U2\Repository\SystemSettingRepository;
use U2\SystemSettings\SystemSettings;

class SystemSettingsTest extends UnitTestCase
{
    public function test_gets_current_system_settings(): void
    {
        $systemSettingRepository = $this->createMock(SystemSettingRepository::class);
        $systemSettings = new SystemSettings(
            $this->createMock(EntityManagerInterface::class),
            $systemSettingRepository,
            [
                'u2.locale' => 'en',
            ]
        );

        $systemSettingRepository->expects($this->atLeastOnce())->method('findAll')->willReturn([
            new SystemSetting(SystemSettingName::U2Locale, 'value'),
        ]);
        self::assertSame('value', $systemSettings->get(SystemSettingName::U2Locale));
    }

    public function test_initializes_system_settings_only_once(): void
    {
        $systemSettingRepository = $this->createMock(SystemSettingRepository::class);
        $systemSettings = new SystemSettings(
            $this->createMock(EntityManagerInterface::class),
            $systemSettingRepository,
            [
                'u2.locale' => 'EUR',
                'application_currency' => 'EUR',
            ]
        );

        $systemSettingRepository
            ->expects($this->once())
            ->method('findAll')
            ->willReturn(
                [
                    new SystemSetting(SystemSettingName::U2Locale, 'value 1'),
                    new SystemSetting(SystemSettingName::ApplicationCurrency, 'value 2'),
                ]
            );
        self::assertSame('value 1', $systemSettings->get(SystemSettingName::U2Locale));
        self::assertSame('value 2', $systemSettings->get(SystemSettingName::ApplicationCurrency));
    }

    public function test_gets_settings_from_the_default_system_settings(): void
    {
        $systemSettingRepository = $this->createMock(SystemSettingRepository::class);
        $systemSettings = new SystemSettings(
            $this->createMock(EntityManagerInterface::class),
            $systemSettingRepository,
            [
                SystemSettingName::U2Locale->value => 'value',
            ]
        );

        $systemSettingRepository->expects($this->atLeastOnce())->method('findAll')->willReturn([]);
        self::assertSame('value', $systemSettings->get(SystemSettingName::U2Locale));
    }

    public function test_get_default_setting_if_current_is_null(): void
    {
        $systemSettingRepository = $this->createMock(SystemSettingRepository::class);
        $systemSettings = new SystemSettings(
            $this->createMock(EntityManagerInterface::class),
            $systemSettingRepository,
            [
                SystemSettingName::U2Locale->value => 'value',
            ]
        );

        $systemSettingRepository->expects($this->atLeastOnce())->method('findAll')->willReturn([
            new SystemSetting(SystemSettingName::U2Locale, null),
        ]);
        self::assertSame('value', $systemSettings->get(SystemSettingName::U2Locale));
    }

    public function test_returns_the_default_if_the_setting_is_not_set(): void
    {
        $systemSettingRepository = $this->createMock(SystemSettingRepository::class);
        $systemSettings = new SystemSettings(
            $this->createMock(EntityManagerInterface::class),
            $systemSettingRepository,
            [
                'u2.locale' => 'en',
            ]
        );

        $systemSettingRepository->expects($this->atLeastOnce())->method('findAll')->willReturn([]);
        self::assertSame('en', $systemSettings->get(SystemSettingName::U2Locale));
    }

    public function test_falls_back_to_default_system_settings_if_the_database_is_not_ready(): void
    {
        $systemSettingRepository = $this->createMock(SystemSettingRepository::class);
        $systemSettings = new SystemSettings(
            $this->createMock(EntityManagerInterface::class),
            $systemSettingRepository,
            [
                SystemSettingName::U2Locale->value => 'value',
            ]
        );
        $systemSettingRepository->method('findAll')->willThrowException($this->createMock(SchemaDoesNotExist::class));
        self::assertSame('value', $systemSettings->get(SystemSettingName::U2Locale));
    }
}
