<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Http;

use Tests\U2\UnitTestCase;
use U2\Http\DownloadFilenameSanitizer;

class DownloadFilenameSanitizerTest extends UnitTestCase
{
    private DownloadFilenameSanitizer $downloadFilenameSanitizer;

    public function test_sanitizes_filename(): void
    {
        self::assertSame('filename.txt', $this->downloadFilenameSanitizer->sanitize('filename.txt'));
        self::assertSame('file-name.txt', $this->downloadFilenameSanitizer->sanitize('file/name.txt'));
        self::assertSame('file-name.txt', $this->downloadFilenameSanitizer->sanitize('file\\name.txt'));
        self::assertSame('file---name.txt', $this->downloadFilenameSanitizer->sanitize('file/\\/name.txt'));
    }

    public function test_sanitizes_filename_fallback(): void
    {
        self::assertSame('filename.txt', $this->downloadFilenameSanitizer->sanitizeFallback('filename.txt'));
        self::assertSame('file-name.txt', $this->downloadFilenameSanitizer->sanitizeFallback('file/name.txt'));
        self::assertSame('file-name.txt', $this->downloadFilenameSanitizer->sanitizeFallback('file\\name.txt'));
        self::assertSame('file-name.txt', $this->downloadFilenameSanitizer->sanitizeFallback('file%name.txt'));
        self::assertSame('file--name--.txt', $this->downloadFilenameSanitizer->sanitizeFallback('fileąęnameäß.txt'));
    }

    protected function setUp(): void
    {
        $this->downloadFilenameSanitizer = new DownloadFilenameSanitizer();
    }
}
