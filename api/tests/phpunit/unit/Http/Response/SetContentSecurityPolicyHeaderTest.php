<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Http\Response;

use Symfony\Component\HttpFoundation\Response;
use Tests\U2\UnitTestCase;
use U2\Http\Response\SetContentSecurityPolicyHeader;

class SetContentSecurityPolicyHeaderTest extends UnitTestCase
{
    public function test_set_content_security_policy(): void
    {
        // Given
        $setContentSecurityPolicyHeader = new SetContentSecurityPolicyHeader();

        // When
        $setContentSecurityPolicyHeader(
            $response = new Response()
        );

        // Then
        self::assertSame(
            "base-uri 'none'; object-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com; style-src 'self' 'unsafe-inline';",
            $response->headers->get('Content-Security-Policy')
        );
    }
}
