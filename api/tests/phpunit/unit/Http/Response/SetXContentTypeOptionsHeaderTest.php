<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Http\Response;

use Symfony\Component\HttpFoundation\Response;
use Tests\U2\UnitTestCase;
use U2\Http\Response\SetXContentTypeOptionsHeader;

class SetXContentTypeOptionsHeaderTest extends UnitTestCase
{
    public function test_set_x_content_type_options_header(): void
    {
        // When
        (new SetXContentTypeOptionsHeader())(
            $response = new Response()
        );

        // Then
        self::assertSame('text/html', $response->headers->get('X-Content-Type-Options'));
    }
}
