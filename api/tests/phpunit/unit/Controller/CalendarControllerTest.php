<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Calendar\AggregateCalendar;
use U2\Calendar\CalendarEntry;
use U2\Controller\CalendarController;
use U2\TaxCompliance\OtherDeadline\OtherDeadlineCalendar;
use U2\Util\Collection;

class CalendarControllerTest extends UnitTestCase
{
    private CalendarController $calendarController;

    /**
     * @var AggregateCalendar&MockObject
     */
    private MockObject $aggregateCalendar;

    protected function setUp(): void
    {
        $this->aggregateCalendar = $this->createMock(AggregateCalendar::class);
        $this->createMock(Environment::class);
        $this->calendarController = new CalendarController($this->aggregateCalendar, $this->createMock(TranslatorInterface::class));
    }

    public function test_gets_the_next_three_days_with_task_from_now_to_up_an_month(): void
    {
        $entryOne = new CalendarEntry(new \DateTime(), new \DateTime(), '', '', '', $this->createMock(OtherDeadlineCalendar::class), false, 1, 1, 1);
        $entryTwo = new CalendarEntry(new \DateTime(), new \DateTime(), '', '', '', $this->createMock(OtherDeadlineCalendar::class), false, 1, 1, 1);
        $entryThree = new CalendarEntry(new \DateTime(), new \DateTime(), '', '', '', $this->createMock(OtherDeadlineCalendar::class), false, 1, 1, 1);
        $entryFour = new CalendarEntry(new \DateTime(), new \DateTime(), '', '', '', $this->createMock(OtherDeadlineCalendar::class), false, 1, 1, 1);
        $entryFive = new CalendarEntry(new \DateTime(), new \DateTime(), '', '', '', $this->createMock(OtherDeadlineCalendar::class), false, 1, 1, 1);
        $upNext = [
            '1970-1-1' => [
                $entryOne,
                $entryTwo,
            ],
            '1970-1-2' => [],
            '1970-1-3' => [
                $entryThree,
            ],
            '1970-1-4' => [],
            '1970-1-5' => [
                $entryFour,
                $entryFive,
            ],
            '1970-1-6' => [],
        ];
        $this->aggregateCalendar->expects($this->atLeastOnce())
            ->method('getEntriesByDay')->with(self::isInstanceOf(\DateTime::class))->willReturn(new Collection($upNext));
        self::assertEquals(
            json_encode([
                '1970-1-1' => [
                    $entryOne,
                    $entryTwo,
                ],
                '1970-1-3' => [
                    $entryThree,
                ],
                '1970-1-5' => [
                    $entryFour,
                    $entryFive,
                ],
            ], \JSON_THROW_ON_ERROR),
            $this->calendarController->upNext($this->aggregateCalendar)->getContent()
        );
    }
}
