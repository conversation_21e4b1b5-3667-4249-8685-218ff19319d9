<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller;

use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Tests\U2\UnitTestCase;
use Twig\Environment;
use U2\Controller\DatasheetUnitViewXls;
use U2\Controller\Helper;
use U2\Datasheets\TemplateProvider;
use U2\Datasheets\View\Unit\UnitView;
use U2\Datasheets\View\Unit\UnitViewFactory;
use U2\Entity\Datasheet;
use U2\Entity\Period;
use U2\Entity\Unit;
use U2\Form\Type\UnitViewFormType;
use U2\Repository\DatasheetRepository;
use U2\Repository\PeriodRepository;
use U2\Repository\UnitRepository;
use U2\Security\Voter\VoterAttributes;

class DatasheetUnitViewXlsTest extends UnitTestCase
{
    public function test_does_not_export_the_current_view_for_invalid_parameters(): void
    {
        $invalidLayoutId = 1;
        $datasheetRepository = $this->createMock(DatasheetRepository::class);
        $datasheetRepository->method('find')->with($invalidLayoutId)->willReturn(null);
        $periodId = 2;
        $period = $this->createMock(Period::class);
        $period->method('getId')->willReturn($periodId);
        $periodRepository = $this->createMock(PeriodRepository::class);
        $periodRepository->method('find')->with($periodId)->willReturn($period);
        $unitId = 3;
        $unit = $this->createMock(Unit::class);
        $unit->method('getId')->willReturn($unitId);
        $unitRepository = $this->createMock(UnitRepository::class);
        $unitRepository->method('find')->with($unitId)->willReturn($unit);
        $unitViewXlsAction = new DatasheetUnitViewXls(
            $this->createMock(Environment::class),
            $this->createMock(UnitViewFactory::class),
            $this->createMock(FormFactoryInterface::class),
            $this->createMock(Helper::class),
            $this->createMock(TemplateProvider::class),
            $datasheetRepository,
            $periodRepository,
            $unitRepository,
        );

        $this->expectException(UnprocessableEntityHttpException::class);
        $this->expectExceptionMessage('Invalid datasheet provided');

        $unitViewXlsAction->__invoke($invalidLayoutId, $periodId, $unitId);
    }

    public function test_denies_access_if_user_does_not_have_read_access_to_the_view(): void
    {
        $layoutId = 1;
        $layout = $this->createMock(Datasheet::class);
        $datasheetRepository = $this->createMock(DatasheetRepository::class);
        $datasheetRepository->method('find')->with($layoutId)->willReturn($layout);
        $periodId = 2;
        $period = $this->createMock(Period::class);
        $period->method('getId')->willReturn($periodId);
        $periodRepository = $this->createMock(PeriodRepository::class);
        $periodRepository->method('find')->with($periodId)->willReturn($period);
        $unitId = 3;
        $unit = $this->createMock(Unit::class);
        $unit->method('getId')->willReturn($unitId);
        $unitRepository = $this->createMock(UnitRepository::class);
        $unitRepository->method('find')->with($unitId)->willReturn($unit);
        $unitViewFactory = $this->createMock(UnitViewFactory::class);
        $unitViewFactory->expects($this->atLeastOnce())->method('create')->with($layout, $unit, $period)->willReturn($this->createMock(UnitView::class));
        $helper = $this->createMock(Helper::class);
        $helper->method('denyAccessUnlessGranted')->with(VoterAttributes::read, $this->createMock(UnitView::class))->willThrowException(new AccessDeniedException());
        $unitViewXlsAction = new DatasheetUnitViewXls(
            $this->createMock(Environment::class),
            $unitViewFactory,
            $this->createMock(FormFactoryInterface::class),
            $helper,
            $this->createMock(TemplateProvider::class),
            $datasheetRepository,
            $periodRepository,
            $unitRepository,
        );
        $this->expectException(AccessDeniedException::class);

        $unitViewXlsAction->__invoke($layoutId, $periodId, $unitId);
    }

    public function test_exports_the_current_view(): void
    {
        $layoutId = 1;
        $layout = $this->createMock(Datasheet::class);
        $datasheetRepository = $this->createMock(DatasheetRepository::class);
        $datasheetRepository->method('find')->with($layoutId)->willReturn($layout);
        $periodId = 2;
        $period = $this->createMock(Period::class);
        $period->method('getId')->willReturn($periodId);
        $periodRepository = $this->createMock(PeriodRepository::class);
        $periodRepository->method('find')->with($periodId)->willReturn($period);
        $unitId = 3;
        $unit = $this->createMock(Unit::class);
        $unit->method('getId')->willReturn($unitId);
        $unitRepository = $this->createMock(UnitRepository::class);
        $unitRepository->method('find')->with($unitId)->willReturn($unit);
        $formView = $this->createMock(FormView::class);
        $unitViewFactory = $this->createMock(UnitViewFactory::class);
        $unitViewFactory->expects($this->atLeastOnce())->method('create')->with($layout, $unit, $period)->willReturn($this->createMock(UnitView::class));
        $form = $this->createMock(FormInterface::class);
        $form->expects($this->atLeastOnce())->method('createView')->willReturn($formView);
        $formFactory = $this->createMock(FormFactoryInterface::class);
        $formFactory->expects($this->atLeastOnce())->method('create')->with(UnitViewFormType::class)->willReturn($form);
        $layoutTemplateProvider = $this->createMock(TemplateProvider::class);
        $layoutTemplateProvider->expects($this->atLeastOnce())->method('getContent')->with($layout)->willReturn('layout template content');
        $environment = $this->createMock(Environment::class);
        $environment->expects($this->atLeastOnce())
            ->method('render')
            ->with('datasheet/unit_view_excel_export.html.twig')
            ->willReturn('content');
        $helper = $this->createMock(Helper::class);
        $helper->expects($this->atLeastOnce())->method('denyAccessUnlessGranted')->with(VoterAttributes::read, $this->createMock(UnitView::class));
        $unitViewXlsAction = new DatasheetUnitViewXls(
            $environment,
            $unitViewFactory,
            $formFactory,
            $helper,
            $layoutTemplateProvider,
            $datasheetRepository,
            $periodRepository,
            $unitRepository,
        );

        $response = $unitViewXlsAction->__invoke($layoutId, $periodId, $unitId);

        self::assertEquals(
            new StreamedResponse(
                static function () use ($response): void {
                    echo $response->getContent();
                }, Response::HTTP_OK,
                [
                    'Content-Type' => 'application/vnd.ms-excel',
                ]),
            $response
        );
    }
}
