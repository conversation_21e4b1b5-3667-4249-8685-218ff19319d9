<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Controller\ConfigurationData;

use Knp\Menu\ItemInterface;
use Knp\Menu\Provider\MenuProviderInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Tests\U2\UnitTestCase;
use U2\Controller\ConfigurationData\Sidebar;

class SidebarTest extends UnitTestCase
{
    public function test_gets_the_main_as_json(): void
    {
        $menuProvider = $this->createMock(MenuProviderInterface::class);
        $menuItem = $this->createMock(ItemInterface::class);
        $menuProvider->method('get')->willReturn($menuItem);

        $serializer = $this->createMock(SerializerInterface::class);
        $serializer->method('serialize')->with($menuItem)->willReturn('renderedContent');

        $sidebar = new Sidebar(
            $menuProvider,
            $serializer
        );
        self::assertEquals(
            'renderedContent',
            $sidebar()->getContent()
        );
    }
}
