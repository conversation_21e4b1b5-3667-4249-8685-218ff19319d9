<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Tests\U2\UnitTestCase;
use U2\Util\ClassInspector;

trait ClassInspectorTestTraitC
{
}

trait ClassInspectorTestTraitB
{
    use ClassInspectorTestTraitC;
}

trait ClassInspectorTestTraitA
{
    use ClassInspectorTestTraitB;
}

class ClassInspectorTest extends UnitTestCase
{
    public function test_get_used_traits(): void
    {
        self::assertEquals(
            [
                ClassInspectorTestTraitA::class => ClassInspectorTestTraitA::class,
                ClassInspectorTestTraitB::class => ClassInspectorTestTraitB::class,
                ClassInspectorTestTraitC::class => ClassInspectorTestTraitC::class,
            ],
            ClassInspector::getUsedTraits(ClassInspectorTestClass::class)
        );
    }

    public function test_has_trait(): void
    {
        self::assertTrue(ClassInspector::usesTrait(ClassInspectorTestClass::class, ClassInspectorTestTraitA::class));
        self::assertTrue(ClassInspector::usesTrait(ClassInspectorTestClass::class, ClassInspectorTestTraitB::class));
        self::assertTrue(ClassInspector::usesTrait(ClassInspectorTestClass::class, ClassInspectorTestTraitC::class));
    }
}

class ClassInspectorTestClass
{
    use ClassInspectorTestTraitA;
}
