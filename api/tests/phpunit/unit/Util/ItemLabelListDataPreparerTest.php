<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Tests\U2\UnitTestCase;
use U2\Util\ItemLabelListDataPreparer;

class ItemLabelListDataPreparerTest extends UnitTestCase
{
    public function test_data_is_created_for_each_element(): void
    {
        $itemLabelListDataPreparer = new ItemLabelListDataPreparer();

        $elementOne = $this->createMock(TestElement::class);
        $elementOne
            ->method('__toString')
            ->willReturn('test');

        $elementTwo = $this->createMock(TestElement::class);
        $elementTwo
            ->method('__toString')
            ->willReturn('test2');

        $data = $itemLabelListDataPreparer->prepare([$elementOne, $elementTwo]);

        self::assertCount(2, $data);

        self::assertFalse($data[0]['inherited']);
        self::assertFalse($data[1]['inherited']);
    }

    public function test_it_sorts_the_prepared_data_by_name(): void
    {
        $itemLabelListDataPreparer = new ItemLabelListDataPreparer();

        $inheritedElementA = $this->createMock(TestElement::class);
        $inheritedElementA
            ->method('__toString')
            ->willReturn('A');

        $directAssignedElementB = $this->createMock(TestElement::class);
        $directAssignedElementB
            ->method('__toString')
            ->willReturn('B');

        $data = $itemLabelListDataPreparer->prepare([$directAssignedElementB], [$inheritedElementA]);

        self::assertCount(2, $data);

        $firstItem = $data[0];
        self::assertSame('A', $firstItem['name']);
        self::assertTrue($firstItem['inherited']);

        $secondItem = $data[1];
        self::assertSame('B', $secondItem['name']);
        self::assertFalse($secondItem['inherited']);
    }

    public function test_it_stores_element_data_for_an_element_only_once(): void
    {
        $itemLabelListDataPreparer = new ItemLabelListDataPreparer();

        $elementAInherited = $this->createMock(TestElement::class);
        $elementAInherited
            ->method('__toString')
            ->willReturn('A');

        $elementADirect = $this->createMock(TestElement::class);
        $elementADirect
            ->method('__toString')
            ->willReturn('A');

        $preparedData = $itemLabelListDataPreparer->prepare([$elementADirect], [$elementAInherited]);

        self::assertCount(1, $preparedData);
    }

    public function test_it_stores_element_data_marked_direct_if_element_is_provided_as_direct_and_inherited_element(): void
    {
        $itemLabelListDataPreparer = new ItemLabelListDataPreparer();

        $elementAInherited = $this->createMock(TestElement::class);
        $elementAInherited
            ->method('__toString')
            ->willReturn('A');

        $elementADirect = $this->createMock(TestElement::class);
        $elementADirect
            ->method('__toString')
            ->willReturn('A');

        $preparedData = $itemLabelListDataPreparer->prepare([$elementADirect], [$elementAInherited]);

        self::assertCount(1, $preparedData);
        self::assertFalse($preparedData[0]['inherited']);
    }
}

class TestElement
{
    public function __toString(): string
    {
        throw new \BadMethodCallException('This class is only for testing purposes and should never be used outside \U2\CoreBundle\Item\ItemLabelListDataPreparerTest');
    }
}
