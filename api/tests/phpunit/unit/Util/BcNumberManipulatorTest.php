<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\U2\UnitTestCase;
use U2\Util\BcNumberManipulator;

class BcNumberManipulatorTest extends UnitTestCase
{
    public static function roundedBcNumberProvider(): array
    {
        return [
            'integer rounding not needed' => ['123', 0, '123'],
            'rounding integer down' => ['123.1', 0, '123'],
            'rounding integer up' => ['123.5', 0, '124'],
            'rounding integer' => ['123.4999999999999', 0, '123'],
            'rounding down with scale 1' => ['123.44', 1, '123.4'],
            'rounding up with scale 1' => ['123.45', 1, '123.5'],
            'rounding down with scale 4' => ['123.4444499999999', 4, '123.4444'],
            'rounding up with scale 4' => ['123.4444599999999', 4, '123.4445'],
            'float rounding not needed' => ['123.4', 4, '123.4000'],
            'rounding with big precision' => ['777777777777777777777777777777777.1234567', 5, '777777777777777777777777777777777.12346'],
            'rounding negative integer' => ['-7', 0, '-7'],
            'rounding down negative float' => ['-1234567.12345674999', 7, '-1234567.1234567'],
            'rounding up negative float' => ['-1234567.12345675000', 7, '-1234567.1234568'],
        ];
    }

    #[DataProvider('roundedBcNumberProvider')]
    public function test_round_bc_number(string $bcNumber, int $scale, string $expectedRoundedBcNumber): void
    {
        self::assertSame($expectedRoundedBcNumber, BcNumberManipulator::round($bcNumber, $scale));
    }

    public function test_abs_bc_number(): void
    {
        self::assertSame('123', BcNumberManipulator::abs('123', 0));
        self::assertSame('0', BcNumberManipulator::abs('0', 0));
        self::assertSame('123', BcNumberManipulator::abs('-123', 0));
    }

    public function test_is_bc_number_zero(): void
    {
        self::assertFalse(BcNumberManipulator::isZero('17', 0));
        self::assertTrue(BcNumberManipulator::isZero('0', 0));
        self::assertFalse(BcNumberManipulator::isZero('-17', 0));
        self::assertFalse(BcNumberManipulator::isZero('0.000001', 6));
        self::assertTrue(BcNumberManipulator::isZero('0.000001', 5));
    }

    public function test_is_bc_number_negative(): void
    {
        self::assertFalse(BcNumberManipulator::isNegative('17', 0));
        self::assertFalse(BcNumberManipulator::isNegative('0', 0));
        self::assertTrue(BcNumberManipulator::isNegative('-17', 0));
        self::assertTrue(BcNumberManipulator::isNegative('-0.000001', 6));
    }
}
