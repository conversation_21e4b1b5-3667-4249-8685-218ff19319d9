<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Tests\U2\UnitTestCase;
use U2\Entity\Interfaces\ValidDateRange;
use U2\Util\DateTime;

class DateTimeTest extends UnitTestCase
{
    public function test_gets_the_ultimo_date(): void
    {
        $datesToTryToExpectedDatePairs = [
            [
                new \DateTime('2000-2-1'),
                new \DateTime('2000-1-31'),
            ],
            [
                new \DateTime('2000-2-28'),
                new \DateTime('2000-1-31'),
            ],
            [
                new \DateTime('2000-5-15'),
                new \DateTime('2000-4-30'),
            ],
            [
                new \DateTime('2000-10-30'),
                new \DateTime('2000-9-30'),
            ],
            [
                new \DateTime('2000-12-31'),
                new \DateTime('2000-11-30'),
            ],
            [
                new \DateTime('2000-1-1'),
                new \DateTime('2000-1-31'),
            ],
            [
                new \DateTime('2000-1-15'),
                new \DateTime('2000-1-31'),
            ],
            [
                new \DateTime('2000-1-30'),
                new \DateTime('2000-1-31'),
            ],
            [
                new \DateTime('2000-1-31'),
                new \DateTime('2000-1-31'),
            ],
        ];

        foreach ($datesToTryToExpectedDatePairs as $datePair) {
            self::assertEquals(
                $datePair[1],
                DateTime::getUltimoDate($datePair[0])
            );
        }
    }

    /**
     * @covers \U2\Util\DateTime::getLastDayOfMonth
     */
    public function test_gets_the_last_day_of_month(): void
    {
        $testDateExpectedResultPairs = [
            [
                new \DateTime('2000-1-1'),
                new \DateTime('2000-1-31'),
            ],
            [
                new \DateTime('2000-12-15'),
                new \DateTime('2000-12-31'),
            ],
            [
                new \DateTime('2000-02-17'),
                new \DateTime('2000-02-29'),
            ],
            [
                new \DateTime('2001-02-17'),
                new \DateTime('2001-02-28'),
            ],
        ];

        foreach ($testDateExpectedResultPairs as $datePair) {
            self::assertEquals(
                $datePair[1],
                DateTime::getLastDayOfMonth($datePair[0])
            );
        }
    }

    public function test_knows_if_a_period_is_in_the_date_range_of_a_unit(): void
    {
        $validDateRange = $this->createMock(ValidDateRange::class);
        $validDateRange->expects($this->atLeastOnce())->method('getValidFrom')->willReturn(null);
        $validDateRange->expects($this->atLeastOnce())->method('getValidTo')->willReturn(new \DateTime('tomorrow'));
        self::assertTrue(DateTime::isInDateRange($validDateRange, new \DateTime('today')));
        $validDateRange->expects($this->atLeastOnce())->method('getValidFrom')->willReturn(null);
        $validDateRange->expects($this->atLeastOnce())->method('getValidTo')->willReturn(new \DateTime('today'));
        self::assertTrue(DateTime::isInDateRange($validDateRange, new \DateTime('today')));
        $validDateRange->expects($this->atLeastOnce())->method('getValidFrom')->willReturn(new \DateTime('today'));
        $validDateRange->expects($this->atLeastOnce())->method('getValidTo')->willReturn(new \DateTime('tomorrow'));
        self::assertTrue(DateTime::isInDateRange($validDateRange, new \DateTime('today')));
    }

    public function test_knows_if_a_period_is_not_in_the_date_range_of_a_unit(): void
    {
        $validDateRange = $this->createMock(ValidDateRange::class);
        $validDateRange->expects($this->atLeastOnce())->method('getValidFrom')->willReturn(new \DateTime('today'));
        $validDateRange->expects($this->atLeastOnce())->method('getValidTo')->willReturn(new \DateTime('today'));
        self::assertFalse(DateTime::isInDateRange($validDateRange, new \DateTime('yesterday')));
        self::assertFalse(DateTime::isInDateRange($validDateRange, new \DateTime('tomorrow')));
    }

    public function test_sets_first_day_of_week(): void
    {
        self::assertEquals(new \DateTime('2001-01-29'), DateTime::getFirstDayOfWeek(new \DateTime('2001-02-02')));
        self::assertEquals(new \DateTime('2001-01-29'), DateTime::getFirstDayOfWeek(new \DateTime('2001-01-29')));
    }
}
