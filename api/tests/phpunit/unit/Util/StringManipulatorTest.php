<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\U2\UnitTestCase;
use U2\Util\StringManipulator;

class StringManipulatorTest extends UnitTestCase
{
    public function test_dash_separated_to_underscore_separated(): void
    {
        self::assertSame('test_some_sting', StringManipulator::dashSeparatedToUnderscoreSeparated('test-some-sting'));
    }

    public function test_sepearate_camel_case(): void
    {
        self::assertSame('Testing Some String', StringManipulator::separateCamelCase('TestingSomeString'));
    }

    public function test_camel_case_to_underscore_separated(): void
    {
        $tests = [
            'CamelCaseWord' => 'camel_case_word',
            'CamelCaseWordWithASingleLetter' => 'camel_case_word_with_a_single_letter',
            'CamelCaseWordEndingInUppercaseA' => 'camel_case_word_ending_in_uppercase_a',
            'smallFirstCaseWord' => 'small_first_case_word',
        ];

        foreach ($tests as $test => $result) {
            self::assertSame($result, StringManipulator::camelCaseToUnderscoreSeparated($test), 'Failed to convert camel case to underscore separated');
        }
    }

    public function test_lower_string_and_replace_space_with_underscore(): void
    {
        self::assertSame('expected_string', StringManipulator::lowerStringAndReplaceSpaceWithUnderscore('Expected String'));
    }

    public function test_lower_string_and_replace_space_with_dash(): void
    {
        self::assertSame('expected-string', StringManipulator::lowerStringAndReplaceSpaceWithDash('Expected String'));
    }

    public function test_lower_string_and_remove_spaces(): void
    {
        self::assertSame('expectedstring', StringManipulator::lowerStringAndRemoveSpaces('Expected String'));
    }

    public function test_camel_case_to_char_separated(): void
    {
        self::assertSame('abcdef', StringManipulator::camelCaseToCharSeparated('abcdef', '-'));
        self::assertSame('abc-Def', StringManipulator::camelCaseToCharSeparated('abcDef', '-'));
        self::assertSame('Abc-Def', StringManipulator::camelCaseToCharSeparated('AbcDef', '-'));
        self::assertSame('abc-Def-Ghi-Jkl', StringManipulator::camelCaseToCharSeparated('abcDefGhiJkl', '-'));
        self::assertSame('abc', StringManipulator::camelCaseToCharSeparated('abc', '-'));
        self::assertSame('Abc', StringManipulator::camelCaseToCharSeparated('Abc', '-'));
        self::assertSame('a-Bc', StringManipulator::camelCaseToCharSeparated('aBc', '-'));
        self::assertSame('ab-C', StringManipulator::camelCaseToCharSeparated('abC', '-'));
        self::assertSame('A-Bc', StringManipulator::camelCaseToCharSeparated('ABc', '-'));
        self::assertSame('Ab-C', StringManipulator::camelCaseToCharSeparated('AbC', '-'));
        self::assertSame('a-B-C', StringManipulator::camelCaseToCharSeparated('aBC', '-'));
        self::assertSame('A-B-C', StringManipulator::camelCaseToCharSeparated('ABC', '-'));
    }

    public function test_camel_case(): void
    {
        self::assertSame('ExpectedString', StringManipulator::toCamelCase('Expected String', true));
        self::assertSame('expectedString', StringManipulator::toCamelCase('Expected String'));
        self::assertSame('ExpectedString', StringManipulator::toCamelCase('expected string', true));
        self::assertSame('expectedString', StringManipulator::toCamelCase('expected string'));
        self::assertSame('ExpectedString', StringManipulator::toCamelCase('Expected string', true));
        self::assertSame('expectedString', StringManipulator::toCamelCase('Expected string'));
        self::assertSame('ExpectedString', StringManipulator::toCamelCase('expected String', true));
        self::assertSame('expectedString', StringManipulator::toCamelCase('expected String'));
    }

    public function test_it_converts_empty_strings_to_null(): void
    {
        self::assertNull(StringManipulator::emptyToNull(null));
        self::assertNull(StringManipulator::emptyToNull(''));
        self::assertNull(StringManipulator::emptyToNull('  '));
        self::assertSame('Expected String', StringManipulator::emptyToNull('Expected String'));
        self::assertSame('  Expected String  ', StringManipulator::emptyToNull('  Expected String  '));
    }

    /**
     * @return array<string, array<string, string|array<int,string>>>
     */
    public static function provideStringForSplitIntoSearchToken(): array
    {
        return [
            'empty string' => [
                'input' => '',
                'expected' => [],
            ],
            'With trailing "-"' => [
                'input' => ' CORP9 -',
                'expected' => ['CORP9', '-'],
            ],
            'single word' => [
                'input' => 'ABcd ',
                'expected' => ['ABcd'],
            ],
            'split just by space' => [
                'input' => 'ABcd efgHij ',
                'expected' => ['ABcd', 'efgHij'],
            ],
            'split just by multiple spaces' => [
                'input' => 'ABcd efgHij klmnop "qrst uvw" xyz ',
                'expected' => ['ABcd', 'efgHij', 'klmnop', 'qrst uvw', 'xyz'],
            ],
            'split by space and keep content in double quotes' => [
                'input' => 'ABcd "efgHij"',
                'expected' => ['ABcd', 'efgHij'],
            ],
            'split by space and keep content in single quotes' => [
                'input' => 'ABcd "efgHij"',
                'expected' => ['ABcd', 'efgHij'],
            ],
            'split by space and keep content in single quotes with double quotes' => [
                'input' => "ABcd 'LmNop \"efgHij\"'",
                'expected' => ['ABcd', 'LmNop "efgHij"'],
            ],
            'split by space and keep content in double quotes with single quotes' => [
                'input' => "ABcd \"LmNop 'efgHij'\"",
                'expected' => ['ABcd', 'LmNop \'efgHij\''],
            ],
        ];
    }

    /**
     * @param array<int,string> $expected
     */
    #[DataProvider('provideStringForSplitIntoSearchToken')]
    public function test_to_search_tokens(string $input, array $expected): void
    {
        self::assertEquals($expected, StringManipulator::toSearchTokens($input));
    }
}
