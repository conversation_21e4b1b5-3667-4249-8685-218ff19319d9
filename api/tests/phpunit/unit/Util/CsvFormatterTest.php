<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Tests\U2\UnitTestCase;
use U2\Util\CsvFormatter;

class CsvFormatterTest extends UnitTestCase
{
    /**
     * @covers \U2\Util\CsvFormatter::associativeArrayToCsv
     *
     * @throws \Exception
     */
    public function test_associative_array_to_csv(): void
    {
        $associativeArray = [
            [
                'header1' => new \DateTime('2012-01-01 23:59:24'),
                'header2' => 2,
                'header3' => 'SomeString',
                'header4' => [
                    'hello' => 'world',
                    'best' => 'test ever',
                ],
                'header5' => 'I\'ve got "Quotes" and spaces going on',
            ],
        ];

        $expectedResult = 'header1,header2,header3,header4,header5' . "\n" . '2012-01-01T23:59:24+00:00,2,SomeString,world,"I\'ve got ""Quotes"" and spaces going on"' . "\n";

        self::assertSame($expectedResult, CsvFormatter::associativeArrayToCsv($associativeArray));
    }
}
