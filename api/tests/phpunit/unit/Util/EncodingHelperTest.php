<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Tests\U2\UnitTestCase;
use U2\Util\EncodingHelper;

class EncodingHelperTest extends UnitTestCase
{
    public function test_encoding_is_not_utf_7(): void
    {
        // Given
        $string = <<<EOF
            TR0130010;{TR0110010}+({TR0110010}*{TR0120010})
            EOF;

        // Then
        self::assertSame('UTF-8', EncodingHelper::getEncoding($string));
    }
}
