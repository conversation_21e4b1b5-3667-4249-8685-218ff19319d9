<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Symfony\Component\Mime\Exception\InvalidArgumentException;
use Tests\U2\UnitTestCase;
use U2\Util\MimeTypeGuesser;

class MimeTypeGuesserTest extends UnitTestCase
{
    private MimeTypeGuesser $mimeTypeGuesser;

    public function test_guesses_the_mime_type_of_a_given_file(): void
    {
        self::assertSame('text/x-php', $this->mimeTypeGuesser->guess(__FILE__));
    }

    public function test_throws_not_found_for_non_existing_files(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->mimeTypeGuesser->guess('test');
    }

    protected function setUp(): void
    {
        $this->mimeTypeGuesser = new MimeTypeGuesser();
    }
}
