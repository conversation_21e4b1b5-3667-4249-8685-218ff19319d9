<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Doctrine\ORM\EntityManager;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Mapping\ClassMetadata;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\SystemSetting;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Util\EntityPropertyMappingHelper;

class EntityPropertyMappingHelperTest extends UnitTestCase
{
    /**
     * @var EntityManager&MockObject
     */
    private MockObject $entityManager;

    private EntityPropertyMappingHelper $entityPropertyMappingHelper;

    public function test_knows_if_a_property_of_an_entity_is_associative(): void
    {
        $classMetadata = $this->createMock(ClassMetadata::class);
        $className = User::class;
        $associationMappings = ['userGroups' => []];
        /* @var EntityManager $entityManager */
        $this->entityManager->expects($this->atLeastOnce())->method('getClassMetadata')->with($className)->willReturn($classMetadata);
        $classMetadata->expects($this->atLeastOnce())->method('getAssociationMappings')->willReturn($associationMappings);
        $this->entityPropertyMappingHelper->isAssociativeProperty('userGroups', $className);
    }

    public function test_finds_an_associated_entity_by_id_primary_key(): void
    {
        $classMetadata = $this->createMock(ClassMetadata::class);
        $targetClassMetadata = $this->createMock(ClassMetadata::class);
        $entityRepository = $this->createMock(EntityRepository::class);
        $className = User::class;
        $targetEntity = UserGroup::class;
        $associationMappings = ['userGroups' => ['targetEntity' => $targetEntity]];
        $this->entityManager->expects($this->atLeastOnce())
            ->method('getClassMetadata')
            ->willReturnCallback(static fn ($class): MockObject => match ($class) {
                $className => $classMetadata,
                $targetEntity => $targetClassMetadata,
                default => throw new \InvalidArgumentException('Invalid class'),
            });
        $classMetadata->expects($this->atLeastOnce())->method('getAssociationMappings')->willReturn($associationMappings);
        $targetClassMetadata->expects($this->atLeastOnce())->method('getIdentifier')->willReturn(['id']);
        $this->entityManager->expects($this->atLeastOnce())->method('getRepository')->with($targetEntity)->willReturn($entityRepository);
        $id = 1;
        $entityRepository->expects($this->atLeastOnce())->method('find')->with($id);

        $this->entityPropertyMappingHelper->findAssociatedEntityByIdentifier('userGroups', $id, $className);
    }

    public function test_finds_an_associated_entity_by_string_primary_key(): void
    {
        $classMetadata = $this->createMock(ClassMetadata::class);
        $targetClassMetadata = $this->createMock(ClassMetadata::class);
        $entityRepository = $this->createMock(EntityRepository::class);
        $className = \stdClass::class;
        $targetEntity = SystemSetting::class;
        $associationMappings = ['settings' => ['targetEntity' => $targetEntity]];
        $classMetadata->expects($this->atLeastOnce())->method('getAssociationMappings')->willReturn($associationMappings);

        $this->entityManager->expects($this->atLeastOnce())
            ->method('getClassMetadata')
            ->willReturnCallback(static fn ($class): MockObject => match ($class) {
                $className => $classMetadata,
                $targetEntity => $targetClassMetadata,
                default => throw new \InvalidArgumentException('Unexpected class name'),
            });

        $targetClassMetadata->expects($this->atLeastOnce())->method('getIdentifier')->willReturn(['name']);
        $this->entityManager->expects($this->atLeastOnce())->method('getRepository')->with($targetEntity)->willReturn($entityRepository);
        $value = 'a setting';
        $entityRepository->expects($this->atLeastOnce())->method('findOneBy')->with(['name' => $value]);
        $this->entityPropertyMappingHelper->findAssociatedEntityByIdentifier('settings', $value, $className);
    }

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->entityPropertyMappingHelper = new EntityPropertyMappingHelper($this->entityManager);
    }
}
