<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Util;

use Tests\U2\UnitTestCase;
use U2\Util\ValueToStringNormalizer;

class ValueToStringNormalizerTest extends UnitTestCase
{
    public function test_normalise_value(): void
    {
        self::assertSame('test string', ValueToStringNormalizer::normaliseValue('test string'));
        self::assertSame('1', ValueToStringNormalizer::normaliseValue(true));
        self::assertSame('0', ValueToStringNormalizer::normaliseValue(false));
        self::assertSame('Test', ValueToStringNormalizer::normaliseValue(new ClassWithToString()));
        self::assertSame('Test', ValueToStringNormalizer::normaliseValue([
            'name' => 'Test',
            'closed' => true,
        ]));
        self::assertSame('Value1', ValueToStringNormalizer::normaliseValue([
            'Value1',
            'Value2',
        ]));
        self::assertSame('1', ValueToStringNormalizer::normaliseValue(1));
        self::assertSame('1', ValueToStringNormalizer::normaliseValue(1.0));
        self::assertSame('', ValueToStringNormalizer::normaliseValue(null));
        self::assertSame(
            '2020-01-01T14:21:21+01:00',
            ValueToStringNormalizer::normaliseValue(
                (new \DateTime('2020-01-01 13:21:21')
                )->setTimezone(new \DateTimeZone('Europe/Berlin'))
            )
        );
    }
}

class ClassWithToString
{
    public function __toString(): string
    {
        return 'Test';
    }
}
