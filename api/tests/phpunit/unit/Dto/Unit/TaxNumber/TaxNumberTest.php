<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Dto\Unit\TaxNumber;

use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;
use Tests\U2\UnitTestCase;
use U2\Dto\Unit\TaxNumber\TaxNumber as TaxNumberDto;
use U2\Entity\Country;
use U2\Entity\LegalUnit;
use U2\Entity\TaxNumber;
use U2\Entity\Unit;

class TaxNumberTest extends UnitTestCase
{
    public function test_ensure_unit_has_no_tax_number_for_given_country(): void
    {
        // Given
        $unit = new Unit();
        $country = new Country();

        $taxNumberInput = new TaxNumberDto('12345678', $unit, $country);

        $context = $this->createMock(ExecutionContextInterface::class);
        $context
            ->expects($this->never())
            ->method('buildViolation');

        // When
        $taxNumberInput->ensureUnitHasNoTaxNumberForGivenCountry($context);
    }

    public function test_build_a_violation_if_tax_number_for_given_country_exists_on_unit(): void
    {
        // Given
        $unit = new LegalUnit();
        $country = new Country();

        $taxNumberInput = new TaxNumberDto('12345678', $unit, $country);
        $unit->addTaxNumber(TaxNumber::fromDto($taxNumberInput));

        // Then
        $violationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $context = $this->createMock(ExecutionContextInterface::class);
        $context
            ->expects($this->once())
            ->method('buildViolation')
            ->with('u2.tax_number.unit_owns_tax_number_for_given_country')
            ->willReturn($violationBuilder);

        $violationBuilder
            ->expects($this->once())
            ->method('atPath')
            ->with('country')
            ->willReturn($violationBuilder);

        $violationBuilder
            ->expects($this->once())
            ->method('addViolation');

        // When
        $taxNumberInput->ensureUnitHasNoTaxNumberForGivenCountry($context);
    }
}
