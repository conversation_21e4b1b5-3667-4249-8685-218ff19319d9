<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Dto\File;

use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;
use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Dto\File\FileInput;
use U2\Dto\Permission\GroupPermissionInput;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\User;
use U2\Entity\UserGroup;

/**
 * @covers \U2\Dto\File\FileInput
 */
class FileInputValidationTest extends UnitTestCase
{
    public function test_validates_duplicate_user_permissions_as_invalid(): void
    {
        $context = $this->createMock(ExecutionContextInterface::class);
        $constraintViolationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $constraintViolationBuilder
            ->expects($this->atLeastOnce())
            ->method('atPath')
            ->with('userPermissions')
            ->willReturn($constraintViolationBuilder);
        $context->expects($this->atLeastOnce())->method('buildViolation')->willReturn($constraintViolationBuilder);

        $constraintViolationBuilder->expects($this->atLeastOnce())->method('addViolation');

        $user = new User();
        TestUtils::setProperty($user, 'id', 1);

        $fileInput = new FileInput(
            [
                new UserPermissionInput($user, 141),
                new UserPermissionInput($user, 141),
            ],
            [],
            '',
            [],
            null,
            null,
            null
        );

        self::assertFalse($fileInput->validateUsersAreUnique($context));
    }

    public function test_validates_duplicate_group_permissions_as_invalid(): void
    {
        $context = $this->createMock(ExecutionContextInterface::class);
        $constraintViolationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $constraintViolationBuilder
            ->expects($this->atLeastOnce())
            ->method('atPath')
            ->with('groupPermissions')
            ->willReturn($constraintViolationBuilder);
        $context->expects($this->atLeastOnce())->method('buildViolation')->willReturn($constraintViolationBuilder);

        $constraintViolationBuilder->expects($this->atLeastOnce())->method('addViolation');

        $userGroup = new UserGroup();
        TestUtils::setProperty($userGroup, 'id', 1);

        $fileInput = new FileInput(
            [],
            [
                new GroupPermissionInput($userGroup, 141),
                new GroupPermissionInput($userGroup, 141),
            ],
            '',
            [],
            null,
            null,
            null
        );

        self::assertFalse($fileInput->validateGroupsAreUnique($context));
    }

    public function test_validates_unique_group_permissions_as_valid(): void
    {
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->never())->method('buildViolation');

        $fileInput = new FileInput(
            [],
            [
                new GroupPermissionInput(new UserGroup(), 141),
                new GroupPermissionInput(new UserGroup(), 141),
            ],
            '',
            [],
            null,
            null,
            null
        );

        self::assertTrue($fileInput->validateGroupsAreUnique($context));
    }

    public function test_validates_unique_user_permissions_as_valid(): void
    {
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->never())->method('buildViolation');

        $fileInput = new FileInput(
            [
                new UserPermissionInput(new User(), 141),
                new UserPermissionInput(new User(), 141),
            ],
            [],
            '',
            [],
            null,
            null,
            null
        );

        self::assertTrue($fileInput->validateGroupsAreUnique($context));
    }

    public function test_validates_missing_owner_user_permission_as_invalid(): void
    {
        $context = $this->createMock(ExecutionContextInterface::class);
        $constraintViolationBuilder = $this->createMock(ConstraintViolationBuilderInterface::class);
        $constraintViolationBuilder
            ->expects($this->atLeastOnce())
            ->method('atPath')
            ->with('userPermissions')
            ->willReturn($constraintViolationBuilder);
        $context
            ->expects($this->atLeastOnce())
            ->method('buildViolation')
            ->with('u2_core.user_permissions.one_user_should_have_permission_to_manage')
            ->willReturn($constraintViolationBuilder);

        $constraintViolationBuilder->expects($this->atLeastOnce())->method('addViolation');

        $user = new User();
        TestUtils::setProperty($user, 'id', 1);

        $fileInput = new FileInput(
            [],
            [],
            '',
            [],
            null,
            null,
            null
        );

        self::assertFalse($fileInput->validateOwnerUserPermissionExist($context));
    }

    public function test_validates_with_owner_user_permission_as_valid(): void
    {
        $context = $this->createMock(ExecutionContextInterface::class);
        $context->expects($this->never())->method('buildViolation');

        $fileInput = new FileInput(
            [
                new UserPermissionInput(new User(), 141),
            ],
            [],
            '',
            [],
            null,
            null,
            null
        );

        self::assertTrue($fileInput->validateOwnerUserPermissionExist($context));
    }
}
