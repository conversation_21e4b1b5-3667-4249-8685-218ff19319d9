<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit;

use Tests\U2\UnitTestCase;
use U2\Security\Authorization\Authorizable;
use U2\Security\Authorization\AuthorizationManager;
use U2\SystemSettings\SystemSettings;
use U2\Unit\FieldSecurity;

class FieldSecurityTest extends UnitTestCase
{
    public function test_returns_true_if_the_property_is_inside_the_white_list(): void
    {
        $authorizationManager = $this->createMock(AuthorizationManager::class);
        $user = $this->createMock(Authorizable::class);

        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityUnitEditFieldWhitelist')->willReturn(['property']);

        $fieldSecurity = new FieldSecurity($authorizationManager, $systemSettings);
        $authorizationManager->expects($this->atLeastOnce())->method('isAuthorized')->with(self::equalTo($user))->willReturn(false);
        self::assertTrue($fieldSecurity->canEdit($user, 'property'));
    }

    public function test_returns_true_if_the_user_is_a_unit_manager(): void
    {
        $authorizationManager = $this->createMock(AuthorizationManager::class);
        $user = $this->createMock(Authorizable::class);

        $fieldSecurity = new FieldSecurity($authorizationManager, $this->createMock(SystemSettings::class));
        $authorizationManager->expects($this->atLeastOnce())->method('isAuthorized')->with(self::equalTo($user))->willReturn(true);
        self::assertTrue($fieldSecurity->canEdit($user, 'property'));
    }

    public function test_returns_false_if_the_property_is_not_inside_the_white_list(): void
    {
        $authorizationManager = $this->createMock(AuthorizationManager::class);
        $user = $this->createMock(Authorizable::class);

        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityUnitEditFieldWhitelist')->willReturn(['property']);

        $fieldSecurity = new FieldSecurity($authorizationManager, $systemSettings);
        $authorizationManager->expects($this->atLeastOnce())->method('isAuthorized')->with(self::equalTo($user))->willReturn(false);
        self::assertFalse($fieldSecurity->canEdit($user, 'another property'));
    }

    public function test_returns_false_if_the_white_list_is_empty(): void
    {
        $authorizationManager = $this->createMock(AuthorizationManager::class);
        $user = $this->createMock(Authorizable::class);

        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityUnitEditFieldWhitelist')->willReturn([]);

        $fieldSecurity = new FieldSecurity($authorizationManager, $systemSettings);
        $authorizationManager->expects($this->atLeastOnce())->method('isAuthorized')->with(self::equalTo($user))->willReturn(false);
        self::assertFalse($fieldSecurity->canEdit($user, 'property'));
    }
}
