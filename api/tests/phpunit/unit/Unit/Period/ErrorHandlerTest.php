<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit\Period;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\Currency;
use U2\Entity\Period;
use U2\Exception\ExchangeRateNotFoundException;
use U2\Exception\MissingPreviousPeriodException;
use U2\Unit\Period\ErrorHandler;
use U2\Util\FlashMessageHandler;

class ErrorHandlerTest extends UnitTestCase
{
    private ErrorHandler $errorHandler;

    /**
     * @var FlashMessageHandler&MockObject
     */
    private MockObject $flashMessageHandler;

    /**
     * @var MockObject&TranslatorInterface
     */
    private MockObject $translator;

    public function test_handles_a_missing_previous_period_exception(): void
    {
        $period = $this->createMock(Period::class);
        $exception = $this->createMock(MissingPreviousPeriodException::class);
        $exception->expects($this->atLeastOnce())->method('getPeriod')->willReturn($period);
        $errorMessage = 'error';
        $this->translator->expects($this->atLeastOnce())->method('trans')->with(self::anything())->willReturn($errorMessage);
        $this->flashMessageHandler->expects($this->atLeastOnce())->method('addError')->with(self::equalTo($errorMessage));
        $this->errorHandler->handle($exception);
    }

    public function test_handles_a_exchange_rate_not_found_exception(): void
    {
        $period = $this->createMock(Period::class);
        $sourceCurrency = $this->createMock(Currency::class);
        $destinationCurrency = $this->createMock(Currency::class);
        $exception = $this->createMock(ExchangeRateNotFoundException::class);
        $exception->expects($this->atLeastOnce())->method('getSource')->willReturn($sourceCurrency);
        $exception->expects($this->atLeastOnce())->method('getDestination')->willReturn($destinationCurrency);
        $exception->expects($this->atLeastOnce())->method('getPeriod')->willReturn($period);
        $errorMessage = 'error';
        $this->translator->expects($this->atLeastOnce())->method('trans')->with(self::anything())->willReturn($errorMessage);
        $this->flashMessageHandler->expects($this->atLeastOnce())->method('addError')->with(self::equalTo($errorMessage));
        $this->errorHandler->handle($exception);
    }

    public function test_does_not_handle_an_unknown_exception(): void
    {
        $exception = $this->createMock(\Exception::class);
        $this->expectExceptionObject($exception);
        $this->errorHandler->handle($exception);
    }

    protected function setUp(): void
    {
        $this->translator = $this->createMock(TranslatorInterface::class);
        $this->flashMessageHandler = $this->createMock(FlashMessageHandler::class);
        $this->errorHandler = new ErrorHandler($this->flashMessageHandler, $this->translator);
    }
}
