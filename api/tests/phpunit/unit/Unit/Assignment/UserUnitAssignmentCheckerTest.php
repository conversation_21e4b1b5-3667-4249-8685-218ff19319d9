<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit\Assignment;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\UnitFactory;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Repository\UnitRepository;
use U2\Unit\Assignment\UserUnitAssignmentChecker;

class UserUnitAssignmentCheckerTest extends UnitTestCase
{
    public function test_check_return_false(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);

        $unitRepository
            ->method('findUserAssignedInSubset')
            ->with(self::anything())
            ->willReturn([]);

        $userUnitAssignmentChecker = new UserUnitAssignmentChecker($unitRepository);

        $areUnitsAssignedToUser = $userUnitAssignmentChecker->check(new User(), [new Unit()]);
        self::assertFalse($areUnitsAssignedToUser);
    }

    public function test_check_return_true(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);

        $unit = new Unit();
        $unitRepository
            ->method('findUserAssignedInSubset')
            ->with(self::anything())
            ->willReturn([$unit]);

        $userUnitAssignmentChecker = new UserUnitAssignmentChecker($unitRepository);

        $areUnitsAssignedToUser = $userUnitAssignmentChecker->check(new User(), [$unit]);
        self::assertTrue($areUnitsAssignedToUser);
    }

    public function test_find_units_not_assigned_to_user_returns_empty_array_if_no_sub_set(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);

        $user = new User();

        $unitRepository
            ->expects($this->never())
            ->method('findUserAssignedInSubset')
            ->with($user);

        $subSet = [];

        $userUnitAssignmentChecker = new UserUnitAssignmentChecker($unitRepository);

        self::assertEquals([], $userUnitAssignmentChecker->findUnitsNotAssignedToUserInSubSet($user, $subSet));
    }

    public function test_find_units_not_assigned_to_user_in_sub_set(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);

        $user = new User();

        $unit = UnitFactory::createOne()->_real();
        $unitNotAssignedToUser = UnitFactory::createOne()->_real();

        $unitRepository
            ->method('findUserAssigned')
            ->with($user)
            ->willReturn([$unit]);

        $subSet = [$unitNotAssignedToUser, $unit];

        $userUnitAssignmentChecker = new UserUnitAssignmentChecker($unitRepository);
        $test = $userUnitAssignmentChecker->findUnitsNotAssignedToUserInSubSet($user, $subSet);
        self::assertEquals([$unitNotAssignedToUser], $test);
    }
}
