<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit\Hierarchy;

use Doctrine\ORM\EntityManager;
use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\Unit;
use U2\Unit\Hierarchy\DefinitionFactory;

class DefinitionFactoryTest extends UnitTestCase
{
    private DefinitionFactory $definitionFactory;

    /**
     * @var EntityManager&MockObject
     */
    private MockObject $entityManager;

    public function test_creates_a_definition_with_a_child_and_parent(): void
    {
        $childUnit = $this->createMock(Unit::class);
        $parentUnit = $this->createMock(Unit::class);
        $childId = 1;
        $parentId = 2;
        $this->entityManager
            ->expects($this->atLeastOnce())
            ->method('getReference')
            ->with(Unit::class)
            ->willReturnOnConsecutiveCalls($childUnit, $parentUnit);
        $definition = $this->definitionFactory->create($childId, $parentId);
        self::assertSame($childUnit, $definition->getUnit());
        self::assertSame($parentUnit, $definition->getParentUnit());
    }

    public function test_creates_a_definition_with_a_child(): void
    {
        $childUnit = $this->createMock(Unit::class);
        $childId = 1;
        $this->entityManager->expects($this->atLeastOnce())->method('getReference')->with(Unit::class)->willReturn($childUnit);
        $unitHierarchyDefinition = $this->definitionFactory->create($childId);
        self::assertSame($childUnit, $unitHierarchyDefinition->getUnit());
        self::assertNull($unitHierarchyDefinition->getParentUnit());
    }

    protected function setUp(): void
    {
        $this->entityManager = $this->createMock(EntityManager::class);
        $this->definitionFactory = new DefinitionFactory($this->entityManager);
    }
}
