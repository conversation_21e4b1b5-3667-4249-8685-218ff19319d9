<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit\Hierarchy;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\UnitFactory;
use U2\Entity\UnitHierarchyDefinition;
use U2\Unit\Hierarchy\DefinitionFactory;
use U2\Unit\Hierarchy\Snapshot;
use U2\Unit\Hierarchy\SnapshotManager;
use U2\Unit\Hierarchy\SnapshotToJsTreeTransformer;

class SnapshotToJsTreeTransformerTest extends UnitTestCase
{
    public function test_transform(): void
    {
        $parent = UnitFactory::createOne(['id' => 100, 'name' => 'Parent', 'refId' => 'RefIdParent'])->_real();
        $child1 = UnitFactory::createOne(['id' => 200, 'name' => 'Child One', 'refId' => 'RefIdChildOne'])->_real();
        $child2 = UnitFactory::createOne(['id' => 300, 'name' => 'Child Two', 'refId' => 'RefIdChildTwo'])->_real();

        $snapshot = $this->createMock(Snapshot::class);
        $snapshot
            ->method('getChildUnits')
            ->willReturnCallback(static fn ($argument): array => match ($argument) {
                null => [$parent],
                $parent => [$child1, $child2],
                $child1, $child2 => [],
                default => throw new \InvalidArgumentException('Unexpected argument'),
            });

        $expectedJson = '[{"title":"RefIdParent - Parent","icon":"icon-unit","id":100,"children":[{"title":"RefIdChildOne - Child One","icon":"icon-unit","id":200,"children":[]},{"title":"RefIdChildTwo - Child Two","icon":"icon-unit","id":300,"children":[]}]}]';

        $definitionFactory = $this->createMock(DefinitionFactory::class);
        $snapshotManager = $this->createMock(SnapshotManager::class);

        $snapshotToJsTreeTransformer = new SnapshotToJsTreeTransformer($definitionFactory, $snapshotManager);

        self::assertSame($expectedJson, $snapshotToJsTreeTransformer->transform($snapshot));
    }

    public function test_transform_correctly_sorts_units_with_names_that_contain_whitespace(): void
    {
        $unit1 = UnitFactory::createOne(['id' => 1, 'name' => 'Test', 'refId' => 'WhiteSpaceTest'])->_real();
        $unit2 = UnitFactory::createOne(['id' => 2, 'name' => 'Test', 'refId' => 'White Space Test'])->_real();

        $snapshot = $this->createMock(Snapshot::class);
        $snapshot
            ->method('getChildUnits')
            ->willReturnCallback(
                static fn ($argument): array => match ($argument) {
                    null => [$unit1, $unit2],
                    $unit1, $unit2 => [],
                    default => throw new \InvalidArgumentException('Unexpected argument'),
                }
            );

        $expectedJson = <<<EOF
            [{"title":"White Space Test - Test","icon":"icon-unit","id":2,"children":[]},{"title":"WhiteSpaceTest - Test","icon":"icon-unit","id":1,"children":[]}]
            EOF;

        $definitionFactory = $this->createMock(DefinitionFactory::class);
        $snapshotManager = $this->createMock(SnapshotManager::class);

        $snapshotToJsTreeTransformer = new SnapshotToJsTreeTransformer($definitionFactory, $snapshotManager);

        self::assertSame($expectedJson, $snapshotToJsTreeTransformer->transform($snapshot));
    }

    public function test_reverse_transform(): void
    {
        $parent = UnitFactory::createOne(['id' => 100, 'name' => 'Parent', 'refId' => 'RefIdParent'])->_real();
        $child1 = UnitFactory::createOne(['id' => 200, 'name' => 'Child One', 'refId' => 'RefIdChildOne'])->_real();
        $child2 = UnitFactory::createOne(['id' => 300, 'name' => 'Child Two', 'refId' => 'RefIdChildTwo'])->_real();

        $jsonIn = '[{"id":100,"children":[{"id":200},{"id":300}]}]';

        $flatArray = [
            0 => [
                'self' => '100',
                'parent' => 'root',
            ],
            1 => [
                'self' => '200',
                'parent' => '100',
            ],
            2 => [
                'self' => '300',
                'parent' => '100',
            ],
        ];

        $snapshotManager = $this->createMock(SnapshotManager::class);
        $snapshotManager
            ->method('jsTreeJsonToFlatArray')
            ->with($jsonIn)
            ->willReturn($flatArray);

        $definitionFactory = $this->createMock(DefinitionFactory::class);
        $parentDefinition = $this->createMock(UnitHierarchyDefinition::class);
        $parentDefinition
            ->method('getUnit')
            ->willReturn($parent);
        $parentDefinition
            ->method('getParentUnit')
            ->willReturn(null);

        $child1Definition = $this->createMock(UnitHierarchyDefinition::class);
        $child1Definition
            ->method('getUnit')
            ->willReturn($child1);
        $child1Definition
            ->method('getParentUnit')
            ->willReturn($parent);

        $child2Definition = $this->createMock(UnitHierarchyDefinition::class);
        $child2Definition
            ->method('getUnit')
            ->willReturn($child2);
        $child2Definition
            ->method('getParentUnit')
            ->willReturn($parent);

        $definitionFactory
            ->method('create')
            ->willReturnCallback(
                static fn ($argument, $argument2): MockObject => match (\sprintf('%s-%s', $argument, $argument2)) {
                    '100-root' => $parentDefinition,
                    '200-100' => $child1Definition,
                    '300-100' => $child2Definition,
                    default => throw new \InvalidArgumentException('Unexpected argument'),
                }
            );

        $snapshotToJsTreeTransformer = new SnapshotToJsTreeTransformer($definitionFactory, $snapshotManager);
        $snapshot = $snapshotToJsTreeTransformer->reverseTransform($jsonIn);

        $expectedJson = '[{"title":"RefIdParent - Parent","icon":"icon-unit","id":100,"children":[{"title":"RefIdChildOne - Child One","icon":"icon-unit","id":200,"children":[]},{"title":"RefIdChildTwo - Child Two","icon":"icon-unit","id":300,"children":[]}]}]';

        self::assertSame($expectedJson, $snapshotToJsTreeTransformer->transform($snapshot));
    }
}
