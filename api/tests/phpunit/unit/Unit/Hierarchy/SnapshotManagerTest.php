<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit\Hierarchy;

use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Entity\Unit;
use U2\Repository\UnitRepository;
use U2\Unit\Hierarchy\Snapshot;
use U2\Unit\Hierarchy\SnapshotDiffFactory;
use U2\Unit\Hierarchy\SnapshotManager;

class SnapshotManagerTest extends UnitTestCase
{
    /**
     * @testdox Verify that jsTreeJsonToFlatArray returns an array with the correct ids of each unit "self" and its parent "parent" for a simple jsTree JSON string.
     */
    public function test_js_tree_json_to_flat_array_simple(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $snapshotManager = new SnapshotManager($diffFactory, $unitRepository);

        self::assertEquals(
            [
                0 => [
                    'self' => 7,
                    'parent' => 'root',
                ],
                1 => [
                    'self' => 23,
                    'parent' => 7,
                ],
                2 => [
                    'self' => 42,
                    'parent' => 7,
                ],
            ],
            $snapshotManager->jsTreeJsonToFlatArray('[{"id":7,"children":[{"id":23},{"id":42}]}]')
        );
    }

    /**
     * @testdox Asserting that jsTreeJsonToFlatArray throws a exception on invalid input data.
     */
    public function test_js_tree_json_to_flat_array_broken_structure(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Array needs to contain an attribute called id. Invalid data submitted.');

        $unitHirarchyManager = new SnapshotManager($diffFactory, $unitRepository);

        $unitHirarchyManager->jsTreeJsonToFlatArray(
            <<< 'JSON'
                [{"id":"root","children":
                    [
                        {"data":"Unit"},
                        {"id":"child5"},
                        {"id":"group2","children":
                            [
                                {"id":"child2"},
                                {"id":"child3"},
                                {"id":"child4"},
                                "Unit",
                                {"id":"group2","children":
                                    [
                                        "Unit",
                                        "Unit"
                                    ]
                                }
                            ]
                        },
                        {"id":"group2","children":
                            [
                                "Unit",
                                "Unit"
                            ]
                        }
                    ]
                }]
                JSON
        );
    }

    /**
     * @testdox Asserting that jsTreeJsonToFlatArray throws a exception on non-unique unit ids.
     */
    public function test_js_tree_json_to_flat_array_medium_non_unique(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Unit ids need to be unique.');

        $unitHirarchyManager = new SnapshotManager($diffFactory, $unitRepository);

        $unitHirarchyManager->jsTreeJsonToFlatArray(
            <<< 'JSON'
                [{"id":"root","children":
                    [
                        {"id":"child5"},
                        {"id":"group2","children":
                            [
                                {"id":"child2"},
                                {"id":"child3"},
                                {"id":"child4"},
                                {"id":"group2"}
                            ]
                        },
                        {"id":"group2"}
                    ]
                }]
                JSON
        );
    }

    /**
     * @testdox Verify that jsTreeJsonToFlatArray returns an array with the correct ids of each unit "self" and its parant "parent" for a JSON string.
     */
    public function test_js_tree_json_to_flat_array_medium(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $unitHirarchyManager = new SnapshotManager($diffFactory, $unitRepository);

        self::assertEquals(
            [
                0 => [
                    'self' => 'root',
                    'parent' => 'root',
                ],
                1 => [
                    'self' => 'child5',
                    'parent' => 'root',
                ],
                2 => [
                    'self' => 'group2',
                    'parent' => 'root',
                ],
                3 => [
                    'self' => 'child2',
                    'parent' => 'group2',
                ],
                4 => [
                    'self' => 'child3',
                    'parent' => 'group2',
                ],
                5 => [
                    'self' => 'child4',
                    'parent' => 'group2',
                ],
                6 => [
                    'self' => 'group3',
                    'parent' => 'group2',
                ],
                7 => [
                    'self' => 'group1',
                    'parent' => 'root',
                ],
            ],
            $unitHirarchyManager->jsTreeJsonToFlatArray(
                <<< 'JSON'
                    [{"id":"root","children":
                        [
                            {"id":"child5"},
                            {"id":"group2","children":
                                [
                                    {"id":"child2"},
                                    {"id":"child3"},
                                    {"id":"child4"},
                                    {"id":"group3"}
                                ]
                            },
                            {"id":"group1"}
                        ]
                    }]
                    JSON
            )
        );
    }

    /**
     * @testdox Verify that jsTreeJsonToFlatArray returns an array with the correct ids of each unit "self" and its parent "parent" for a JSON string with
     *     multiple root nodes.
     */
    public function test_js_tree_json_to_flat_array_medium_multiple_root_nodes(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $unitHirarchyManager = new SnapshotManager($diffFactory, $unitRepository);

        self::assertEquals(
            [
                0 => [
                    'self' => 1111,
                    'parent' => 'root',
                ],
                1 => [
                    'self' => 2222,
                    'parent' => 'root',
                ],
                2 => [
                    'self' => 3333,
                    'parent' => 2222,
                ],
            ],
            $unitHirarchyManager->jsTreeJsonToFlatArray(
                <<< 'JSON'
                    [
                       {
                         "id":1111
                       },
                       {
                         "id":2222,
                          "children":[
                             {
                               "id":3333
                             }
                          ]
                       }
                    ]
                    JSON
            )
        );
    }

    /**
     * @testdox Verify that jsTreeArrayToFlatArray can handle an empty array.
     */
    public function test_js_tree_array_to_flat_array_empty_input(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $snapshotManager = new SnapshotManager($diffFactory, $unitRepository);

        $jsonIn = '[]';

        $flatArray = $snapshotManager->jsTreeJsonToFlatArray($jsonIn);

        self::assertEquals([], $flatArray);
    }

    /**
     * @testdox Verify that the method getUnusedUnits returns exactly and only the units not used by a given snapshot
     */
    public function test_get_unused_units(): void
    {
        $unitRepository = $this->createMock(UnitRepository::class);
        $diffFactory = $this->createMock(SnapshotDiffFactory::class);

        $usedUnitIds = [1, 3, 5, 6];

        // Mock the Units entities
        $unit7 = new Unit();
        TestUtils::setId($unit7, 7);

        $unit23 = new Unit();
        TestUtils::setId($unit23, 23);

        $unit42 = new Unit();
        TestUtils::setId($unit42, 42);

        $unusedUnits = [$unit7, $unit23, $unit42];

        $unitRepository
            ->method('findExcludingGiven')
            ->with($usedUnitIds)
            ->willReturn($unusedUnits);

        // mock snapshot
        $snapshot = $this->createMock(Snapshot::class);
        $snapshot
            ->method('getUsedUnitIds')
            ->willReturn($usedUnitIds);

        $snapshotManager = new SnapshotManager($diffFactory, $unitRepository);

        // do the actual test
        $result = $snapshotManager->getUnusedUnits($snapshot);
        self::assertEquals($unusedUnits, $result);
    }
}
