<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Unit\Hierarchy;

use Tests\U2\UnitTestCase;
use U2\Entity\Country;
use U2\Entity\Unit;
use U2\Entity\UnitHierarchy;
use U2\Entity\UnitHierarchyDefinition;
use U2\Exception\Exception;
use U2\Unit\Hierarchy\Snapshot;

class SnapshotTest extends UnitTestCase
{
    public function test_add_unit_to_parent_not_in_snapshot(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('infinite loop');

        new Snapshot([
            new UnitHierarchyDefinition(
                $this->createMock(UnitHierarchy::class),
                $this->getUnitMock(23),
                $this->getUnitMock(7)
            ),
        ]);
    }

    public function test_get_used_unit_ids(): void
    {
        $parent = $this->getUnitMock(7);
        $child1 = $this->getUnitMock(23);
        $child2 = $this->getUnitMock(42);
        $hierarchy = $this->createMock(UnitHierarchy::class);

        $snapshot = new Snapshot([
            new UnitHierarchyDefinition($hierarchy, $parent),
            new UnitHierarchyDefinition($hierarchy, $child1, $parent),
            new UnitHierarchyDefinition($hierarchy, $child2, $parent),
        ]);

        self::assertEquals([7, 23, 42], $snapshot->getUsedUnitIds());
    }

    public function test_get_child_units(): void
    {
        $parent = $this->getUnitMock(7);
        $child1 = $this->getUnitMock(23);
        $child2 = $this->getUnitMock(42);
        $hierarchy = $this->createMock(UnitHierarchy::class);

        $snapshot = new Snapshot([
            new UnitHierarchyDefinition($hierarchy, $parent),
            new UnitHierarchyDefinition($hierarchy, $child1, $parent),
            new UnitHierarchyDefinition($hierarchy, $child2, $parent),
        ]);

        $childUnits = $snapshot->getChildUnits();

        self::assertSame(7, $childUnits[0]->getId());

        $childUnits = $snapshot->getChildUnits($parent);
        self::assertSame(23, $childUnits[0]->getId());
        self::assertSame(42, $childUnits[1]->getId());
    }

    public function test_get_parent_unit(): void
    {
        $parent = $this->getUnitMock(7);
        $child1 = $this->getUnitMock(23);
        $child2 = $this->getUnitMock(42);
        $hierarchy = $this->createMock(UnitHierarchy::class);

        $snapshot = new Snapshot([
            new UnitHierarchyDefinition($hierarchy, $parent),
            new UnitHierarchyDefinition($hierarchy, $child1, $parent),
            new UnitHierarchyDefinition($hierarchy, $child2, $parent),
        ]);

        self::assertSame(7, $snapshot->getParentUnit($child1)->getId());
        self::assertSame(7, $snapshot->getParentUnit($child2)->getId());
        self::assertNull($snapshot->getParentUnit($parent));
    }

    public function test_get_toplevel_units(): void
    {
        $hierarchy = $this->createMock(UnitHierarchy::class);

        $snapshot = new Snapshot([
            new UnitHierarchyDefinition($hierarchy, $this->getUnitMock(7)),
            new UnitHierarchyDefinition($hierarchy, $this->getUnitMock(23)),
            new UnitHierarchyDefinition($hierarchy, $this->getUnitMock(42)),
        ]);

        $topLevelUnits = $snapshot->getTopLevelUnits();

        self::assertCount(3, $topLevelUnits);
        self::assertSame(7, $topLevelUnits[0]->getId());
        self::assertSame(23, $topLevelUnits[1]->getId());
        self::assertSame(42, $topLevelUnits[2]->getId());
    }

    public function test_nested_array(): void
    {
        $parent = $this->getUnitMock(7);
        $child1 = $this->getUnitMock(23);
        $child2 = $this->getUnitMock(42);

        $hierarchy = $this->createMock(UnitHierarchy::class);

        $snapshot = new Snapshot([
            new UnitHierarchyDefinition($hierarchy, $parent),
            new UnitHierarchyDefinition($hierarchy, $child1, $parent),
            new UnitHierarchyDefinition($hierarchy, $child2, $parent),
        ]);

        $nestedArray = $snapshot->getNestedArray();

        self::assertEquals('7', $nestedArray[0]['id']);
        self::assertEquals('23', $nestedArray[0]['children'][0]['id']);
        self::assertEquals('42', $nestedArray[0]['children'][1]['id']);
    }

    private function getUnitMock(int $unitId): Unit
    {
        $country = $this->createMock(Country::class);
        $country->method('getId')->willReturn(1);

        $unit = $this->createMock(Unit::class);
        $unit->method('getId')->willReturn($unitId);
        $unit->method('getRefIdAndName')->willReturn(\sprintf('XX%d - The %d Company', $unitId, $unitId));
        $unit->method('getCountry')->willReturn($country);

        return $unit;
    }
}
