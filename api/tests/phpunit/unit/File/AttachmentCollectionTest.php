<?php

declare(strict_types=1);
namespace Tests\Unit\U2\File;

use Tests\U2\TestUtils;
use Tests\U2\UnitTestCase;
use U2\Api\Property\AttachmentLinks;
use U2\Entity\File;
use U2\Entity\Task\Task;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\File\Attachment;
use U2\File\AttachmentCollection;

class AttachmentCollectionTest extends UnitTestCase
{
    public function test_can_set_an_attachment(): void
    {
        $file = new File('test');
        TestUtils::setId($file, 1);
        $file->setCreatedBy(new User());
        $file->setUpdatedBy(new User());

        $attachment = new Attachment($file, new Task('xyz', new Status()), $file->getName(), new AttachmentLinks(null, null, null));
        $attachmentCollection = new AttachmentCollection([]);
        $attachmentCollection->offsetSet(1, $attachment);
    }
}
