<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Country;

use Tests\U2\UnitTestCase;
use U2\Country\CountryCollection;
use U2\Entity\Country;

class CountryCollectionTest extends UnitTestCase
{
    private CountryCollection $countryCollection;

    public function test_sorts_collection_by_name_short(): void
    {
        $countryA = $this->createMock(Country::class);
        $countryB = $this->createMock(Country::class);
        $countryC = $this->createMock(Country::class);
        $countryA->expects($this->atLeastOnce())->method('getNameShort')->willReturn('Angola');
        $countryB->expects($this->atLeastOnce())->method('getNameShort')->willReturn('Bhutan');
        $countryC->expects($this->atLeastOnce())->method('getNameShort')->willReturn('Congo');
        $this->countryCollection = new CountryCollection([
            $countryC,
            $countryA,
            $countryB,
        ]);
        $this->countryCollection->sortByNameShort();

        self::assertEquals(
            new CountryCollection([
                $countryA,
                $countryB,
                $countryC,
            ]),
            $this->countryCollection
        );
    }

    protected function setUp(): void
    {
        $this->countryCollection = new CountryCollection();
    }
}
