<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Import\Interpreter;

use Tests\U2\UnitTestCase;
use U2\Entity\Interfaces\Entity;
use U2\Import\Interpreter\EntityLookupCache;

class EntityLookupCacheTest extends UnitTestCase
{
    public function test_find(): void
    {
        // Given
        $entityLookupCache = new EntityLookupCache();

        // When
        $entityLookupCache->add(
            $entity = new EntityLookupCacheTestEntity('value')
        );

        // Then
        self::assertSame($entity, $entityLookupCache->find(EntityLookupCacheTestEntity::class, 'lookupProperty', 'value'));
        self::assertNull($entityLookupCache->find(\stdClass::class, 'lookupProperty', 'value'));
    }
}

class EntityLookupCacheTestEntity implements Entity
{
    private string $lookupProperty;

    public function __construct(string $lookupPropertyValue)
    {
        $this->lookupProperty = $lookupPropertyValue;
    }

    public function getId(): int
    {
        return 1;
    }

    public function getLookupProperty(): string
    {
        return $this->lookupProperty;
    }
}
