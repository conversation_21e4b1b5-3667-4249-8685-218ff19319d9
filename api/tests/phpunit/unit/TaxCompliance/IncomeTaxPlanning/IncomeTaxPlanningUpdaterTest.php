<?php

declare(strict_types=1);
namespace Tests\Unit\U2\TaxCompliance\IncomeTaxPlanning;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\TaxCompliance\IncomeTaxPlanning\IncomeTaxPlanningCalculator;
use U2\TaxCompliance\IncomeTaxPlanning\IncomeTaxPlanningUpdater;

class IncomeTaxPlanningUpdaterTest extends UnitTestCase
{
    private IncomeTaxPlanningUpdater $incomeTaxPlanningUpdater;

    /**
     * @var IncomeTaxPlanningCalculator&MockObject
     */
    private MockObject $incomeTaxPlanningCalculator;

    protected function setUp(): void
    {
        $this->incomeTaxPlanningCalculator = $this->createMock(IncomeTaxPlanningCalculator::class);
        $this->incomeTaxPlanningUpdater = new IncomeTaxPlanningUpdater($this->incomeTaxPlanningCalculator);
    }

    public function test_updates_income_tax_planning(): void
    {
        $incomeTaxPlanning = $this->createMock(IncomeTaxPlanning::class);
        $this->incomeTaxPlanningCalculator->expects($this->atLeastOnce())
            ->method('calculateTaxableIncome')
            ->with(self::equalTo($incomeTaxPlanning))
            ->willReturn('1');
        $incomeTaxPlanning->expects($this->atLeastOnce())->method('setTaxableIncome')->with(self::equalTo('1'));
        $this->incomeTaxPlanningCalculator->expects($this->atLeastOnce())
            ->method('calculateIncomeTax')
            ->with(self::equalTo($incomeTaxPlanning))
            ->willReturn('2');
        $incomeTaxPlanning->expects($this->atLeastOnce())->method('setIncomeTax')->with(self::equalTo('2'));
        $this->incomeTaxPlanningCalculator->expects($this->atLeastOnce())->method('calculateEtr')->with(self::equalTo($incomeTaxPlanning))->willReturn('3');
        $incomeTaxPlanning->expects($this->atLeastOnce())->method('setEtr')->with(self::equalTo('3'));
        $this->incomeTaxPlanningUpdater->update($incomeTaxPlanning);
    }
}
