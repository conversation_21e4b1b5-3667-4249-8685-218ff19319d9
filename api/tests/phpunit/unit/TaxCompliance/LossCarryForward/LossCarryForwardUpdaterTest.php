<?php

declare(strict_types=1);
namespace Tests\Unit\U2\TaxCompliance\LossCarryForward;

use PHPUnit\Framework\MockObject\MockObject;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\TaxCompliance\LossCarryForward\LossCarryForwardCalculator;
use U2\TaxCompliance\LossCarryForward\LossCarryForwardUpdater;

class LossCarryForwardUpdaterTest extends UnitTestCase
{
    private LossCarryForwardUpdater $lossCarryForwardUpdater;

    /**
     * @var LossCarryForwardCalculator&MockObject
     */
    private MockObject $lossCarryForwardCalculator;

    protected function setUp(): void
    {
        $this->lossCarryForwardCalculator = $this->createMock(LossCarryForwardCalculator::class);
        $this->lossCarryForwardUpdater = new LossCarryForwardUpdater($this->lossCarryForwardCalculator);
    }

    public function test_updates_loss_carry_forwards(): void
    {
        $lossCarryForward = $this->createMock(LossCarryForward::class);
        $this->lossCarryForwardCalculator->expects($this->atLeastOnce())
            ->method('calculateEndOfYear')
            ->with(self::equalTo($lossCarryForward))
            ->willReturn('1');
        $lossCarryForward->expects($this->atLeastOnce())->method('setEndOfYear')->with(self::equalTo('1'));
        $this->lossCarryForwardUpdater->update($lossCarryForward);
    }
}
