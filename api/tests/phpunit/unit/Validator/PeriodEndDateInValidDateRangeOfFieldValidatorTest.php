<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Validator;

use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\OtherDeadlineFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\TaxAuditRiskFactory;
use U2\Validator\PeriodEndDateInValidDateRangeOfField;
use U2\Validator\PeriodEndDateInValidDateRangeOfFieldValidator;
use Zenstruck\Foundry\Test\Factories;

/**
 * @extends ConstraintValidatorTestCase<PeriodEndDateInValidDateRangeOfFieldValidator>
 */
class PeriodEndDateInValidDateRangeOfFieldValidatorTest extends ConstraintValidatorTestCase
{
    use Factories;

    public function test_validates_as_valid_if_subject_has_no_period_set(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('yesterday'), 'validTo' => null]);
        $subject = TaxAuditRiskFactory::getObject(['period' => null, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_invalid_if_the_period_end_date_is_before_the_valid_from_date(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('tomorrow'), 'validTo' => null]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->buildViolation('u2.value_is_not_valid_for_the_end_date_of_the_selected_period')
            ->setParameter('%date%', (new \DateTime())->format('d.m.Y'))
            ->atPath('property.path.unit')
            ->assertRaised();
    }

    public function test_validates_as_invalid_if_the_period_end_date_is_after_the_valid_to_date(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $unit = LegalUnitFactory::getObject(['validFrom' => null, 'validTo' => new \DateTime('yesterday')]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->buildViolation('u2.value_is_not_valid_for_the_end_date_of_the_selected_period')
            ->setParameter('%date%', (new \DateTime())->format('d.m.Y'))
            ->atPath('property.path.unit')
            ->assertRaised();
    }

    public function test_throws_an_exception_if_the_configured_field_is_not_accessible(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('field'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_the_value_of_the_configured_field_does_not_implement_valid_date_range_interface(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('additions'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_valid_from_and_valid_to_dates_are_not_set(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $unit = LegalUnitFactory::getObject(['validFrom' => null, 'validTo' => null]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_the_period_end_date_is_after_the_valid_from_date(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('yesterday'), 'validTo' => null]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_the_period_end_date_is_before_the_valid_to_date(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $unit = LegalUnitFactory::getObject(['validFrom' => null, 'validTo' => new \DateTime('tomorrow')]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_the_period_end_date_is_between_valid_from_and_valid_to_dates(): void
    {
        $period = PeriodFactory::getObject(['endDate' => new \DateTime()]);
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('yesterday'), 'validTo' => new \DateTime('tomorrow')]);
        $subject = TaxAuditRiskFactory::getObject(['period' => $period, 'unit' => $unit]);

        $this->validator->validate($subject, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_invalid_if_there_is_no_period_field_and_the_valid_from_date_has_not_yet_been_reached_yet(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('tomorrow'), 'validTo' => null]);
        $subjectWithoutPeriod = OtherDeadlineFactory::getObject(['unit' => $unit]);

        $this->validator->validate($subjectWithoutPeriod, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->buildViolation('u2.value_is_not_currently_in_its_valid_date_range')
            ->atPath('property.path.unit')
            ->assertRaised();
    }

    public function test_validates_as_invalid_if_there_is_no_period_field_and_the_valid_to_date_has_passed(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => null, 'validTo' => new \DateTime('yesterday')]);
        $subjectWithoutPeriod = OtherDeadlineFactory::getObject(['unit' => $unit]);

        $this->validator->validate($subjectWithoutPeriod, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->buildViolation('u2.value_is_not_currently_in_its_valid_date_range')
            ->atPath('property.path.unit')
            ->assertRaised();
    }

    public function test_validates_as_valid_if_there_is_no_period_field_and_the_valid_from_and_valid_to_dates_are_not_set(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => null, 'validTo' => null]);
        $subjectWithoutPeriod = OtherDeadlineFactory::getObject(['unit' => $unit]);

        $this->validator->validate($subjectWithoutPeriod, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_there_is_no_period_field_and_the_valid_from_date_has_passed(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('yesterday'), 'validTo' => null]);
        $subjectWithoutPeriod = OtherDeadlineFactory::getObject(['unit' => $unit]);

        $this->validator->validate($subjectWithoutPeriod, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_there_is_no_period_field_and_the_valid_to_date_has_not_been_reached(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => null, 'validTo' => new \DateTime('tomorrow')]);
        $subjectWithoutPeriod = OtherDeadlineFactory::getObject(['unit' => $unit]);

        $this->validator->validate($subjectWithoutPeriod, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    public function test_validates_as_valid_if_there_is_no_period_field_and_current_date_is_between_valid_from_and_valid_to_dates(): void
    {
        $unit = LegalUnitFactory::getObject(['validFrom' => new \DateTime('yesterday'), 'validTo' => new \DateTime('tomorrow')]);
        $subjectWithoutPeriod = OtherDeadlineFactory::getObject(['unit' => $unit]);

        $this->validator->validate($subjectWithoutPeriod, new PeriodEndDateInValidDateRangeOfField('unit'));

        $this->assertNoViolation();
    }

    protected function createValidator(): PeriodEndDateInValidDateRangeOfFieldValidator
    {
        return new PeriodEndDateInValidDateRangeOfFieldValidator(PropertyAccess::createPropertyAccessor());
    }
}
