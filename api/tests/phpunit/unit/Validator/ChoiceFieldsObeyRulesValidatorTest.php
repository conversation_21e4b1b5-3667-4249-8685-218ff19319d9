<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Validator;

use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\PropertyAccess\PropertyAccess;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\Test\ConstraintValidatorTestCase;
use U2\DataFixtures\Example\ApmTransactionFactory;
use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\DataFixtures\Example\Igt1TransactionFactory;
use U2\DataFixtures\Example\TraceIdFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\Entity\Task\TaskType;
use U2\Entity\Unit;
use U2\Exception\Exception;
use U2\Task\TaskChoiceFields;
use U2\Validator\ChoiceFieldsObeyRules;
use U2\Validator\ChoiceFieldsObeyRulesValidator;
use Zenstruck\Foundry\Object\Instantiator;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\UnitTestConfig;

/**
 * @extends ConstraintValidatorTestCase<ChoiceFieldsObeyRulesValidator>
 */
class ChoiceFieldsObeyRulesValidatorTest extends ConstraintValidatorTestCase
{
    use Factories;

    public static function setUpBeforeClass(): void
    {
        $instantiator = Instantiator::withConstructor()->allowExtra();
        UnitTestConfig::configure($instantiator);
    }

    public function test_throws_an_exception_if_the_wrong_constrain_is_provided(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage(\sprintf('This validator supports only "%s" constraints', ChoiceFieldsObeyRules::class));

        $this->validator->validate($this->createMock(TaskType::class), $this->createMock(Constraint::class));
    }

    public function test_throws_an_exception_if_the_configured_field_is_not_accessible(): void
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage(\sprintf('This validator supports only "%s" classes', TaskType::class));

        $this->validator->validate($this->createMock(Unit::class), new ChoiceFieldsObeyRules());
    }

    /**
     * @param ModelFactory<TaskType> $factory
     */
    #[DataProvider('provideInvalidTasks')]
    public function test_invalid_when_task_does_not_obey_rules(ModelFactory $factory): void
    {
        $this->validator->validate($factory->create()->_real(), new ChoiceFieldsObeyRules());

        $this->buildViolation('u2.choice_field_does_not_meet_rule')
            ->atPath('property.path.traceId')
            ->assertRaised();
    }

    /**
     * @return iterable<array<covariant ModelFactory>>
     */
    public static function provideInvalidTasks(): iterable
    {
        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => [1, 2]],
                    ['fact' => 'unknown', 'operator' => 'in', 'value' => [1, 2]],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1000]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => [1, 2]],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1000]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => [1, 2]],
                    ['fact' => 'partnerUnit.id', 'operator' => 'in', 'value' => [1, 2]],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1]),
            'partnerUnit' => UnitFactory::new(['id' => 1000]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => [1, 2]],
                    ['fact' => 'partnerUnit.id', 'operator' => 'in', 'value' => [1, 2]],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1000]),
            'partnerUnit' => UnitFactory::new(['id' => 1]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => []],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1000]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'type', 'operator' => 'in', 'value' => ['apm_transaction']],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1]),
            'partnerUnit' => UnitFactory::new(['id' => 1]),
        ])];
    }

    /**
     * @param ModelFactory<TaskType> $factory
     */
    #[DataProvider('provideValidTasks')]
    public function test_valid_when_task_obeys_rules(ModelFactory $factory): void
    {
        $this->validator->validate($factory->create()->_real(), new ChoiceFieldsObeyRules());

        $this->assertNoViolation();
    }

    /**
     * @return iterable<array<covariant ModelFactory>>
     */
    public static function provideValidTasks(): iterable
    {
        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => [1, 2]],
                    ['fact' => 'partnerUnit.id', 'operator' => 'in', 'value' => [1, 2]],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1]),
            'partnerUnit' => UnitFactory::new(['id' => 1]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unit.id', 'operator' => 'in', 'value' => [5, 6, 7]],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 7]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'unknown', 'operator' => 'in', 'value' => [5, 6, 7]],
                ],
            ]]),
        ])];

        yield [Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => [
                'all' => [
                    ['fact' => 'type', 'operator' => 'in', 'value' => ['igt_igt1_transaction']],
                ],
            ]]),
            'unit' => UnitFactory::new(['id' => 1]),
            'partnerUnit' => UnitFactory::new(['id' => 1]),
        ])];

        yield [ApmTransactionFactory::new()];
    }

    public function test_valid_when_the_rule_is_null(): void
    {
        $subject = Igt1TransactionFactory::new([
            'traceId' => TraceIdFactory::new(['rules' => null]),
            'unit' => UnitFactory::new(['id' => 1]),
            'partnerUnit' => UnitFactory::new(['id' => 5]),
        ]);

        $this->validator->validate($subject->create()->_real(), new ChoiceFieldsObeyRules());

        $this->assertNoViolation();
    }

    protected function createValidator(): ChoiceFieldsObeyRulesValidator
    {
        $choiceFields = $this->createMock(TaskChoiceFields::class);
        $choiceFields->method('getByTaskType')
            ->willReturnCallback(function ($class): array {
                return match ($class) {
                    TaskType\Igt1Transaction::class => ['traceId'],
                    default => [],
                };
            });

        return new ChoiceFieldsObeyRulesValidator(
            PropertyAccess::createPropertyAccessor(),
            $choiceFields
        );
    }
}
