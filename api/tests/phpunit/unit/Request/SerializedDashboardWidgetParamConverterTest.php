<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Request;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use Tests\U2\UnitTestCase;
use U2\Request\SerializedDashboardWidgetResolver;
use U2\Widget\Dashboard\DashboardWidgetInterface;
use U2\Widget\Dashboard\DashboardWidgetSerializer;

class SerializedDashboardWidgetParamConverterTest extends UnitTestCase
{
    public function test_converts_a_widget_configuration_form_request_into_a_widget(): void
    {
        $widget = $this->createMock(DashboardWidgetInterface::class);
        $request = new Request(query: ['widget' => '{json}']);
        $widgetSerializer = $this->createMock(DashboardWidgetSerializer::class);
        $widgetSerializer
            ->expects($this->atLeastOnce())
            ->method('deserialize')->with('{json}')->willReturn($widget);
        $resolver = new SerializedDashboardWidgetResolver($widgetSerializer);

        $result = $resolver->resolve($request, new ArgumentMetadata('widget', DashboardWidgetInterface::class, false, false, null));

        self::assertEquals([$widget], $result);
    }
}
