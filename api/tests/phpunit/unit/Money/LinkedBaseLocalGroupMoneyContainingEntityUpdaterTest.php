<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Money;

use Symfony\Component\PropertyAccess\PropertyAccessorInterface;
use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\PeriodFactory;
use U2\Entity\BaseLocalGroupMoney;
use U2\Entity\Currency;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Unit;
use U2\Entity\Workflow\Status;
use U2\Money\DependentPropertyUpdater;
use U2\Money\LinkedBaseLocalGroupMoneyContainingEntityUpdater;

class LinkedBaseLocalGroupMoneyContainingEntityUpdaterTest extends UnitTestCase
{
    public function test_it_updates_each_base_local_group_money_property(): void
    {
        $entity = new FinancialData(new Status());
        $period = PeriodFactory::getObject();
        $entity->setPeriod($period);

        $unit = new Unit();
        $localCurrency = new Currency();
        $unit->setCurrency($localCurrency);
        $entity->setUnit($unit);

        $property1 = new BaseLocalGroupMoney();
        $property2 = new BaseLocalGroupMoney();
        $property3 = new BaseLocalGroupMoney();
        $property4 = new BaseLocalGroupMoney();
        $property5 = new BaseLocalGroupMoney();
        $property6 = new BaseLocalGroupMoney();
        $property7 = new BaseLocalGroupMoney();
        $property8 = new BaseLocalGroupMoney();
        $property9 = new BaseLocalGroupMoney();

        $propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $propertyAccessor
            ->method('getValue')
            ->willReturnCallback(
                static fn ($object, $property): BaseLocalGroupMoney => match ($property) {
                    'totalRevenueUnrelatedValue' => $property1,
                    'totalRevenueRelatedValue' => $property2,
                    'totalRevenueValue' => $property3,
                    'profitLossBeforeIncomeTaxValue' => $property4,
                    'incomeTaxPaidValue' => $property5,
                    'incomeTaxAccruedValue' => $property6,
                    'statedCapitalValue' => $property7,
                    'accumulatedEarningsValue' => $property8,
                    'tangibleAssetsValue' => $property9,
                    default => throw new \InvalidArgumentException('Unexpected property'),
                }
            );

        $dependentPropertyUpdater = $this->createMock(DependentPropertyUpdater::class);
        $dependentPropertyUpdater
            ->expects($this->exactly(9))
            ->method('update')
            ->with(self::callback(
                static fn ($property): bool => \in_array(
                    $property,
                    [$property1, $property2, $property3, $property4, $property5, $property6, $property7, $property8, $property9],
                    true
                )
            ),
                self::callback(
                    static fn ($currency): bool => $currency === $localCurrency
                ),
                self::callback(
                    static fn ($actualPeriod): bool => $actualPeriod === $period
                )
            );

        $linkedBasedLocalGroupMoneyContainingEntityUpdater = new LinkedBaseLocalGroupMoneyContainingEntityUpdater(
            $dependentPropertyUpdater,
            $propertyAccessor
        );

        $linkedBasedLocalGroupMoneyContainingEntityUpdater->update($entity);
    }

    public function test_it_does_not_update_if_the_unit_is_null(): void
    {
        $entity = new FinancialData(new Status());
        $period = PeriodFactory::getObject();
        $entity->setPeriod($period);

        $entity->setUnit(null);

        $property1 = new BaseLocalGroupMoney();
        $property2 = new BaseLocalGroupMoney();
        $property3 = new BaseLocalGroupMoney();
        $property4 = new BaseLocalGroupMoney();
        $property5 = new BaseLocalGroupMoney();
        $property6 = new BaseLocalGroupMoney();
        $property7 = new BaseLocalGroupMoney();
        $property8 = new BaseLocalGroupMoney();
        $property9 = new BaseLocalGroupMoney();

        $propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $propertyAccessor
            ->method('getValue')
            ->willReturnCallback(
                static fn ($object, $property): BaseLocalGroupMoney => match ($property) {
                    'totalRevenueUnrelatedValue' => $property1,
                    'totalRevenueRelatedValue' => $property2,
                    'totalRevenueValue' => $property3,
                    'profitLossBeforeIncomeTaxValue' => $property4,
                    'incomeTaxPaidValue' => $property5,
                    'incomeTaxAccruedValue' => $property6,
                    'statedCapitalValue' => $property7,
                    'accumulatedEarningsValue' => $property8,
                    'tangibleAssetsValue' => $property9,
                    default => throw new \InvalidArgumentException('Unexpected property'),
                }
            );

        $dependentPropertyUpdater = $this->createMock(DependentPropertyUpdater::class);
        $dependentPropertyUpdater
            ->expects($this->never())
            ->method('update');

        $linkedBasedLocalGroupMoneyContainingEntityUpdater = new LinkedBaseLocalGroupMoneyContainingEntityUpdater(
            $dependentPropertyUpdater,
            $propertyAccessor
        );

        $linkedBasedLocalGroupMoneyContainingEntityUpdater->update($entity);
    }

    public function test_it_does_not_try_to_update_if_the_property_is_null(): void
    {
        $entity = new FinancialData(new Status());
        $period = PeriodFactory::getObject();
        $entity->setPeriod($period);

        $unit = new Unit();
        $localCurrency = new Currency();
        $unit->setCurrency($localCurrency);
        $entity->setUnit($unit);

        $property1 = new BaseLocalGroupMoney();
        $property2 = new BaseLocalGroupMoney();
        $property3 = null;
        $property4 = new BaseLocalGroupMoney();
        $property5 = new BaseLocalGroupMoney();
        $property6 = null;
        $property7 = new BaseLocalGroupMoney();
        $property8 = null;
        $property9 = new BaseLocalGroupMoney();

        $propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $propertyAccessor
            ->method('getValue')
            ->willReturnCallback(
                static fn ($object, $property): ?BaseLocalGroupMoney => match ($property) {
                    'totalRevenueUnrelatedValue' => $property1,
                    'totalRevenueRelatedValue' => $property2,
                    'totalRevenueValue' => $property3,
                    'profitLossBeforeIncomeTaxValue' => $property4,
                    'incomeTaxPaidValue' => $property5,
                    'incomeTaxAccruedValue' => $property6,
                    'statedCapitalValue' => $property7,
                    'accumulatedEarningsValue' => $property8,
                    'tangibleAssetsValue' => $property9,
                    default => throw new \InvalidArgumentException('Unexpected property'),
                }
            );

        $dependentPropertyUpdater = $this->createMock(DependentPropertyUpdater::class);
        $dependentPropertyUpdater
            ->expects($this->exactly(6))
            ->method('update')
            ->with(self::callback(
                static fn ($property): bool => \in_array(
                    $property,
                    [$property1, $property2, $property3, $property4, $property5, $property6, $property7, $property8, $property9],
                    true
                )
            ),
                self::callback(
                    static fn ($currency): bool => $currency === $localCurrency
                ),
                self::callback(
                    static fn ($actualPeriod): bool => $actualPeriod === $period
                )
            );

        $linkedBasedLocalGroupMoneyContainingEntityUpdater = new LinkedBaseLocalGroupMoneyContainingEntityUpdater(
            $dependentPropertyUpdater,
            $propertyAccessor
        );

        $linkedBasedLocalGroupMoneyContainingEntityUpdater->update($entity);
    }
}
