<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Workflow\Action\Executor;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Tests\U2\UnitTestCase;
use U2\Datasheets\Item\UnitValue\DefaultSetter;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\User;
use U2\Entity\Workflow\Action\SetDefaultItemValuesAction;
use U2\Entity\Workflow\Status;
use U2\Entity\Workflow\Transition;
use U2\Event\WorkflowPostTransitionEvent;
use U2\Workflow\Action\Executor\SetDefaultItemValuesActionExecutor;

class SetDefaultItemValuesActionExecutorTest extends UnitTestCase
{
    public function test_resets_item_value_defaults(): void
    {
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->once())->method('flush');
        $defaultSetter = $this->createMock(DefaultSetter::class);
        $defaultSetter->expects($this->once())->method('set');

        $transition = $this->createMock(Transition::class);
        $setDefaultItemValuesAction = new SetDefaultItemValuesAction($transition);
        $transition->expects($this->atLeastOnce())->method('getActions')->willReturn(new ArrayCollection([$setDefaultItemValuesAction]));
        $entity = new UnitPeriod(new Status());
        $event = $this->createMock(WorkflowPostTransitionEvent::class);
        $event->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $event->expects($this->atLeastOnce())->method('getTransition')->willReturn($transition);
        $resetReviewsActionExecutor = new SetDefaultItemValuesActionExecutor(
            $entityManager,
            $defaultSetter,
        );

        $resetReviewsActionExecutor->execute($event);
    }

    public function test_does_not_reset_if_the_task_type_is_not_unit_period(): void
    {
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->never())->method('flush');
        $defaultSetter = $this->createMock(DefaultSetter::class);
        $defaultSetter->expects($this->never())->method('set');
        $event = $this->createMock(WorkflowPostTransitionEvent::class);
        $entity = new OtherDeadline(new Status());
        $event->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $resetReviewsActionExecutor = new SetDefaultItemValuesActionExecutor(
            $entityManager,
            $defaultSetter,
        );

        $resetReviewsActionExecutor->execute($event);
    }

    public function test_does_not_reset_for_transitions_without_the_action(): void
    {
        $entityManager = $this->createMock(EntityManagerInterface::class);
        $entityManager->expects($this->never())->method('flush');
        $defaultSetter = $this->createMock(DefaultSetter::class);
        $defaultSetter->expects($this->never())->method('set');
        $entity = new UnitPeriod(new Status());
        $entity->getTask()->review(new User());
        $transition = $this->createMock(Transition::class);
        $transition->expects($this->atLeastOnce())->method('getActions')->willReturn(new ArrayCollection());
        $event = $this->createMock(WorkflowPostTransitionEvent::class);
        $event->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $event->expects($this->atLeastOnce())->method('getTransition')->willReturn($transition);
        $resetReviewsActionExecutor = new SetDefaultItemValuesActionExecutor(
            $entityManager,
            $defaultSetter,
        );

        $resetReviewsActionExecutor->execute($event);
    }
}
