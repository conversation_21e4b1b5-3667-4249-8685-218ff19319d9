<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Workflow\Bindings;

use Tests\U2\UnitTestCase;
use U2\Entity\Workflow\Binding;
use U2\Entity\Workflow\Workflow;
use U2\Workflow\Bindings\AuthorizationItemResolver;

class AuthorizationItemResolverTest extends UnitTestCase
{
    public function test_convert(): void
    {
        $authorizationItemResolver = new AuthorizationItemResolver();
        self::assertSame(
            'TEST_AUTHORIZATION',
            $authorizationItemResolver->resolve(new Binding('test_authorization', new Workflow()))
        );
    }
}
