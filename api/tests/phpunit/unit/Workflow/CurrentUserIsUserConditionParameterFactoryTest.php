<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Workflow;

use Tests\U2\UnitTestCase;
use U2\Entity\User;
use U2\Repository\UserRepository;
use U2\Workflow\Condition\CurrentUserIsUserConditionParameterFactory;

class CurrentUserIsUserConditionParameterFactoryTest extends UnitTestCase
{
    public function test_create(): void
    {
        $user = new User();

        $userRepository = $this->createMock(UserRepository::class);
        $userRepository
            ->expects($this->once())
            ->method('find')
            ->with(1)
            ->willReturn($user);

        $factory = new CurrentUserIsUserConditionParameterFactory($userRepository);

        self::assertSame(
            [
                'parameters' => [
                    'users' => [
                        $user,
                    ],
                ],
            ],
            $factory->create(
                [
                    'parameters' => [
                        'users' => [
                            1,
                        ],
                    ],
                ]
            ));
    }
}
