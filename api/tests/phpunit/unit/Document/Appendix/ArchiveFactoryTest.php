<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Document\Appendix;

use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\UnitTestCase;
use U2\Archive\ZipArchive;
use U2\Archive\ZipFactory;
use U2\DataFixtures\Example\FileFactory;
use U2\DataFixtures\Example\MasterFileSectionFactory;
use U2\Document\Appendix\ArchiveFactory;
use U2\Entity\Task\TaskType\AbstractDocument;
use U2\Entity\User;
use U2\File\Attachment;
use U2\File\Helper;
use U2\FileSystem\TenantFilesystemOperator;
use U2\Security\Voter\VoterAttributes;

class ArchiveFactoryTest extends UnitTestCase
{
    public function test_gets_all_user_accessible_files_and_adds_them_to_archive(): void
    {
        // Given
        $zipFactory = $this->createMock(ZipFactory::class);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $filesystem = $this->createMock(TenantFilesystemOperator::class);
        $archiveFactory = new ArchiveFactory(
            $zipFactory,
            $authorizationChecker,
            $filesystem
        );

        $user = new User();
        $file1 = FileFactory::getObject(['id' => 1, 'path' => 'file_1.txt', 'name' => 'File 1.txt', 'createdBy' => $user, 'updatedBy' => $user]);
        $file2 = FileFactory::getObject(['id' => 2, 'createdBy' => $user, 'updatedBy' => $user]);
        $file3 = FileFactory::getObject(['id' => 3, 'path' => 'file_3.doc', 'name' => 'File 3.doc', 'createdBy' => $user, 'updatedBy' => $user]);

        $section1 = MasterFileSectionFactory::getObject(['id' => 1, 'files' => new ArrayCollection([$file1, $file2, $file3])]);
        $section2 = MasterFileSectionFactory::getObject(['id' => 2, 'files' => new ArrayCollection([$file1, $file2])]);

        $document = $this->createMock(AbstractDocument::class);
        $document
            ->method('getSections')
            ->willReturn(new ArrayCollection([$section1, $section2]));

        // Ensure we check permissions for every attachment
        $authorizationChecker
            ->expects($this->exactly(5))
            ->method('isGranted')
            ->willReturnCallback(fn (string $a, Attachment $b): bool => match (
                [VoterAttributes::read, $b->getFile()->getId(), $b->getEntity()->getId()]
            ) {
                [VoterAttributes::read, 1, 1] => true,
                [VoterAttributes::read, 2, 1] => false,
                [VoterAttributes::read, 3, 1] => true,
                [VoterAttributes::read, 1, 2] => true,
                [VoterAttributes::read, 2, 2] => false,
                default => throw new \Exception('Unexpected attributes' . VoterAttributes::read . $b->getFile()->getId() . $b->getEntity()->getId()),
            });

        $filesystem
            ->expects($this->exactly(3))
            ->method('read')
            ->willReturnCallback(fn (string $location): string => match ($location) {
                Helper::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . 'file_1.txt' => 'content 1',
                Helper::attachmentsSubdirectory . \DIRECTORY_SEPARATOR . 'file_3.doc' => 'content 3',
                default => throw new \Exception('Unexpected attributes' . $location),
            });

        $archive = $this->createMock(ZipArchive::class);
        $zipFactory->expects($this->once())->method('create')->willReturn($archive);

        // Then
        $archive
            ->expects($this->exactly(3)) // only 3 of 5 attachments are added due to permissions
            ->method('addStringAsFile')
            ->willReturnCallback(fn (string $string, string $destination): MockObject => match ([$string, $destination]) {
                ['content 1', '1-File 1.txt'] => $archive, // Internally this will only be added once because it has the same path
                ['content 3', '3-File 3.doc'] => $archive,
                default => throw new \Exception('Unexpected attributes'),
            });
        $archive->expects($this->once())->method('save')->willReturn('the archive filename');

        // When
        self::assertSame('the archive filename', $archiveFactory->create($document));
    }
}
