<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Document\Structure\SectionListManipulation;

use Doctrine\Common\Collections\ArrayCollection;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Symfony\Component\Validator\Violation\ConstraintViolationBuilderInterface;
use Tests\U2\UnitTestCase;
use U2\Document\Structure\HierarchicalBrowser;
use U2\Document\Structure\SectionListManipulation\Addition;
use U2\Document\Structure\SectionListManipulation\SectionPlacement;
use U2\Entity\Task\TaskType\AbstractDocument;

class AdditionAsSubsectionTest extends UnitTestCase
{
    public function test_it_fails_if_section_to_add_as_subsection_of_does_not_belong_to_document(): void
    {
        $testHelper = new ManipulationTestHelper();

        $sectionToBeAdded = $testHelper->makeNewSection();
        $sectionToMoveAsSubsectionOf = $testHelper->makeSection(1, 1);

        $sectionListBeforeAdding = new ArrayCollection([$sectionToBeAdded]);

        $document = $this->createMock(AbstractDocument::class);
        $testHelper->setupDocumentSections($document, $sectionListBeforeAdding);
        $document
            ->expects($this->never())
            ->method('setSections')
            ->with(self::anything());

        $context = $this->createMock(ExecutionContextInterface::class);
        $testHelper->setupValidationContext(
            $context,
            $this->createMock(ConstraintViolationBuilderInterface::class),
            'u2.structured_section.section_does_not_belong_to_the_same_document'
        );

        $addition = new Addition($sectionToBeAdded, $document, new HierarchicalBrowser());
        $addition->setPlacement(SectionPlacement::SUBSECTION_OF);
        $addition->setReferenceSection($sectionToMoveAsSubsectionOf);
        $addition->validateReferenceSectionDocument($context);
    }

    public function test_it_can_add_section_as_subsection_of_another_section(): void
    {
        $testHelper = new ManipulationTestHelper();

        $sectionToBeAdded = $testHelper->makeNewSection();
        $sectionToMoveAsSubsectionOf = $testHelper->makeSection(1, 1);

        $sectionListBeforeAdding = new ArrayCollection(
            [
                $sectionToMoveAsSubsectionOf,
            ]
        );
        $expectedSectionListAfterAdding = new ArrayCollection(
            [
                $sectionToMoveAsSubsectionOf,
                $sectionToBeAdded,
            ]
        );

        $document = $this->createMock(AbstractDocument::class);
        $testHelper->setupDocumentSections($document, $sectionListBeforeAdding);
        $document
            ->expects($this->once())
            ->method('setSections')
            ->with($expectedSectionListAfterAdding);

        $addition = new Addition($sectionToBeAdded, $document, new HierarchicalBrowser());
        $addition->setPlacement(SectionPlacement::SUBSECTION_OF);
        $addition->setReferenceSection($sectionToMoveAsSubsectionOf);
        $addition->manipulate();

        $testHelper->assertSection($sectionToMoveAsSubsectionOf, 1, 1);
        $testHelper->assertSection($sectionToBeAdded, 2, 2);
        self::assertSame($document, $sectionToBeAdded->getDocument());
    }

    public function test_it_can_add_section_as_subsection_of_another_section_with_subsections(): void
    {
        $testHelper = new ManipulationTestHelper();

        $sectionToBeAdded = $testHelper->makeNewSection();
        $sectionToMoveAsSubsectionOf = $testHelper->makeSection(1, 1);
        $subsection1OfSectionToMoveAsSubsectionOf = $testHelper->makeSection(2, 2);
        $subsection2OfSectionToMoveAsSubsectionOf = $testHelper->makeSection(3, 2);

        $sectionListBeforeAdding = new ArrayCollection(
            [
                $sectionToMoveAsSubsectionOf,
                $subsection1OfSectionToMoveAsSubsectionOf,
                $subsection2OfSectionToMoveAsSubsectionOf,
            ]
        );
        $expectedSectionListAfterAdding = new ArrayCollection(
            [
                $sectionToMoveAsSubsectionOf,
                $subsection1OfSectionToMoveAsSubsectionOf,
                $subsection2OfSectionToMoveAsSubsectionOf,
                $sectionToBeAdded,
            ]
        );

        $document = $this->createMock(AbstractDocument::class);
        $testHelper->setupDocumentSections($document, $sectionListBeforeAdding);
        $document
            ->expects($this->once())
            ->method('setSections')
            ->with($expectedSectionListAfterAdding);

        $addition = new Addition($sectionToBeAdded, $document, new HierarchicalBrowser());
        $addition->setPlacement(SectionPlacement::SUBSECTION_OF);
        $addition->setReferenceSection($sectionToMoveAsSubsectionOf);
        $addition->manipulate();

        $testHelper->assertSection($sectionToMoveAsSubsectionOf, 1, 1);
        $testHelper->assertSection($subsection1OfSectionToMoveAsSubsectionOf, 2, 2);
        $testHelper->assertSection($subsection2OfSectionToMoveAsSubsectionOf, 3, 2);
        $testHelper->assertSection($sectionToBeAdded, 4, 2);
        self::assertSame($document, $sectionToBeAdded->getDocument());
    }

    public function test_it_can_add_subsection_between_two_subsection_structures(): void
    {
        $testHelper = new ManipulationTestHelper();

        $sectionToBeAdded = $testHelper->makeNewSection();
        $sectionA = $testHelper->makeSection(1, 1);  // A
        $sectionB = $testHelper->makeSection(2, 2);  //   B
        $sectionC = $testHelper->makeSection(3, 3);  //     C
        $sectionD = $testHelper->makeSection(4, 2);  //   D
        $sectionE = $testHelper->makeSection(5, 1);  // E

        $sectionListBeforeAdding = new ArrayCollection(
            [
                $sectionA,
                $sectionB,
                $sectionC,
                $sectionD,
                $sectionE,
            ]
        );
        $expectedSectionListAfterAdding = new ArrayCollection(
            [
                $sectionA,
                $sectionB,
                $sectionC,
                $sectionToBeAdded,
                $sectionD,
                $sectionE,
            ]
        );

        $document = $this->createMock(AbstractDocument::class);
        $testHelper->setupDocumentSections($document, $sectionListBeforeAdding);
        $document
            ->expects($this->once())
            ->method('setSections')
            ->with($expectedSectionListAfterAdding);

        $addition = new Addition($sectionToBeAdded, $document, new HierarchicalBrowser());
        $addition->setPlacement(SectionPlacement::SUBSECTION_OF);
        $addition->setReferenceSection($sectionC);
        $addition->manipulate();

        $testHelper->assertSection($sectionA, 1, 1);          // A
        $testHelper->assertSection($sectionB, 2, 2);          //   B
        $testHelper->assertSection($sectionC, 3, 3);          //     C
        $testHelper->assertSection($sectionToBeAdded, 4, 4);  //       new
        $testHelper->assertSection($sectionD, 5, 2);          //   D
        $testHelper->assertSection($sectionE, 6, 1);          // E
        self::assertSame($document, $sectionToBeAdded->getDocument());
    }

    public function test_it_can_add_next_subsection_between_two_subsection_structures(): void
    {
        $testHelper = new ManipulationTestHelper();

        $sectionToBeAdded = $testHelper->makeNewSection();
        $sectionA = $testHelper->makeSection(1, 1);  // A
        $sectionB = $testHelper->makeSection(2, 2);  //   B
        $sectionC = $testHelper->makeSection(3, 3);  //     C
        $sectionD = $testHelper->makeSection(4, 2);  //   D
        $sectionE = $testHelper->makeSection(5, 1);  // E

        $sectionListBeforeAdding = new ArrayCollection(
            [
                $sectionA,
                $sectionB,
                $sectionC,
                $sectionD,
                $sectionE,
            ]
        );
        $expectedSectionListAfterAdding = new ArrayCollection(
            [
                $sectionA,
                $sectionB,
                $sectionC,
                $sectionToBeAdded,
                $sectionD,
                $sectionE,
            ]
        );

        $document = $this->createMock(AbstractDocument::class);
        $testHelper->setupDocumentSections($document, $sectionListBeforeAdding);
        $document
            ->expects($this->once())
            ->method('setSections')
            ->with($expectedSectionListAfterAdding);

        $addition = new Addition($sectionToBeAdded, $document, new HierarchicalBrowser());
        $addition->setPlacement(SectionPlacement::SUBSECTION_OF);
        $addition->setReferenceSection($sectionB);
        $addition->manipulate();

        $testHelper->assertSection($sectionA, 1, 1);          // A
        $testHelper->assertSection($sectionB, 2, 2);          //   B
        $testHelper->assertSection($sectionC, 3, 3);          //     C
        $testHelper->assertSection($sectionToBeAdded, 4, 3);  //     new
        $testHelper->assertSection($sectionD, 5, 2);          //   D
        $testHelper->assertSection($sectionE, 6, 1);          // E
        self::assertSame($document, $sectionToBeAdded->getDocument());
    }
}
