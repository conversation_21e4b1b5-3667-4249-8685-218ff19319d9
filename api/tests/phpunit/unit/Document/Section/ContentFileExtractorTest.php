<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Document\Section;

use Doctrine\Common\Collections\ArrayCollection;
use Tests\U2\UnitTestCase;
use U2\Document\Section\ContentFileExtractor;
use U2\Entity\File;
use U2\Repository\FileRepository;
use U2\Widget\Document\DocumentWidgetInterface;
use U2\Widget\Document\Encoder;
use U2\Widget\Document\File as FileWidget;
use U2\Widget\Document\Image;

class ContentFileExtractorTest extends UnitTestCase
{
    public function test_file_extraction_from_content(): void
    {
        // When
        $fileWidget = $this->createMock(FileWidget::class);
        $fileWidget->method('getId')->willReturn(1);

        $imageWidget = $this->createMock(Image::class);
        $imageWidget->method('getId')->willReturn(2);

        $encoder = $this->createMock(Encoder::class);
        $encoder->method('decode')->willReturnCallback(function (string $name) use ($fileWidget, $imageWidget): \PHPUnit\Framework\MockObject\MockObject {
            return match ($name) {
                'file-widget' => $fileWidget,
                'image-widget' => $imageWidget,
                default => $this->createMock(DocumentWidgetInterface::class),
            };
        });

        $fileRepository = $this->createMock(FileRepository::class);
        $fileRepository->method('findBy')->with(['id' => [1, 2]])->willReturn([$file1 = new File(), $file2 = new File()]);

        $contentFileExtractor = new ContentFileExtractor($encoder, $fileRepository);

        // When
        $extractedFileCollection = $contentFileExtractor->extract('
            <widget>file-widget</widget>
            <widget>image-widget</widget>
            <widget>unsupported-widget</widget>
        ');

        // Then
        self::assertEquals(new ArrayCollection([$file1, $file2]), $extractedFileCollection);
    }
}
