<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Document\Template;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tests\U2\UnitTestCase;
use U2\Document\Template\XmlToSectionsConverter;
use U2\Event\Document\PostCreateSectionEvent;

class XmlToSectionsConverterTest extends UnitTestCase
{
    /**
     * @var EventDispatcherInterface&MockObject
     */
    private MockObject $eventDispatcher;

    private XmlToSectionsConverter $xmlToSectionsConverter;

    public function test_converting_xml_to_sections(): void
    {
        $xml = <<<'XML'
            <?xml version="1.0" ?>
            <document>
                <name><![CDATA[master file name]]></name>
                <description><![CDATA[master file description]]></description>
                <section level="1" editable="true" required="true">
                    <name><![CDATA[name 1]]></name>
                    <content><![CDATA[content 1]]></content>
                </section>
                <section level="2" editable="false" required="false">
                    <name><![CDATA[name 2]]></name>
                    <content><![CDATA[content 2]]></content>
                </section>
            </document>
            XML;

        $postCreateSectionEvent = $this->createMock(PostCreateSectionEvent::class);
        $this->eventDispatcher
            ->expects($this->exactly(2))
            ->method('dispatch')
            ->with(self::isInstanceOf(PostCreateSectionEvent::class))
            ->willReturn($postCreateSectionEvent);

        $sections = $this->xmlToSectionsConverter->getSectionsFromXml($xml);

        self::assertSame('name 1', $sections[0]?->getName());
        self::assertSame('name 2', $sections[1]?->getName());
        self::assertSame('content 1', $sections[0]->getContent());
        self::assertSame('content 2', $sections[1]->getContent());
        self::assertSame(1, $sections[0]->getLevel());
        self::assertTrue($sections[0]->getEditable());
        self::assertTrue($sections[0]->getRequired());
        self::assertSame(2, $sections[1]->getLevel());
        self::assertFalse($sections[1]->getEditable());
        self::assertFalse($sections[1]->getRequired());
    }

    public function test_converting_xml_that_contains_unknown_options(): void
    {
        $xml = <<<'XML'
            <?xml version="1.0" ?>
            <document>
                <name><![CDATA[master file name]]></name>
                <description><![CDATA[master file description]]></description>
                <section level="1" editable="true" required="true" option="true">
                    <name><![CDATA[name 1]]></name>
                    <content><![CDATA[content 1]]></content>
                </section>
            </document>
            XML;

        $postCreateSectionEvent = $this->createMock(PostCreateSectionEvent::class);
        $this->eventDispatcher
            ->expects($this->once())
            ->method('dispatch')
            ->with(self::isInstanceOf(PostCreateSectionEvent::class))
            ->willReturn($postCreateSectionEvent);

        $sections = $this->xmlToSectionsConverter->getSectionsFromXml($xml);

        self::assertSame('name 1', $sections[0]?->getName());
        self::assertSame('content 1', $sections[0]->getContent());
        self::assertSame(1, $sections[0]->getLevel());
        self::assertTrue($sections[0]->getEditable());
        self::assertTrue($sections[0]->getRequired());
    }

    public function test_converting_xml_without_any_sections(): void
    {
        $xml = <<<'XML'
            <?xml version="1.0" ?>
            <document>
                <name><![CDATA[master file name]]></name>
                <description><![CDATA[master file description]]></description>
            </document>
            XML;

        $this->eventDispatcher
            ->expects($this->never())
            ->method('dispatch')
            ->with(self::isInstanceOf(PostCreateSectionEvent::class));

        $sections = $this->xmlToSectionsConverter->getSectionsFromXml($xml);

        self::assertCount(0, $sections);
    }

    public function test_converting_xml_with_sections_without_name_and_description(): void
    {
        $xml = <<<'XML'
            <?xml version="1.0" ?>
            <document>
                <name><![CDATA[master file name]]></name>
                <description><![CDATA[master file description]]></description>
                <section level="1" editable="true" required="true">
                </section>
            </document>
            XML;

        $postCreateSectionEvent = $this->createMock(PostCreateSectionEvent::class);
        $this->eventDispatcher
            ->expects($this->atLeastOnce())
            ->method('dispatch')
            ->with(self::isInstanceOf(PostCreateSectionEvent::class))
            ->willReturn($postCreateSectionEvent);

        $sections = $this->xmlToSectionsConverter->getSectionsFromXml($xml);

        self::assertSame('', $sections[0]?->getName());
        self::assertSame('', $sections[0]->getContent());
        self::assertSame(1, $sections[0]->getLevel());
        self::assertTrue($sections[0]->getEditable());
        self::assertTrue($sections[0]->getRequired());
    }

    public function test_converting_xml_with_sections_without_attributes(): void
    {
        $xml = <<<'XML'
            <?xml version="1.0" ?>
            <document>
                <name><![CDATA[master file name]]></name>
                <description><![CDATA[master file description]]></description>
                <section editable="true" required="true">
                    <name><![CDATA[section without level attribute]]></name>
                </section>
                <section level="1" required="true">
                    <name><![CDATA[section without editable attribute]]></name>
                </section>
                <section level="1" editable="true">
                    <name><![CDATA[section without required attribute]]></name>
                </section>
                <section level="1">
                    <name><![CDATA[section without editable and required attributes]]></name>
                </section>
                <section editable="true">
                    <name><![CDATA[section without level and required attributes]]></name>
                </section>
                <section required="true">
                    <name><![CDATA[section without level and editable attributes]]></name>
                </section>
                <section>
                    <name><![CDATA[section without level and editable and required attributes]]></name>
                </section>
            </document>
            XML;

        $postCreateSectionEvent = $this->createMock(PostCreateSectionEvent::class);
        $this->eventDispatcher
            ->expects($this->exactly(7))
            ->method('dispatch')
            ->with(self::isInstanceOf(PostCreateSectionEvent::class))
            ->willReturn($postCreateSectionEvent);

        $sections = $this->xmlToSectionsConverter->getSectionsFromXml($xml);

        self::assertSame('section without level attribute', $sections[0]?->getName());
        self::assertSame(1, $sections[0]->getLevel());
        self::assertTrue($sections[0]->getEditable());
        self::assertTrue($sections[0]->getRequired());
        self::assertSame('section without editable attribute', $sections[1]?->getName());
        self::assertSame(1, $sections[1]->getLevel());
        self::assertTrue($sections[1]->getEditable());
        self::assertTrue($sections[1]->getRequired());
        self::assertSame('section without required attribute', $sections[2]?->getName());
        self::assertSame(1, $sections[2]->getLevel());
        self::assertTrue($sections[2]->getEditable());
        self::assertFalse($sections[2]->getRequired());
        self::assertSame('section without editable and required attributes', $sections[3]?->getName());
        self::assertSame(1, $sections[3]->getLevel());
        self::assertTrue($sections[3]->getEditable());
        self::assertFalse($sections[3]->getRequired());
        self::assertSame('section without level and required attributes', $sections[4]?->getName());
        self::assertSame(1, $sections[4]->getLevel());
        self::assertTrue($sections[4]->getEditable());
        self::assertFalse($sections[4]->getRequired());
        self::assertSame('section without level and editable attributes', $sections[5]?->getName());
        self::assertSame(1, $sections[5]->getLevel());
        self::assertTrue($sections[5]->getEditable());
        self::assertTrue($sections[5]->getRequired());
        self::assertSame('section without level and editable and required attributes', $sections[6]?->getName());
        self::assertSame(1, $sections[6]->getLevel());
        self::assertTrue($sections[6]->getEditable());
        self::assertFalse($sections[6]->getRequired());
    }

    protected function setUp(): void
    {
        $this->eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $this->xmlToSectionsConverter = new XmlToSectionsConverter($this->eventDispatcher);
    }
}
