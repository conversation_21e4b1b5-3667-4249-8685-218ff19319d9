<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Igt;

use Tests\U2\UnitTestCase;
use U2\Igt\TypeToPropertyMapInterface;
use U2\Igt\TypeToPropertyMapTrait;

/**
 * @covers \U2\Igt\TypeToPropertyMapTrait
 */
class TypeToPropertyMapTraitTest extends UnitTestCase
{
    public function test_get_unused_fields(): void
    {
        $typeToPropertyMap = new TestTypeToPropertyMap();

        self::assertNotContains($typeToPropertyMap::getSupportedFields('included'), $typeToPropertyMap::getUnsupportedFields('included'));
        self::assertSame(['d', 'e', 'f'], $typeToPropertyMap::getUnsupportedFields('included'));
    }

    public function test_get_used_fields(): void
    {
        $typeToPropertyMap = new TestTypeToPropertyMap();

        self::assertNotContains($typeToPropertyMap::getSupportedFields('included'), $typeToPropertyMap::getUnsupportedFields('included'));
        self::assertSame(['a', 'b', 'c'], $typeToPropertyMap::getSupportedFields('included'));
    }
}

class TestTypeToPropertyMap implements TypeToPropertyMapInterface
{
    use TypeToPropertyMapTrait;

    /**
     * @return array<string, array<int, string>>
     */
    public static function getTypeToPropertiesMap(): array
    {
        return [
            'included' => [
                'a',
                'b',
                'c',
            ],
            'excluded' => [
                'd',
                'e',
                'f',
            ],
        ];
    }
}
