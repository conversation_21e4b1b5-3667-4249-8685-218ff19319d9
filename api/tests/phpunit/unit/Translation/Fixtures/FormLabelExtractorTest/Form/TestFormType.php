<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Translation\Fixtures\FormLabelExtractorTest\Form;

use Symfony\Component\Form\FormBuilderInterface;

use function Symfony\Component\Translation\t;

class TestFormType
{
    public function buildForm(FormBuilderInterface $builder): void
    {
        $builder
            ->add(
                'daysUntilAutomaticRenewal',
                null,
                [
                    'required' => false,
                    'label' => t('some_translation'),
                ]
            );
    }
}
