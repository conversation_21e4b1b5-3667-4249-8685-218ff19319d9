<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Xbrl\Oecd;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\CountryByCountryReportFactory;
use U2\Entity\Currency;
use U2\Repository\MainBusinessActivityRepository;
use U2\TransferPricing\FinancialData\SingleCountrySummaryCollection;
use U2\TransferPricing\FinancialData\SingleCountrySummaryCollectionFactory;
use U2\Util\ApplicationCurrencyProvider;
use U2\Xbrl\Oecd\OecdDataFactory;

class OecdDataFactoryTest extends UnitTestCase
{
    public function test_create(): void
    {
        // Given
        $countryByCountryReport = CountryByCountryReportFactory::createOne();

        $applicationCurrencyProvider = $this->createMock(ApplicationCurrencyProvider::class);
        $groupCurrency = new Currency();
        $applicationCurrencyProvider
            ->expects($this->once())
            ->method('get')
            ->willReturn($groupCurrency);

        $singleCountrySummaryCollection = new SingleCountrySummaryCollection();

        $singleCountrySummaryCollectionFactory = $this->createMock(SingleCountrySummaryCollectionFactory::class);
        $singleCountrySummaryCollectionFactory
            ->expects($this->once())
            ->method('create')
            ->with($countryByCountryReport->getPeriod(), $countryByCountryReport->getUnits())
            ->willReturn($singleCountrySummaryCollection);

        $mainBusinessActivityRepository = $this->createMock(MainBusinessActivityRepository::class);
        $mainBusinessActivityRepository
            ->expects($this->once())
            ->method('findByPeriodAndUnits')
            ->with($countryByCountryReport->getPeriod(), $countryByCountryReport->getUnits())
            ->willReturn([]);

        $oecdDataFactory = new OecdDataFactory(
            $applicationCurrencyProvider,
            $singleCountrySummaryCollectionFactory,
            $mainBusinessActivityRepository
        );

        // When
        $oecdData = $oecdDataFactory->create($countryByCountryReport->_real());

        // Then
        self::assertEquals($countryByCountryReport->_real(), $oecdData->getCountryByCountryReport());
        self::assertEquals($groupCurrency, $oecdData->getGroupCurrency());
        self::assertEquals($singleCountrySummaryCollection, $oecdData->getFinancialDataSingleCountrySummaryCollection());
        self::assertEquals([], $oecdData->getMainBusinessActivities());
    }
}
