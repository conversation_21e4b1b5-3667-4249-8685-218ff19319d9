<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Xbrl\At\Schema\UrnOecdTiesCbcV1\CorrectableCbcReportType;

use Tests\U2\UnitTestCase;
use U2\TransferPricing\FinancialData\Summary;
use U2\Xbrl\At\Schema\UrnOecdTiesCbcV2\CorrectableCbcReportType\SummaryAType;
use U2\Xbrl\At\Schema\UrnOecdTiesCbcV2\CorrectableCbcReportType\SummaryAType\RevenuesAType;
use U2\Xbrl\At\Schema\UrnOecdTiesCbcV2\MonAmntType;

class SummaryATypeTest extends UnitTestCase
{
    public function test_create_from_summary(): void
    {
        // Given
        $summary = new Summary(
            [],
            1,
            2,
            3,
            4,
            5,
            6,
            7,
            8,
            9,
            1.5,
            'EUR',
        );

        // When
        $summaryAType = SummaryAType::createFromSummary($summary);

        // Then
        self::assertEquals(
            new RevenuesAType(
                new MonAmntType(
                    $summary->getTotalRevenueUnrelatedValue(),
                    $summary->getGroupCurrency()
                ),
                new MonAmntType(
                    $summary->getTotalRevenueRelatedValue(),
                    $summary->getGroupCurrency()
                ),
                new MonAmntType(
                    $summary->getTotalRevenueValue(),
                    $summary->getGroupCurrency()
                )
            ),
            $summaryAType->getRevenues()
        );
    }
}
