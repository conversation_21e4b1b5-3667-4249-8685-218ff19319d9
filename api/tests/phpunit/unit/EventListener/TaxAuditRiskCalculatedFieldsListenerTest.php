<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\EventListener\TaxAuditRiskCalculatedFieldsListener;
use U2\TaxCompliance\TaxAuditRisk\TaxAuditRiskUpdater;

class TaxAuditRiskCalculatedFieldsListenerTest extends UnitTestCase
{
    /**
     * @covers \U2\EventListener\TaxAuditRiskCalculatedFieldsListener::prePersist
     */
    public function test_pre_persist(): void
    {
        $taxAuditRisk = $this->createMock(TaxAuditRisk::class);
        $taxAuditRiskUpdater = $this->createMock(TaxAuditRiskUpdater::class);
        $taxAuditRiskUpdater
            ->expects($this->once())
            ->method('update')
            ->with($taxAuditRisk);

        $taxAuditRiskCalculatedFieldsListener = new TaxAuditRiskCalculatedFieldsListener($taxAuditRiskUpdater);

        $lifeCycleEventArgs = new PrePersistEventArgs($taxAuditRisk, $this->createMock(EntityManagerInterface::class));
        $taxAuditRiskCalculatedFieldsListener->prePersist($lifeCycleEventArgs);
    }

    /**
     * @covers \U2\EventListener\TaxAuditRiskCalculatedFieldsListener::preUpdate
     */
    public function test_pre_update(): void
    {
        $taxAuditRisk = $this->createMock(TaxAuditRisk::class);
        $taxAuditRiskUpdater = $this->createMock(TaxAuditRiskUpdater::class);
        $taxAuditRiskUpdater
            ->expects($this->once())
            ->method('update')
            ->with($taxAuditRisk);

        $taxAuditRiskCalculatedFieldsListener = new TaxAuditRiskCalculatedFieldsListener($taxAuditRiskUpdater);

        $preUpdateEventArgs = $this->createMock(PreUpdateEventArgs::class);
        $preUpdateEventArgs
            ->method('getObject')
            ->willReturn($taxAuditRisk);
        $taxAuditRiskCalculatedFieldsListener->preUpdate($preUpdateEventArgs);
    }
}
