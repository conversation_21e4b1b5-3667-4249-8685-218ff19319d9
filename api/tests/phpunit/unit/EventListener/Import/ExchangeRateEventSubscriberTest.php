<?php

declare(strict_types=1);
namespace Tests\Unit\U2\EventListener\Import;

use Tests\U2\UnitTestCase;
use U2\Entity\ExchangeRate;
use U2\Entity\Interfaces\Entity;
use U2\Event\Import\PreBindDataImportEvent;
use U2\EventListener\Import\ExchangeRateEventSubscriber;
use U2\Exception\ImportInterpreterException;

class ExchangeRateEventSubscriberTest extends UnitTestCase
{
    private ExchangeRateEventSubscriber $exchangeRateEventSubscriber;

    protected function setUp(): void
    {
        $this->exchangeRateEventSubscriber = new ExchangeRateEventSubscriber();
    }

    public function test_does_not_handle_non_exchange_rates(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $entity = $this->createMock(Entity::class);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity');
        $preBindDataImportEvent->expects($this->never())->method('setInterpretedData');
        $preBindDataImportEvent->expects($this->never())->method('getInterpretedData');

        $this->exchangeRateEventSubscriber->preBindData($preBindDataImportEvent);
    }

    public function test_handles_direct_exchange_rates(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $entity = $this->createMock(ExchangeRate::class);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData')->willReturn(
            [
                'isIndirect' => false,
                'value' => '0.03',
            ]
        );
        $preBindDataImportEvent
            ->expects($this->atLeastOnce())
            ->method('setInterpretedData')
            ->with([
                'directRate' => '0.03',
            ]);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity');
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData');

        $this->exchangeRateEventSubscriber->preBindData($preBindDataImportEvent);
    }

    public function test_handles_indirect_exchange_rates(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $entity = $this->createMock(ExchangeRate::class);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData')->willReturn(
            [
                'isIndirect' => true,
                'value' => '3',
            ]
        );
        $preBindDataImportEvent->expects(
            $this->atLeastOnce()
        )->method('setInterpretedData')->with(self::equalTo([
            'indirectRate' => '3',
        ]));
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity');
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData');

        $this->exchangeRateEventSubscriber->preBindData($preBindDataImportEvent);
    }

    public function test_throws_an_exception_for_an_indirect_rate_without_a_value(): void
    {
        $preBindDataImportEvent = $this->createMock(PreBindDataImportEvent::class);
        $entity = $this->createMock(ExchangeRate::class);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity')->willReturn($entity);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData')->willReturn(
            [
                'isIndirect' => true,
            ]
        );
        $this->expectException(ImportInterpreterException::class);
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getEntity');
        $preBindDataImportEvent->expects($this->atLeastOnce())->method('getInterpretedData');
        $preBindDataImportEvent->expects($this->never())->method('setInterpretedData');

        $this->exchangeRateEventSubscriber->preBindData($preBindDataImportEvent);
    }
}
