<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Tests\U2\UnitTestCase;
use U2\Event\Menu\ConfigureAdminModuleMenuEvent;
use U2\Event\Menu\ConfigureRootMenuEvent;
use U2\Menu\TaxAccountingMenuSubscriber;

class TaxAccountingMenuSubscriberTest extends UnitTestCase
{
    public function test_subscribed_events(): void
    {
        self::assertSame(
            [
                ConfigureRootMenuEvent::class => ['onMenuConfigureRoot', 60],
                ConfigureAdminModuleMenuEvent::class => ['onMenuConfigureAdminModule', 60],
            ],
            TaxAccountingMenuSubscriber::getSubscribedEvents()
        );
    }
}
