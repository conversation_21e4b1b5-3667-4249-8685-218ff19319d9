<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\User;
use U2\Menu\SeparatorBuilder;
use U2\Menu\UserMenuBuilder;
use U2\User\CurrentUserProvider;

class UserMenuBuilderTest extends UnitTestCase
{
    public function test_menu_creation_for_anonymous_users(): void
    {
        // Given
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with('IS_AUTHENTICATED_REMEMBERED')->willReturn(false);
        $loginMenu = $this->createMock(ItemInterface::class);
        $userMenu = $this->createMock(ItemInterface::class);
        $userMenu->method('addChild')->with($loginMenu)->willReturn($loginMenu);
        $factory = $this->createMock(FactoryInterface::class);
        $factory->method('createItem')->willReturnCallback(function (string $name) use ($userMenu, $loginMenu): ItemInterface {
            switch ($name) {
                case 'user-menu':
                    return $userMenu;
                case 'Login':
                    return $loginMenu;
            }
            throw new \Exception('unknown menu item');
        });
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $userMenuBuilder = new UserMenuBuilder(
            $factory,
            $authorizationChecker,
            $currentUserProvider,
            $this->createMock(SeparatorBuilder::class)
        );

        // When
        $menu = $userMenuBuilder->create();

        // Then
        self::assertSame($userMenu, $menu);
    }

    public function test_user_menu_creation_for_logged_in_users(): void
    {
        // Given
        $separator = $this->createMock(ItemInterface::class);
        $separatorBuilder = $this->createMock(SeparatorBuilder::class);
        $separatorBuilder->method('create')->willReturn($separator);

        $userNameMenu = $this->createMock(ItemInterface::class);
        $userNameMenu
            ->expects($this->exactly(5))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Profile', 'UserSettings', 'Calendar', $separator, 'Logout' => true,
                    default => false,
                }
            ));
        $userMenu = $this->createMock(ItemInterface::class);
        $userMenu->method('addChild')->with($userNameMenu)->willReturn($userNameMenu);
        $factory = $this->createMock(FactoryInterface::class);
        $factory->method('createItem')->willReturnCallback(function (string $name) use ($userMenu, $userNameMenu): ItemInterface {
            switch ($name) {
                case 'user-menu':
                    return $userMenu;
                case 'Username':
                    return $userNameMenu;
            }
            throw new \Exception('unknown menu item');
        });
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker->method('isGranted')->with('IS_AUTHENTICATED_REMEMBERED')->willReturn(true);
        $currentUserProvider = $this->createMock(CurrentUserProvider::class);
        $currentUserProvider->method('get')->willReturn(new User());

        $userMenuBuilder = new UserMenuBuilder(
            $factory,
            $authorizationChecker,
            $currentUserProvider,
            $separatorBuilder
        );

        // When
        $menu = $userMenuBuilder->create();

        // Then
        self::assertSame($userMenu, $menu);
    }
}
