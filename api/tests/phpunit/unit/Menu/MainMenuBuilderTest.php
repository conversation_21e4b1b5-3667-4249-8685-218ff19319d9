<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\AuthorizationItem;
use U2\Event\Menu\ConfigureMenuEvent;
use U2\Event\Menu\ConfigureRootExtraMenuEvent;
use U2\Event\Menu\ConfigureRootMenuEvent;
use U2\Event\Menu\ConfigureToolsMenuEvent;
use U2\Menu\MainMenuBuilder;
use U2\Menu\SeparatorBuilder;
use U2\Repository\DashboardRepository;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\UserRoles;

class MainMenuBuilderTest extends UnitTestCase
{
    public function test_menu_creation_for_anonymous_users(): void
    {
        // Given
        $factory = $this->createMock(FactoryInterface::class);
        $mainMenu = $this->createMock(ItemInterface::class);
        $factory
            ->expects($this->once())
            ->method('createItem')
            ->with('main-menu')
            ->willReturn($mainMenu);
        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->expects($this->once())
            ->method('isGranted')
            ->with('IS_AUTHENTICATED_REMEMBERED')
            ->willReturn(false);
        $mainMenuBuilder = new MainMenuBuilder(
            $factory,
            $authorizationChecker,
            $this->createMock(EventDispatcherInterface::class),
            $this->createMock(DashboardRepository::class),
            $this->createMock(SeparatorBuilder::class),
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
        );

        // When
        $menu = $mainMenuBuilder->create();

        // Then
        self::assertSame($mainMenu, $menu);
    }

    public function test_menu_creation_for_admins(): void
    {
        // Given
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $eventDispatcher
            ->expects($this->exactly(3))
            ->method('dispatch')
            ->with(self::callback(
                static fn (ConfigureMenuEvent $item): bool => match ($item::class) {
                    ConfigureRootMenuEvent::class,
                    ConfigureToolsMenuEvent::class,
                    ConfigureRootExtraMenuEvent::class, => true,
                    default => false,
                }
            ));
        $helpMenu = $this->createMock(ItemInterface::class);
        $helpMenu
            ->expects($this->exactly(2))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Support',
                    'About' => true,
                    default => false,
                }
            ));

        $separator = $this->createMock(ItemInterface::class);
        $separatorBuilder = $this->createMock(SeparatorBuilder::class);
        $separatorBuilder->method('create')->willReturn($separator);

        $toolsMenu = $this->createMock(ItemInterface::class);
        $toolsMenu
            ->expects($this->exactly(9))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Units',
                    $separator,
                    'Files', 'Periods', 'DataTransfers', 'Imports',
                    'Filters', 'Subscriptions', 'Administration', => true,
                    default => false,
                }
            ));

        $dashboardMenu = $this->createMock(ItemInterface::class);

        $mainMenu = $this->createMock(ItemInterface::class);
        $mainMenu
            ->expects($this->exactly(3))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    $dashboardMenu,
                    $toolsMenu,
                    $helpMenu, => true,
                    default => false,
                }
            ));

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->method('isGranted')
            ->willReturnCallback(
                static function ($attribute): bool {
                    switch ($attribute) {
                        case AuthorizationItem::Period->value . ':' . AuthorizationRight::UPDATE->value:
                        case UserRoles::UserGroupAdmin->value:
                        case AuthorizationItem::Unit->value . ':' . AuthorizationRight::MANAGE->value:
                            return false;
                        case UserRoles::Admin->value:
                        case 'IS_AUTHENTICATED_REMEMBERED':
                            return true;
                    }
                    throw new \RuntimeException('unknown attribute');
                }
            );

        $factory = $this->createMock(FactoryInterface::class);
        $factory
            ->expects($this->exactly(4))
            ->method('createItem')
            ->willReturnCallback(static fn (string $string): MockObject => match ($string) {
                'main-menu' => $mainMenu,
                'Dashboard' => $dashboardMenu,
                'Tools' => $toolsMenu,
                'Help' => $helpMenu,
                default => throw new \RuntimeException('unknown string'),
            });

        $mainMenuBuilder = new MainMenuBuilder(
            $factory,
            $authorizationChecker,
            $eventDispatcher,
            $this->createMock(DashboardRepository::class),
            $separatorBuilder,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
        );
        // When
        $menu = $mainMenuBuilder->create();

        // Then
        self::assertSame($mainMenu, $menu);
    }

    public function test_menu_creation_for_exchange_rate_manager(): void
    {
        // Given
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $eventDispatcher
            ->expects($this->exactly(3))
            ->method('dispatch')
            ->with(self::callback(
                static fn (ConfigureMenuEvent $item): bool => match ($item::class) {
                    ConfigureRootMenuEvent::class,
                    ConfigureToolsMenuEvent::class,
                    ConfigureRootExtraMenuEvent::class, => true,
                    default => false,
                }
            ));
        $helpMenu = $this->createMock(ItemInterface::class);
        $helpMenu
            ->expects($this->exactly(2))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Support',
                    'About', => true,
                    default => false,
                }
            ));

        $separator = $this->createMock(ItemInterface::class);
        $separatorBuilder = $this->createMock(SeparatorBuilder::class);
        $separatorBuilder->method('create')->willReturn($separator);

        $toolsMenu = $this->createMock(ItemInterface::class);
        $toolsMenu
            ->expects($this->exactly(6))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Units',
                    $separator,
                    'Files',
                    'Periods',
                    'DataTransfers',
                    'Imports',
                    'Filters',
                    'Subscriptions' => true,
                    default => false,
                }
            ));

        $dashboardMenu = $this->createMock(ItemInterface::class);
        $mainMenu = $this->createMock(ItemInterface::class);
        $mainMenu
            ->expects($this->exactly(3))
            ->method('addChild')
            ->with(self::callback(static fn (ItemInterface $item): true => match ($item) {
                $helpMenu,
                $dashboardMenu,
                $toolsMenu => true,
                default => throw new \RuntimeException('unknown name'),
            }));

        $factory = $this->createMock(FactoryInterface::class);
        $factory
            ->expects($this->exactly(4))
            ->method('createItem')
            ->willReturnCallback(static fn (string $name): MockObject => match ($name) {
                'main-menu' => $mainMenu,
                'Dashboard' => $dashboardMenu,
                'Tools' => $toolsMenu,
                'Help' => $helpMenu,
                default => throw new \RuntimeException('unknown name'),
            });

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->method('isGranted')
            ->willReturnCallback(
                static function ($attribute): bool {
                    switch ($attribute) {
                        case UserRoles::Admin->value:
                        case UserRoles::UserGroupAdmin->value:
                        case AuthorizationItem::Unit->value . ':' . AuthorizationRight::MANAGE->value:
                            return false;
                        case 'IS_AUTHENTICATED_REMEMBERED':
                        case AuthorizationItem::Period->value . ':' . AuthorizationRight::UPDATE->value:
                            return true;
                    }
                    throw new \RuntimeException('unknown attribute');
                }
            );

        $mainMenuBuilder = new MainMenuBuilder(
            $factory,
            $authorizationChecker,
            $eventDispatcher,
            $this->createMock(DashboardRepository::class),
            $separatorBuilder,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
        );

        // When
        $menu = $mainMenuBuilder->create();

        // Then
        self::assertSame($mainMenu, $menu);
    }

    public function test_menu_creation_for_unit_manager(): void
    {
        // Given
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $eventDispatcher
            ->expects($this->exactly(3))
            ->method('dispatch')
            ->with(self::callback(
                static fn (ConfigureMenuEvent $event): bool => match ($event::class) {
                    ConfigureRootMenuEvent::class, ConfigureToolsMenuEvent::class, ConfigureRootExtraMenuEvent::class => true,
                    default => false,
                }
            ));

        $helpMenu = $this->createMock(ItemInterface::class);
        $helpMenu
            ->expects($this->exactly(2))
            ->method('addChild')
            ->with(self::callback(
                static fn (string $string): bool => match ($string) {
                    'Support', 'About' => true,
                    default => false,
                }
            ));

        $separator = $this->createMock(ItemInterface::class);
        $separatorBuilder = $this->createMock(SeparatorBuilder::class);
        $separatorBuilder->method('create')->willReturn($separator);

        $toolsMenu = $this->createMock(ItemInterface::class);
        $toolsMenu
            ->expects($this->exactly(7))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Units', 'Unit Hierarchies', $separator, 'Files', 'Periods', 'DataTransfers', 'Imports', 'Filters' => true,
                    default => false,
                })
            );

        $dashboardMenu = $this->createMock(ItemInterface::class);
        $mainMenu = $this->createMock(ItemInterface::class);
        $mainMenu
            ->expects($this->exactly(3))
            ->method('addChild')
            ->with(self::callback(
                static fn (ItemInterface $menuItem): bool => match ($menuItem) {
                    $dashboardMenu, $toolsMenu, $helpMenu => true,
                    default => false,
                }
            ));

        $factory = $this->createMock(FactoryInterface::class);
        $factory
            ->expects($this->exactly(4))
            ->method('createItem')
            ->willReturnCallback(static fn (string $string): MockObject => match ($string) {
                'main-menu' => $mainMenu,
                'Dashboard' => $dashboardMenu,
                'Tools' => $toolsMenu,
                'Help' => $helpMenu,
                default => throw new \RuntimeException('unknown attribute'),
            });

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->method('isGranted')
            ->willReturnCallback(
                static function ($attribute): bool {
                    switch ($attribute) {
                        case AuthorizationItem::Period->value . ':' . AuthorizationRight::UPDATE->value:
                        case UserRoles::Admin->value:
                        case UserRoles::UserGroupAdmin->value:
                            return false;
                        case 'IS_AUTHENTICATED_REMEMBERED':
                        case AuthorizationItem::Unit->value . ':' . AuthorizationRight::MANAGE->value:
                            return true;
                    }
                    throw new \RuntimeException('unknown attribute');
                }
            );

        $mainMenuBuilder = new MainMenuBuilder(
            $factory,
            $authorizationChecker,
            $eventDispatcher,
            $this->createMock(DashboardRepository::class),
            $separatorBuilder,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
        );

        // When
        $menu = $mainMenuBuilder->create();

        // Then
        self::assertSame($mainMenu, $menu);
    }

    public function test_menu_creation_for_user_group_admin(): void
    {
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $eventDispatcher
            ->expects($this->exactly(3))
            ->method('dispatch')
            ->with(self::callback(
                static fn ($event): bool => match ($event::class) {
                    ConfigureRootMenuEvent::class,
                    ConfigureToolsMenuEvent::class,
                    ConfigureRootExtraMenuEvent::class => true,

                    default => false,
                }
            ));

        $dashboardRepository = $this->createMock(DashboardRepository::class);
        $dashboardRepository
            ->expects($this->once())
            ->method('findCurrentUserAssigned')
            ->willReturn([]);

        $helpMenu = $this->createMock(ItemInterface::class);
        $helpMenu
            ->expects($this->exactly(2))
            ->method('addChild')
            ->with(self::callback(
                static fn (string $menuItemName): bool => match ($menuItemName) {
                    'Support',
                    'About' => true,
                    default => false,
                }
            ));

        $separator = $this->createMock(ItemInterface::class);
        $separatorBuilder = $this->createMock(SeparatorBuilder::class);
        $separatorBuilder->method('create')->willReturn($separator);

        $toolsMenu = $this->createMock(ItemInterface::class);
        $toolsMenu
            ->expects($this->exactly(10))
            ->method('addChild')
            ->with(self::callback(
                static fn (string|ItemInterface $item): bool => match ($item) {
                    'Units',
                    'Users',
                    'User Groups',
                    'Authorisation',
                    'Files',
                    'Periods',
                    'DataTransfers',
                    'Imports',
                    'Filters',
                    $separator => true,
                    default => false,
                }
            ));

        $mainMenu = $this->createMock(ItemInterface::class);
        $menuItem = $this->createMock(ItemInterface::class);
        $mainMenu
            ->expects($this->exactly(3))
            ->method('addChild')
            ->with(self::callback(
                static fn (ItemInterface $item): bool => match ($item->getName()) {
                    $menuItem->getName(), $toolsMenu->getName(), $helpMenu->getName() => true,
                    default => false,
                }
            ));

        $factory = $this->createMock(FactoryInterface::class);
        $factory
            ->expects($this->exactly(4))
            ->method('createItem')
            ->willReturnCallback(fn (string $name): MockObject => match ($name) {
                'main-menu' => $mainMenu,
                'Dashboard' => $this->createMock(ItemInterface::class),
                'Tools' => $toolsMenu,
                'Help' => $helpMenu,
                default => throw new \RuntimeException('unknown name'),
            });

        $authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $authorizationChecker
            ->method('isGranted')
            ->willReturnCallback(
                static function ($attribute): bool {
                    switch ($attribute) {
                        case AuthorizationItem::Period->value . ':' . AuthorizationRight::UPDATE->value:
                        case UserRoles::Admin->value:
                        case AuthorizationItem::Unit->value . ':' . AuthorizationRight::MANAGE->value:
                            return false;
                        case 'IS_AUTHENTICATED_REMEMBERED':
                        case UserRoles::UserGroupAdmin->value:
                            return true;
                    }
                    throw new \RuntimeException('unknown attribute');
                }
            );

        $mainMenuBuilder = new MainMenuBuilder(
            $factory,
            $authorizationChecker,
            $eventDispatcher,
            $dashboardRepository,
            $separatorBuilder,
            $this->createMock(Security::class),
            $this->createMock(RouterInterface::class),
        );

        // When
        $menu = $mainMenuBuilder->create();

        // Then
        self::assertSame($mainMenu, $menu);
    }
}
