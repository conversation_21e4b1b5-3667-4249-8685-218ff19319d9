<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Flagception\Manager\FeatureManagerInterface;
use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use Tests\U2\UnitTestCase;
use U2\Event\Menu\ConfigureRootExtraMenuEvent;
use U2\Menu\ReferenceMenuSubscriber;

class MenuSubscriberTest extends UnitTestCase
{
    public function test_adds_menu_items_in_dev_mode(): void
    {
        $featureManager = $this->createMock(FeatureManagerInterface::class);
        $featureManager->method('isActive')->with('api_docs')->willReturn(true);
        $menuSubscriber = new ReferenceMenuSubscriber();
        $menuSubscriber->setFeatures($featureManager);
        $mainMenu = $this->createMock(ItemInterface::class);
        $referenceMenu = $this->createMock(ItemInterface::class);
        $referenceMenu
            ->expects($this->atLeastOnce())
            ->method('addChild')
            ->with(self::callback(
                static fn ($arg): bool => match ($arg) {
                    'Tables', 'Email Templates', 'Entity Mappings' => true,
                    default => false,
                }
            ));
        $factory = $this->createMock(FactoryInterface::class);
        $factory->expects($this->atLeastOnce())->method('createItem')->with('Dev Reference')->willReturn($referenceMenu);

        $mainMenu->expects($this->atLeastOnce())->method('addChild')->with($referenceMenu);
        $menuSubscriber->onMenuConfigureRootExtra(new ConfigureRootExtraMenuEvent($factory, $mainMenu));
    }

    public function test_does_not_add_menu_items_when_feature_is_disabled(): void
    {
        $featureManager = $this->createMock(FeatureManagerInterface::class);
        $featureManager->method('isActive')->with('api_docs')->willReturn(false);
        $factory = $this->createMock(FactoryInterface::class);
        $mainMenu = $this->createMock(ItemInterface::class);
        $menuSubscriber = new ReferenceMenuSubscriber();
        $menuSubscriber->setFeatures($featureManager);
        $menuSubscriber->onMenuConfigureRootExtra(new ConfigureRootExtraMenuEvent($factory, $mainMenu));
        $factory->expects($this->never())->method('createItem')->with('Dev Reference', self::anything());
        $mainMenu->expects($this->never())->method('addChild')->with(self::anything());
    }
}
