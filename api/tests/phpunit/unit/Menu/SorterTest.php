<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Knp\Menu\ItemInterface;
use Tests\U2\UnitTestCase;
use U2\Menu\Sorter;

class SorterTest extends UnitTestCase
{
    private Sorter $sorter;

    public function test_reorders_menu_children_by_name(): void
    {
        $menu = $this->createMock(ItemInterface::class);
        $menuA = $this->createMock(ItemInterface::class);
        $menuB = $this->createMock(ItemInterface::class);
        $menu->expects($this->atLeastOnce())->method('getChildren')->willReturn(['B' => $menuB, 'A' => $menuA]);
        $menu->expects($this->atLeastOnce())->method('reorderChildren')->with(self::equalTo([0 => 'A', 1 => 'B']));
        $this->sorter->sortByName($menu);
    }

    protected function setUp(): void
    {
        $this->sorter = new Sorter();
    }
}
