<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Tests\U2\UnitTestCase;
use U2\Event\Menu\ConfigureAdminModuleMenuEvent;
use U2\Menu\StructuredDocumentMenuSubscriber;
use U2\Security\UserRoles;

class StructuredDocumentMenuSubscriberTest extends UnitTestCase
{
    private StructuredDocumentMenuSubscriber $structuredDocumentMenuSubscriber;

    /**
     * @var AuthorizationCheckerInterface&MockObject
     */
    private MockObject $authorizationChecker;

    protected function setUp(): void
    {
        $this->authorizationChecker = $this->createMock(AuthorizationCheckerInterface::class);
        $this->structuredDocumentMenuSubscriber = new StructuredDocumentMenuSubscriber($this->authorizationChecker);
    }

    public function test_adds_administration_menu_items_for_admins(): void
    {
        $factory = $this->createMock(FactoryInterface::class);
        $adminMainMenu = $this->createMock(ItemInterface::class);
        $structuredDocumentMenu = $this->createMock(ItemInterface::class);
        $this->authorizationChecker->expects($this->atLeastOnce())->method('isGranted')->with(self::equalTo(UserRoles::Admin->value))->willReturn(true);
        $factory->expects($this->atLeastOnce())->method('createItem')->with(self::equalTo('Documents'))->willReturn($structuredDocumentMenu);
        $structuredDocumentMenu->expects($this->atLeastOnce())->method('addChild')->with(self::equalTo('Templates'));
        $adminMainMenu->expects($this->atLeastOnce())->method('addChild')->with(self::equalTo($structuredDocumentMenu));
        $this->structuredDocumentMenuSubscriber->addStructuredDocumentAdministration(new ConfigureAdminModuleMenuEvent($factory, $adminMainMenu));
    }

    public function test_does_not_add_administration_menu_items_for_non_admins(): void
    {
        $factory = $this->createMock(FactoryInterface::class);
        $adminMainMenu = $this->createMock(ItemInterface::class);
        $this->authorizationChecker->expects($this->atLeastOnce())->method('isGranted')->with(self::equalTo(UserRoles::Admin->value))->willReturn(false);
        $this->structuredDocumentMenuSubscriber->addStructuredDocumentAdministration(new ConfigureAdminModuleMenuEvent($factory, $adminMainMenu));
        $adminMainMenu->expects($this->never())->method('addChild')->with(self::anything());
    }
}
