<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Tests\U2\UnitTestCase;
use U2\Event\Menu\ConfigureAdminModuleMenuEvent;
use U2\Menu\ConfigurationDataMenuBuilder;
use U2\Menu\Sorter;

class ConfigurationDataMenuBuilderTest extends UnitTestCase
{
    public function test_creates_the_menu(): void
    {
        $eventDispatcher = $this->createMock(EventDispatcherInterface::class);
        $eventDispatcher
            ->method('dispatch')
            ->with(self::isInstanceOf(ConfigureAdminModuleMenuEvent::class));
        $generalMenu = $this->createMock(ItemInterface::class);
        $generalMenu
            ->method('addChild')
            ->with(self::callback(
                static fn ($arg): bool => match ($arg) {
                    'Branches',
                    'Countries',
                    'Currencies',
                    'File Types',
                    'Auditors',
                    'Legal Forms',
                    'System Messages',
                    'System Images',
                    'System Settings' => true,
                    default => false,
                }
            ));
        $choiceFieldsMenu = $this->createMock(ItemInterface::class);
        $choiceFieldsMenu
            ->method('addChild')
            ->with(self::callback(
                static fn ($arg): bool => match ($arg) {
                    'Advice Types',
                    'Assessment Types',
                    'Billing Types',
                    'Business Activities',
                    'Business Cases',
                    'Collateralisation Types',
                    'Contract Types',
                    'Deadline Types',
                    'Declaration Types',
                    'Instrument Id Types',
                    'Lines of Business',
                    'Loss Restrictions',
                    'Loss Types',
                    'Pricing Methods',
                    'Restriction Reasons',
                    'Risk Types',
                    'Service Types',
                    'Specifications',
                    'Tax Credit Types',
                    'Tax Types',
                    'Trace Id',
                    'Transaction Types' => true,
                    default => false,
                }
            ));

        $workflowMenu = $this->createMock(ItemInterface::class);
        $workflowMenu
            ->method('addChild')
            ->with(
                self::callback(
                    static fn ($arg): bool => match ($arg) {
                        'Statuses',
                        'Field Configurations',
                        'Workflows',
                        'Workflow Assignment' => true,
                        default => false,
                    }
                ));

        $dashboardsMenuItem = $this->createMock(ItemInterface::class);
        $experimentalMenu = $this->createMock(ItemInterface::class);
        $experimentalMenu->expects($this->atLeastOnce())->method('addChild')->with('Dashboards')->willReturn($dashboardsMenuItem);

        $mainMenu = $this->createMock(ItemInterface::class);
        $mainMenu
            ->expects($this->atLeastOnce())
            ->method('addChild')
            ->with(self::callback(
                static fn ($arg): bool => match ($arg) {
                    $generalMenu,
                    $choiceFieldsMenu,
                    $workflowMenu,
                    $experimentalMenu => true,
                    default => false,
                }
            ));
        $factory = $this->createMock(FactoryInterface::class);
        $factory
            ->expects($this->atLeastOnce())
            ->method('createItem')
            ->willReturnCallback(static fn (string $name): MockObject => match ($name) {
                'configuration-data-menu' => $mainMenu,
                'General' => $generalMenu,
                'ChoiceFields' => $choiceFieldsMenu,
                'Workflow Configuration' => $workflowMenu,
                'Experimental' => $experimentalMenu,
                default => throw new \InvalidArgumentException('Unexpected menu name: ' . $name),
            });
        $menuSorter = $this->createMock(Sorter::class);
        $menuSorter->expects($this->atLeastOnce())->method('sortByName')->with($generalMenu);

        $configurationDataMenuBuilder = new ConfigurationDataMenuBuilder(
            $factory,
            $eventDispatcher,
            $menuSorter
        );

        self::assertInstanceOf(ItemInterface::class, $configurationDataMenuBuilder->create());
    }
}
