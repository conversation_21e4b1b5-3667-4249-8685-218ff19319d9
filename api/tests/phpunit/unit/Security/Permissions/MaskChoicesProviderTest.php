<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Permissions;

use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tests\U2\UnitTestCase;
use U2\Security\Permissions\MaskChoicesProvider;

class MaskChoicesProviderTest extends UnitTestCase
{
    public function test_compounds_masks_indexed_by_their_translation(): void
    {
        $translator = $this->createMock(TranslatorInterface::class);
        $maskChoicesProvider = new MaskChoicesProvider($translator);

        $translator
            ->method('trans')
            ->willReturnCallback(
                static fn ($id): string => match ($id) {
                    'u2_core.permission_mask.1' => 'Read',
                    'u2_core.permission_mask.4' => 'Write',
                    'u2_core.permission_mask.8' => 'Delete',
                    default => throw new \InvalidArgumentException('Unexpected item'),
                }
            );
        self::assertSame(
            [
                'Read' => 1,
                'Read, Write' => 5,
                'Read, Write, Delete' => 13,
            ],
            $maskChoicesProvider->get(
                [
                    MaskBuilder::MASK_EDIT,
                    MaskBuilder::MASK_VIEW,
                    MaskBuilder::MASK_DELETE,
                ]
            )
        );
    }

    public function test_sorts_masks_by_size_and_compounds_them_index_by_their_translation(): void
    {
        $translator = $this->createMock(TranslatorInterface::class);
        $maskChoicesProvider = new MaskChoicesProvider($translator);

        $translator
            ->method('trans')
            ->willReturnCallback(
                static fn ($id): string => match ($id) {
                    'u2_core.permission_mask.1' => 'Read',
                    'u2_core.permission_mask.8' => 'Delete',
                    'u2_core.permission_mask.128' => 'Manage',
                    default => throw new \InvalidArgumentException('Unexpected item'),
                }
            );

        self::assertSame([
            'Read' => 1,
            'Read, Delete' => 9,
            'Read, Delete, Manage' => 137,
        ], $maskChoicesProvider->get(
            [
                MaskBuilder::MASK_OWNER,
                MaskBuilder::MASK_VIEW,
                MaskBuilder::MASK_DELETE,
            ]
        ));
    }
}
