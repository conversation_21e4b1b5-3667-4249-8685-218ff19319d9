<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Permissions;

use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\UnitTestCase;
use U2\Security\Permissions\MaskCompounder;

class MaskCompounderTest extends UnitTestCase
{
    public function test_compounds_masks(): void
    {
        self::assertSame(MaskBuilder::MASK_VIEW | MaskBuilder::MASK_DELETE, MaskCompounder::compound(
            [
                MaskBuilder::MASK_DELETE,
                MaskBuilder::MASK_VIEW,
            ]
        ));

        self::assertSame(MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE, MaskCompounder::compound(
            [
                MaskBuilder::MASK_EDIT,
                MaskBuilder::MASK_VIEW,
                MaskBuilder::MASK_DELETE,
            ]
        ));

        self::assertSame(MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER, MaskCompounder::compound(
            [
                MaskBuilder::MASK_OWNER,
                MaskBuilder::MASK_VIEW,
                MaskBuilder::MASK_EDIT,
                MaskBuilder::MASK_DELETE,
            ]
        ));
    }
}
