<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Authentication\TwoFactor;

use <PERSON><PERSON><PERSON>\TwoFactorBundle\Security\TwoFactor\AuthenticationContextInterface;
use Scheb\TwoFactorBundle\Security\TwoFactor\Provider\Email\EmailTwoFactorProvider;
use Scheb\TwoFactorBundle\Security\TwoFactor\Provider\TwoFactorFormRendererInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\User;
use U2\Security\Authentication\TwoFactor\EmailProvider;
use U2\SystemSettings\SystemSettings;

class EmailProviderTest extends UnitTestCase
{
    public function test_email_two_factor_system_setting_enforces_two_factor_authentication(): void
    {
        // Given
        $emailTwoFactorProvider = $this->createMock(EmailTwoFactorProvider::class);

        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityTwoFactorIsEnforced')->willReturn(true);

        $emailProvider = new EmailProvider(
            $emailTwoFactorProvider,
            $systemSettings
        );

        $authenticationContext = $this->createMock(AuthenticationContextInterface::class);
        $emailTwoFactorProvider
            ->expects($this->never())
            ->method('beginAuthentication');

        // Then
        self::assertTrue($emailProvider->beginAuthentication($authenticationContext));
    }

    public function test_email_two_factor_provider_decision_enforce_email_two_factor_authentication(): void
    {
        // Given
        $emailTwoFactorProvider = $this->createMock(EmailTwoFactorProvider::class);

        $systemSettings = $this->createMock(SystemSettings::class);
        $systemSettings->expects($this->once())->method('getSecurityTwoFactorIsEnforced')->willReturn(true);

        $emailProvider = new EmailProvider(
            $emailTwoFactorProvider,
            $systemSettings
        );

        $emailTwoFactorProvider
            ->method('beginAuthentication')
            ->with(
                $authenticationContext = $this->createMock(AuthenticationContextInterface::class)
            )
            ->willReturn(true);

        // Then
        self::assertTrue($emailProvider->beginAuthentication($authenticationContext));
    }

    public function test_get_form_renderer(): void
    {
        // Given
        $emailTwoFactorProvider = $this->createMock(EmailTwoFactorProvider::class);

        $emailProvider = new EmailProvider(
            $emailTwoFactorProvider,
            $this->createMock(SystemSettings::class)
        );

        $emailTwoFactorProvider
            ->method('getFormRenderer')
            ->willReturn(
                $formRenderer = $this->createMock(TwoFactorFormRendererInterface::class)
            );

        // Then
        self::assertSame($formRenderer, $emailProvider->getFormRenderer());
    }

    public function test_validate_authentication_code(): void
    {
        // Given
        $emailTwoFactorProvider = $this->createMock(EmailTwoFactorProvider::class);

        $emailProvider = new EmailProvider(
            $emailTwoFactorProvider,
            $this->createMock(SystemSettings::class)
        );

        $user = new User();

        $emailTwoFactorProvider
            ->method('validateAuthenticationCode')
            ->with($user, 'code')->willReturn(false);

        // Then
        self::assertFalse($emailProvider->validateAuthenticationCode($user, 'code'));
    }

    public function test_prepare_authentication(): void
    {
        // Given
        $emailTwoFactorProvider = $this->createMock(EmailTwoFactorProvider::class);

        $emailProvider = new EmailProvider(
            $emailTwoFactorProvider,
            $this->createMock(SystemSettings::class)
        );

        $user = new User();

        // Then
        $emailTwoFactorProvider
            ->expects($this->once())
            ->method('prepareAuthentication')
            ->with($user);

        // When
        $emailProvider->prepareAuthentication($user);
    }
}
