<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Security\Voter\Task;

use Symfony\Component\Security\Core\Authentication\Token\UsernamePasswordToken;
use Symfony\Component\Security\Core\Authorization\Voter\VoterInterface;
use Tests\U2\UnitTestCase;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Security\Voter\Task\TaskTypeReviewVoter;
use U2\Security\Voter\VoterAttributes;

class TaskTypeReviewVoterTest extends UnitTestCase
{
    public function test_supports_attributes(): void
    {
        $voter = new TaskTypeReviewVoter();

        self::assertTrue($voter->supportsAttribute(VoterAttributes::review));
        self::assertTrue($voter->supportsAttribute(VoterAttributes::removeReview));
        self::assertFalse($voter->supportsAttribute('UNSUPPORTED_ATTRIBUTE'));
    }

    public function test_supports_types(): void
    {
        $voter = new TaskTypeReviewVoter();

        self::assertTrue($voter->supportsType(OtherDeadline::class));
        self::assertFalse($voter->supportsType(\stdClass::class));
    }

    public function test_grant_review_when_current_user_has_not_reviewed(): void
    {
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $voter = new TaskTypeReviewVoter();

        $decision = $voter->vote($token, new OtherDeadline(new Status()), [VoterAttributes::review]);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision for an issue that has not been reviewed by the current user');
    }

    public function test_grant_remove_review_when_current_user_has_reviewed(): void
    {
        $currentUser = new User();
        $token = new UsernamePasswordToken($currentUser, 'TEST_PROVIDER');
        $taskType = new OtherDeadline(new Status());
        $taskType->getTask()->review($currentUser);
        $voter = new TaskTypeReviewVoter();

        $decision = $voter->vote($token, $taskType, [VoterAttributes::removeReview]);

        self::assertSame(VoterInterface::ACCESS_GRANTED, $decision, 'The voter returned the wrong decision for an issue that has been reviewed by the current user');
    }

    public function test_deny_review_when_current_user_has_reviewed(): void
    {
        $currentUser = new User();
        $token = new UsernamePasswordToken($currentUser, 'TEST_PROVIDER');
        $taskType = new OtherDeadline(new Status());
        $taskType->getTask()->review($currentUser);
        $voter = new TaskTypeReviewVoter();

        $decision = $voter->vote($token, $taskType, [VoterAttributes::review]);

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision for an issue that has been reviewed by the current user');
    }

    public function test_deny_remove_review_when_current_user_has_not_reviewed(): void
    {
        $token = new UsernamePasswordToken(new User(), 'TEST_PROVIDER');
        $voter = new TaskTypeReviewVoter();

        $decision = $voter->vote($token, new OtherDeadline(new Status()), [VoterAttributes::removeReview]);

        self::assertSame(VoterInterface::ACCESS_DENIED, $decision, 'The voter returned the wrong decision for an issue that has not been reviewed by the current user');
    }
}
