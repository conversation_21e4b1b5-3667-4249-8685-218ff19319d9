<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Datasheets\Item;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\ItemFactory;
use U2\Datasheets\Item\ItemCollection;

class ItemCollectionTest extends UnitTestCase
{
    public function test_unique(): void
    {
        $item1 = ItemFactory::new()->withId()->create()->_real();
        $item2 = ItemFactory::new()->withId()->create()->_real();
        $item3 = ItemFactory::new()->withId()->create()->_real();
        $collection = new ItemCollection([$item1, $item2, $item3, $item1, $item2, $item3]);

        self::assertCount(6, $collection, 'The collection should contain duplicates');

        $uniqueCollection = $collection->unique();

        self::assertCount(3, $uniqueCollection, 'The collection should be unique based in the item id');
        self::assertCount(6, $collection, 'The original collection should be unchanged');
    }
}
