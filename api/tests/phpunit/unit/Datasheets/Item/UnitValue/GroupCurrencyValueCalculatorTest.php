<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Datasheets\Item\UnitValue;

use Tests\U2\UnitTestCase;
use U2\DataFixtures\Example\CurrencyFactory;
use U2\DataFixtures\Example\ItemFactory;
use U2\DataFixtures\Example\MoneyItemUnitValueFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\Datasheets\Item\ExchangeMethods;
use U2\Datasheets\Item\UnitValue\GroupCurrencyValueCalculator;
use U2\Exception\ExchangeRateNotFoundException;
use U2\Exception\MissingPreviousPeriodException;
use U2\Money\ExchangeRate\ExchangeRateTypes;
use U2\Repository\ExchangeRateRepository;

use function Zenstruck\Foundry\Persistence\unproxy;

class GroupCurrencyValueCalculatorTest extends UnitTestCase
{
    public function test_throws_exception_if_previous_exchange_method_is_used_without_previous_period(): void
    {
        $unit = UnitFactory::createOne();
        $period = PeriodFactory::createOne(['previousPeriod' => null]);
        $item = ItemFactory::new()->money()->create(['exchangeMethod' => ExchangeMethods::PREVIOUS_AVERAGE]);
        $itemUnitValue = MoneyItemUnitValueFactory::createOne(['item' => $item, 'unit' => $unit, 'period' => $period]);
        $groupCurrencyValueCalculator = new GroupCurrencyValueCalculator(
            $this->createMock(ExchangeRateRepository::class),
        );

        $this->expectException(MissingPreviousPeriodException::class);

        $groupCurrencyValueCalculator->calculate($itemUnitValue);
    }

    public function test_calculates_the_group_value_of_a_money_values_with_a_previous_period_exchange_rate(): void
    {
        $unit = UnitFactory::createOne(['id' => 1, 'currency' => CurrencyFactory::new(['id' => 2])]);
        $period = PeriodFactory::createOne(['id' => 1, 'previousPeriod' => PeriodFactory::new(['id' => 2])]);
        $item = ItemFactory::new()->money()->create(['exchangeMethod' => ExchangeMethods::PREVIOUS_AVERAGE]);
        $exchangeRateRepository = $this->createMock(ExchangeRateRepository::class);
        $exchangeRateRepository->expects($this->once())
            ->method('findExchangeRateValue')
            ->with(
                $unit->_real(),
                unproxy($period->getPreviousPeriod()),
                ExchangeRateTypes::AVERAGE,
            )
            ->willReturn('0.5');
        $itemUnitValue = MoneyItemUnitValueFactory::createOne(['localCurrencyValue' => '200', 'item' => $item, 'unit' => $unit, 'period' => $period]);

        $groupCurrencyValueCalculator = new GroupCurrencyValueCalculator($exchangeRateRepository);

        self::assertSame('100.0000000000', $groupCurrencyValueCalculator->calculate($itemUnitValue));
    }

    public function test_calculates_the_group_value_of_a_money_value(): void
    {
        $unit = UnitFactory::createOne(['id' => 1, 'currency' => CurrencyFactory::new(['id' => 2])]);
        $period = PeriodFactory::createOne(['id' => 1]);
        $item = ItemFactory::new()->money()->create(['exchangeMethod' => ExchangeMethods::CURRENT]);
        $itemUnitValue = MoneyItemUnitValueFactory::createOne(['localCurrencyValue' => '200', 'item' => $item, 'unit' => $unit, 'period' => $period]);
        $exchangeRateRepository = $this->createMock(ExchangeRateRepository::class);
        $exchangeRateRepository->expects($this->once())
            ->method('findExchangeRateValue')
            ->with(
                $unit->_real(),
                $period->_real(),
                ExchangeRateTypes::CURRENT,
            )
            ->willReturn('0.5');

        $groupCurrencyValueCalculator = new GroupCurrencyValueCalculator($exchangeRateRepository);

        self::assertSame('100.0000000000', $groupCurrencyValueCalculator->calculate($itemUnitValue));
    }

    public function test_uses1_as_exchange_rate_if_source_and_destination_currency_is_the_same(): void
    {
        $unit = UnitFactory::createOne(['id' => 1, 'currency' => CurrencyFactory::getApplicationCurrency()]);
        $period = PeriodFactory::createOne(['id' => 1]);
        $item = ItemFactory::new()->money()->create(['exchangeMethod' => ExchangeMethods::CURRENT]);
        $itemUnitValue = MoneyItemUnitValueFactory::createOne(['localCurrencyValue' => '200', 'item' => $item, 'unit' => $unit, 'period' => $period]);
        $exchangeRateRepository = $this->createMock(ExchangeRateRepository::class);
        $exchangeRateRepository
            ->expects($this->once())
            ->method('findExchangeRateValue')
            ->with(
                $unit->_real(),
                $period->_real(),
                ExchangeRateTypes::CURRENT,
            )
            ->willReturn('1');

        $groupCurrencyValueCalculator = new GroupCurrencyValueCalculator($exchangeRateRepository);

        self::assertSame('200.0000000000', $groupCurrencyValueCalculator->calculate($itemUnitValue));
    }

    public function test_throws_an_exception_if_no_exchange_rate_can_be_found(): void
    {
        $unit = UnitFactory::createOne(['id' => 1, 'currency' => $currency = CurrencyFactory::createOne(['id' => 2])]);
        $period = PeriodFactory::createOne(['id' => 1]);
        $item = ItemFactory::new()->money()->create(['exchangeMethod' => ExchangeMethods::CURRENT]);
        $itemUnitValue = MoneyItemUnitValueFactory::createOne(['item' => $item, 'unit' => $unit, 'period' => $period]);
        $applicationCurrency = CurrencyFactory::getApplicationCurrency();
        $exchangeRateRepository = $this->createMock(ExchangeRateRepository::class);
        $exchangeRateRepository->expects($this->once())
            ->method('findExchangeRateValue')
            ->willThrowException(new ExchangeRateNotFoundException($period, $currency, $applicationCurrency->_real(), ExchangeRateTypes::CURRENT));

        $groupCurrencyValueCalculator = new GroupCurrencyValueCalculator($exchangeRateRepository);

        $this->expectException(ExchangeRateNotFoundException::class);

        $groupCurrencyValueCalculator->calculate($itemUnitValue);
    }
}
