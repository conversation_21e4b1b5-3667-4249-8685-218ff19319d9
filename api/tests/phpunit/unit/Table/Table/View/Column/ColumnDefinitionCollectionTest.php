<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Table\Table\View\Column;

use Tests\U2\UnitTestCase;
use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;

class ColumnDefinitionCollectionTest extends UnitTestCase
{
    public function test_get_all(): void
    {
        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);
        $columnDefinitionCollection->add('test1', null, []);
        $columnDefinitionCollection->add('test2', null, []);

        self::assertCount(3, $columnDefinitionCollection->all());
    }

    public function test_add_field_with_existing_id_throws_exception(): void
    {
        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Column test has already been defined.');

        $columnDefinitionCollection->add('test', null, []);
    }

    public function test_add_field_before_with_existing_id_throws_exception(): void
    {
        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Column test has already been defined.');

        $columnDefinitionCollection->addBefore('test', 'test', null, []);
    }

    public function test_add_field_after_with_existing_id_throws_exception(): void
    {
        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Column test has already been defined.');

        $columnDefinitionCollection->addAfter('test', 'test', null, []);
    }

    public function test_add(): void
    {
        $columnId = 'test';

        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add($columnId, null, []);

        self::assertSame($columnId, $columnDefinitionCollection[0]->getId());
    }

    public function test_before(): void
    {
        $columnId = 'before';

        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);
        $columnDefinitionCollection->addBefore('test', $columnId, null, []);

        self::assertSame($columnId, $columnDefinitionCollection[0]->getId());
    }

    public function test_after(): void
    {
        $columnId = 'after';

        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);
        $columnDefinitionCollection->addAfter('test', $columnId, null, []);

        self::assertSame($columnId, $columnDefinitionCollection[1]->getId());
    }

    public function test_find_by_column_id_returns_column(): void
    {
        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);

        $columnDefinition = $columnDefinitionCollection->findByColumnId('test');

        self::assertInstanceOf(ColumnDefinition::class, $columnDefinition);
    }

    public function test_off_set_unset(): void
    {
        $columnDefinitionCollection = new ColumnDefinitionCollection();
        $columnDefinitionCollection->add('test', null, []);
        self::assertArrayHasKey(0, $columnDefinitionCollection->all());

        $columnDefinitionCollection->offsetUnset(0);
        self::assertArrayNotHasKey(0, $columnDefinitionCollection->all());
    }
}
