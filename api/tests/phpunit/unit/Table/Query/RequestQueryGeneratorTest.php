<?php

declare(strict_types=1);
namespace Tests\Unit\U2\Table\Query;

use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Tests\U2\UnitTestCase;
use U2\DataSourcery\DataSource\DataSource;
use U2\DataSourcery\Query\FilterInterface;
use U2\DataSourcery\Query\Pagination;
use U2\DataSourcery\Query\Query;
use U2\DataSourcery\Query\Sort;
use U2\DataSourcery\Query\SortCondition;
use U2\DataSourcery\UQL\Interpreter;
use U2\Table\Query\RequestQueryGenerator;

class RequestQueryGeneratorTest extends UnitTestCase
{
    /**
     * @var Interpreter&MockObject
     */
    private MockObject $interpreter;

    private RequestQueryGenerator $requestQueryGenerator;

    /**
     * @var MockObject&RequestStack
     */
    private MockObject $requestStack;

    public function test_generates_a_query_based_on_a_complete_pagination(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => '',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => '',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '1337:313373',
            ]
        );
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $expectedQuery = new Query(
            null,
            null,
            $filter,
            new Pagination(1337, 313373)
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_generates_a_query_based_on_a_partial_pagination(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => '',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => '',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '12345',
            ]
        );
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $expectedQuery = new Query(
            null,
            null,
            $filter,
            new Pagination(12345, Pagination::defaultCount)
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_generates_a_query_based_on_a_select(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => 'FIELD_1,FIELD_3,FIELD_8',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => '',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '',
            ]
        );
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);

        $expectedQuery = new Query(
            [
                'FIELD_1',
                'FIELD_3',
                'FIELD_8',
            ],
            null,
            $filter,
            null
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_generates_a_query_based_on_an_empty_pagination(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => '',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => '',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '',
            ]
        );
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $expectedQuery = new Query(
            null,
            null,
            $filter,
            new Pagination(0, Pagination::defaultCount)
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_generates_a_query_with_default_sort_order_if_not_defined(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => '',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => 'FIELD',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '',
            ]
        );
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);

        $expectedQuery = new Query(
            null,
            new Sort([new SortCondition('FIELD', 'ASC')]),
            $filter
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_generates_a_query_based_on_ordered_sort(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => '',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => 'FIELD:DESC',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '',
            ]
        );
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $expectedQuery = new Query(
            null,
            new Sort([new SortCondition('FIELD', 'DESC')]),
            $filter
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_generates_a_query_based_on_multi_sort(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $request = new Request(
            query: [RequestQueryGenerator::QUERY_PARAMETER_SELECT => '',
                RequestQueryGenerator::QUERY_PARAMETER_SORT => 'FIELD_1:ASC,FIELD_2:DESC,FIELD_3',
                RequestQueryGenerator::QUERY_PARAMETER_UQL => '',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '',
            ]
        );
        $filter = $this->createMock(FilterInterface::class);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('')->willReturn($filter);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $expectedQuery = new Query(
            null,
            new Sort(
                [
                    new SortCondition('FIELD_1', 'ASC'),
                    new SortCondition('FIELD_2', 'DESC'),
                    new SortCondition('FIELD_3', 'ASC'),
                ]
            ),
            $filter
        );
        self::assertEquals(
            $expectedQuery,
            $this->requestQueryGenerator->generate($dataSource)
        );
    }

    public function test_can_can_generate_a_query_from_environment_if_uql_is_set(): void
    {
        $request = new Request([RequestQueryGenerator::QUERY_PARAMETER_UQL => '']);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        self::assertTrue($this->requestQueryGenerator->canGenerateQueryFromEnvironment());
    }

    public function test_can_can_generate_a_query_from_environment_if_select_is_set(): void
    {
        $request = new Request([RequestQueryGenerator::QUERY_PARAMETER_SELECT => '']);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        self::assertTrue($this->requestQueryGenerator->canGenerateQueryFromEnvironment());
    }

    public function test_can_can_generate_a_query_from_environment_if_pagination_is_set(): void
    {
        $request = new Request([RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '']);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        self::assertTrue($this->requestQueryGenerator->canGenerateQueryFromEnvironment());
    }

    public function test_can_can_generate_a_query_from_environment_if_sort_is_set(): void
    {
        $request = new Request([RequestQueryGenerator::QUERY_PARAMETER_SORT => '']);
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        self::assertTrue($this->requestQueryGenerator->canGenerateQueryFromEnvironment());
    }

    public function test_cannot_generate_a_query_from_environment_if_no_parameter_is_set(): void
    {
        $request = new Request();
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        self::assertFalse($this->requestQueryGenerator->canGenerateQueryFromEnvironment());
    }

    public function test_does_not_resets_the_page_number_if_uql_has_not_been_changed(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $filter = $this->createMock(FilterInterface::class);
        $request = new Request(
            [
                RequestQueryGenerator::QUERY_PARAMETER_UQL => 'x = 2',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '7',
            ]
        );
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('x = 2')->willReturn($filter);
        $filter->expects($this->atLeastOnce())->method('getUql')->willReturn('x = 2');
        $previousQuery = new Query(
            null,
            null,
            $filter,
            new Pagination(7, Pagination::defaultCount)
        );
        self::assertEquals(
            clone $previousQuery,
            $this->requestQueryGenerator->generate($dataSource, $previousQuery)
        );
    }

    public function test_does_reset_the_page_number_if_uql_has_been_changed(): void
    {
        $dataSource = $this->createMock(DataSource::class);
        $previousFilter = $this->createMock(FilterInterface::class);
        $filter = $this->createMock(FilterInterface::class);
        $request = new Request(
            [
                RequestQueryGenerator::QUERY_PARAMETER_UQL => 'x = 3',
                RequestQueryGenerator::QUERY_PARAMETER_PAGINATION => '7',
            ]
        );
        $this->requestStack->expects($this->atLeastOnce())->method('getCurrentRequest')->willReturn($request);
        $dataSource->expects($this->atLeastOnce())->method('getFields')->willReturn([]);
        $dataSource->expects($this->atLeastOnce())->method('getEntityClass')->willReturn(\stdClass::class);
        //        $interpreter->interpret('x = 2', new ImmutableCollection(), \stdClass::class)->shouldBeCalled()->willReturn($previousFilter);
        $this->interpreter->expects($this->atLeastOnce())->method('interpret')->with('x = 3')->willReturn($filter);
        $previousFilter->expects($this->atLeastOnce())->method('getUql')->willReturn('x = 2');
        $previousQuery = new Query(
            null,
            null,
            $previousFilter,
            new Pagination(7, Pagination::defaultCount)
        );
        $filter->expects($this->atLeastOnce())->method('getUql')->willReturn('x = 3');

        self::assertEquals(
            new Query(
                null,
                null,
                $filter,
                new Pagination(0, Pagination::defaultCount)
            ),
            $this->requestQueryGenerator->generate($dataSource, $previousQuery)
        );
    }

    protected function setUp(): void
    {
        $this->requestStack = $this->createMock(RequestStack::class);
        $this->interpreter = $this->createMock(Interpreter::class);
        $this->requestQueryGenerator = new RequestQueryGenerator($this->requestStack, $this->interpreter);
    }
}
