<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\UnitHierarchy;

/**
 * @covers \U2\Entity\UnitHierarchy
 */
class UnitHierarchyPostItemTest extends ApiTestCase
{
    public function test_create(): void
    {
        // When
        $owner = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($owner);
        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/unit-hierarchies',
            [
                'json' => [
                    'name' => 'title',
                    'description' => 'Description',
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        /** @var string $responseContent */
        $responseContent = $response->getContent();

        /** @var array{id: int} $unitHierarchyResponse */
        $unitHierarchyResponse = json_decode($responseContent, true, 512, \JSON_THROW_ON_ERROR);

        self::assertJsonContains([
            '@id' => (string) $this->findIriBy(UnitHierarchy::class, ['id' => $unitHierarchyResponse['id']]),
            'id' => $unitHierarchyResponse['id'],
            '@type' => 'UnitHierarchy',
            'name' => 'title',
            'description' => 'Description',
        ]);
    }

    public function test_create_unauthorized(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            '/api/unit-hierarchies',
            [
                'json' => [
                    'name' => 'title',
                    'description' => 'Description',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
