<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;

/**
 * @covers \U2\Entity\Comment
 */
class CommentGetCollectionTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $user = UserFactory::getObject();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/comments',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_METHOD_NOT_ALLOWED);
    }
}
