<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use Tests\U2\TestUtils;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\Comment;
use U2\Entity\Task\Task;
use U2\Entity\UserGroup;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\Task\Task
 */
class TaskAddReviewTest extends ApiTestCase
{
    public function test_review_a_task(): void
    {
        $legalUnit = LegalUnitFactory::createOne();
        $contract = ContractFactory::createOne(['unit' => $legalUnit]);
        $authorizedUser = UserFactory::createOne([
            'authorizations' => [AuthorizationFactory::new(['item' => AuthorizationItem::Contract->value, 'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value]])],
            'units' => [$legalUnit],
        ]);
        $client = self::createClientWithAuth($authorizedUser);

        $task = $contract->getTask();
        self::assertFalse($task->reviewExistsForUser($authorizedUser));

        $userGroup = UserGroupFactory::createOne();

        // Ensure updated at is some days before so we can detect if it was updated
        $updatedAt = new \DateTime('-5 days');
        TestUtils::setProperty($task, 'updatedAt', $updatedAt);
        $contract->_save();
        self::assertSame($updatedAt->format(\DATE_ATOM), $task->getUpdatedAt()->format(\DATE_ATOM));

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/tasks/%s/add-review', $task->getId()->toRfc4122()),
            [
                'json' => [
                    'commentContent' => 'A comment',
                    'userGroup' => $this->findIriBy(UserGroup::class, ['id' => $userGroup->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        $entityManager = self::getEntityManager();
        $entityManager->refresh($task);

        self::assertTrue($task->reviewExistsForUser($authorizedUser));
        /** @var Comment $comment */
        $comment = $task->getComments()->first();
        self::assertSame('A comment', $comment->getBody());

        // Ensure updatedAt has been updated
        self::assertNotSame(
            $task->getUpdatedAt()->getTimestamp(),
            $updatedAt->getTimestamp()
        );
    }

    public function test_unauthorized_user_cannot_review_a_task(): void
    {
        $contract = ContractFactory::createOne(['unit' => LegalUnitFactory::createOne()]);
        $authorizedUser = UserFactory::createOne([
            'authorizations' => [AuthorizationFactory::new(['item' => AuthorizationItem::Contract->value, 'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value]])],
        ]);
        $client = self::createClientWithAuth($authorizedUser);

        self::assertFalse($contract->getTask()->reviewExistsForUser($authorizedUser));

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/tasks/%s/add-review', $contract->getTask()->getId()->toRfc4122()),
            ['json' => []]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);

        $entityManager = self::getEntityManager();
        $task = $entityManager->find(Task::class, $contract->getTask()->getId());
        self::assertFalse($task?->reviewExistsForUser($authorizedUser));
    }

    public function test_a_user_that_already_has_reviewed_cannot_review_again(): void
    {
        $legalUnit = LegalUnitFactory::createOne();
        $contract = ContractFactory::createOne(['unit' => $legalUnit]);
        $authorizedUser = UserFactory::createOne([
            'authorizations' => [AuthorizationFactory::new(['item' => AuthorizationItem::Contract->value, 'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value]])],
            'units' => [$legalUnit],
        ]);
        $client = self::createClientWithAuth($authorizedUser);

        $contract->getTask()->review($authorizedUser->_real());
        $contract->_save();

        self::assertTrue($contract->getTask()->reviewExistsForUser($authorizedUser));

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/tasks/%s/add-review', $contract->getTask()->getId()->toRfc4122()),
            ['json' => []]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
