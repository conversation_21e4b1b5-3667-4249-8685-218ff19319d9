<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\CommentFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\Comment;
use U2\Entity\Task\Task;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\Comment
 */
class CommentGetItemTest extends ApiTestCase
{
    public function test_get_item(): void
    {
        $legalUnit = LegalUnitFactory::createOne()->_real();
        $contract = ContractFactory::createOne(['unit' => $legalUnit])->_real();

        $group = UserGroupFactory::createOne();
        $authorizedUser = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::Contract->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
                'units' => [$legalUnit],
                'groups' => [$group],
            ]
        )->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $parentComment = CommentFactory::createOne(['author' => $authorizedUser, 'task' => $contract->getTask()]);
        $comment = CommentFactory::createOne(['author' => $authorizedUser, 'task' => $contract->getTask(), 'group' => $group, 'parent' => $parentComment]);

        /** @var string $commentIri */
        $commentIri = $this->findIriBy(Comment::class, ['id' => $comment->getId()]);

        // When
        $client->request(HttpOperation::METHOD_GET, $commentIri);

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        $authorIri = $this->findIriBy(User::class, ['id' => $authorizedUser->getId()]);

        self::assertJsonEquals([
            '@context' => '/api/contexts/Comment',
            '@type' => 'Comment',
            '@id' => $commentIri,
            'id' => $comment->getId(),
            'task' => $this->findIriBy(Task::class, ['id' => $contract->getTask()->getId()]),
            'content' => $comment->getBody(),
            'quote' => $this->findIriBy(Comment::class, ['id' => $parentComment->getId()]),
            'author' => $authorIri,
            'createdAt' => $comment->getCreatedAt()->format(\DATE_W3C),
            'updatedAt' => $comment->getUpdatedAt()->format(\DATE_W3C),
            'deleted' => false,
            'group' => $this->findIriBy(UserGroup::class, ['id' => $group->getId()]),
            'u2:extra' => [
                '@id' => $this->getIriFromResource($comment->getCommentExtra()),
                '@type' => 'CommentExtra',
                'canWrite' => true,
                'canDelete' => true,
            ],
        ]);
    }
}
