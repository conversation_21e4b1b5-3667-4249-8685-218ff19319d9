<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;

/**
 * @covers \U2\Entity\Task\Task
 */
class TaskGetCollectionTest extends ApiTestCase
{
    public function test_get_collection(): void
    {
        $authorizedUser = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($authorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/tasks',
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NOT_FOUND);
    }
}
