<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Task;

use ApiPlatform\Metadata\HttpOperation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use Tests\U2\TestUtils;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\ContractFactory;
use U2\DataFixtures\Example\FileFactory;
use U2\DataFixtures\Example\LegalUnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Entity\File;
use U2\Entity\Task\Task;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Entity\Task\Task
 */
class TaskLinkAttachmentTest extends ApiTestCase
{
    public function test_link_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'accessType' => File::PUBLIC_ACCESS,
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
            ]
        )->_real();

        $contract = ContractFactory::createOne(
            [
                'unit' => $legalUnit = LegalUnitFactory::createOne()->_real(),
                'name' => 'Contract name',
            ]
        );

        self::assertCount(0, $contract->getTask()->getFiles());

        // Ensure updated at is some days before so we can detect if it was updated
        $updatedAt = new \DateTime('-5 days');
        TestUtils::setProperty($contract->getTask(), 'updatedAt', $updatedAt);
        $contract->_save();
        self::assertSame($updatedAt->format(\DATE_ATOM), $contract->getTask()->getUpdatedAt()->format(\DATE_ATOM));
        $user = UserFactory::createOne([
            'units' => [$legalUnit],
            'authorizations' => new ArrayCollection([
                AuthorizationFactory::createOne([
                    'item' => AuthorizationItem::Contract->value,
                    'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                ])->_real(),
            ]),
        ])->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            $this->findIriBy(Task::class, ['id' => $contract->getTask()->getId()->toRfc4122()]) . '/attachments',
            [
                'json' => [
                    'file' => $this->findIriBy(File::class, ['id' => $file->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);

        static::getContainer()->get(EntityManagerInterface::class)->refresh($contract->getTask());

        self::assertCount(1, $contract->getTask()->getFiles());

        // Ensure updatedAt has been updated
        $entityManager = self::getEntityManager();
        self::assertNotSame(
            $entityManager->find(Task::class, $contract->getTask()->getId())?->getUpdatedAt()?->getTimestamp(),
            $updatedAt->getTimestamp()
        );
    }

    public function test_unauthorized_user_cannot_link_an_attachment(): void
    {
        $file = FileFactory::createOne(
            [
                'createdBy' => UserFactory::createOne()->_real(),
                'path' => 'my-file.ext',
                'description' => 'My awesome file',
            ]
        )->_real();

        $contract = ContractFactory::createOne(
            [
                'unit' => LegalUnitFactory::createOne()->_real(),
                'name' => 'Contract name',
                'files' => [],
            ]
        )->_real();

        self::assertCount(0, $contract->getTask()->getFiles());
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            \sprintf('/api/tasks/%s/attachments', $contract->getTask()->getId()->toRfc4122()),
            [
                'json' => [
                    'file' => $this->findIriBy(File::class, ['id' => $file->getId()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);

        static::getContainer()->get(EntityManagerInterface::class)->refresh($contract->getTask());

        self::assertCount(0, $contract->getTask()->getFiles());
    }
}
