<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\Api\Resource\ItemUnitHierarchyValue;
use U2\DataFixtures\Example\AuthorizationFactory;
use U2\DataFixtures\Example\CheckboxItemUnitValueFactory;
use U2\DataFixtures\Example\DatasheetFactory;
use U2\DataFixtures\Example\DiffItemUnitValueFactory;
use U2\DataFixtures\Example\FieldFactory;
use U2\DataFixtures\Example\ItemFactory;
use U2\DataFixtures\Example\MoneyItemUnitValueFactory;
use U2\DataFixtures\Example\PercentItemUnitValueFactory;
use U2\DataFixtures\Example\PeriodFactory;
use U2\DataFixtures\Example\TextItemUnitValueFactory;
use U2\DataFixtures\Example\UnitHierarchyDefinitionFactory;
use U2\DataFixtures\Example\UnitHierarchyFactory;
use U2\DataFixtures\Example\UnitPeriodFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\AuthorizationItem;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @covers \U2\Api\Resource\ItemUnitHierarchyValue
 */
class ItemUnitHierarchyValueGetCollectionTest extends ApiTestCase
{
    public function test_get_collection_by_unit_hierarchy_and_period(): void
    {
        $previousPeriod = PeriodFactory::createOne();

        // The data we do not expect to get returned
        $anotherPercentItem = ItemFactory::new()->editable()->percent()->create();
        $anotherTextItem = ItemFactory::new()->editable()->text()->create();
        $anotherMoneyItem = ItemFactory::new()->editable()->money()->create();
        $anotherDiffItem = ItemFactory::new()->editable()->diff()->create();
        $anotherCheckboxItem = ItemFactory::new()->editable()->checkbox()->create();

        // The data we expect to get returned
        $previousPeriodUnitPeriod = UnitPeriodFactory::createOne(['period' => $previousPeriod]);

        $user = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::UnitPeriod->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
                'units' => [$previousPeriodUnitPeriod->getUnit()],
            ]
        )->_real();
        $client = self::createClientWithAuth($user);

        $unitPeriod = UnitPeriodFactory::createOne(['period' => PeriodFactory::createOne(['previousPeriod' => $previousPeriod]), 'unit' => $previousPeriodUnitPeriod->getUnit()]);

        $percentItemUnitHierarchyValue = PercentItemUnitValueFactory::new()
            ->withItem(ItemFactory::new()->editable()->percent()->create())
            ->withUnitPeriod($unitPeriod)->withValue('10')
            ->create();

        $textItemUnitHierarchyValue = TextItemUnitValueFactory::new()
            ->withItem(ItemFactory::new()->editable()->text()->create())
            ->withUnitPeriod($unitPeriod)
            ->withComment('A Comment')
            ->create();

        $moneyItemUnitHierarchyValue = MoneyItemUnitValueFactory::new()
            ->withItem(ItemFactory::new()->editable()->money()->create())
            ->withUnitPeriod($unitPeriod)
            ->withValues('10', '20')
            ->create();

        $diffItemUnitHierarchyValue = DiffItemUnitValueFactory::new()
            ->withItem(ItemFactory::new()->editable()->diff()->create())
            ->withUnitPeriod($unitPeriod)
            ->withDiff('1337')
            ->create();

        $checkboxItemUnitHierarchyValue = CheckboxItemUnitValueFactory::new()
            ->withItem(ItemFactory::new()->editable()->checkbox()->create())
            ->withUnitPeriod($unitPeriod)
            ->withIsChecked(false)
            ->create();

        $unitHierarchy = UnitHierarchyFactory::createOne();
        $unit = $unitPeriod->getUnit();
        \assert(null !== $unit);
        UnitHierarchyDefinitionFactory::new()->withUnitHierarchy($unitHierarchy)->with(['unit' => $unit])->create();

        // When
        $period = $unitPeriod->getPeriod();
        \assert(null !== $period);
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/item-unit-hierarchy-values?unitHierarchy=' . $unitHierarchy->getId() . '&period=' . $period->getId(),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        self::assertJsonContains([
            '@context' => '/api/contexts/ItemUnitHierarchyValue',
            '@id' => '/api/item-unit-hierarchy-values',
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($anotherPercentItem, $period, $unitHierarchy),
                    '@type' => 'PercentItemUnitHierarchyValue',
                    'value' => '0.0000000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($anotherTextItem, $period, $unitHierarchy),
                    '@type' => 'TextItemUnitHierarchyValue',
                    'comment' => null,
                    'value' => null,
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($anotherMoneyItem, $period, $unitHierarchy),
                    '@type' => 'MoneyItemUnitHierarchyValue',
                    'value' => '0.000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($anotherDiffItem, $period, $unitHierarchy),
                    '@type' => 'DiffItemUnitHierarchyValue',
                    'diff' => '0.000000',
                    'value' => '0.000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($anotherCheckboxItem, $period, $unitHierarchy),
                    '@type' => 'CheckboxItemUnitHierarchyValue',
                    'isChecked' => null,
                    'value' => null,
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($percentItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'PercentItemUnitHierarchyValue',
                    'value' => '10.0000000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($textItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'TextItemUnitHierarchyValue',
                    'comment' => null,
                    'value' => null,
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($moneyItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'MoneyItemUnitHierarchyValue',
                    'value' => '20.000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($diffItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'DiffItemUnitHierarchyValue',
                    'diff' => '1337.000000',
                    'value' => '1337.000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($checkboxItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'CheckboxItemUnitHierarchyValue',
                    'isChecked' => null,
                    'value' => null,
                ],
            ],
        ]);
    }

    public function test_get_collection_by_unit_hierarchy_period_and_datasheet(): void
    {
        // These items are not assigned to the datasheet and will therefore not be returned
        ItemFactory::new()->editable()->percent()->create();
        ItemFactory::new()->editable()->text()->create();
        ItemFactory::new()->editable()->money()->create();
        ItemFactory::new()->editable()->diff()->create();
        ItemFactory::new()->editable()->checkbox()->create();

        $previousPeriod = PeriodFactory::createOne();
        $previousPeriodUnitPeriod = UnitPeriodFactory::createOne(['period' => $previousPeriod]);

        $user = UserFactory::createOne(
            [
                'authorizations' => [
                    AuthorizationFactory::new(
                        [
                            'item' => AuthorizationItem::UnitPeriod->value,
                            'rights' => [AuthorizationRight::READ->value, AuthorizationRight::UPDATE->value],
                        ]
                    ),
                ],
                'units' => [$previousPeriodUnitPeriod->getUnit()],
            ]
        )->_real();
        $client = self::createClientWithAuth($user);

        $unitPeriod = UnitPeriodFactory::createOne(['period' => PeriodFactory::createOne(['previousPeriod' => $previousPeriod]), 'unit' => $previousPeriodUnitPeriod->getUnit()]);

        $percentItem = ItemFactory::new()->editable()->percent()->create();
        $percentItemUnitHierarchyValue = PercentItemUnitValueFactory::new()
            ->withItem($percentItem)
            ->withUnitPeriod($unitPeriod)->withValue('10')
            ->create();

        $textItem = ItemFactory::new()->editable()->text()->create();
        $textItemUnitHierarchyValue = TextItemUnitValueFactory::new()
            ->withItem($textItem)
            ->withUnitPeriod($unitPeriod)
            ->withComment('A Comment')
            ->create();

        $moneyItem = ItemFactory::new()->editable()->money()->create();
        $moneyItemUnitHierarchyValue = MoneyItemUnitValueFactory::new()
            ->withItem($moneyItem)
            ->withUnitPeriod($unitPeriod)
            ->withValues('10', '20')
            ->create();

        $diffItem = ItemFactory::new()->editable()->diff()->create();
        $diffItemUnitHierarchyValue = DiffItemUnitValueFactory::new()
            ->withItem($diffItem)
            ->withUnitPeriod($unitPeriod)
            ->withDiff('1337')
            ->create();

        $checkboxItem = ItemFactory::new()->editable()->checkbox()->create();
        $checkboxItemUnitHierarchyValue = CheckboxItemUnitValueFactory::new()
            ->withItem($checkboxItem)
            ->withUnitPeriod($unitPeriod)
            ->withIsChecked(false)
            ->create();

        $unitHierarchy = UnitHierarchyFactory::createOne();
        $unit = $unitPeriod->getUnit();
        \assert(null !== $unit);
        UnitHierarchyDefinitionFactory::new()->withUnitHierarchy($unitHierarchy)->with(['unit' => $unit])->create();

        $datasheet = DatasheetFactory::createOne();
        FieldFactory::createOne(['layout' => $datasheet, 'item' => $percentItem]);
        FieldFactory::createOne(['layout' => $datasheet, 'item' => $textItem]);
        FieldFactory::createOne(['layout' => $datasheet, 'item' => $moneyItem]);
        FieldFactory::createOne(['layout' => $datasheet, 'item' => $diffItem]);
        FieldFactory::createOne(['layout' => $datasheet, 'item' => $checkboxItem]);

        // When
        $period = $unitPeriod->getPeriod();
        \assert(null !== $period);
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/item-unit-hierarchy-values?unitHierarchy=' . $unitHierarchy->getId() . '&period=' . $period->getId() . '&datasheet=' . $datasheet->getId(),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        self::assertJsonContains([
            '@context' => '/api/contexts/ItemUnitHierarchyValue',
            '@id' => '/api/item-unit-hierarchy-values',
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($percentItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'PercentItemUnitHierarchyValue',
                    'value' => '10.0000000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($textItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'TextItemUnitHierarchyValue',
                    'comment' => null,
                    'value' => null,
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($moneyItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'MoneyItemUnitHierarchyValue',
                    'value' => '20.000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($diffItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'DiffItemUnitHierarchyValue',
                    'diff' => '1337.000000',
                    'value' => '1337.000000',
                ],
                [
                    '@id' => '/api/item-unit-hierarchy-values/' . ItemUnitHierarchyValue::createId($checkboxItemUnitHierarchyValue->getItem(), $period, $unitHierarchy),
                    '@type' => 'CheckboxItemUnitHierarchyValue',
                    'isChecked' => null,
                    'value' => null,
                ],
            ],
        ]);
    }

    public function test_get_collection_throws_403_for_unit_period_user_has_no_access_to(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $previousPeriod = PeriodFactory::createOne();
        $previousPeriodUnitPeriod = UnitPeriodFactory::createOne(['period' => $previousPeriod]);
        $unitPeriod = UnitPeriodFactory::createOne(['period' => PeriodFactory::createOne(['previousPeriod' => $previousPeriod]), 'unit' => $previousPeriodUnitPeriod->getUnit()]);

        $unitHierarchy = UnitHierarchyFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            '/api/item-unit-hierarchy-values?unitHierarchy=' . $unitHierarchy->getId() . '&period=' . $unitPeriod->getPeriod()?->getId(),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
