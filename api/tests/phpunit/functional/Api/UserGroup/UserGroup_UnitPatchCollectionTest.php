<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\UserGroup;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\Unit;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\UserGroup
 */
class UserGroup_UnitPatchCollectionTest extends ApiTestCase
{
    public function test_update_units(): void
    {
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);
        $client = self::createClientWithAuth($admin);

        $userGroup = UserGroupFactory::createOne([
            'units' => [UnitFactory::createOne()],
        ]);

        self::assertCount(1, $userGroup->getUnits());

        $requestUrl = "/api/user-groups/{$userGroup->getId()}/units";

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            $requestUrl,
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'units' => array_map(static fn (Unit $unit): string => '/api/units/' . $unit->getId(), [...$userGroup->getUnits(), UnitFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertCount(2, $userGroup->getUnits());
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_update_units_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $userGroup = UserGroupFactory::createOne([]);

        $requestUrl = "/api/user-groups/{$userGroup->getId()}/units";

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            $requestUrl,
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'units' => array_map(static fn (Unit $unit): string => '/api/units/' . $unit->getId(), [...$userGroup->getUnits(), UnitFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
