<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Datasheet;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DatasheetFactory;
use U2\DataFixtures\Example\ItemFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\Field;

/**
 * @covers \U2\Entity\Field
 */
class Datasheet_FieldPostItemTest extends ApiTestCase
{
    public function test_post_field_of_a_layout(): void
    {
        $user = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($user);

        $layout = DatasheetFactory::createOne();

        // When
        $layoutIri = '/api/layouts/' . $layout->getId();
        $itemIri = $this->getIriFromResource(ItemFactory::new()->money()->create()->_real());
        $client->request(
            HttpOperation::METHOD_POST,
            $layoutIri . '/fields',
            [
                'json' => [
                    'name' => 'MyField',
                    'disabled' => true,
                    'item' => $itemIri,
                    'helpText' => 'Help!',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);

        /** @var Field $firstField */
        $firstField = $layout->getFields()->first();

        self::assertJsonContains([
            '@type' => 'Field',
            '@id' => $layoutIri . '/fields/' . $firstField->getId(),
            'name' => 'MyField',
            'disabled' => true,
            'item' => $itemIri,
            'helpText' => 'Help!',
        ]);
    }

    public function test_post_field_of_a_layout_as_unauthorized(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $layout = DatasheetFactory::createOne();

        // When
        $layoutIri = '/api/layouts/' . $layout->getId();
        $client->request(
            HttpOperation::METHOD_POST,
            $layoutIri . '/fields',
            [
                'json' => [
                    'name' => 'My Field',
                    'disabled' => true,
                    'helpText' => 'Help!',
                ],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
