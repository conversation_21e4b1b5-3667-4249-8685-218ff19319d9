<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Datasheet;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DatasheetFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\UserPermission;

/**
 * @covers \U2\Entity\Datasheet
 */
class Datasheet_UserPermissionGetCollectionTest extends ApiTestCase
{
    public function test_get_user_permissions(): void
    {
        $client = self::createClient();
        $layout = DatasheetFactory::createOne([
            'userPermissions' => [
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
                new UserPermissionInput(UserFactory::createOne()->_real(), MaskBuilder::MASK_VIEW),
            ],
        ]);

        self::assertCount(3, $layout->getPermissions()->getUserPermissions());

        $user = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($user);

        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/layouts/%s/user-permissions', $layout->getId()),
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        /** @var UserPermission $userPermission */
        $userPermission = $layout->getPermissions()->getUserPermissions()->first();
        self::assertJsonContains([
            '@context' => '/api/contexts/UserPermission',
            '@id' => \sprintf('/api/layouts/%s/user-permissions', $layout->getId()),
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/user-permissions/' . $userPermission->getId(),
                    '@type' => 'UserPermission',
                    'id' => $userPermission->getId(),
                    'user' => '/api/users/' . $userPermission->getUser()->getId(),
                    'mask' => 1,
                ],
            ],
        ]);
    }
}
