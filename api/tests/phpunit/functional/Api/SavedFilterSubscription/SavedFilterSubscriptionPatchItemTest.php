<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\SavedFilterSubscription;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\SavedFilterFactory;
use U2\DataFixtures\Example\SavedFilterSubscriptionFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\Frequency;
use U2\Entity\SavedFilter;
use U2\Entity\User;

/**
 * @covers \U2\Entity\SavedFilterSubscription
 */
class SavedFilterSubscriptionPatchItemTest extends ApiTestCase
{
    public function test_update(): void
    {
        // Given
        $adminUser = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($adminUser);

        $initialDate = new \DateTime('-1 year');
        $subscription = SavedFilterSubscriptionFactory::createOne([
            'createdBy' => $adminUser,
            'updatedBy' => $adminUser,
            'createdAt' => $initialDate,
            'updatedAt' => $initialDate,
            'lastRun' => $initialDate,
            'savedFilter' => SavedFilterFactory::createOne(['owner' => $adminUser]),
            'frequency' => new Frequency('5 * * * *'),
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            "/api/saved-filter-subscriptions/{$subscription->getId()}",
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => $newData = [
                    'name' => 'My new description',
                    'description' => 'My new description',
                    'frequency' => '5 * * * 6',
                    'active' => false,
                    'lastRun' => new \DateTime('now'),
                ],
            ]
        );

        // Then
        $subscription->_refresh();
        self::assertResponseIsSuccessful();
        self::assertJsonContains([
            '@context' => '/api/contexts/SavedFilterSubscription',
            '@id' => "/api/saved-filter-subscriptions/{$subscription->getId()}",
            '@type' => 'SavedFilterSubscription',
            'id' => $subscription->getId(),
            'name' => $newData['name'],
            'description' => $newData['description'],
            'frequency' => $newData['frequency'],
            'lastRun' => $initialDate->format(\DATE_W3C), // last run should not update
            'savedFilter' => [
                '@id' => $this->findIriBy(SavedFilter::class, ['id' => $subscription->getSavedFilter()->getId()]),
                '@type' => 'SavedFilter',
                'id' => $subscription->getSavedFilter()->getId(),
                'name' => $subscription->getSavedFilter()->getName(),
                'owner' => $this->findIriBy(User::class, ['id' => $adminUser->getId()]),
                'uql' => $subscription->getSavedFilter()->getUql(),
                'canEdit' => true,
                'canDelete' => true,
            ],
            'active' => $newData['active'],
            'createdBy' => $this->findIriBy(User::class, ['id' => $subscription->getCreatedBy()?->getId()]),
            'updatedBy' => $this->findIriBy(User::class, ['id' => $subscription->getUpdatedBy()?->getId()]),
            'createdAt' => $subscription->getCreatedAt()?->format(\DATE_W3C),
            'updatedAt' => $subscription->getUpdatedAt()?->format(\DATE_W3C),
            'nextRun' => null, // next run should be null because we set `active` to false
            'userCount' => $subscription->getUserCount(),
            'groupCount' => $subscription->getGroupCount(),
        ]);
    }

    public function test_update_as_unauthorized(): void
    {
        $user = UserFactory::createOne();
        $client = self::createClientWithAuth($user->_real());

        $subscription = SavedFilterSubscriptionFactory::createOne([
            'createdBy' => $user,
            'updatedBy' => $user,
            'savedFilter' => SavedFilterFactory::createOne(['owner' => $user]),
        ]);

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            "/api/saved-filter-subscriptions/{$subscription->getId()}",
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
