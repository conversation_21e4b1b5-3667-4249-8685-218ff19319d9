<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\SavedFilterSubscription;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\SavedFilterSubscriptionFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\SavedFilterSubscription;

/**
 * @covers \U2\Entity\SavedFilterSubscription
 */
class SavedFilterSubscriptionDeleteItemTest extends ApiTestCase
{
    public function test_delete(): void
    {
        $adminUser = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($adminUser);

        $subscription = SavedFilterSubscriptionFactory::createOne();

        // When
        $id = $subscription->getId();
        $client->request(
            HttpOperation::METHOD_DELETE,
            "/api/saved-filter-subscriptions/{$id}",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        self::assertNull(
            $this->findIriBy(SavedFilterSubscription::class, ['id' => $id])
        );
    }

    public function test_delete_as_unauthorized(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $subscription = SavedFilterSubscriptionFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_DELETE,
            "/api/saved-filter-subscriptions/{$subscription->getId()}",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
