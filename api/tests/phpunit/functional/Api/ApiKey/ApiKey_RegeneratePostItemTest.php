<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\ApiKey;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bridge\PhpUnit\ClockMock;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\ApiKeyFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\ApiKey;
use U2\Repository\ApiKeyRepository;

/**
 * @covers \U2\Entity\ApiKey
 */
class ApiKey_RegeneratePostItemTest extends ApiTestCase
{
    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }

    public function test_regenerate(): void
    {
        $authorizedUser = UserFactory::createOne();
        $client = self::createClientWithAuth($authorizedUser);

        ClockMock::withClockMock(strtotime('2020-01-01T00:00:00'));

        $apiKey = ApiKeyFactory::createOne([
            'name' => 'Api Key Name',
            'createdBy' => $authorizedUser,
        ]);

        $originalEncryptedApiKey = $apiKey->getEncryptedApiKey();

        self::assertSame('2020-01-08T00:00:00+00:00', $apiKey->getExpiresAt()?->format(\DateTimeInterface::W3C));

        // When
        $response = $client->request(
            HttpOperation::METHOD_POST,
            "/api/api-keys/{$apiKey->getId()}/regenerate",
            [
                'json' => [
                    'ttl' => 60,
                ],
            ]
        );
        self::assertResponseIsSuccessful();

        $responseContent = json_decode($response->getContent());
        \assert($responseContent instanceof \stdClass && isset($responseContent->id));
        $id = $responseContent->id;

        /** @var ApiKey $apiKey */
        $apiKey = self::getService(ApiKeyRepository::class)->findOneBy([
            'id' => $id,
        ]);

        self::assertNotSame($originalEncryptedApiKey, $apiKey->getEncryptedApiKey());
        self::assertNotNull($responseContent->readableApiKey);

        self::assertSame('2020-03-01T00:00:00+00:00', $apiKey->getExpiresAt()?->format(\DateTimeInterface::W3C));
    }
}
