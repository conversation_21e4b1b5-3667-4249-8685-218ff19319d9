<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\DocumentTemplate;

use ApiPlatform\Metadata\HttpOperation;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DocumentTemplateFactory;
use U2\DataFixtures\Example\UserFactory;

/**
 * @covers \U2\Entity\DocumentTemplate
 */
class DocumentTemplateGetItemTest extends ApiTestCase
{
    public function test_get_item(): void
    {
        $authorizedUser = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($authorizedUser);

        // Given

        $documentTemplate = DocumentTemplateFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/document-templates/%s', $documentTemplate->getId()),
            ['json' => []],
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@id' => \sprintf('/api/document-templates/%s', $documentTemplate->getId()),
            '@type' => 'DocumentTemplate',
            'id' => $documentTemplate->getId(),
            'name' => $documentTemplate->getName(),
            'description' => $documentTemplate->getDescription(),
        ]);
    }
}
