<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api;

use ApiPlatform\Metadata\HttpOperation;
use ApiPlatform\Symfony\Bundle\Test\Client;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\SystemMessage;
use U2\Entity\User;
use U2\SystemMessage\SystemMessageTypes;

/**
 * @covers \U2\Entity\SystemMessage
 */
class SystemMessageTest extends ApiTestCase
{
    private Client $client;

    private EntityManagerInterface $entityManager;

    private User $adminUser;

    private User $user;

    private SystemMessage $systemMessage;

    public function test_anon_user_can_get_collection(): void
    {
        // When
        $this->client->request(
            HttpOperation::METHOD_GET,
            '/api/system-messages'
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/SystemMessage',
            '@id' => '/api/system-messages',
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 1,
        ]);

        self::assertMatchesResourceCollectionJsonSchema(SystemMessage::class);
    }

    public function test_anon_user_can_get(): void
    {
        $iri = $this->findIriBy(SystemMessage::class, ['content' => SystemMessage::class]);
        \assert(null !== $iri);
        $this->client = self::createClientWithAuth($this->adminUser);

        // When
        $this->client->request(
            HttpOperation::METHOD_GET,
            $iri,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        $displayFrom = $this->systemMessage->getDisplayFrom();
        \assert(null !== $displayFrom);
        $displayTo = $this->systemMessage->getDisplayTo();
        \assert(null !== $displayTo);

        self::assertJsonEquals([
            '@context' => '/api/contexts/SystemMessage',
            '@id' => '/api/system-messages/' . $this->systemMessage->getId(),
            '@type' => 'SystemMessage',
            'id' => $this->systemMessage->getId(),
            'displayFrom' => $displayFrom->format(\DATE_W3C),
            'displayTo' => $displayTo->format(\DATE_W3C),
            'type' => 'INFO',
            'content' => SystemMessage::class,
            'status' => SystemMessage::statusActive,
        ]);
    }

    public function test_create(): void
    {
        $this->client = self::createClientWithAuth($this->adminUser);

        // When
        $this->client->request(
            HttpOperation::METHOD_POST,
            '/api/system-messages',
            [
                'json' => [
                    'displayFrom' => (new \DateTime())->format(\DATE_W3C),
                    'displayTo' => (new \DateTime('+1 years'))->format(\DATE_W3C),
                    'content' => 'New ' . SystemMessage::class,
                    'type' => SystemMessageTypes::TYPE_INFO,
                ],
            ]
        );

        $createdSystemMessage = static::getContainer()->get(EntityManagerInterface::class)->getRepository(SystemMessage::class)->findOneBy(['content' => 'New ' . SystemMessage::class]);

        \assert(null !== $createdSystemMessage);

        // Then
        self::assertResponseStatusCodeSame(201);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        $displayFrom = $createdSystemMessage->getDisplayFrom();
        \assert(null !== $displayFrom);
        $displayTo = $createdSystemMessage->getDisplayTo();
        \assert(null !== $displayTo);
        self::assertJsonEquals([
            '@context' => '/api/contexts/SystemMessage',
            '@id' => $this->findIriBy(SystemMessage::class, ['content' => 'New ' . SystemMessage::class]),
            '@type' => 'SystemMessage',
            'id' => $createdSystemMessage->getId(),
            'displayFrom' => $displayFrom->format(\DATE_W3C),
            'displayTo' => $displayTo->format(\DATE_W3C),
            'type' => 'INFO',
            'status' => SystemMessage::statusActive,
            'content' => 'New ' . SystemMessage::class,
        ]);

        self::assertMatchesResourceItemJsonSchema(SystemMessage::class);
    }

    public function test_update_a_resource(): void
    {
        // Given
        $iri = $this->findIriBy(SystemMessage::class, ['content' => SystemMessage::class]);
        \assert(null !== $iri);
        $this->client = self::createClientWithAuth($this->adminUser);

        // When
        $this->client->request(
            HttpOperation::METHOD_PATCH,
            $iri,
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'content' => 'Updated content',
                ],
            ]
        );

        $this->entityManager->refresh($this->systemMessage);

        $displayFrom = $this->systemMessage->getDisplayFrom();
        \assert(null !== $displayFrom);
        $displayTo = $this->systemMessage->getDisplayTo();
        \assert(null !== $displayTo);

        // Then
        self::assertResponseIsSuccessful();
        self::assertJsonEquals([
            '@context' => '/api/contexts/SystemMessage',
            '@id' => $iri,
            '@type' => 'SystemMessage',
            'id' => $this->systemMessage->getId(),
            'status' => SystemMessage::statusActive,
            'displayFrom' => $displayFrom->format(\DATE_W3C),
            'displayTo' => $displayTo->format(\DATE_W3C),
            'type' => 'INFO',
            'content' => 'Updated content',
        ]);
    }

    public function test_delete_a_resource(): void
    {
        // Given
        $iri = $this->findIriBy(SystemMessage::class, ['content' => SystemMessage::class]);
        \assert(null !== $iri);
        $this->client = self::createClientWithAuth($this->adminUser);

        // When
        $this->client->request(
            HttpOperation::METHOD_DELETE,
            $iri,
        );

        // Then
        self::assertResponseStatusCodeSame(204);
        self::assertNull(
            static::getContainer()->get(EntityManagerInterface::class)->getRepository(SystemMessage::class)->findOneBy(['content' => SystemMessage::class])
        );
    }

    public function test_an_unauthorized_user_should_be_forbidden_to_put(): void
    {
        $iri = $this->findIriBy(SystemMessage::class, ['content' => SystemMessage::class]);
        \assert(null !== $iri);
        $this->client = self::createClientWithAuth($this->user);

        $this->client->request(
            HttpOperation::METHOD_PATCH,
            $iri,
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN, 'An unauthorized user should be forbidden');
    }

    public function test_an_unauthorized_user_should_be_forbidden_to_post(): void
    {
        $this->client = self::createClientWithAuth($this->user);
        $this->client->request(
            HttpOperation::METHOD_POST,
            '/api/system-messages',
            ['json' => []]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN, 'An unauthorized user should be forbidden');
    }

    public function test_an_unauthorized_user_should_be_forbidden_delete(): void
    {
        $iri = $this->findIriBy(SystemMessage::class, ['content' => SystemMessage::class]);
        \assert(null !== $iri);

        $this->client = self::createClientWithAuth($this->user);
        $this->client->request(
            HttpOperation::METHOD_DELETE,
            $iri,
        );

        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN, 'An unauthorized user should be forbidden');
    }

    protected function setUp(): void
    {
        $this->client = self::createClient();
        $admin = UserFactory::getAdmin()->_real();
        $this->adminUser = $admin;
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);

        $this->user = UserFactory::getObject(['username' => 'user']);
        $this->entityManager->persist(
            $this->user
        );

        $this->systemMessage = new SystemMessage();

        $this->systemMessage->setContent(SystemMessage::class);
        $this->systemMessage->setType(SystemMessageTypes::TYPE_INFO);
        $this->systemMessage->setDisplayFrom(new \DateTime());
        $this->systemMessage->setDisplayTo(new \DateTime('+1 years'));

        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->entityManager->persist($this->systemMessage);
        $this->entityManager->flush();
    }
}
