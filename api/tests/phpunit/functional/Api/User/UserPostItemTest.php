<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\CountryFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Repository\UserRepository;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\User
 */
class UserPostItemTest extends ApiTestCase
{
    public function test_admin_user_creates_a_user(): void
    {
        $country = CountryFactory::findOrCreate(['iso3166code' => 'DE']);

        $userRepository = static::getContainer()->get(UserRepository::class);
        $admin = $userRepository->findOneBy(['username' => 'admin']);
        \assert(null !== $admin);
        $client = self::createClientWithAuth($admin);

        // When
        $response = $client->request(
            HttpOperation::METHOD_POST,
            '/api/users',
            [
                'json' => [
                    'contact' => [
                        'nameLast' => 'Meyer',
                        'email' => '<EMAIL>',
                        'nameFirst' => 'New First Name',
                        'telephone' => 'XXX',
                        'address' => [
                            'line1' => 'Alt-Moabit 83a',
                            'line2' => 'Vorderhaus',
                            'line3' => '6 OG',
                            'city' => 'Berlin',
                            'state' => 'Berlin',
                            'postcode' => '10555',
                            'country' => $this->getIriFromResource($country),
                        ],
                        'country' => $this->getIriFromResource($country),
                    ],
                    'username' => 'meyer',
                ],
            ]
        );

        $user = UserFactory::find(['username' => 'meyer']);

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_CREATED);
        self::assertJsonEqualsExcludeGeneratedIri($response, [
            '@context' => '/api/contexts/User',
            '@id' => \sprintf('/api/users/%s', $user->getId()),
            '@type' => 'User',
            'id' => $user->getId(),
            'lastLogin' => null,
            'lastActivity' => null,
            'accountExpires' => null,
            'contact' => [
                '@type' => 'Contact',
                'id' => $user->getContact()?->getId(),
                'title' => null,
                'nameFirst' => 'New First Name',
                'company' => null,
                'nameLast' => 'Meyer',
                'email' => '<EMAIL>',
                'telephone' => 'XXX',
                'mobile' => null,
                'fax' => null,
                'address' => [
                    '@type' => 'Address',
                    'id' => $user->getContact()?->getAddress()?->getId(),
                    'line1' => 'Alt-Moabit 83a',
                    'line2' => 'Vorderhaus',
                    'line3' => '6 OG',
                    'city' => 'Berlin',
                    'state' => 'Berlin',
                    'postcode' => '10555',
                    'country' => '/api/countries/' . $country->getId(),
                ],
                'country' => '/api/countries/' . $country->getId(),
            ],
            'locked' => false,
            'parentUser' => null,
            'passwordExpires' => $user->getPasswordExpires()?->format('Y-m-d'),
            'groups' => [],
            'userRoles' => [
                0 => UserRoles::User->value,
            ],
            'username' => $user->getUsername(),
            'roles' => [
                UserRoles::User->value,
            ],
        ]);
    }

    public function test_unauthorized_user_creates_a_new_user(): void
    {
        $unauthorizedUser = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($unauthorizedUser);

        // When
        $client->request(
            HttpOperation::METHOD_POST,
            '/api/users',
            [
                'json' => [
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }
}
