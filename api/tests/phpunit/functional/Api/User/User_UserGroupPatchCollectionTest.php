<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Entity\UserGroup;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\User
 */
class User_UserGroupPatchCollectionTest extends ApiTestCase
{
    public function test_update_groups_as_an_user_group_admin(): void
    {
        $admin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::UserGroupAdmin->value]])->_real();
        $client = self::createClientWithAuth($admin);

        $user = UserFactory::createOne([
            'groups' => [UserGroupFactory::createOne()],
        ]);

        self::assertCount(1, $user->getGroups());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/users/%s/groups', $user->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groups' => array_map(static fn (UserGroup $userGroup): string => '/api/user-groups/' . $userGroup->getId(), [...$user->getGroups(), UserGroupFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertCount(2, $user->getGroups());

        self::assertJsonContains([
            '@type' => 'User',
            '@id' => \sprintf('/api/users/%s/groups', $user->getId()),
        ]);
    }

    public function test_update_groups_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]])->_real();
        $client = self::createClientWithAuth($notAnAdmin);

        $user = UserFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/users/%s/groups', $user->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'groups' => array_map(static fn (UserGroup $userGroup): string => '/api/user-groups/' . $userGroup->getId(), [...$user->getGroups(), UserGroupFactory::createOne()->_real()]),
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }
}
