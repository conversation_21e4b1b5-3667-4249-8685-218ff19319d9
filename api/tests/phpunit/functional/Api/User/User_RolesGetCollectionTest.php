<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UserFactory;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\User
 */
class User_RolesGetCollectionTest extends ApiTestCase
{
    public function test_get_assigned_roles_as_user_group_admin(): void
    {
        // Given
        $userGroupAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::UserGroupAdmin->value]]);
        $client = self::createClientWithAuth($userGroupAdmin);

        $someUserWithRoles = UserFactory::createOne(['userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);

        self::assertCount(2, $someUserWithRoles->getRoles());

        // When
        $requestUrl = "/api/users/{$someUserWithRoles->getId()}/roles";

        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/Role',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/roles/' . UserRoles::User->value,
                    '@type' => 'Role',
                    'name' => UserRoles::User->value,
                ],
                [
                    '@id' => '/api/roles/' . UserRoles::Admin->value,
                    '@type' => 'Role',
                    'name' => UserRoles::Admin->value,
                ],
            ],
        ]);
    }

    public function test_get_assigned_roles_for_myself(): void
    {
        // Given
        $someUserWithRoles = UserFactory::createOne(['userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($someUserWithRoles);

        self::assertCount(1, $someUserWithRoles->getRoles());

        // When
        $requestUrl = "/api/users/{$someUserWithRoles->getId()}/roles";

        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/Role',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:member' => [
                [
                    '@id' => '/api/roles/' . UserRoles::User->value,
                    '@type' => 'Role',
                    'name' => UserRoles::User->value,
                ],
            ],
        ]);
    }

    public function test_get_assigned_roles_as_non_user_group_admin(): void
    {
        $client = self::createClientWithAuth(UserFactory::createOne());

        // Given
        $someUserWithRoles = UserFactory::createOne(['userRoles' => [UserRoles::User->value, UserRoles::Admin->value]]);

        // When
        $requestUrl = "/api/users/{$someUserWithRoles->getId()}/roles";

        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
