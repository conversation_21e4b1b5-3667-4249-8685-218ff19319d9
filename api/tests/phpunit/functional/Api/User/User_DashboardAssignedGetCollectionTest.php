<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DashboardFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\DataFixtures\Example\UserGroupFactory;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\Dashboard
 */
class User_DashboardAssignedGetCollectionTest extends ApiTestCase
{
    public function test_get_my_assigned_dashboards(): void
    {
        // Given
        $user = UserFactory::createOne();
        $client = self::createClientWithAuth($user);

        // Direct assigned dashboard
        DashboardFactory::createOne([
            'directUsers' => [$user],
            'public' => false,
        ]);

        // Indirect assigned dashboard
        DashboardFactory::createOne([
            'groups' => [UserGroupFactory::createOne(['users' => [$user]])],
        ]);

        // Public dashboard
        DashboardFactory::createOne([
            'directUsers' => [$user],
            'public' => true,
        ]);

        // This one should not be found
        DashboardFactory::createOne(['public' => false]);

        // When
        $requestUrl = '/api/users/' . $user->getId() . '/dashboards';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/Dashboard',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 3,
        ]);
    }

    public function test_get_assigned_dashboards_of_another_user_as_user_group_admin(): void
    {
        // Given
        $user = UserFactory::createOne();
        $currentUser = UserFactory::createOne([
            'userRoles' => [UserRoles::UserGroupAdmin->value],
        ])->_real();
        $client = self::createClientWithAuth($currentUser);

        // Direct assigned dashboard
        DashboardFactory::createOne([
            'directUsers' => [$user],
            'public' => false,
        ]);

        // Indirect assigned dashboard
        DashboardFactory::createOne([
            'groups' => [UserGroupFactory::createOne(['users' => [$user]])],
        ]);

        // Public dashboard
        DashboardFactory::createOne([
            'directUsers' => [$user],
            'public' => true,
        ]);

        // This one should not be found
        DashboardFactory::createOne(['public' => false]);

        // When
        $requestUrl = '/api/users/' . $user->getId() . '/dashboards';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_OK);
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');

        self::assertJsonContains([
            '@context' => '/api/contexts/Dashboard',
            '@id' => $requestUrl,
            '@type' => 'hydra:Collection',
            'hydra:totalItems' => 3,
        ]);
    }

    public function test_get_assigned_dashboards_of_another_user_as_non_user_group_admin(): void
    {
        // Given
        $user = UserFactory::createOne();
        $currentUser = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($currentUser);

        // When
        $requestUrl = '/api/users/' . $user->getId() . '/dashboards';
        $client->request(
            HttpOperation::METHOD_GET,
            $requestUrl,
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
