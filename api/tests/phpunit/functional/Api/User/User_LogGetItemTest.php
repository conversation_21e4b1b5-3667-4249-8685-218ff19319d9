<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\User;

use ApiPlatform\Metadata\HttpOperation;
use ApiPlatform\Symfony\Bundle\Test\Client;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\AuditLog\Change\Change;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\User;
use U2\Entity\UserLog;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\UserLog
 */
class User_LogGetItemTest extends ApiTestCase
{
    protected User $adminUser;

    protected Client $client;

    protected EntityManagerInterface $entityManager;

    protected User $user;

    public function test_user_can_get_his_own_logs(): void
    {
        // Given
        $this->entityManager->persist(
            $auditLog = new UserLog(
                [
                    new Change('test', 'Old Value', 'New Value'),
                ],
                $this->user,
                $this->user
            )
        );

        $this->entityManager->flush();

        // When
        $this->client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/users/%s/logs/%s', $auditLog->getAuditedEntity()->getId(), $auditLog->getId())
        );

        // Then
        $auditUser = $auditLog->getUser();
        \assert($auditUser instanceof User);

        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEquals([
            '@context' => '/api/contexts/UserLog',
            '@id' => \sprintf('/api/users/%s/logs/%s', $auditLog->getAuditedEntity()->getId(), $auditLog->getId()),
            '@type' => 'UserLog',
            'id' => $auditLog->getId(),
            'auditedEntity' => \sprintf('/api/users/%s', $auditLog->getAuditedEntity()->getId()),
            'timestamp' => $auditLog->getTimestamp()->format(\DATE_W3C),
            'user' => \sprintf('/api/users/%s', $auditUser->getId()),
            'username' => $auditUser->getUsername(),
            'changes' => [
                [
                    'type' => 'change',
                    'field' => 'test',
                    'from' => 'Old Value',
                    'to' => 'New Value',
                ],
            ],
        ]);

        /*
         * TODO: Fix this assertion
         * The `changes` is an array of JSON data-types. Technically they are strings but are de-serialized as an object.
         * For some reason this now breaks after upgrading to symfony 5.3.
         */
        // self::assertMatchesResourceItemJsonSchema(UserLog::class);
    }

    public function test_user_group_admin_can_get_any_users_logs(): void
    {
        // Given
        $user = UserFactory::createOne(['username' => 'user role admin', 'userRoles' => [UserRoles::UserGroupAdmin->value]])->_real();
        $this->client = self::createClientWithAuth($user);

        $this->entityManager->persist(
            $auditLog = new UserLog(
                [
                    new Change('test', 'Old Value', 'New Value'),
                ],
                $user,
                $user
            )
        );

        $this->entityManager->flush();

        // When
        $this->client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/users/%s/logs/%s', $auditLog->getAuditedEntity()->getId(), $auditLog->getId()),
        );

        // Then
        $auditUser = $auditLog->getUser();
        \assert($auditUser instanceof User);

        self::assertResponseIsSuccessful();
        self::assertResponseHeaderSame('content-type', 'application/ld+json; charset=utf-8');
        self::assertJsonEquals([
            '@context' => '/api/contexts/UserLog',
            '@id' => \sprintf('/api/users/%s/logs/%s', $auditLog->getAuditedEntity()->getId(), $auditLog->getId()),
            '@type' => 'UserLog',
            'id' => $auditLog->getId(),
            'auditedEntity' => \sprintf('/api/users/%s', $auditLog->getAuditedEntity()->getId()),
            'timestamp' => $auditLog->getTimestamp()->format(\DATE_W3C),
            'user' => \sprintf('/api/users/%s', $auditUser->getId()),
            'username' => $auditUser->getUsername(),
            'changes' => [
                [
                    'type' => 'change',
                    'field' => 'test',
                    'from' => 'Old Value',
                    'to' => 'New Value',
                ],
            ],
        ]);
        self::assertMatchesResourceItemJsonSchema(UserLog::class);
    }

    public function test_get_logs_denies_access_if_requested_logs_are_not_from_current_user(): void
    {
        // Given
        $anyUser = UserFactory::createOne()->_real();

        $this->entityManager->persist(
            $auditLog = new UserLog(
                [
                    new Change('test', 'Old Value', 'New Value'),
                ],
                $anyUser,
                $anyUser
            )
        );

        $this->entityManager->flush();

        // When
        $this->client->request(
            HttpOperation::METHOD_GET,
            \sprintf('/api/users/%s/logs/%s', $auditLog->getAuditedEntity()->getId(), $auditLog->getId())
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    protected function setUp(): void
    {
        $this->user = UserFactory::createOne(['username' => 'user'])->_real();
        $this->client = self::createClientWithAuth($this->user);
        $this->entityManager = static::getContainer()->get(EntityManagerInterface::class);
        $this->adminUser = UserFactory::getAdmin()->_real();
    }
}
