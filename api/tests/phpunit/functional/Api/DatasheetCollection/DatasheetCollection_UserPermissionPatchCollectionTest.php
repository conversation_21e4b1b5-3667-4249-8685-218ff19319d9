<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\DatasheetCollection;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\DatasheetCollectionFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\User;

/**
 * @covers \U2\Entity\DatasheetCollection
 */
class DatasheetCollection_UserPermissionPatchCollectionTest extends ApiTestCase
{
    public function test_update_user_permissions(): void
    {
        $layoutCollection = DatasheetCollectionFactory::createOne();
        self::assertCount(0, $layoutCollection->getPermissions()->getUserPermissions());

        $user = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($user);

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/layout-collections/%s/user-permissions', $layoutCollection->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $user->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                    ],
                ],
            ]
        );

        self::assertResponseStatusCodeSame(Response::HTTP_OK);

        // Then
        self::assertCount(1, $layoutCollection->getPermissions()->getUserPermissions());
    }

    public function test_update_user_permissions_as_unauthorized_user(): void
    {
        $someOtherUser = UserFactory::createOne();
        $unauthorizedUser = UserFactory::createOne()->_real();
        $layoutCollection = DatasheetCollectionFactory::createOne();
        $client = self::createClientWithAuth($unauthorizedUser);

        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/layout-collections/%s/user-permissions', $layoutCollection->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'userPermissions' => [
                        [
                            'user' => $this->findIriBy(User::class, ['id' => $someOtherUser->getId()]),
                            'mask' => MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
                        ],
                    ],
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
