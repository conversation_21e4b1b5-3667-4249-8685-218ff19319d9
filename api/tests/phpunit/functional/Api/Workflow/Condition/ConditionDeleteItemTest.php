<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Workflow\Condition;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\CurrentUserHasRoleConditionFactory;
use U2\DataFixtures\Example\UserFactory;

/**
 * @covers \U2\Entity\Workflow\Condition\Condition
 */
class ConditionDeleteItemTest extends ApiTestCase
{
    public function test_delete_item(): void
    {
        $authorizedUser = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($authorizedUser);

        $condition = CurrentUserHasRoleConditionFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_DELETE,
            "/api/transitions/{$condition->getTransition()->getId()}/conditions/{$condition->getId()}"
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_delete_item_as_unauthorized(): void
    {
        $user = UserFactory::createOne()->_real();
        $client = self::createClientWithAuth($user);

        $condition = CurrentUserHasRoleConditionFactory::createOne();

        // When
        $client->request(
            HttpOperation::METHOD_DELETE,
            "/api/transitions/{$condition->getTransition()->getId()}/conditions/{$condition->getId()}",
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
