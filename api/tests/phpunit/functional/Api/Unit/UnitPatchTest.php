<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Unit;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bridge\PhpUnit\ClockMock;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\CountryFactory;
use U2\DataFixtures\Example\CurrencyFactory;
use U2\DataFixtures\Example\UnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Entity\Unit;
use U2\Repository\UnitRepository;

/**
 * @covers \U2\Entity\Unit
 */
class UnitPatchTest extends ApiTestCase
{
    public function test_update_a_resource(): void
    {
        $admin = UserFactory::getAdmin()->_real();
        $client = self::createClientWithAuth($admin);
        $country = CountryFactory::findOrCreate(['iso3166code' => 'DE']);
        $unit = UnitFactory::createOne()->_real();
        $currency = CurrencyFactory::getApplicationCurrency();

        // When
        $response = $client->request(
            HttpOperation::METHOD_PATCH,
            '/api/units/' . $unit->getId(),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'verified' => true,
                    'country' => '/api/countries/' . $country->getId(),
                    'currency' => '/api/currencies/' . $currency->getId(),
                    'refId' => 'ABCD',
                    'name' => 'Updated Unit',
                    'description' => 'Updated Description',
                ],
            ]
        );

        /** @var array{id: int, "u2:extra": array{"@id": string}} $responseContent */
        $responseContent = json_decode($response->getContent(), true);

        $updatedUnit = static::getContainer()->get(UnitRepository::class)->findOneBy(['id' => $unit->getId()]);
        \assert($updatedUnit instanceof Unit);

        // Then
        self::assertResponseIsSuccessful();
        self::assertJsonEquals([
            '@context' => '/api/contexts/Unit',
            '@id' => '/api/units/' . $updatedUnit->getId(),
            '@type' => 'Unit',
            'id' => $updatedUnit->getId(),
            'refId' => 'ABCD',
            'name' => 'Updated Unit',
            'description' => 'Updated Description',
            'verified' => true,
            'country' => '/api/countries/' . $country->getId(),
            'currency' => '/api/currencies/' . $currency->getId(),
            'taxNumbers' => [],
            'createdBy' => null,
            'updatedBy' => '/api/users/' . $admin->getId(),
            'createdAt' => $updatedUnit->getCreatedAt()?->format(\DATE_W3C),
            'updatedAt' => $updatedUnit->getUpdatedAt()?->format(\DATE_W3C),
            'attachmentCount' => 0,
            'u2:extra' => [
                '@id' => $responseContent['u2:extra']['@id'],
                '@type' => 'UnitExtra',
                'canAddAttachment' => true,
            ],
        ]);
    }

    public function test_update_a_resource_as_unauthorized(): void
    {
        $user = UserFactory::createOne();
        $client = self::createClientWithAuth($user);
        $country = CountryFactory::findOrCreate(['iso3166code' => 'DE']);
        $unit = UnitFactory::createOne()->_real();
        $currency = CurrencyFactory::getApplicationCurrency();

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            '/api/units/' . $unit->getId(),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'verified' => true,
                    'country' => '/api/countries/' . $country->getId(),
                    'currency' => '/api/currencies/' . $currency->getId(),
                    'refId' => 'ABCD',
                    'name' => 'Updated Unit',
                    'description' => 'Updated Description',
                ],
            ]
        );

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }

    protected function tearDown(): void
    {
        ClockMock::withClockMock(false);
        parent::tearDown();
    }
}
