<?php

declare(strict_types=1);
namespace Tests\Functional\U2\Api\Unit;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\Response;
use Tests\U2\ApiTestCase;
use U2\DataFixtures\Example\UnitFactory;
use U2\DataFixtures\Example\UserFactory;
use U2\Security\UserRoles;

/**
 * @covers \U2\Entity\Unit
 */
class Unit_UserPatchTest extends ApiTestCase
{
    public function test_update_direct_users(): void
    {
        $unit = UnitFactory::createOne();
        $admin = UserFactory::createOne(['units' => [$unit], 'username' => 'user group admin', 'userRoles' => [UserRoles::User->value, UserRoles::Admin->value, UserRoles::UserGroupAdmin->value]]);
        $client = self::createClientWithAuth($admin);

        $toAssignUser = UserFactory::createOne();
        self::assertCount(0, $toAssignUser->getUnits());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/units/%s/direct-users', $unit->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => ['/api/users/' . $toAssignUser->getId()],
                ],
            ]
        );

        self::assertCount(1, $toAssignUser->getUnits());

        // Then
        self::assertResponseIsSuccessful();
        self::assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }

    public function test_update_direct_users_as_an_unauthorized_user(): void
    {
        $notAnAdmin = UserFactory::createOne(['username' => 'user group admin', 'userRoles' => [UserRoles::User->value]]);
        $client = self::createClientWithAuth($notAnAdmin);

        $unit = UnitFactory::createOne();

        self::assertCount(0, $notAnAdmin->getUnits());

        // When
        $client->request(
            HttpOperation::METHOD_PATCH,
            \sprintf('/api/units/%s/direct-users', $unit->getId()),
            [
                'headers' => [
                    'Content-Type' => 'application/merge-patch+json',
                ],
                'json' => [
                    'directUsers' => ['/api/users/' . $notAnAdmin->getId()],
                ],
            ]
        );
        self::assertCount(0, $notAnAdmin->getUnits());

        // Then
        self::assertResponseStatusCodeSame(Response::HTTP_FORBIDDEN);
    }
}
