<?php

declare(strict_types=1);
namespace Tests\Migration\U2;

use U2\Migrations\Version20250516104928;
use U2\Migrations\Version20250519101914;

class Version20250519101914Test extends MigrationTestCase
{
    public function test_up(): void
    {
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250516104928::class]);

        $entityManager = self::getEntityManager();
        $connection = $entityManager->getConnection();

        $connection->executeQuery(<<<SQL
                insert into authorization (id, name, item, rights) values (1337, "test", "CM_CONTRACT", "[\\"READ\\", \\"WRITE\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1338, "test", "CM_CONTRACT", "[\\"WRITE\\", \\"READ\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1339, "test", "CM_CONTRACT", "[\\"WRITE\\", \\"DELETE\\", \\"READ\\"]");
                insert into authorization (id, name, item, rights) values (1340, "test", "CM_CONTRACT", "[\\"WRITE\\", \\"READ\\"]");
                insert into authorization (id, name, item, rights) values (1341, "test", "CM_CONTRACT", "[\\"WRITE\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1342, "test", "CM_CONTRACT", "[\\"WRITE\\"]");
                insert into authorization (id, name, item, rights) values (1343, "test", "CM_CONTRACT", "[\\"READ\\", \\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1344, "test", "CM_CONTRACT", "[\\"DELETE\\"]");
                insert into authorization (id, name, item, rights) values (1345, "test", "CM_CONTRACT", "[\\"READ\\"]");
            SQL
        );

        $authorization = $connection->executeQuery('select id, rights from authorization where id in (1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345);')->fetchAllAssociative();
        self::assertSame([
            [
                'id' => 1337,
                'rights' => '["READ", "WRITE", "DELETE"]',
            ],
            [
                'id' => 1338,
                'rights' => '["WRITE", "READ", "DELETE"]',
            ],
            [
                'id' => 1339,
                'rights' => '["WRITE", "DELETE", "READ"]',
            ],
            [
                'id' => 1340,
                'rights' => '["WRITE", "READ"]',
            ],
            [
                'id' => 1341,
                'rights' => '["WRITE", "DELETE"]',
            ],
            [
                'id' => 1342,
                'rights' => '["WRITE"]',
            ],
            [
                'id' => 1343,
                'rights' => '["READ", "DELETE"]',
            ],
            [
                'id' => 1344,
                'rights' => '["DELETE"]',
            ],
            [
                'id' => 1345,
                'rights' => '["READ"]',
            ],
        ], $authorization);

        // When
        $this->runCommand('doctrine:migrations:migrate', ['version' => Version20250519101914::class]);

        // Then - Check authorization
        $authorization = $connection->executeQuery('select id, rights from authorization where id in (1337, 1338, 1339, 1340, 1341, 1342, 1343, 1344, 1345);')->fetchAllAssociative();
        self::assertSame([
            [
                'id' => 1337,
                'rights' => '["READ", "WRITE", "DELETE", "CREATE"]',
            ],
            [
                'id' => 1338,
                'rights' => '["WRITE", "READ", "DELETE", "CREATE"]',
            ],
            [
                'id' => 1339,
                'rights' => '["WRITE", "DELETE", "READ", "CREATE"]',
            ],
            [
                'id' => 1340,
                'rights' => '["WRITE", "READ", "CREATE"]',
            ],
            [
                'id' => 1341,
                'rights' => '["WRITE", "DELETE", "CREATE"]',
            ],
            [
                'id' => 1342,
                'rights' => '["WRITE", "CREATE"]',
            ],
            [
                'id' => 1343,
                'rights' => '["READ", "DELETE"]',
            ],
            [
                'id' => 1344,
                'rights' => '["DELETE"]',
            ],
            [
                'id' => 1345,
                'rights' => '["READ"]',
            ],
        ], $authorization);
    }
}
