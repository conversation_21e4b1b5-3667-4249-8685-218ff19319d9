<?php

declare(strict_types=1);
namespace Tests\U2;

use PHPUnit\Framework\TestCase;
use Zenstruck\Foundry\Object\Instantiator;
use Zenstruck\Foundry\Test\Factories;
use Zenstruck\Foundry\Test\UnitTestConfig;

abstract class UnitTestCase extends TestCase
{
    use Factories;

    public static function setUpBeforeClass(): void
    {
        $instantiator = Instantiator::withConstructor()->allowExtra();
        UnitTestConfig::configure($instantiator);
    }
}
