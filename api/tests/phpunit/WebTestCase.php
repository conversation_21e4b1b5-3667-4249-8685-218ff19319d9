<?php

declare(strict_types=1);
namespace Tests\U2;

use Symfony\Bundle\FrameworkBundle\KernelBrowser;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase as BaseWebTestCase;
use Symfony\Component\HttpKernel\KernelInterface;

abstract class WebTestCase extends BaseWebTestCase
{
    use ServiceContainerHelpersTrait;

    protected static string $environment = 'test';

    /**
     * @param array<mixed> $options
     */
    protected static function bootKernel(array $options = []): KernelInterface
    {
        $options['environment'] = static::$environment;

        return parent::bootKernel($options);
    }

    protected static function createClient(array $kernelOptions = [], array $defaultOptions = []): KernelBrowser
    {
        $client = parent::createClient($kernelOptions, $defaultOptions);

        $client->setServerParameter('HTTP_HOST', 'test.u2.localhost');

        return $client;
    }
}
