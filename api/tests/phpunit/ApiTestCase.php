<?php

declare(strict_types=1);
namespace Tests\U2;

use ApiPlatform\Metadata\IriConverterInterface;
use ApiPlatform\Symfony\Bundle\Test\ApiTestCase as BaseApiTestCase;
use ApiPlatform\Symfony\Bundle\Test\Client;
use Hautelook\AliceBundle\PhpUnit\RefreshDatabaseTrait;
use Lexik\Bundle\JWTAuthenticationBundle\Services\JWTTokenManagerInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;
use U2\DataFixtures\Example\ApiKeyFactory;
use U2\Entity\User;
use U2\Security\Authentication\Api\ApiKeySecret;
use Zenstruck\Foundry\Persistence\Proxy;

use function Zenstruck\Foundry\Persistence\unproxy;

use Zenstruck\Foundry\Test\Factories;

abstract class ApiTestCase extends BaseApiTestCase
{
    use Factories;
    use RefreshDatabaseTrait;
    use ServiceContainerHelpersTrait;
    protected static ?bool $alwaysBootKernel = false;

    /**
     * @param User $user the user for which to generate a jwt
     */
    protected static function generateJwt(User $user): string
    {
        return static::getService(JWTTokenManagerInterface::class)->create($user);
    }

    /**
     * @param array<string, mixed> $payload
     *
     * @return array<string, mixed>
     */
    public function authorizePayloadWithApiKey(User $user, array $payload = []): array
    {
        $secret = ApiKeySecret::generate();
        $apiKey = ApiKeyFactory::createOne([
            'createdBy' => $user,
            'encryptedApiKey' => $secret->getEncrypted(),
        ]);

        /** @var array<string,string> $headers */
        $headers = \in_array('headers', $payload, true) ? $payload['headers'] : [];

        return array_merge([
            ...$payload,
            'headers' => [
                ...$headers,
                'Authorization' => 'ApiKey u2_' . $apiKey->getId()->toBase58() . '_' . $secret->getRaw(),
            ],
        ]);
    }

    /**
     * Create a client that is authorized for the given user.
     *
     * @param User|Proxy<User> $user the user to log in as
     */
    protected static function createClientWithAuth(User|Proxy $user): Client
    {
        /** @var User $user */
        $user = unproxy($user);

        return static::createClient(
            defaultOptions: ['auth_bearer' => static::generateJwt($user)]
        );
    }

    protected static function createClient(array $kernelOptions = [], array $defaultOptions = []): Client
    {
        $client = parent::createClient($kernelOptions, array_merge_recursive(
            [
                'base_uri' => 'http://test.u2.localhost/',
                'headers' => ['HOST' => 'test.u2.localhost'],
            ],
            $defaultOptions,
        ));

        self::setTenant('test');

        return $client;
    }

    protected function getIriFromResource(object $resource): ?string
    {
        return static::getService(IriConverterInterface::class)->getIriFromResource($resource);
    }

    /**
     * @param array<mixed>|string $json
     *
     * @throws \JsonException
     */
    public static function assertJsonEqualsExcludeGeneratedIri(ResponseInterface $response, array|string $json, string $message = ''): void
    {
        if (\is_string($json)) {
            $json = json_decode($json, true, 512, \JSON_THROW_ON_ERROR);
        }
        if (!\is_array($json)) {
            throw new \InvalidArgumentException('$json must be array or string (JSON array or JSON object)');
        }

        $responseData = $response->toArray(throw: false);

        // remove all '@id' entries from each level of the array which contains `well-known`
        $cleanData = self::removeWellKnownId($responseData);

        static::assertEqualsCanonicalizing($json, $cleanData, $message);
    }

    /**
     * @param array<mixed> $array
     *
     * @return array<mixed>
     */
    private static function removeWellKnownId(array $array): array
    {
        foreach ($array as $key => $value) {
            if (\is_string($value) && '@id' === $key && str_contains($value, 'well-known')) {
                unset($array[$key]);
            } elseif (\is_array($value)) {
                $array[$key] = self::removeWellKnownId($value);
            }
        }

        return $array;
    }
}
