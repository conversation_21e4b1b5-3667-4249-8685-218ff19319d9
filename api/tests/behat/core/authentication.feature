@clear-database
Feature: Authentication
  In order to access secure areas of u2
  As a User
  I need to be authenticated

  <PERSON><PERSON><PERSON>: A user tries to visit a secure page without logging in
    Given I am not logged in
    When I go to "/dashboard"
    Then I should be on "/login"

  Scenario: A user logs in using the login form
    Given there is a user named <PERSON><PERSON><PERSON>
    When I go to "/login"
    And I fill in the "login" form with:
      | username | testUser |
      | password | testUser |
    And I click the "Login" button
    Then I should see "Dashboard"

  Scenario: A user logs in with an invalid password
    Given there is a user named test<PERSON><PERSON>
    When I go to "/login"
    And I fill in the "login" form with:
      | username | testUser         |
      | password | invalid_password |
    And I click the "Login" button
    Then I should see an error message

  Sc<PERSON>rio: An unregistered user tries to log in
    Given I am on "/login"
    When I fill in the "login" form with:
      | username | unregistered_user     |
      | password | unregistered_password |
    And I click the "Login" button
    Then I should be on "/login"
    And I should see an error message
