@clear-database
Feature: Currency Navigation
  In order to manage Currency
  As an Admin
  I should be able to navigate through the Currency pages

  Background:
    Given the following Currency:
      | Name         | Iso 4217 Code |
      | Polish Zloty | PLN           |
    And I am logged in as an administrator

  Scenario: A User with the required authorisation navigates through the menu to the list page
    Given I am on the homepage
    When I click "Administration" in the menu under "Tools"
    And I click "Currencies"
    Then I should be on "/configuration/currencies"

  Scenario: A User with the required authorisation accessing the new page over the list page
    Given I am on "/configuration/currencies"
    When I click the "New" button in "Page Controls"
    Then I should be on "/configuration/currencies/new"

  Scenario: A User with the required authorisation accessing the edit form
    Given I am on "/configuration/currencies"
    Then I should see "Polish Zloty"
    When I click "Edit" on the table row for "Polish Zloty"
    Then I should be on "/configuration/currencies/1"
