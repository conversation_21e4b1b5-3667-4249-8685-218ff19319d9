@clear-database
Feature: Branch Navigation
  In order to manage branches
  I should be able to navigate through the branch pages

  Background:
    Given I am logged in as an administrator

  <PERSON><PERSON><PERSON>: An Admin navigates through the menu to the list page
    Given I am on the homepage
    When I click "Administration" in the menu under "Tools"
    And I click "Branches"
    Then I should be on "/configuration/branch"

  Scenario: An Admin accessing the new page over the list page
    Given I am on "/configuration/branch"
    When I click the "New" button in "Page Controls"
    Then I should be on "/configuration/branches/new"
