@clear-database
Feature: User Assignment
  As a task user
  I should be able to assign a user to the task

  Background:
    Given there is a user named testuser
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Other Deadline Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tcm_other_deadline | Other Deadline Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong                    | NationalityShort | NationalityLong |
      | DE          | Germany   | Federal Republic of Germany | German           | German          |
    And the following Deadline Type:
      | Name            |
      | Deadline Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Germany |
    And the following TCM Other Deadline:
      | Unit   | Description      | Tax Year | Deadline Type   |
      | RefId1 | Other Deadline 1 | 2014     | Deadline Type 1 |
    And the following Authorization:
      | Name                              | Item               | Rights       |
      | TCM Other Deadlines UPDATE Access | TCM_OTHER_DEADLINE | READ, UPDATE |
    And I have the authorization "TCM Other Deadlines UPDATE Access"
    And I am logged in

  Scenario: Assigning a user to a task
    Given user testuser has the authorization "TCM Other Deadlines UPDATE Access"
    And I am assigned to unit "RefId1"
    And user testuser is assigned to unit "RefId1"
    When I go to "/tcm/other-deadline/1/edit"
    And I click the button with locator "Assign" in "Entity Information Section"
    Then I should see "Assign User"
    When I fill in the "Assign to User" form with:
      | Assignee | testuser |
    And I click the "Assign" button in the dialog
    Then I should be on "/tcm/other-deadline/1/edit"
    And I should see a success message
