@clear-database @clear-formula-cache
Feature: Set default item values action
  In order to set the default item values
  Layouts should recalculate the default values when transitioning with the action


  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name                   | Origin status | Destination Status |
      | Transition with action | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions            |
      | Layout Monitor Workflow | in progress    | Transition with action |
    And the following SetDefaultItemValuesAction:
      | Transition             |
      | Transition with action |
    And the following Workflow Binding:
      | Binding Id  | Workflow                |
      | unit_period | Layout Monitor Workflow |
    And the following Currency:
      | Name      | Iso 4217 Code |
      | Euro      | EUR           |
      | US Dollar | USD           |
    And the following Unit:
      | Id | Ref Id      | Name | Currency |
      | 1  | Unit Ref ID | Unit | USD      |
    And the following System Setting:
      | Id                   | Value |
      | application_currency | EUR   |
    And the following Period:
      | Id | Name            | Previous Period |
      | 1  | Previous Period |                 |
      | 2  | Current Period  | Previous Period |
    And the following Authorization:
      | Name             | Item        | Rights |
      | Unit Period Read | UNIT_PERIOD | READ   |
    And the following Unit Period:
      | Unit        | Period         | Status |
      | Unit Ref ID | Current Period | open   |
    And I have the authorization "Unit Period Read"
    And I am assigned to unit "Unit Ref ID"
    And I am logged in

  Scenario: Previous period values are initialized when browsing a layout for first time
    Given the following Item:
      | Ref Id                            | Type    | Editable | Exchange Method |
      | item_with_no_default              | Percent | True     | Current         |
      | item_with_previous_period_default | Percent | True     | Current         |
      | item_with_default_dependency      | Percent | False    | Current         |
      | item_with_static_default          | Percent | True     | Current         |
      | item_with_a_formula               | Percent | False    | Current         |
    And the following Percent Item Unit Value:
      | Period          | Unit        | Item                              | Value |
      | Previous Period | Unit Ref ID | item_with_no_default              | 0.1   |
      | Current Period  | Unit Ref ID | item_with_previous_period_default | 0.2   |
      | Current Period  | Unit Ref ID | item_with_default_dependency      | 0.3   |
      | Current Period  | Unit Ref ID | item_with_static_default          | 0.4   |
      | Current Period  | Unit Ref ID | item_with_a_formula               | 0     |
    And the following Field:
      | Name                                | Item                              | Disabled |
      | item_with_previous_period_default_1 | item_with_previous_period_default | true     |
      | item_with_default_dependency_1      | item_with_default_dependency      | true     |
      | item_with_static_default_1          | item_with_static_default          | false    |
      | item_with_a_formula_1               | item_with_a_formula               | false    |
    And the following Datasheet:
      | Id | Group | Fields                                                                                                                 | Name |
      | 1  | Group | item_with_previous_period_default_1, item_with_a_formula_1, item_with_default_dependency_1, item_with_static_default_1 | Test |
    And the following Datasheet Collection:
      | Id                         | Name  | Public |
      | 11H678XXXN6JAJQCZX01TZYVKX | Test1 | True   |
    And the following Datasheet Collection Entry:
      | Layout Collection | Layout | Position |
      | Test1             | Test   | 1        |
    And the following Item Formula:
      | Item                              | Formula String                                                 |
      | item_with_static_default          | 0.5                                                            |
      | item_with_previous_period_default | {Pitem_with_no_default}                                        |
      | item_with_default_dependency      | {item_with_previous_period_default}+{item_with_static_default} |
      | item_with_a_formula               | 1+2                                                            |
    And I have view permission to DTM Layout "Test"
    When I go to "/datasheets/collections/11H678XXXN6JAJQCZX01TZYVKX/sheets/1?period=2&unit=1&layout=1"
    Then the "Unit View" form field "Item_with_previous_period_default_1 > Value" should be "20.0000"
    And the "Unit View" form field "Item_with_default_dependency_1 > Value" should be "30.0000"
    And the "Unit View" form field "Item_with_static_default_1 > Value" should be "40.0000"
    And the "Unit View" form field "Item_with_a_formula_1 > Value" should be "0.0000"
    When I click the "open" button
    And I click the "Transition with action" button
    And I click the "Change status to IN PROGRESS" button
    Then the "Unit View" form field "Item_with_previous_period_default_1 > Value" should be "10.0000"
    And the "Unit View" form field "Item_with_default_dependency_1 > Value" should be "60.0000"
    And the "Unit View" form field "Item_with_static_default_1 > Value" should be "50.0000"
    And the "Unit View" form field "Item_with_a_formula_1 > Value" should be "300.0000"
