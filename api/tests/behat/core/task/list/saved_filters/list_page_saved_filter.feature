@clear-database
Feature: Saved filters on list pages
  As a user
  I should be able to perform create and edit saved filters from a UQL list page

  Background:
    Given the following Period:
      | Name        |
      | Period 2012 |
    And the following Authorization:
      | Name                        | Item            | Rights                         |
      | TPM Transaction Full Access | TPM_TRANSACTION | READ, UPDATE, DELETE, TRANSFER |
    And I have the authorization "TPM Transaction Full Access"
    And I am logged in

  Scenario: A user creates and save a new filter
    Given I am on "/tpm/transaction?q=Period = 'Period 2012'"
    And I click the "save" button in "Page Controls"
    Then I should see "New Saved Filter"
    When I fill in the "saved_filter" form field "name" with "Test Filter"
    And I click the "Save" button in the dialog
    Then I should be on "/tpm/transaction?f=1"
    And I should see a success message

  Scenario: A User shares a saved filter from the list page
    Given I am on "/tpm/transaction?q="
    When I click the "Share" button in "Page Controls"
    Then I should see "Share Filter"
