@clear-database
Feature: Unit Hierarchy Authorization
  As a user or admin without authorization to manage Units
  I should have no access to any feature of Unit Hierarchies

  Background:
    Given the following Currency:
      | Iso4217Code |
      | TEU         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
    And the following Unit Hierarchy:
      | Name             | Description                  |
      | Unit Hierarchy 1 | Unit Hierarchy description 1 |
    And the following Unit Hierarchy Definition:
      | Unit Hierarchy   | Unit         | Parent Unit |
      | Unit Hierarchy 1 | Legal Unit 1 |             |
    And I am logged in

  Scenario: A user without admin rights tries to edit a unit hierarchy
    When I go to "/units/hierarchies/1"
    Then I should see "403 Access Denied"

  Scenario: A user without admin rights tries to list the unit Hierarchies
    When I go to "/units/hierarchies"
    Then I should see "403 Access Denied"

  Scenario: A user without admin rights tries to create a unit hierarchy
    When I go to "/units/hierarchies/new"
    Then I should see "403 Access Denied"

  Scenario: A user without admin rights tries to delete a unit hierarchy
    When I go to "/units/hierarchies/1"
    Then I should see "403 Access Denied"
