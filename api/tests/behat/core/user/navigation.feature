@clear-database
Feature: User Navigation
  In order to manage users
  As a user with ROLE_USER_GROUP_ADMIN
  I should be able to navigate through the user pages

  Background:
    Given I am logged in as a user group administrator

  <PERSON><PERSON><PERSON>: A user group admin navigates through the menu to the list page
    Given I am on the homepage
    When I click "Users" in the menu under "Tools"
    Then I should be on "/users"

  Scenario: A user group admin accessing the new page over the list page
    Given I am on "/users"
    When I click the "New" button in "Page Controls"
    Then I should be on "/users/new"

  Scenario: A user group admin the edit form over the list page
    Given the following User:
      | username |
      | testUser |
    And I am on "/users"
    When I click "Edit" on the table row for "testUser"
    Then I should be on "/users/2"
