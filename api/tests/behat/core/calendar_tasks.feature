@clear-database
Feature: Calendar
  In order to manage my tasks in a prompt way
  As a user
  I need to see them on my calendar

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | Other Deadline Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tcm_other_deadline | Other Deadline Workflow |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong          | NationalityShort | NationalityLong |
      | YE          | Yemen     | Republic of Yemen | Yemeni           | Yemeni          |
    And the following Deadline Type:
      | Name            |
      | Deadline Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | EUR      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Yemen   |
    And the following TCM Other Deadline:
      | Id | Unit   | Tax Year | Deadline Type   | Name          |
      | 1  | RefId1 | 2014     | Deadline Type 1 | My Deadline 1 |
      | 2  | RefId2 | 2014     | Deadline Type 1 |               |
    And "TCM Other Deadline" where "id" is "1" has Due Date today
    And "TCM Other Deadline" where "id" is "2" has Due Date today
    And I am logged in

  Scenario: A user with permissions can see Other Deadlines
    Given the following Authorization:
      | Name                       | Item               | Rights       |
      | TCM Other Deadlines Access | TCM_OTHER_DEADLINE | READ, UPDATE |
    And I have the authorization "TCM Other Deadlines Access"
    And I am assigned to unit "RefId1"
    And I am assigned to "TCM Other Deadline" where "id" is "1"
    And I am assigned to unit "RefId2"
    And I am watching "TCM Other Deadline" where "id" is "2"
    When I go to "/me/calendar"
    Then I should see "My Deadline 1" on the calendar entry for today
    And I should see "#2 - Deadline Type 1: RefId2, 2014" on the calendar entry for today
    When I click the "New" button in "Page Controls"
    Then I should see "Other Deadline"

  Scenario: A user without permissions cannot see Other Deadlines
    Given I am assigned to unit "RefId1"
    Given I am assigned to unit "RefId2"
    And I am assigned to "TCM Other Deadline" where "id" is "1"
    And I am watching "TCM Other Deadline" where "id" is "2"
    When I go to "/me/calendar"
    Then I should not see "My Deadline 1" on the calendar entry for today
    And I should not see "#2 - Deadline Type 1: RefId2, 2014" on the calendar entry for today
    And the "New" button in "Page Controls" should be disabled
