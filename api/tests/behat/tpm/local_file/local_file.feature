@clear-database
Feature: Local Files
  In order to manage Local Files
  As a user allowed to the TPM Local Files
  I should be able to perform create, read, update and delete actions on Local File records

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions             |
      | TPM Local File Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id     | Workflow                |
      | tpm_local_file | TPM Local File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Country:
      | Iso3166Code | NameShort | NameLong           | NationalityShort | NationalityLong |
      | ZM          | Zambia    | Republic of Zambia | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen  | Yemeni           | Yemeni          |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Local File:
      | Name         | Period      | Unit Hierarchy   | Country            |
      | Local File 1 | Period 2012 | Unit Hierarchy 1 | Republic of Zambia |
      | Local File 2 | Period 2013 | Unit Hierarchy 1 | Republic of Yemen  |
      | Local File 3 | Period 2013 | Unit Hierarchy 1 | Republic of Yemen  |
    And the following Authorization:
      | Name                            | Item           | Rights         |
      | Local File Access/Create Access | TPM_LOCAL_FILE | ACCESS, CREATE |
    And I have the authorization "Local File Access/Create Access"
    And I am logged in

  Scenario: A user visits the local File list page without any record permission assigned
    Given I am on "/tpm/local-file?q="
    Then I should see "No results found"

  Scenario: A user lists Local File records where he has group based permission
    Given I am assigned to group "Group 1"
    And user group "Group 1" has view permission to TPM Local File "Local File 3"
    When I am on "/tpm/local-file?q="
    Then I should see the following table:
      | Name         | Hierarchy        |
      | Local File 3 | Unit Hierarchy 1 |

  Scenario: A user lists local file records where he has permission
    Given I have view permission to TPM Local File "Local File 1"
    And I have view permission to TPM Local File "Local File 2"
    When I am on "/tpm/local-file?q="
    Then I should see the following table:
      | Name         | Hierarchy        |
      | Local File 2 | Unit Hierarchy 1 |
      | Local File 1 | Unit Hierarchy 1 |

  Scenario: A user creates a new Local File
    Given I am on "/tpm/local-file/new"
    When I fill in the "Local File" form with:
      | Name           | Local File 4     |
      | Unit Hierarchy | Unit Hierarchy 1 |
      | Period         | Period 2012      |
      | Country        | Zambia           |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/local-file/4/edit"

  Scenario: A user creates a new Local File from document template
    Given the following Document Template:
      | Name            | Type       |
      | A Base Template | local-file |
    And there is a user named creator_user
    And there is a user named reader_user
    And the following User Group:
      | Name              |
      | Group For Writing |
    And reader_user has view permission to Document Template "A Base Template"
    And user group "Group For Writing" has view and edit permission to Document Template "A Base Template"
    And user creator_user has the authorization "Local File Access/Create Access"
    And I am logged in as creator_user
    And I am on "/tpm/local-file/new"
    When I fill in the "Local File" form with:
      | Name           | Local File 4     |
      | Unit Hierarchy | Unit Hierarchy 1 |
      | Period         | Period 2012      |
      | Country        | Zambia           |
      | Base Template  | A Base Template  |
    And I click the "Save" button in "Page Controls"
    Then I should see a success message
    And I should be on "/tpm/local-file/4/edit"
    And TPM Local File "Local File 4" should have following user permission table defined:
      | name         | permissions                  |
      | creator_user | view, edit, delete and owner |
      | reader_user  | view                         |
    And TPM Local File "Local File 4" should have following group permission table defined:
      | name              | permissions   |
      | Group For Writing | view and edit |

  Scenario: Updating a Local File
    Given I have view, edit, delete and owner permission to TPM Local File "Local File 2"
    And I am on "/tpm/local-file/2/edit"
    When I fill in the "Local File" form with:
      | Name | Updated name |
    And I press "Save"
    Then I should see a success message
    And I should be on "/tpm/local-file/2/edit"

  Scenario: Deleting a Local File from the configuration page
    Given I have view, edit, delete and owner permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/local-file"
    And I should not see "Local File 1"

  Scenario: Deleting a Local File from the edit page
    Given I have view, edit and delete permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file/1/edit"
    When I click the "dots" button in "Page Controls"
    And I click the "Delete" button
    Then I should see "Confirm Deletion"
    And I click the "Delete" button in the dialog
    Then I should see a success message
    And I should be on "/tpm/local-file"
    And I should not see "Local File 1"

  Scenario: When creating a new local file, the last filtered period is used to pre-populate the period field
    Given I am on "/tpm/local-file?q=Period = 'Period 2012'"
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/local-file/new"
    And the "Local File" form field "Period" should be "Period 2012"

  Scenario: Assigning unauthorised units to local file document fails
    Given the following Unit:
      | Ref Id     | Name | Country            |
      | RefId Unit | Unit | Republic of Zambia |
    And the following Unit Hierarchy Definition:
      | Unit Hierarchy   | Unit       | Parent Unit |
      | Unit Hierarchy 1 | RefId Unit |             |
    And I have view, edit, delete and owner permission to TPM Local File "Local File 1"
    And I am on "/tpm/local-file/1/edit"
    Then I should see "RefId Unit - Unit"
    When I check "RefId Unit - Unit"
    And I press "Save"
    Then I should see an error message
