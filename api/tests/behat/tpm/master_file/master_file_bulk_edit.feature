@clear-database
Feature: Loss Carry Forwards Bulk Edit
  In order to bulk edit Loss Carry Forwards
  As a User with the required authorisation
  I should be able to perform bulk edit on Loss Carry Forward records that are not completed

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                     | Initial Status | Transitions             |
      | TPM Master File Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id      | Workflow                 |
      | tpm_master_file | TPM Master File Workflow |
    And the following Unit Hierarchy:
      | Name             |
      | Unit Hierarchy 1 |
    And the following Period:
      | Name        |
      | Period 2012 |
      | Period 2013 |
    And the following User Group:
      | Name    |
      | Group 1 |
    And the following TPM Master File:
      | Name          | Period      | Unit Hierarchy   |
      | Master File 1 | Period 2012 | Unit Hierarchy 1 |
      | Master File 2 | Period 2013 | Unit Hierarchy 1 |
      | Master File 3 | Period 2013 | Unit Hierarchy 1 |
    And the following Authorization:
      | Name                    | Item            | Rights         |
      | Master File Read Access | TPM_MASTER_FILE | ACCESS, CREATE |
    And I am logged in

  Scenario: A user is redirected to the single edit page if there is only one local file is selected
    Given I have the authorization "Master File Read Access"
    And I have view, edit, delete and owner permission to TPM Master File "Master File 1"
    And I am on "/tpm/master-file?q="
    When I check the checkbox on "Master File 1" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/tpm/master-file/1/edit"

  Scenario: A user can bulk edit a local file
    Given I have the authorization "Master File Read Access"
    And I have view, edit, delete and owner permission to TPM Master File "Master File 1"
    And I have view, edit, delete and owner permission to TPM Master File "Master File 2"
    And I am on "/tpm/master-file?q="
    When I check the checkbox on "Master File 1" table row for bulk action
    And I check the checkbox on "Master File 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-master-file/edit?selection=1%2C2"
    When I enable the "task-description" field in "Bulk Edit Form"
    And I fill in the "Bulk Edit" form with:
      | task-description | Test |
    And I press "Save"
    Then I should see "Confirm Bulk Edit"
    When I click the "Save" button in the dialog
    Then I should be on "/tpm/master-file"
    And I should see a success message

  Scenario: A user cannot bulk edit properties that are disabled/hidden
    Given I have the authorization "Master File Read Access"
    And the following Field Configuration:
      | Name                 |
      | Description disabled |
    And the following Field State:
      | Field            | Field Configuration  | State    |
      | task-description | Description disabled | disabled |
    And the following Field Configuration Statuses:
      | Field Configuration  | Statuses | Workflow                 |
      | Description disabled | done     | TPM Master File Workflow |
    And I have view, edit, delete and owner permission to TPM Master File "Master File 1"
    And I have view, edit, delete and owner permission to TPM Master File "Master File 2"
    And I am on "/tpm/master-file?q="
    When I check the checkbox on "Master File 1" table row for bulk action
    And I check the checkbox on "Master File 2" table row for bulk action
    And I click the "dots" button in "Page Controls"
    And I click the "Edit Selected Record(s)" button
    Then I should be on "/bulk/tpm-master-file/edit?selection=1%2C2"
    And the element "#enables-period input[type='checkbox']" should be enabled
    And the element "#enables-description input[type='checkbox']" should be disabled
