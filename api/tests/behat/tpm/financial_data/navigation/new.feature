@clear-database
Feature: Financial Data Navigation - New
  In order to manage Financial Data
  As a user allowed to the TPM Financial Data
  I should be able to navigate through the Financial Data pages

  Background:
    Given the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Financial Data Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id         | Workflow                |
      | tpm_financial_data | Financial Data Workflow |
    And the following Authorization:
      | Name                | Item               | Rights       |
      | Financial Data Full | TPM_FINANCIAL_DATA | CREATE, READ |
    And I have the authorization "Financial Data Full"
    And I am logged in

  Scenario: A user navigates to the new page from the list page
    Given I am on "/tpm/financial-data?q="
    When I click the "New" button in "Page Controls"
    Then I should be on "/tpm/financial-data/new"
