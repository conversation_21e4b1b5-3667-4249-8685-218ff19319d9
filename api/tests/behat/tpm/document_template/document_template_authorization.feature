@clear-database
Feature: TPM Document Template Authorization
  As a user with no authorization to the TPM Module
  I should have no access to any feature of TPM Document Template

  Background:
    Given I am logged in

  Scenario: A User without admin rights tries to list document template records
    When I go to "/configuration/document/templates"
    Then I should see "403 Access Denied"

  Scenario: A User without admin rights tries to import a document template
    When I go to "/configuration/document/templates/import"
    Then I should see "403 Access Denied"

  Scenario: A User without admin rights tries to configure a document template
    Given the following Document Template:
      | Id | Name                     | Type       |
      | 1  | Test Local File Template | local-file |
    When I go to "/configuration/document/templates/1/configure"
    Then I should see "403 Access Denied"
