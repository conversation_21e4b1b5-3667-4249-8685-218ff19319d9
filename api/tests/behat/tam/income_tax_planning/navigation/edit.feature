@clear-database
Feature: Income Tax Planning Navigation - Edit
  In order to manage Income Tax Planning
  As a user allowed to the TAM Income Tax Planning
  I should be able to navigate through the Income Tax Planning pages

  Background:
    Given the following Period:
      | Name        |
      | Period 2015 |
    And the following Currency:
      | Iso4217Code |
      | EUR         |
    And the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                         | Initial Status | Transitions |
      | Income Tax Planning Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id              | Workflow                     |
      | tam_income_tax_planning | Income Tax Planning Workflow |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TAM Income Tax Planning:
      | Unit   | Period      | Tax Type   | Description |
      | RefId1 | Period 2015 | Tax Type 1 | ITP 1       |
    And the following Authorization:
      | Name                           | Item                    | Rights                         |
      | Income Tax Planing Full Access | TAM_INCOME_TAX_PLANNING | READ, UPDATE, DELETE, TRANSFER |
    And I have the authorization "Income Tax Planing Full Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tam/income-tax-planning?q="
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tam/income-tax-planning/1/edit"
