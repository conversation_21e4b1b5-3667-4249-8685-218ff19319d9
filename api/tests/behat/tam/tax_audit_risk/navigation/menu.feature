@clear-database
Feature: Tax Audit Risk Navigation - Menu
  In order to manage Tax Audit Risk
  As a user allowed to the TAM Tax Audit Risk
  I should be able to navigate through the Tax Audit Risk pages

  Background:
    Given the following Authorization:
      | Name                       | Item               | Rights                         |
      | Tax Audit Risk Full Access | TAM_TAX_AUDIT_RISK | READ, UPDATE, DELETE, TRANSFER |
    And I have the authorization "Tax Audit Risk Full Access"
    And I am logged in
    And I am on the homepage

  Scenario: A user navigates to the list page by clicking the menu
    When I click "Tax Audit Risk" in the menu under "TAM"
    Then I should be on "/tam/tax-audit-risk"
