@clear-database
Feature: TCM Tax Filing Monitor Authorization
  As a user with no authorization to the TCM Module
  I should have no access to any feature of TCM Tax Filing Monitor

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Start    | open          | in progress        |
      | Complete | in progress   | done               |
      | Reopen   | done          | open               |
    And the following Workflow:
      | Name                        | Initial Status | Transitions             |
      | Tax Filing Monitor Workflow | open           | Start, Complete, Reopen |
    And the following Workflow Binding:
      | Binding Id             | Workflow                    |
      | tcm_tax_filing_monitor | Tax Filing Monitor Workflow |
    And the following Currency:
      | Iso4217Code |
      | TEU         |
      | EUR         |
    And the following Country:
      | Iso3166Code | NameShort | NameLong             | NationalityShort | NationalityLong |
      | ZW          | Zimbabwe  | Republic of Zimbabwe | Zimbabwean       | Zimbabwean      |
      | ZM          | Zambia    | Republic of Zambia   | Zambian          | Zambian         |
      | YE          | Yemen     | Republic of Yemen    | Yemeni           | Yemeni          |
    And the following Declaration Type:
      | Name             |
      | Declaration Type |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         | Currency | Country |
      | RefId1 | Legal Unit 1 | TEU      | Yemen   |
      | RefId2 | Legal Unit 2 | EUR      | Zambia  |
    And the following TCM Tax Filing Monitor:
      | Unit   | Description          | Tax Year | Tax Type   | Declaration Type |
      | RefId1 | Tax Filing Monitor 1 | 2014     | Tax Type 1 | Declaration Type |
      | RefId2 | Tax Filing Monitor 2 | 2015     | Tax Type 1 | Declaration Type |
    And I am logged in

  Scenario: A User without the required authorisation tries to edit a Tax Filing Monitor record
    When I go to "/tcm/tax-filing-monitor/1/edit"
    Then I should see "403 Access Denied"

  Scenario: A User without the required authorisation tries to list the Tax Filing Monitor records
    When I go to "/tcm/tax-filing-monitor"
    Then I should see "403 Access Denied"

  Scenario: A User without the required authorisation tries to create a Tax Filing Monitor record
    When I go to "/tcm/tax-filing-monitor/new"
    Then I should see "403 Access Denied"

  Scenario: A User with read only authorisation accessing the Tax Filing Monitor creation form
    Given the following Authorization:
      | Name                           | Item                   | Rights |
      | Tax Filing Monitor Read Access | TCM_TAX_FILING_MONITOR | READ   |
    And I have the authorization "Tax Filing Monitor Read Access"
    When I go to "/tcm/tax-filing-monitor/new"
    Then I should see "403 Access Denied"

  Scenario: A user with read only authorisation tries to bulk delete Tax Filing Monitor records
    Given the following Authorization:
      | Name                           | Item                   | Rights |
      | Tax Filing Monitor Read Access | TCM_TAX_FILING_MONITOR | READ   |
    And I have the authorization "Tax Filing Monitor Read Access"
    When I go to "/tcm/tax-filing-monitor"
    And I click the "dots" button in "Page Controls"
    Then the "Delete" button should be disabled
