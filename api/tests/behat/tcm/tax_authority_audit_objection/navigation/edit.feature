@clear-database
Feature: Tax Authority / Audit Objection - Edit
  In order to edit a Tax Authority / Audit Objection
  As a user with the required authorisation
  I should be able to browse the Tax Authority / Audit Objection edit page

  Background:
    Given the following Status:
      | Type        | Name        |
      | OPEN        | open        |
      | IN_PROGRESS | in progress |
    And the following Transition:
      | Name  | Origin status | Destination Status |
      | Start | open          | in progress        |
    And the following Workflow:
      | Name                                   | Initial Status | Transitions |
      | Tax Authority Audit Objection Workflow | open           | Start       |
    And the following Workflow Binding:
      | Binding Id                        | Workflow                               |
      | tcm_tax_authority_audit_objection | Tax Authority Audit Objection Workflow |
    And the following Tax Type:
      | Name       |
      | Tax Type 1 |
    And the following Legal Unit:
      | Ref Id | Name         |
      | RefId1 | Legal Unit 1 |
    And the following TCM Tax Authority Audit Objection:
      | Unit   | Description                     | Tax Type   |
      | RefId1 | Tax Authority Audit Objection 1 | Tax Type 1 |
    And the following Authorization:
      | Name                                            | Item                              | Rights       |
      | TCM Tax Authority Audit Objection UPDATE Access | TCM_TAX_AUTHORITY_AUDIT_OBJECTION | READ, UPDATE |
    And I have the authorization "TCM Tax Authority Audit Objection UPDATE Access"
    And I am assigned to unit "RefId1"
    And I am logged in

  Scenario: A user navigates to the edit page from the list page
    Given I am on "/tcm/tax-authority-audit-objection"
    When I click "Edit" on the table row for "Tax Type 1"
    Then I should be on "/tcm/tax-authority-audit-objection/1/edit"
