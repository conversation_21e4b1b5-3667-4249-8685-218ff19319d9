@clear-database @clear-formula-cache
Feature: Layout unit view
  In order to edit item values on a unit view
  As a DTM user
  I should be able to enter and save data

  Background:
    Given the following Status:
      | Type        | Name        |
      | IN_PROGRESS | in progress |
      | COMPLETE    | done        |
    And the following Transition:
      | Name     | Origin status | Destination Status |
      | Complete | in progress   | done               |
    And the following Workflow:
      | Name                    | Initial Status | Transitions |
      | Layout Monitor Workflow | in progress    | Complete    |
    And the following Workflow Binding:
      | Binding Id  | Workflow                |
      | unit_period | Layout Monitor Workflow |
    And the following Currency:
      | Name | Iso 4217 Code |
      | Euro | EUR           |
    And the following System Setting:
      | Id                   | Value |
      | application_currency | EUR   |
    And the following Unit:
      | Id | Ref Id      | Name | Currency |
      | 1  | Unit Ref ID | Unit | EUR      |
    And the following Item:
      | Ref Id | Type  | Editable | Exchange Method |
      | item1  | Money | True     | Current         |
      | item2  | Money | True     | Current         |
      | item3  | Money | False    | Current         |
      | item4  | Money | False    | Current         |
    And the following Field:
      | Name      | Item  | Disabled |
      | position1 | item1 | false    |
      | position2 | item2 | false    |
      | position3 | item3 | true     |
      | position4 | item4 | true     |
    And the following Datasheet:
      | Id | Group | Fields                          | Name        |
      | 1  | Group | position1, position2, position4 | Test Layout |
    And the following Datasheet Collection:
      | Id                         | Name            | Public |
      | 11H678XXXN6JAJQCZX01TZYVKX | Test Collection | true   |
    And the following Datasheet Collection Entry:
      | Layout Collection | Layout      | Position |
      | Test Collection   | Test Layout | 1        |
    And the following Item Formula:
      | Item  | Formula String  |
      | item3 | {item1}+{item2} |
      | item4 | {item3}+5       |
    And the following Period:
      | Id | Name           |
      | 1  | Current Period |
    And the following Unit Period:
      | Unit        | Period         | Status      |
      | Unit Ref ID | Current Period | IN_PROGRESS |
    And the following Authorization:
      | Name                  | Item        | Rights       |
      | Unit Period ReadWrite | UNIT_PERIOD | READ, UPDATE |
    And I have the authorization "Unit Period ReadWrite"
    And I am assigned to unit "Unit Ref ID"
    And I am logged in

  Scenario: A unit view is saved with correctly calculated data
    Given I have view and edit permission to DTM Layout "Test Layout"
    And I go to "/datasheets/collections/11H678XXXN6JAJQCZX01TZYVKX/sheets/1?period=1&unit=1"
    When I fill in the "Unit View" form with:
      | Position1 > Local Currency Value | 7  |
      | Position2 > Local Currency Value | 14 |
    And I press "Save"
    Then I should be on "/datasheets/collections/11H678XXXN6JAJQCZX01TZYVKX/sheets/1?period=1&unit=1"
    And I should see a success message
    And the "Unit View" form field "Position4 > Local Currency Value" should be "26.00"

  Scenario: A unit view is not editable if the period is closed
    Given The period "Current Period" is closed
    And I have view permission to DTM Layout "Test Layout"
    And I go to "/datasheets/collections/11H678XXXN6JAJQCZX01TZYVKX/sheets/1?period=1&unit=1"
    Then the "Save" button in "Page Controls" should be disabled
