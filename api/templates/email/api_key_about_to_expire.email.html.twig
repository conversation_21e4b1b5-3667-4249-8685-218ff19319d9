{% extends 'email_layout.html.twig' %}

{% block body_title_wrapper %}{% endblock %}

{% block body %}
    <p>
        {{ 'u2.email.greeting'|trans({'%username_or_email%': username | escape }) | raw }}
    </p>
    <p>
        {{ 'u2.email.api_key.content.api_key_will_expire_at'|trans({'%apiKeyName%': apiKey.name | purify, '%apiKeyExpiresDate%': apiKey.expiresOn }, null, locale) | raw }}
    </p>
    <p>
        {{ 'u2.email.api_key.content.if_needed_regenerate'|trans({'%userSettingUrl%': url('u2_user_settings')}, null, locale) | raw }}
    </p>
    <p>
        {{ 'u2.email.sincerely_universal_units'|trans({}, null, locale) | raw }}
    </p>
{% endblock body %}
