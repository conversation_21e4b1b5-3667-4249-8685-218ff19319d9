{% extends 'default.form_theme.html.twig' %}

{# Widgets #}
{% block form_widget_simple %}
  {% set isLoading = true %}
  <DatasheetFormFieldWrapperLegacy
    :field="{{  _context.attr.field }}"
    :errors="{{ 'errors' in _context.attr|keys ? _context.attr.errors : 'undefined' }}"
    v-slot="{ isLoading }"
    :disabled="{{ disabled | json_encode }}"
  >
    <div class="w-full">
      {{ parent() }}
    </div>
  </DatasheetFormFieldWrapperLegacy>
{% endblock form_widget_simple %}

{% block checkbox_widget %}
  {% set isLoading = true %}
  <DatasheetFormFieldWrapperLegacy
    :field="{{  _context.attr.field }}"
    :errors="{{ 'errors' in _context.attr|keys ? _context.attr.errors : 'undefined' }}"
    v-slot="{ isLoading }"
    :disabled="{{ disabled | json_encode }}"
    class="form-widget-checkbox-item-value"
  >
    {{ parent() }}
  </DatasheetFormFieldWrapperLegacy>
{% endblock checkbox_widget %}

{% block textarea_widget %}
  {% set isLoading = true %}
  <DatasheetFormFieldWrapperLegacy
    :field="{{  _context.attr.field }}"
    :errors="{{ 'errors' in _context.attr|keys ? _context.attr.errors : 'undefined' }}"
    v-slot="{ isLoading }"
    :disabled="{{ disabled | json_encode }}"
  >
    {{ parent() }}
  </DatasheetFormFieldWrapperLegacy>
{% endblock textarea_widget %}

{% block percent_widget %}
  {% set isLoading = true %}
  <DatasheetFormFieldWrapperLegacy
    :field="{{  _context.attr.field }}"
    :errors="{{ 'errors' in (_context.attr|keys) ? _context.attr.errors : 'undefined' }}"
    v-slot="{ isLoading }"
    :disabled="{{ disabled | json_encode }}"
  >
    {{ parent() }}
  </DatasheetFormFieldWrapperLegacy>
{% endblock percent_widget %}

{% block u2_money_widget %}
  {% set isLoading = true %}
  <DatasheetFormFieldWrapperLegacy
    :field="{{  _context.attr.field }}"
    :errors="{{ 'errors' in _context.attr|keys ? _context.attr.errors : 'undefined' }}"
    v-slot="{ isLoading }"
    :disabled="{{ disabled | json_encode }}"
  >
    {{ parent() }}
  </DatasheetFormFieldWrapperLegacy>
{% endblock u2_money_widget %}

{% block u2_decimal_widget %}
  {% set isLoading = true %}
  <DatasheetFormFieldWrapperLegacy
    :field="{{  _context.attr.field }}"
    :errors="{{ 'errors' in _context.attr|keys ? _context.attr.errors : 'undefined' }}"
    v-slot="{ isLoading }"
    :disabled="{{ disabled | json_encode }}"
  >
    {{ block('number_widget') }}
  </DatasheetFormFieldWrapperLegacy>
{% endblock u2_decimal_widget %}

{% block checkbox_item_value_widget %}
  {{- form_widget(form.isChecked, {'attr': {
    'class': attr.class|default(''),
    'data-item-refid': data.item.refId,
    'errors': form.isChecked.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
  }}) -}}
{% endblock checkbox_item_value_widget %}

{% block diff_item_value_widget %}
  {{- form_widget(form.diff, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.diff.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder':'0',
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock diff_item_value_widget %}

{% block money_item_value_widget %}
  {{- form_widget(form.localCurrencyValue, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.localCurrencyValue.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder': (disabled ? '': '0'),
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock money_item_value_widget %}

{% block number_item_value_widget %}
  {{- form_widget(form.value, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.value.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder': (disabled ? '': '0'),
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock number_item_value_widget %}

{% block percent_item_value_widget %}
  {{- form_widget(form.value, {'attr': {
    'data-item-refid': data.item.refId,
    'errors': form.value.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder':'0',
    'class': attr.class|default() ~ ' size-full',
  }}) -}}
{% endblock percent_item_value_widget %}

{% block text_item_value_widget %}
  {{- form_widget(form.comment, {'attr': {
    'class': attr.class|default(''),
    'data-item-refid': data.item.refId,
    'errors': form.comment.vars.errors.form|extract_errors|json_encode,
    'field': attr.field,
    'placeholder': (disabled ? '': 'u2.form.text_field_placeholder'|trans)
  }}) -}}
{% endblock text_item_value_widget %}
