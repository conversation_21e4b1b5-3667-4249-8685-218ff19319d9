{% if contracts is not empty %}
  {%- if format == 'html' -%}<HorizontalScrollContainer class="py-1.5">{%- endif -%}
    <table class="data-table">
      <thead>
        <tr>
          <th class="table-head-text">{{ 'u2_tpm.unit_country'|trans }}</th>
          <th class="table-head-text">{{ 'u2.unit_ref_id'|trans }}</th>
          <th class="table-head-text">{{ 'u2_tpm.contract_name'|trans }}</th>
          <th class="table-head-text">{{ 'u2_tpm.contract_type'|trans }}</th>
          {% if contracts[0].date is defined %}
            <th class="table-head-text">{{ 'u2_tpm.contract_date'|trans }}</th>
          {% endif %}
          <th class="table-head-text">{{ 'u2.partner_unit_ref_id'|trans }}</th>
          {% if contracts[0].partner_unit_country is defined %}
            <th class="table-head-text">{{ 'u2_tpm.partner_unit_country'|trans }}</th>
          {% endif %}
        </tr>
      </thead>
      <tbody>
        {% for contract in contracts %}
          <tr>
            <td class="table-data-text" v-pre>{{ contract.unit_country }}</td>
            <td class="table-data-text">{{ contract.unit_ref_id }}</td>
            <td class="table-data-name">
              {%- if contract.show_link -%}
                <AppLink to="{{ path('u2_contract_edit', {'id': contract.contract_id}) }}">{{ contract.contract_name }}</AppLink>
              {%- else -%}
                {{ contract.contract_name }}
              {%- endif -%}
            </td>
            <td class="table-data-text" v-pre>{{ contract.contract_type }}</td>
            {% if contract.date is defined %}
              <td class="table-data-date">{{ contract.date ? contract.date|format_datetime('short', 'none') : 'u2_tpm.n_a'|trans }}</td>
            {% endif %}
            <td class="table-data-text">{{ contract.partner_unit_ref_id ?? 'u2_tpm.n_a'|trans }}</td>
            {% if contract.partner_unit_country is defined %}
              <td class="table-data-text" v-pre>{{ contract.partner_unit_country ?? 'u2_tpm.n_a'|trans }}</td>
            {% endif %}
          </tr>
        {% endfor %}
      </tbody>
    </table>
  {%- if format == 'html' -%}</HorizontalScrollContainer>{%- endif -%}
{% else %}
  <p>{{ 'u2_tpm.contracts.is_empty'|trans }}</p>
{% endif %}
