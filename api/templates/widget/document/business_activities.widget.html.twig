{% if mainBusinessActivities is not empty %}
  {%- if format == 'html' -%}<HorizontalScrollContainer class="py-1.5">{%- endif -%}
    <table class="data-table">
      <thead>
        <tr>
          <th class="table-head-name">{{ 'u2.country'|trans }}</th>
          <th class="table-head-text">{{ 'u2.unit'|trans }}</th>
          <th class="table-head-text">{{ 'u2_tpm.business_activity'|trans }}</th>
          <th class="table-head-text">{{ 'u2.description'|trans }}</th>
        </tr>
      </thead>
      <tbody>
        {% for country, units in mainBusinessActivities %}
          {% set countryRows = 0 %}
          {% for businessActivities in units %}
            {% set countryRows = countryRows + (businessActivities|length > 0 ? businessActivities|length : 1) %}
          {% endfor %}
          {% for unitRefId, businessActivities in units %}
            {% set unitLoop = loop %}
            {% set unitRows = businessActivities|length %}
            <tr>
            {% if unitLoop.first %}
              <td class="table-data-name"{% if countryRows > 1 %} rowspan="{{ countryRows }}"{% endif %} v-pre>
                {{- country -}}
              </td>
            {% endif %}
            <td class="table-data-text"{% if unitRows > 1 %} rowspan="{{ unitRows }}"{% endif %} v-pre>
              {{- unitRefId -}}
            </td>
            {% for businessActivity in businessActivities %}
              <td class="table-data-text" v-pre>{{ businessActivity.businessActivity }}</td>
              <td class="table-data-text" v-pre>{{ businessActivity.description }}</td>
              {% if not loop.last %}
                </tr>
                <tr>
              {% endif %}
            {% else %}
              <td class="table-data-text"></td>
              <td class="table-data-text"></td>
            {% endfor %}
            </tr>
          {% endfor %}
        {% endfor %}
      </tbody>
    </table>
  {%- if format == 'html' -%}</HorizontalScrollContainer>{%- endif -%}
{% else %}
  <p>{{ 'u2_tpm.main_business_activity.is_empty'|trans }}</p>
{% endif %}
