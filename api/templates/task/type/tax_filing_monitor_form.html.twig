{% import 'task/task_type_form.macro.html.twig' as form_macro %}

{{ form_start(form) }}
{{ form_errors(form) }}
<FormFieldset label="{{ 'u2_tcm.details'|trans }}">
  <div class="fields-grid sm:grid-cols-2">
    {{ form_macro.form_row_if_defined(form, 'unit', {'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'taxType') }}
    {{ form_macro.form_row_if_defined(form, 'declarationType') }}
    {{ form_macro.form_row_if_defined(form, 'name', {'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'taxYear', {'attr': {'class': 'max-w-xxs'}}) }}
    {{ form_macro.form_row_if_defined(form, 'taxMonth', {'attr': {'class': 'max-w-xxs'}}) }}
    {{ form_macro.form_row_if_defined(form, 'dueDate') }}
    {{ form_macro.form_row_if_defined(form, 'filingDate') }}
    {{ form_macro.form_row_if_defined(form, 'lateFilingPenalty', {'attr': {'class': 'max-w-xxs'}, 'row_attr': {'class': 'col-span-full'}}) }}
  </div>
</FormFieldset>
{% if form.description is defined %}
  <FormFieldset label="{{ 'u2.description'|trans }}">
    {{ form_widget(form.description, {'attr': {'aria-label': 'u2.description'|trans }}) }}
    {{ form_errors(form.description) }}
  </FormFieldset>
{% endif %}

{{ form_end(form) }}
