{% import 'task/task_type_form.macro.html.twig' as form_macro %}

{{ form_start(form) }}
{{ form_errors(form) }}
<FormFieldset label="{{ 'u2_tpm.base'|trans }}" collapsible :collapsed="false">
  <div class="fields-grid sm:grid-cols-2">
    {{ form_macro.form_row_if_defined(form, 'period') }}
    {{ form_macro.form_row_if_defined(form, 'carryForward') }}
  </div>
</FormFieldset>

<FormFieldset label="{{ 'u2_tpm.parties'|trans }}" collapsible :collapsed="false">
  <div class="fields-grid sm:grid-cols-2">
    {{ form_macro.form_row_if_defined(form, 'unit') }}
    {{ form_macro.form_row_if_defined(form, 'partnerUnit', {'row_attr': {'class': 'order-3 sm:order-none'}}) }}
    {{ form_macro.form_row_if_defined(form, 'unitRequiresDocumentation') }}
    {{ form_macro.form_row_if_defined(form, 'partnerUnitRequiresDocumentation', {'row_attr': {'class': 'order-4 sm:order-none'}}) }}
    {{ form_macro.form_row_if_defined(form, 'unitStandardTaxationApplicable') }}
    {{ form_macro.form_row_if_defined(form, 'partnerUnitStandardTaxationApplicable', {'row_attr': {'class': 'order-4 sm:order-none'}}) }}
  </div>
</FormFieldset>

<FormFieldset label="{{ 'u2_tpm.classification'|trans }}" collapsible :collapsed="false">
  <div class="fields-grid sm:grid-cols-2">
    {{ form_macro.form_row_if_defined(form, 'name', {'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'type') }}
    {{ form_macro.form_row_if_defined(form, 'subType') }}
    {{ form_macro.form_row_if_defined(form, 'billingType') }}
    {{ form_macro.form_row_if_defined(form, 'transferPricingMethod') }}
    <FormRow>
      <template #label>
        {{ form.transactionAmount is defined ? form_label(form.transactionAmount) : '' }}
      </template>
      <div class="amount-with-currency-selector flex space-x-1 mt-1">
        {{ form.transactionAmount is defined ? form_widget(form.transactionAmount) : '' }}
        {{ form.transactionCurrency is defined ? form_widget(form.transactionCurrency) : '' }}
      </div>
      {{ form.transactionAmount is defined ? form_errors(form.transactionAmount) : '' }}
      {{ form.transactionCurrency is defined ? form_errors(form.transactionCurrency) : '' }}
    </FormRow>
    {{ form_macro.form_row_if_defined(form, 'transactionVolume') }}
    {{ form_macro.form_row_if_defined(form, 'armsLength') }}
    {{ form_macro.form_row_if_defined(form, 'dueDate') }}
  </div>
</FormFieldset>

<FormFieldset label="{{ 'u2.contract_details'|trans }}" collapsible :collapsed="false">
  <div class="fields-grid sm:grid-cols-2">
    {{ form_macro.form_row_if_defined(form, 'underlyingContract', {'row_attr': {'class': 'col-span-2'}}) }}
    {{ form_macro.form_row_if_defined(form, 'contractDate') }}
    {{ form_macro.form_row_if_defined(form, 'contractExpiryDate') }}
    {{ form_macro.form_row_if_defined(form, 'couponInterestRate', {'attr': {'class': 'max-w-xxs'}}) }}
    {{ form_macro.form_row_if_defined(form, 'couponInterestRateType') }}
    {{ form_macro.form_row_if_defined(form, 'maturityDate') }}
    {{ form_macro.form_row_if_defined(form, 'forwardRate', {'row_attr': {'class': 'col-start-1'}}) }}
    {{ form_macro.form_row_if_defined(form, 'assetLiabilityId') }}
    <FormRow>
      <template #label>
        {{ form.guaranteeFeeAmount is defined ? form_label(form.guaranteeFeeAmount) }}
      </template>
      <div class="amount-with-currency-selector flex space-x-1 mt-1">
        {{ form.guaranteeFeeAmount is defined ? form_widget(form.guaranteeFeeAmount) }}
        {{ form.guaranteeFeeCurrency is defined ? form_widget(form.guaranteeFeeCurrency) }}
      </div>
      {{ form.guaranteeFeeAmount is defined ? form_errors(form.guaranteeFeeAmount) }}
      {{ form.guaranteeFeeCurrency is defined ? form_errors(form.guaranteeFeeCurrency) }}
    </FormRow>
    <FormRow>
      <template #label>
        {{ form.currentPeriodInterestExpenses is defined ? form_label(form.currentPeriodInterestExpenses) : '' }}
      </template>
      <div class="flex amount-with-currency-selector flex space-x-1 mt-1">
        {{ form.currentPeriodInterestExpenses is defined ? form_widget(form.currentPeriodInterestExpenses) : '' }}
        {{ form.currentPeriodInterestExpensesCurrency is defined ? form_widget(form.currentPeriodInterestExpensesCurrency) : '' }}
      </div>
      {{ form.currentPeriodInterestExpenses is defined ? form_errors(form.currentPeriodInterestExpenses) : '' }}
      {{ form.currentPeriodInterestExpensesCurrency is defined ? form_errors(form.currentPeriodInterestExpensesCurrency) : '' }}
    </FormRow>
    <FormRow>
      <template #label>
        {{ form.previousPeriodBookValue is defined ? form_label(form.previousPeriodBookValue) : '' }}
      </template>
      <div class="flex amount-with-currency-selector flex space-x-1 mt-1">
        {{ form.previousPeriodBookValue is defined ? form_widget(form.previousPeriodBookValue) : '' }}
        {{ form.previousPeriodBookValueCurrency is defined ? form_widget(form.previousPeriodBookValueCurrency) : '' }}
      </div>
      {{ form.previousPeriodBookValue is defined ? form_errors(form.previousPeriodBookValue) : '' }}
      {{ form.previousPeriodBookValueCurrency is defined ? form_errors(form.previousPeriodBookValueCurrency) : '' }}
    </FormRow>
    <FormRow>
      <template #label>
        {{ form.currentPeriodBookValue is defined ? form_label(form.currentPeriodBookValue) : '' }}
      </template>
      <div class="flex amount-with-currency-selector space-x-1 mt-1">
        {{ form.currentPeriodBookValue is defined ? form_widget(form.currentPeriodBookValue) : '' }}
        {{ form.currentPeriodBookValueCurrency is defined ? form_widget(form.currentPeriodBookValueCurrency) : '' }}
      </div>
      {{ form.currentPeriodBookValue is defined ? form_errors(form.currentPeriodBookValue) : '' }}
      {{ form.currentPeriodBookValueCurrency is defined ? form_errors(form.currentPeriodBookValueCurrency) : '' }}
    </FormRow>
  </div>
</FormFieldset>

{% if form.description is defined %}
<FormFieldset label="{{ 'u2.description'|trans }}" collapsible :collapsed="false">
  {{ form_widget(form.description) }}
  {{ form_errors(form.description) }}
</FormFieldset>
{% endif %}

{{ form_end(form) }}
