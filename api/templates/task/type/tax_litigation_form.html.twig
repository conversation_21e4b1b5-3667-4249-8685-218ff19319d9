{% import 'task/task_type_form.macro.html.twig' as form_macro %}

{{ form_start(form) }}
{{ form_errors(form) }}
<FormBaseFieldset label="{{ 'u2_tam.base'|trans }}">
  {{ form_macro.form_row_if_defined(form, 'period') }}
</FormBaseFieldset>

<FormFieldset label="{{ 'u2_tam.details'|trans }}">
  <div class="fields-grid sm:grid-cols-2">
    {{ form_macro.form_row_if_defined(form, 'unit', {'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'taxYear', {'attr': {'class': 'max-w-xxs'}, 'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'taxType') }}
    {{ form_macro.form_row_if_defined(form, 'specification') }}
    {{ form_macro.form_row_if_defined(form, 'riskType') }}
    {{ form_macro.form_row_if_defined(form, 'permanent') }}
    {{ form_macro.form_row_if_defined(form, 'riskProbability', {'attr': {'class': 'max-w-xxs' }, 'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'amount', {'attr': {'class': 'max-w-xxs' }, 'row_attr': {'class': 'col-span-full'}}) }}
    {{ form_macro.form_row_if_defined(form, 'dueDate') }}
  </div>
</FormFieldset>
<FormFieldset label="{{ 'u2_tam.accrued'|trans }}">
  <div class="fields-grid">
    {{ form_macro.form_row_if_defined(form, 'plEffectPy', {'attr': {'class': 'max-w-xxs', 'data-js-calc-pl-effect-py': true}}) }}
    {{ form_macro.form_row_if_defined(form, 'accruedBoy', {'attr': {'class': 'max-w-xxs', 'data-js-calc-accrued-boy': true}}) }}
    {{ form_macro.form_row_if_defined(form, 'plEffectCy', {'attr': {'class': 'max-w-xxs', 'data-js-calc-pl-effect-cy': true}}) }}
    {{ form_macro.form_row_if_defined(form, 'accruedEoy', {'attr': {'class': 'max-w-xxs', 'data-js-calc-accrued-eoy': true}}) }}
  </div>
</FormFieldset>
{% if form.description is defined %}
  <FormFieldset label="{{ 'u2.description'|trans }}">
    {{ form_widget(form.description, {'attr': {'aria-label': 'u2.description'|trans }}) }}
    {{ form_errors(form.description) }}
  </FormFieldset>
{% endif %}

{{ form_end(form) }}
