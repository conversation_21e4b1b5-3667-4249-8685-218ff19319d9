{% import 'task/task_type_form.macro.html.twig' as form_macro %}

{{ form_start(form) }}
{{ form_errors(form) }}
<FormBaseFieldset label="{{ 'u2_tam.base'|trans }}">
  {{ form_macro.form_row_if_defined(form, 'period') }}
</FormBaseFieldset>

<FormFieldset label="{{ 'u2_tam.details'|trans }}">
  <div class="fields-grid">
    {{ form_macro.form_row_if_defined(form, 'unit') }}
    {{ form_macro.form_row_if_defined(form, 'taxType') }}
    {{ form_macro.form_row_if_defined(form, 'taxRate', {'attr': {'class': 'max-w-xxs' }}) }}
    {{ form_macro.form_row_if_defined(form, 'dueDate') }}
  </div>
</FormFieldset>
{% if form.description is defined %}
  <FormFieldset label="{{ 'u2.description'|trans }}">
    {{ form_widget(form.description, {'attr': {'aria-label': 'u2.description'|trans }}) }}
    {{ form_errors(form.description) }}
  </FormFieldset>
{% endif %}

{{ form_end(form) }}
