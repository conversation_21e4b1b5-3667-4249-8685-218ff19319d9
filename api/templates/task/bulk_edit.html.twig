{{ form_start(form, {'attr': { 'class': 'divide-y divide-gray-100'}}) }}
{{ form_errors(form) }}
{% for child in form.children %}
  {% if child.vars.enableable %}
    <form-row-enableable
      id="enables-{{ child.vars.name }}"
      :disabled="{{ (child.vars.disabled == true ? true : false) |json_encode }}"
    >
      {{ form_row(child) }}
    </form-row-enableable>
  {% endif %}
{% endfor %}
{{ form_rest(form) }}
{{ form_end(form) }}

