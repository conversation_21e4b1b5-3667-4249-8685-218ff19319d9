U2\Xbrl\De\Schema\UrnOecdTiesCbcstmV2\MessageSpecType:
  properties:
    sendingCompanyIN:
      expose: true
      access_type: public_method
      serialized_name: SendingCompanyIN
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getSendingCompanyIN
        setter: setSendingCompanyIN
      type: U2\Serializer\Jms\Types\XbrlString
    transmittingCountry:
      expose: true
      access_type: public_method
      serialized_name: TransmittingCountry
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getTransmittingCountry
        setter: setTransmittingCountry
      type: U2\Serializer\Jms\Types\XbrlString
    receivingCountry:
      expose: true
      access_type: public_method
      serialized_name: ReceivingCountry
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getReceivingCountry
        setter: setReceivingCountry
      type: U2\Serializer\Jms\Types\XbrlString
    messageType:
      expose: true
      access_type: public_method
      serialized_name: MessageType
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getMessageType
        setter: setMessageType
      type: U2\Serializer\Jms\Types\XbrlString
    warning:
      expose: true
      access_type: public_method
      serialized_name: Warning
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getWarning
        setter: setWarning
      type: U2\Serializer\Jms\Types\XbrlString
    contact:
      expose: true
      access_type: public_method
      serialized_name: Contact
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getContact
        setter: setContact
      type: U2\Serializer\Jms\Types\XbrlString
    messageRefId:
      expose: true
      access_type: public_method
      serialized_name: MessageRefId
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getMessageRefId
        setter: setMessageRefId
      type: U2\Serializer\Jms\Types\XbrlString
    messageTypeIndic:
      expose: true
      access_type: public_method
      serialized_name: MessageTypeIndic
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getMessageTypeIndic
        setter: setMessageTypeIndic
      type: U2\Serializer\Jms\Types\XbrlString
    corrMessageRefId:
      expose: true
      access_type: public_method
      serialized_name: CorrMessageRefId
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getCorrMessageRefId
        setter: setCorrMessageRefId
      xml_list:
        inline: true
        entry_name: CorrMessageRefId
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      type: array<string>
    reportingPeriod:
      expose: true
      access_type: public_method
      serialized_name: ReportingPeriod
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getReportingPeriod
        setter: setReportingPeriod
      type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\Date
    timestamp:
      expose: true
      access_type: public_method
      serialized_name: Timestamp
      xml_element:
        namespace: 'urn:oecd:ties:cbcstm:v2'
        cdata: false
      accessor:
        getter: getTimestamp
        setter: setTimestamp
      type: GoetasWebservices\Xsd\XsdToPhp\XMLSchema\DateTime
