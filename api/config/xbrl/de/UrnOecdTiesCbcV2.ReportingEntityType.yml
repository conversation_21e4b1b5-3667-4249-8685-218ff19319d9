U2\Xbrl\De\Schema\UrnOecdTiesCbcV2\ReportingEntityType:
  properties:
    entity:
      expose: true
      access_type: public_method
      serialized_name: Entity
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getEntity
        setter: setEntity
      type: U2\Xbrl\De\Schema\UrnOecdTiesCbcV2\OrganisationPartyType
    nameMNEGroup:
      expose: true
      access_type: public_method
      serialized_name: NameMNEGroup
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getNameMNEGroup
        setter: setNameMNEGroup
      type: U2\Serializer\Jms\Types\XbrlString
    reportingRole:
      expose: true
      access_type: public_method
      serialized_name: ReportingRole
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getReportingRole
        setter: setReportingRole
      type: U2\Serializer\Jms\Types\XbrlString
    reportingPeriod:
      expose: true
      access_type: public_method
      serialized_name: ReportingPeriod
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getReportingPeriod
        setter: setReportingPeriod
      type: U2\Xbrl\De\Schema\UrnOecdTiesCbcV2\ReportingEntityType\ReportingPeriodAType
