U2\Xbrl\De\Schema\WwwItzbundDeElan01\ELMAKOM\ELMAHeaderAType:
  properties:
    datenArt:
      expose: true
      access_type: public_method
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      serialized_name: DatenArt
      accessor:
        getter: getDatenArt
        setter: setDatenArt
      type: U2\Serializer\Jms\Types\XbrlString
    authSteuernummer:
      expose: true
      access_type: public_method
      serialized_name: AuthSteuernummer
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      accessor:
        getter: getAuthSteuernummer
        setter: setAuthSteuernummer
      type: U2\Xbrl\De\Schema\WwwItzbundDeElan01\AuthSteuernummer
    accountID:
      expose: true
      access_type: public_method
      serialized_name: AccountID
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      accessor:
        getter: getAccountID
        setter: setAccountID
      type: U2\Xbrl\De\Schema\WwwItzbundDeElan01\AccountID
    erstellungsDatum:
      expose: true
      access_type: public_method
      serialized_name: ErstellungsDatum
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      accessor:
        getter: getErstellungsDatum
        setter: setErstellungsDatum
      type: U2\Xbrl\De\Schema\WwwItzbundDeElan01\ErstellungsDatum
    kundeneigeneID:
      expose: true
      access_type: public_method
      serialized_name: KundeneigeneID
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      accessor:
        getter: getKundeneigeneID
        setter: setKundeneigeneID
      type: U2\Xbrl\De\Schema\WwwItzbundDeElan01\KundeneigeneID
    uUID:
      expose: true
      access_type: public_method
      serialized_name: UUID
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      accessor:
        getter: getUUID
        setter: setUUID
      type: U2\Xbrl\De\Schema\WwwItzbundDeElan01\UUID
    verarbeitungslauf:
      expose: true
      access_type: public_method
      serialized_name: Verarbeitungslauf
      xml_element:
        namespace: 'http://www.itzbund.de/ELAN/01'
        cdata: false
      accessor:
        getter: getVerarbeitungslauf
        setter: setVerarbeitungslauf
      type: U2\Xbrl\De\Schema\WwwItzbundDeElan01\Verarbeitungslauf
