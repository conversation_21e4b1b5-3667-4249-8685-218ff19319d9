U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\CorrectableCbcReportType\SummaryAType:
  properties:
    revenues:
      expose: true
      access_type: public_method
      serialized_name: Revenues
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getRevenues
        setter: setRevenues
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\CorrectableCbcReportType\SummaryAType\RevenuesAType
    profitOrLoss:
      expose: true
      access_type: public_method
      serialized_name: ProfitOrLoss
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getProfitOrLoss
        setter: setProfitOrLoss
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\MonAmntType
    taxPaid:
      expose: true
      access_type: public_method
      serialized_name: TaxPaid
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getTaxPaid
        setter: setTaxPaid
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\MonAmntType
    taxAccrued:
      expose: true
      access_type: public_method
      serialized_name: TaxAccrued
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getTaxAccrued
        setter: setTaxAccrued
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\MonAmntType
    capital:
      expose: true
      access_type: public_method
      serialized_name: Capital
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getCapital
        setter: setCapital
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\MonAmntType
    earnings:
      expose: true
      access_type: public_method
      serialized_name: Earnings
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getEarnings
        setter: setEarnings
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\MonAmntType
    nbEmployees:
      expose: true
      access_type: public_method
      serialized_name: NbEmployees
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getNbEmployees
        setter: setNbEmployees
      type: int
    assets:
      expose: true
      access_type: public_method
      serialized_name: Assets
      xml_element:
        namespace: 'urn:oecd:ties:cbc:v2'
        cdata: false
      accessor:
        getter: getAssets
        setter: setAssets
      type: U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\MonAmntType
