monolog:
  channels:
    - deprecation # Deprecations are logged in the dedicated "deprecation" channel when it exists

when@dev:
  monolog:
    handlers:
#      stdout:
#        type: stream
#        level: debug
#        path: "php://stdout"
#        channels: [ '!event' ]
      stderr:
        type: fingers_crossed
        action_level: error
        handler: nested
        excluded_http_codes: [ 403, 404, 405 ]
        channels: [ "!event" ]
      nested:
        type: stream
        level: debug
        path: "php://stderr"
        channels: [ '!event' ]
      console:
        type: console
        process_psr_3_messages: false
        channels: ["!event", "!doctrine", "!console"]

when@test:
  monolog:
    handlers:
      main:
        type: fingers_crossed
        action_level: error
        handler: nested
        excluded_http_codes: [ 403, 404, 405 ]
        channels: [ "!event" ]
        buffer_size: 50 # How many messages should be saved? Prevent memory leaks
      nested:
        type: stream
        path: "%kernel.logs_dir%/%kernel.environment%.log.jsonl"
        level: info
        formatter: monolog.formatter.json

when@behat:
  monolog:
    handlers:
      main:
        type: fingers_crossed
        action_level: error
        handler: nested
        excluded_http_codes: [ 403, 404, 405 ]
        buffer_size: 50 # How many messages should be saved? Prevent memory leaks
      nested:
        type: stream
        path: "%kernel.logs_dir%/%kernel.environment%.log.jsonl"
        level: info
        formatter: monolog.formatter.json
      deprecations:
        type: stream
        path: "%kernel.logs_dir%/%kernel.environment%.log.jsonl"
        channels: [deprecation]
        formatter: monolog.formatter.json

when@prod:
  monolog:
    handlers:
      main:
        type: fingers_crossed
        action_level: error
        handler: nested
        excluded_http_codes: [ 403, 404, 405 ]
        buffer_size: 50 # How many messages should be saved? Prevent memory leaks
      nested:
        type: stream
        path: php://stderr
        level: debug
        formatter: monolog.formatter.json
      console:
        type: console
        process_psr_3_messages: false
        channels: ["!event", "!doctrine"]
      deprecation:
        type: stream
        channels: [deprecation]
        path: php://stderr
        formatter: monolog.formatter.json
