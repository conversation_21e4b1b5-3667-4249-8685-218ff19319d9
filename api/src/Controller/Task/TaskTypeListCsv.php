<?php

declare(strict_types=1);
namespace U2\Controller\Task;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use U2\Controller\Helper;
use U2\Entity\StructuredDocumentInterface;
use U2\Http\Response\CsvResponse;
use U2\Security\Authorization\AuthorizationRight;
use U2\Table\TableFactory;
use U2\Task\TaskTypeKnowledge;

#[Route(path: '/tasktype/{shortName}/list.csv', name: 'u2_tasktype_listcsv', requirements: ['format' => 'html'], options: ['expose' => true], defaults: ['format' => 'html'], methods: [HttpOperation::METHOD_GET])]
class TaskTypeListCsv extends AbstractController
{
    public function __construct(
        private readonly TableFactory $tableFactory,
        private readonly Helper $controllerHelper,
    ) {
    }

    public function __invoke(string $shortName): Response
    {
        $entityClass = TaskTypeKnowledge::resolveEntityClassByShortName($shortName);
        $authorizationItem = TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$entityClass];

        if (is_a($entityClass, StructuredDocumentInterface::class, true)) {
            $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::ACCESS->value, null, 'You do not have rights to configure this entity');
        } else {
            $this->controllerHelper->denyAccessUnlessGranted($authorizationItem->value . ':' . AuthorizationRight::READ->value, null, 'You do not have permission to view this entry.');
        }

        return new CsvResponse($this->tableFactory->createForTaskType($entityClass, true)->getDataNormalized());
    }
}
