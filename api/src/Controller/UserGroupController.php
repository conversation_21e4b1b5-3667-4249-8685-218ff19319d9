<?php

declare(strict_types=1);
namespace U2\Controller;

use ApiPlatform\Metadata\HttpOperation;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use U2\Entity\UserGroup;
use U2\Security\Authorization\AuthorizationManager;
use U2\Security\UserRoles;

#[Route(path: '/user-group')]
#[IsGranted(UserRoles::UserGroupAdmin->value)]
class UserGroupController
{
    public function __construct(
        private readonly AuthorizationManager $authorizationManager,
    ) {
    }

    #[Route(path: '/{id}/rights-list.json', name: 'u2_usergroup_usergrouprightsjson', options: ['expose' => true], methods: [HttpOperation::METHOD_GET])]
    public function userGroupRightsJson(UserGroup $userGroup): JsonResponse
    {
        return new JsonResponse($this->authorizationManager->getRightsByItem($userGroup));
    }
}
