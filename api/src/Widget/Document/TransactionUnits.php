<?php

declare(strict_types=1);
namespace U2\Widget\Document;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Query\Expr\Join;
use Twig\Environment;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Unit;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Unit\Assignment\UserUnitAssignmentChecker;
use U2\User\CurrentUserProvider;

// TODO: remove code duplicated in \U2\Widget\Document\AssociatedUnits
class TransactionUnits extends AbstractDocumentWidget
{
    private string $style = 'table';

    private bool $showRefId = true;

    private bool $showName = true;

    private bool $showCurrency = true;

    private bool $showCountry = true;

    private bool $showCountryFounded = true;

    private bool $showLegalName = false;

    private bool $showLegalForm = false;

    private bool $showTaxNumber = false;

    private bool $showRegisterNumber = false;

    private bool $showRegistryPlace = false;

    private bool $showPostalAddress = false;

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        Environment $templatingEngine,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly UserUnitAssignmentChecker $userUnitAssignmentChecker,
    ) {
        parent::__construct($templatingEngine);
    }

    public function getParameters(): array
    {
        return [
            'style' => $this->style,
            'show_ref_id' => $this->showRefId,
            'show_name' => $this->showName,
            'show_currency' => $this->showCurrency,
            'show_country' => $this->showCountry,
            'show_country_founded' => $this->showCountryFounded,
            'show_legal_name' => $this->showLegalName,
            'show_legal_form' => $this->showLegalForm,
            'show_tax_number' => $this->showTaxNumber,
            'show_register_number' => $this->showRegisterNumber,
            'show_registry_place' => $this->showRegistryPlace,
            'show_postal_address' => $this->showPostalAddress,
        ];
    }

    public function setParameters(array $parameters): void
    {
        if (\array_key_exists('style', $parameters)) {
            /** @var string $style */
            $style = $parameters['style'];
            $this->style = $style;
        }
        if (\array_key_exists('show_ref_id', $parameters)) {
            /** @var bool $showRefId */
            $showRefId = $parameters['show_ref_id'];
            $this->showRefId = $showRefId;
        }
        if (\array_key_exists('show_name', $parameters)) {
            /** @var bool $showName */
            $showName = $parameters['show_name'];
            $this->showName = $showName;
        }
        if (\array_key_exists('show_currency', $parameters)) {
            /** @var bool $showCurrency */
            $showCurrency = $parameters['show_currency'];
            $this->showCurrency = $showCurrency;
        }
        if (\array_key_exists('show_country', $parameters)) {
            /** @var bool $showCountry */
            $showCountry = $parameters['show_country'];
            $this->showCountry = $showCountry;
        }
        if (\array_key_exists('show_country_founded', $parameters)) {
            /** @var bool $showCountryFounded */
            $showCountryFounded = $parameters['show_country_founded'];
            $this->showCountryFounded = $showCountryFounded;
        }
        if (\array_key_exists('show_legal_name', $parameters)) {
            /** @var bool $showLegalName */
            $showLegalName = $parameters['show_legal_name'];
            $this->showLegalName = $showLegalName;
        }
        if (\array_key_exists('show_legal_form', $parameters)) {
            /** @var bool $showLegalForm */
            $showLegalForm = $parameters['show_legal_form'];
            $this->showLegalForm = $showLegalForm;
        }
        if (\array_key_exists('show_tax_number', $parameters)) {
            /** @var bool $showTaxNumber */
            $showTaxNumber = $parameters['show_tax_number'];
            $this->showTaxNumber = $showTaxNumber;
        }
        if (\array_key_exists('show_register_number', $parameters)) {
            /** @var bool $showRegisterNumber */
            $showRegisterNumber = $parameters['show_register_number'];
            $this->showRegisterNumber = $showRegisterNumber;
        }
        if (\array_key_exists('show_registry_place', $parameters)) {
            /** @var bool $showRegistryPlace */
            $showRegistryPlace = $parameters['show_registry_place'];
            $this->showRegistryPlace = $showRegistryPlace;
        }
        if (\array_key_exists('show_postal_address', $parameters)) {
            /** @var bool $showPostalAddress */
            $showPostalAddress = $parameters['show_postal_address'];
            $this->showPostalAddress = $showPostalAddress;
        }
    }

    public function getStyle(): string
    {
        return $this->style;
    }

    public function isShowRefId(): bool
    {
        return $this->showRefId;
    }

    public function isShowName(): bool
    {
        return $this->showName;
    }

    public function isShowCurrency(): bool
    {
        return $this->showCurrency;
    }

    public function isShowCountry(): bool
    {
        return $this->showCountry;
    }

    public function isShowCountryFounded(): bool
    {
        return $this->showCountryFounded;
    }

    public function isShowLegalName(): bool
    {
        return $this->showLegalName;
    }

    public function isShowLegalForm(): bool
    {
        return $this->showLegalForm;
    }

    public function isShowTaxNumber(): bool
    {
        return $this->showTaxNumber;
    }

    public function isShowRegisterNumber(): bool
    {
        return $this->showRegisterNumber;
    }

    public function isShowRegistryPlace(): bool
    {
        return $this->showRegistryPlace;
    }

    public function isShowPostalAddress(): bool
    {
        return $this->showPostalAddress;
    }

    public function getName(): string
    {
        return 'transaction-units';
    }

    public function isConfigurable(): bool
    {
        return true;
    }

    protected function getTemplate(): string
    {
        if ('blocks' === $this->style) {
            return 'widget/document/units_blocks.widget.html.twig';
        }

        return 'widget/document/units_table.widget.html.twig';
    }

    protected function getData(array $options): array
    {
        $section = $this->getDocumentSection($options);
        $document = $section->getDocument();
        \assert(
            $document instanceof LocalFile
            || $document instanceof MasterFile
            || $document instanceof CountryByCountryReport
        );

        if (false === $this->userUnitAssignmentChecker->check(
            $this->currentUserProvider->get(),
            $document->getUnits()->toArray()
        )) {
            throw new InsufficientPermissionsWidgetException($this);
        }

        $query = $this->entityManager->createQueryBuilder()
            ->select(['trans'])
            ->from($document::class, 'doc')
            ->innerJoin('doc.units', 'doc_unit')
            ->innerJoin(Transaction::class, 'trans', Join::WITH, '(trans.unit = doc_unit OR trans.partnerUnit = doc_unit) AND trans.period = doc.period')
            ->where('doc.id = :document')
            ->andWhere('trans.unitRequiresDocumentation = :unit_requires_documentation')
            ->getQuery();

        $query->setParameters(
            [
                'document' => $document,
                'unit_requires_documentation' => true,
            ]
        );

        $transactions = $query->getResult();

        $unitDataMap = $this->buildUnitDataMap($transactions);
        ksort($unitDataMap, \SORT_NATURAL);
        $unitData = array_values($unitDataMap);

        return [
            'units' => $unitData,
        ];
    }

    private function buildUnitDataMap(array $transactions): array
    {
        return array_reduce(
            $transactions,
            function (array $unitData, Transaction $transaction): array {
                if (null !== ($unit = $transaction->getUnit())) {
                    $unitData = $this->addUnitDataItemIfNotExists($unit, $unitData);
                }
                if (null !== ($partnerUnit = $transaction->getPartnerUnit())) {
                    $unitData = $this->addUnitDataItemIfNotExists($partnerUnit, $unitData);
                }

                return $unitData;
            },
            []
        );
    }

    private function addUnitDataItemIfNotExists(Unit $unit, array $unitData): array
    {
        $refId = $unit->getRefId();
        if (!\array_key_exists($refId, $unitData)) {
            $unitData[$refId] = $this->buildUnitData($unit);
        }

        return $unitData;
    }

    private function buildUnitData(Unit $unit): array
    {
        $unitData = [
            'name' => $unit->getName(),
            'currency' => $unit->getCurrency(),
            'country' => $unit->getCountry(),
        ];

        if ($this->showRefId) {
            $unitData['ref_id'] = $unit->getRefId();
        }
        if ($this->showCountryFounded) {
            $unitData['country_founded'] = $this->getUnitCountryFounded($unit);
        }
        if ($this->showLegalName) {
            $unitData['legal_name'] = $this->getUnitLegalName($unit);
        }
        if ($this->showLegalForm) {
            $unitData['legal_form'] = $this->getUnitLegalForm($unit);
        }
        if ($this->showTaxNumber) {
            $unitData['tax_number'] = $this->getUnitTaxNumber($unit);
        }
        if ($this->showRegisterNumber) {
            $unitData['register_number'] = $this->getUnitRegisterNumber($unit);
        }
        if ($this->showRegistryPlace) {
            $unitData['registry_place'] = $this->getUnitRegistryPlace($unit);
        }
        if ($this->showPostalAddress) {
            $unitData['postal_address'] = $this->getUnitPostalAddress($unit);
        }

        return $unitData;
    }

    private function getUnitCountryFounded(Unit $unit): ?string
    {
        if (method_exists($unit, 'getCountryFounded')) {
            return $unit->getCountryFounded()?->getNameShort();
        }

        return null;
    }

    private function getUnitLegalName(Unit $unit): ?string
    {
        if (method_exists($unit, 'getLegalName')) {
            return $unit->getLegalName();
        }

        return null;
    }

    private function getUnitLegalForm(Unit $unit): ?string
    {
        if (method_exists($unit, 'getLegalForm')) {
            return $unit->getLegalForm()?->getName();
        }

        return null;
    }

    private function getUnitTaxNumber(Unit $unit): ?string
    {
        if (method_exists($unit, 'getDefaultTaxNumber')) {
            return $unit->getDefaultTaxNumber();
        }

        return null;
    }

    private function getUnitRegisterNumber(Unit $unit): ?string
    {
        if (method_exists($unit, 'getRegisterNumber')) {
            return $unit->getRegisterNumber();
        }

        return null;
    }

    private function getUnitRegistryPlace(Unit $unit): ?string
    {
        if (method_exists($unit, 'getRegistryPlace')) {
            return $unit->getRegistryPlace();
        }

        return null;
    }

    private function getUnitPostalAddress(Unit $unit): ?string
    {
        if (method_exists($unit, 'getPostalAddress')) {
            return $unit->getPostalAddress()?->getAuditLogValue();
        }

        return null;
    }
}
