<?php

declare(strict_types=1);
namespace U2\Widget\Document;

use U2\Entity\StructuredDocumentInterface;

class SupportedWidgetNameToIsConfigurableListProvider
{
    public function __construct(private readonly DocumentWidgetProvider $provider)
    {
    }

    /**
     * @return DocumentWidgetInterface[]
     */
    public function get(StructuredDocumentInterface $document): array
    {
        $widgetNameToConfigurationStateList = [];

        foreach ($this->provider->all() as $widget) {
            if ($widget->supportsDocument($document)) {
                $widgetNameToConfigurationStateList[$widget->getName()] = $widget->isConfigurable();
            }
        }

        return $widgetNameToConfigurationStateList;
    }
}
