<?php

declare(strict_types=1);
namespace U2\Widget\Document\Editor\TinyMce;

use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Document\Section\AttachmentsCollector;
use U2\Document\Section\SectionContext;
use U2\File\Attachment;
use U2\Widget\Document\Image;
use U2\Widget\Editor\TinyMce\DialogConfig;
use U2\Widget\Editor\TinyMce\Input;
use U2\Widget\Editor\TinyMce\SelectBox;

class ImageConfigurationFactory
{
    public function __construct(
        private readonly TranslatorInterface $translator,
        private readonly AttachmentChoicesListBuilder $attachmentChoicesListBuilder,
        private readonly AttachmentsCollector $attachmentsCollector,
    ) {
    }

    public function create(Image $widget, SectionContext $sectionContext): DialogConfig
    {
        return new DialogConfig(
            $this->translator->trans('u2_tpm.image'),
            [
                new SelectBox(
                    'id',
                    $this->translator->trans('u2_tpm.image'),
                    $this->attachmentChoicesListBuilder->build(
                        $this->attachmentsCollector
                            ->collect($sectionContext->getSection())
                            ->filter(fn (Attachment $attachment): bool => \in_array($attachment->getFile()->getMimeType(), Image::IMAGE_MIME_TYPES, true)),
                        $widget->getId()
                    ),
                    300
                ),
                new Input(
                    'width',
                    $this->translator->trans('u2_tpm.image_width'),
                    '',
                    $this->translator->trans('u2_tpm.image_width.help'),
                    8
                ),
            ],
            [
                'width' => $widget->getWidth(),
            ]
        );
    }
}
