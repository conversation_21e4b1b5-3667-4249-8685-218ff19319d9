<?php

declare(strict_types=1);
namespace U2\Widget\Document\Editor\TinyMce;

use Symfony\Component\Translation\TranslatableMessage;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Entity\Configuration\Field\TransactionType;
use U2\Entity\Currency;
use U2\Repository\CurrencyRepository;
use U2\Repository\TransactionTypeRepository;
use U2\Widget\Document\TransactionTable;
use U2\Widget\Editor\TinyMce\Checkbox;
use U2\Widget\Editor\TinyMce\DialogConfig;
use U2\Widget\Editor\TinyMce\FieldSet;
use U2\Widget\Editor\TinyMce\InfoBox;
use U2\Widget\Editor\TinyMce\SelectBox;
use U2\Widget\Editor\TinyMce\SelectBoxChoice;
use U2\Widget\Editor\TinyMce\TextareaConfiguration;

class TransactionTableConfigurationFactory
{
    public function __construct(
        private readonly TransactionTypeRepository $transactionTypeRepository,
        private readonly TranslatorInterface $translator,
        private readonly CurrencyRepository $currencyRepository,
    ) {
    }

    public function create(TransactionTable $widget): DialogConfig
    {
        $initialData = [
            'group_results' => $widget->isGroupResults(),
            'sub_filter' => $widget->getSubFilter() ?? '',
            'transaction_type' => $widget->getTransactionType() ?? '',
            'output_currency_iso_code' => $widget->getOutputCurrencyIsoCode(),
        ];

        $selectableColumns = [];
        /** @var array<string,string|bool|TranslatableMessage> $field */
        foreach ($widget->getFields() as $field) {
            $fieldId = $field['id'];
            \assert(\is_string($fieldId));

            /** @var string|TranslatableMessage $fieldName */
            $fieldName = $field['name'];

            $parameterKey = $widget->buildParameterKeyFromFieldId($fieldId);

            /** @var bool $alwaysEnabled */
            $alwaysEnabled = $field['required'];

            $selectableColumns[] = new Checkbox(
                $parameterKey,
                $fieldName instanceof TranslatableMessage ? $this->translator->trans($fieldName->getMessage(), $fieldName->getParameters(), $fieldName->getDomain()) : $fieldName,
                !$alwaysEnabled
            );

            $initialData[$parameterKey] = $field['selected'];
        }

        return new DialogConfig(
            $this->translator->trans('u2_tpm.transaction_table'),
            [
                new InfoBox(
                    'info',
                    $this->translator->trans('u2.document.widget.transaction_table.help'),
                    'info'
                ),
                /*
                 * The "group_results" checkbox has to be the first element. If the first element is a dropdown or a text field
                 * any checkbox that has to be scrolled into the view will not be clickable.
                 *
                 * See: https://github.com/tinymce/tinymce/issues/5721
                 */
                new Checkbox(
                    'group_results',
                    $this->translator->trans('u2.document_widget.transaction_table.group_results'),
                    true,
                    $this->translator->trans('u2.document_widget.transaction_table.group_results.help')
                ),
                new SelectBox(
                    'transaction_type',
                    $this->translator->trans('u2_tpm.transaction_type'),
                    [
                        new SelectBoxChoice($this->translator->trans('u2_tpm.all_transaction_types'), ''),
                        ...array_map(
                            static fn (TransactionType $type): SelectBoxChoice => new SelectBoxChoice($type->getName(), $type->getName()),
                            $this->transactionTypeRepository->findAll()
                        ),
                    ]
                ),
                new SelectBox(
                    'output_currency_iso_code',
                    $this->translator->trans('u2_core.output_currency'),
                    array_map(
                        static fn (Currency $currency): SelectBoxChoice => new SelectBoxChoice(
                            \sprintf('%s (%s)', $currency->getName(), $currency->getIso4217code()),
                            $currency->getIso4217code()
                        ),
                        $this->currencyRepository->findAll()
                    )
                ),
                new TextareaConfiguration(
                    'sub_filter',
                    $this->translator->trans('u2.widget.transaction_table.sub_filter'),
                    '',
                    $this->translator->trans('u2.widget.transaction_table.sub_filter.help')
                ),
                new FieldSet(
                    $this->translator->trans('u2_tpm.data_to_show'),
                    $selectableColumns
                ),
            ],
            $initialData
        );
    }
}
