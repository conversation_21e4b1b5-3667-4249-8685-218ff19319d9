<?php

declare(strict_types=1);
namespace U2\Widget\Document;

use Symfony\Component\Translation\TranslatableMessage;
use Twig\Environment;
use U2\Entity\Currency;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Exception\DataSourcery\UQLSyntaxError;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Exception\InvalidArgumentValueWidgetException;
use U2\Repository\CurrencyRepository;
use U2\Table\Query\RequestQueryGenerator;
use U2\Table\View\Column\ColumnDefinition;
use U2\TransferPricing\Transaction\DocumentTransactionTableFactory;
use U2\TransferPricing\Transaction\DocumentTransactionTableType;
use U2\Unit\Assignment\UserUnitAssignmentChecker;
use U2\User\CurrentUserProvider;
use U2\Util\ApplicationCurrencyProvider;

class TransactionTable extends AbstractDocumentWidget
{
    private ?string $transactionType = null;

    private ?string $outputCurrencyIsoCode = null;

    private bool $groupResults = true;

    private string $subFilter = '';

    /**
     * @var array<string,array<string,string|bool|TranslatableMessage>>
     */
    private array $fields = [];

    public function __construct(
        Environment $templatingEngine,
        private readonly UserUnitAssignmentChecker $userUnitAssignmentChecker,
        private readonly DocumentTransactionTableFactory $documentTransactionTableFactory,
        private readonly CurrencyRepository $currencyRepository,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly ApplicationCurrencyProvider $applicationCurrencyProvider,
    ) {
        parent::__construct($templatingEngine);
    }

    public function getParameters(): array
    {
        $parameters = [
            'transaction_type' => $this->transactionType,
            'output_currency_iso_code' => $this->getOutputCurrencyIsoCode(),
            'group_results' => $this->groupResults,
            'sub_filter' => $this->subFilter,
        ];

        foreach ($this->getViewConfiguration()->getColumnDefinitions()->all() as $configuration) {
            $configurationId = $configuration->getId();
            \assert(\is_string($configurationId));
            $id = $this->buildParameterKeyFromFieldId($configurationId);
            $parameters[$id] = $this->getFields()[$id]['selected'];
        }

        return $parameters;
    }

    public function getOutputCurrencyIsoCode(): string
    {
        if (null !== $this->outputCurrencyIsoCode) {
            return $this->outputCurrencyIsoCode;
        }

        return $this->applicationCurrencyProvider->get()->getIso4217code();
    }

    public function buildParameterKeyFromFieldId(string $id): string
    {
        return 'show_' . $id;
    }

    /**
     * @return array<string,array<string,string|bool|TranslatableMessage>>
     */
    public function getFields(): array
    {
        if (0 === \count($this->fields)) {
            /** @var ColumnDefinition[] $configs */
            $configs = $this->getViewConfiguration()
                ->getColumnDefinitions()
                ->all();

            foreach ($configs as $config) {
                $configurationId = $config->getId();
                \assert(\is_string($configurationId));

                $this->fields[$this->buildParameterKeyFromFieldId($configurationId)] = [
                    'id' => $configurationId,
                    'name' => $config->getName(),
                    'selected' => $config->isSelectedByDefault(),
                    'required' => $config->isRequired(),
                ];
            }
        }

        return $this->fields;
    }

    public function setParameters(array $parameters): void
    {
        if (\array_key_exists('transaction_type', $parameters)) {
            /** @var string $transactionType */
            $transactionType = $parameters['transaction_type'];
            $this->transactionType = '' === $transactionType ? null : $transactionType;
        }

        if (\array_key_exists('group_results', $parameters)) {
            /** @var string $groupResults */
            $groupResults = $parameters['group_results'];
            $this->groupResults = (bool) $groupResults;
        }

        if (\array_key_exists('output_currency_iso_code', $parameters)) {
            /** @var string $outputCurrencyIsoCode */
            $outputCurrencyIsoCode = $parameters['output_currency_iso_code'];
            $this->outputCurrencyIsoCode = $outputCurrencyIsoCode;
        }

        if (\array_key_exists('sub_filter', $parameters)) {
            /** @var string $subFilter */
            $subFilter = $parameters['sub_filter'];
            $this->subFilter = $subFilter;
        }

        $configs = $this->getViewConfiguration()
            ->getColumnDefinitions()
            ->all();

        foreach ($configs as $config) {
            $configurationId = $config->getId();
            \assert(\is_string($configurationId));

            $id = $this->buildParameterKeyFromFieldId($configurationId);
            /** @var bool $isSelected */
            $isSelected = \array_key_exists($id, $parameters) ? $parameters[$id] : $config->isSelectedByDefault();
            $this->fields[$id] =
                [
                    'id' => $configurationId,
                    'name' => $config->getName(),
                    'selected' => $isSelected,
                    'required' => $config->isRequired(),
                ];
        }
    }

    public function getName(): string
    {
        return 'transaction-table';
    }

    public function isConfigurable(): bool
    {
        return true;
    }

    public function isGroupResults(): bool
    {
        return $this->groupResults;
    }

    public function getSubFilter(): string
    {
        return $this->subFilter;
    }

    public function getTransactionType(): ?string
    {
        return $this->transactionType;
    }

    /**
     * @return array<int,string>
     */
    public function getSelected(): array
    {
        $selectedFields = [];
        foreach ($this->getFields() as $field) {
            $isFieldSelected = $field['selected'];
            \assert(\is_bool($isFieldSelected));
            if ($isFieldSelected) {
                $id = $field['id'];
                \assert(\is_string($id));
                $selectedFields[] = $id;
            }
        }

        return $selectedFields;
    }

    protected function getTemplate(): string
    {
        return 'widget/document/transaction_table.widget.html.twig';
    }

    /**
     * @throws InsufficientPermissionsWidgetException
     * @throws InvalidArgumentValueWidgetException
     */
    protected function getData(array $options): array
    {
        $section = $this->getDocumentSection($options);
        $document = $section->getDocument();
        \assert(
            $document instanceof LocalFile
            || $document instanceof MasterFile
            || $document instanceof CountryByCountryReport
        );

        if (false === $this->userUnitAssignmentChecker->check(
            $this->currentUserProvider->get(),
            $document->getUnits()->toArray()
        )) {
            throw new InsufficientPermissionsWidgetException($this);
        }

        try {
            $transactionTable = $this->documentTransactionTableFactory->create(
                $document,
                $this->applicationCurrencyProvider->get(),
                $this->getOutputCurrency(),
                $this->groupResults,
                $this->transactionType,
                $this->subFilter,
                $this->getSelected()
            );
        } catch (UQLSyntaxError) {
            throw new InvalidArgumentValueWidgetException($this, 'sub_filter');
        }

        $data['transaction_table'] = $transactionTable->getTwigView();
        $data['uql_query_parameter'] = RequestQueryGenerator::QUERY_PARAMETER_UQL;
        $data['uql'] = $transactionTable->getState()->getQuery()->getFilter()->getUql();

        return $data;
    }

    /**
     * @throws InvalidArgumentValueWidgetException
     */
    private function getOutputCurrency(): Currency
    {
        $outputCurrency = $this->currencyRepository->findOneBy(['iso4217code' => $this->getOutputCurrencyIsoCode()]);
        if ($outputCurrency instanceof Currency) {
            return $outputCurrency;
        }

        throw new InvalidArgumentValueWidgetException($this, 'output_currency_iso_code');
    }

    private function getViewConfiguration(): DocumentTransactionTableType
    {
        return new DocumentTransactionTableType(
            $this->applicationCurrencyProvider->get()
        );
    }
}
