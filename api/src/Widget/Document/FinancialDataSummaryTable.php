<?php

declare(strict_types=1);
namespace U2\Widget\Document;

use Twig\Environment;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Exception\DifferentGroupCurrenciesException;
use U2\Exception\InsufficientPermissionsWidgetException;
use U2\Exception\WidgetException;
use U2\TransferPricing\FinancialData\SummaryFactory;
use U2\Unit\Assignment\UserUnitAssignmentChecker;
use U2\User\CurrentUserProvider;

class FinancialDataSummaryTable extends AbstractDocumentWidget
{
    public function __construct(
        Environment $templatingEngine,
        private readonly UserUnitAssignmentChecker $userUnitAssignmentChecker,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly SummaryFactory $summaryFactory,
    ) {
        parent::__construct($templatingEngine);
    }

    public function getParameters(): array
    {
        return [];
    }

    public function setParameters(array $parameters): void
    {
    }

    public function getName(): string
    {
        return 'financial-data-summary-table';
    }

    public function isConfigurable(): bool
    {
        return false;
    }

    protected function getTemplate(): string
    {
        return 'widget/document/financial_data_summary_table.widget.html.twig';
    }

    protected function getData(array $options): array
    {
        $section = $this->getDocumentSection($options);
        $document = $section->getDocument();
        \assert(
            $document instanceof LocalFile
            || $document instanceof MasterFile
            || $document instanceof CountryByCountryReport
        );

        if (false === $this->userUnitAssignmentChecker->check($this->currentUserProvider->get(), $document->getUnits()->toArray())) {
            throw new InsufficientPermissionsWidgetException($this);
        }

        try {
            $financialDataSummary = $this->summaryFactory->create(
                $document->getPeriod(),
                $document->getUnits()
            );
        } catch (DifferentGroupCurrenciesException $e) {
            throw new WidgetException($this, $e->getMessage());
        }

        return [
            'summary' => $financialDataSummary,
        ];
    }
}
