<?php

declare(strict_types=1);
namespace U2\Widget\Editor\TinyMce;

class FieldSet implements \JsonSerializable
{
    public function __construct(private readonly string $label, private readonly array $items)
    {
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => 'label',
            'label' => $this->label,
            'items' => $this->items,
        ];
    }
}
