<?php

declare(strict_types=1);
namespace U2\Widget\Editor\TinyMce;

class Checkbox implements \JsonSerializable
{
    public function __construct(
        private readonly string $name,
        private readonly string $label,
        private readonly bool $enabled = true,
        private readonly string $help = '',
    ) {
    }

    public function jsonSerialize(): array
    {
        return [
            'type' => 'checkbox',
            'name' => $this->name,
            'label' => $this->label,
            'enabled' => $this->enabled,
            'tooltip' => $this->help,
        ];
    }
}
