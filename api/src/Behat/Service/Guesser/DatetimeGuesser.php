<?php

declare(strict_types=1);
namespace U2\Behat\Service\Guesser;

use Doctrine\ORM\Mapping\AssociationMapping;
use Doctrine\ORM\Mapping\FieldMapping;
use Doctrine\ORM\Mapping\ManyToManyInverseSideMapping;
use Doctrine\ORM\Mapping\ManyToManyOwningSideMapping;
use Doctrine\ORM\Mapping\ManyToOneAssociationMapping;
use Doctrine\ORM\Mapping\OneToManyAssociationMapping;
use Doctrine\ORM\Mapping\OneToOneInverseSideMapping;
use Doctrine\ORM\Mapping\OneToOneOwningSideMapping;

class DatetimeGuesser implements GuesserInterface
{
    public function supports(FieldMapping|AssociationMapping $mapping): bool
    {
        if (!$mapping instanceof FieldMapping) {
            return false;
        }

        return \in_array($mapping->type, ['datetime', 'date', 'time'], true);
    }

    /**
     * @throws \Exception
     */
    public function transform(string $str, FieldMapping|OneToOneOwningSideMapping|OneToOneInverseSideMapping|ManyToOneAssociationMapping|OneToManyAssociationMapping|ManyToManyOwningSideMapping|ManyToManyInverseSideMapping|null $mapping): \DateTime
    {
        try {
            return new \DateTime($str);
        } catch (\Exception $e) {
            throw new \Exception(\sprintf('"%s" is not a supported date/time/datetime format. To know which formats are supported, please visit http://www.php.net/manual/en/datetime.formats.php', $str));
        }
    }

    public function fake(FieldMapping|AssociationMapping $mapping): \DateTime
    {
        return new \DateTime();
    }
}
