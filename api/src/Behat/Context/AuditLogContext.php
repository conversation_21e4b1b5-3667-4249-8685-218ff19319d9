<?php

declare(strict_types=1);
namespace U2\Behat\Context;

use Behat\<PERSON>herkin\Node\TableNode;
use Behat\MinkExtension\Context\RawMinkContext;
use Doctrine\ORM\EntityManagerInterface;
use U2\AuditLog\Change\Addition;
use U2\AuditLog\Change\Change;
use U2\AuditLog\Change\Removal;
use U2\AuditLog\EntityIsAuditedChecker;
use U2\AuditLog\LogEntry;
use U2\AuditLog\LogEntryFactory;
use U2\Behat\Service\ResourceHelper;
use U2\Util\StringManipulator;

class AuditLogContext extends RawMinkContext
{
    public function __construct(private readonly ResourceHelper $resourceHelper, private readonly LogEntryFactory $logEntryFactory, private readonly EntityIsAuditedChecker $checker, private readonly EntityManagerInterface $entityManager)
    {
    }

    /**
     * Example:
     *   | field            | oldValue | newValue | timestamp |
     *   | authorizations   |          | new      | 11.01.2020     |
     *   | authorizations   | old      |          | 12.01.2020     |
     *   | Issue locked     | 0        | 1        | 13.01.2020     |
     * It is also possible to skip oldValue, newValue or timestamp column:
     * Example:
     *  | field            | oldValue |
     *  | authorizations   | old      |
     * Example:
     *  | field            | newValue |
     *  | authorizations   | new      |
     * Empty oldValue means addition.
     * Empty newValue means removal.
     * None empty column means change.
     *
     * @Given /^the ([^"]*) where "(.*)" is "(.*)" has the following audit log entries:$/
     *
     * @throws \Exception
     */
    public function addLogToAuditableEntity(string $resourceName, string $property, string $value, TableNode $table): void
    {
        $resourceShortName = StringManipulator::lowerStringAndReplaceSpaceWithDash($resourceName);
        $entity = $this->resourceHelper->findOneBy($resourceShortName, [$property => $value]);

        if (!$this->checker->isEntityAudited($entity)) {
            throw new \Exception('Entity is not Auditable.');
        }

        foreach ($table->getHash() as $row) {
            $change = $this->createChange($row);
            $logEntry = $this->logEntryFactory->create($entity, [$change]);

            if (isset($row['timestamp'])) {
                $this->updateAuditTimestamp($logEntry, new \DateTime($row['timestamp']));
            }

            $this->entityManager->persist($logEntry);
        }
        $this->entityManager->flush();
    }

    /**
     * @throws \Exception
     */
    private function createChange(array $row): Change
    {
        $field = $row['field'];
        $isOldValueEmpty = !isset($row['oldValue']) || '' === $row['oldValue'];
        $isNewValueEmpty = !isset($row['newValue']) || '' === $row['newValue'];

        if (!$isOldValueEmpty && !$isNewValueEmpty) {
            return new Change($field, $row['oldValue'], $row['newValue']);
        }
        if ($isOldValueEmpty && !$isNewValueEmpty) {
            return new Addition($field, $row['newValue']);
        }
        if (!$isOldValueEmpty && $isNewValueEmpty) {
            return new Removal($field, $row['oldValue']);
        }
        throw new \Exception('Wrong audit change configuration: missing values');
    }

    private function updateAuditTimestamp(LogEntry $logEntry, \DateTime $auditTimestamp): void
    {
        $reflection = new \ReflectionClass($logEntry);

        $property = $reflection->getProperty('timestamp');
        $property->setAccessible(true);
        $property->setValue($logEntry, $auditTimestamp);
    }
}
