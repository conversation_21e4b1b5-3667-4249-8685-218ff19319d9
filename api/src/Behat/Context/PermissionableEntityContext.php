<?php

declare(strict_types=1);
namespace U2\Behat\Context;

use Behat\Gherkin\Node\TableNode;
use Behat\MinkExtension\Context\RawMinkContext;
use Doctrine\ORM\EntityManagerInterface;
use U2\Behat\Service\ResourceHelper;
use U2\Behat\Service\SharedStorage;
use U2\Entity\CollectivePermission;
use U2\Entity\User;
use U2\Entity\UserGroup;
use U2\Exception\Exception;
use U2\Repository\UserGroupRepository;
use U2\Repository\UserRepository;
use U2\Security\Permissions\Assignable\Permissions;
use U2\Security\Permissions\MaskCompounder;
use U2\Security\Permissions\PermissionManager;
use U2\Util\StringManipulator;

class PermissionableEntityContext extends RawMinkContext
{
    public function __construct(
        private readonly ResourceHelper $resourceHelper,
        private readonly SharedStorage $sharedStorage,
        private readonly EntityManagerInterface $entityManager,
        private readonly UserRepository $userRepository,
        private readonly UserGroupRepository $userGroupRepository,
    ) {
    }

    /**
     * @Given /^I have (.*) permission to (.+) "([^"]*)"$/
     */
    public function iHaveAccessToResource(string $permissions, string $resourceName, string $value): void
    {
        $user = $this->sharedStorage->get('current-user');
        \assert($user instanceof User);
        $resource = $this->findResource($resourceName, $value);
        \assert($resource instanceof Permissions);

        $this->grantUserAccessToResource(
            $user,
            $this->compoundPermissions($permissions),
            $resource
        );
    }

    /**
     * @Given /^([^"]+) has (.*) permission to (.+) "([^"]*)"$/
     */
    public function userHasAccessToResource(string $userName, string $permissions, string $resourceName, string $value): void
    {
        $user = $this->userRepository->findOneBy(['username' => $userName]);
        \assert(null !== $user);
        $resource = $this->findResource($resourceName, $value);
        \assert($resource instanceof Permissions);

        $this->grantUserAccessToResource(
            $user,
            $this->compoundPermissions($permissions),
            $resource
        );
    }

    /**
     * @Given /^user group "([^"]*)" has (.*) permission to (.+) "([^"]*)"$/
     */
    public function groupHasAccessToResource(string $groupName, string $permissions, string $resourceName, string $value): void
    {
        $group = $this->userGroupRepository->findOneBy(['name' => $groupName]);
        \assert(null !== $group);
        $resource = $this->findResource($resourceName, $value);
        \assert($resource instanceof Permissions);

        $this->grantGroupAccessToResource(
            $group,
            $this->compoundPermissions($permissions),
            $resource
        );
    }

    /**
     * @Given /(.+) "([^"]*)" should have following (user|group) permission table defined:/
     *
     * @throws Exception
     */
    public function resourceShouldHavePermissionTableDefined(string $resourceName, string $value, string $userOrGroup, TableNode $permissionTable): void
    {
        $resource = $this->findResource($resourceName, $value);

        if (!$resource instanceof Permissions) {
            throw new Exception('The resource must be able to handle permissions.');
        }

        $expectedEntityNameToAccessMap = [];
        foreach ($permissionTable->getHash() as $permissionTableItem) {
            $permissionMask = $this->compoundPermissions($permissionTableItem['permissions']);
            $expectedEntityNameToAccessMap[$permissionTableItem['name']] = $permissionMask;
        }

        if ('user' === $userOrGroup) {
            $this->verifyUserPermissionMap($resource->getPermissions(), $expectedEntityNameToAccessMap);
        } else {
            $this->verifyGroupPermissionMap($resource->getPermissions(), $expectedEntityNameToAccessMap);
        }
    }

    private function grantUserAccessToResource(User $user, int $permissionMask, Permissions $resource): void
    {
        PermissionManager::grantUserPermission($resource->getPermissions(), $user, $permissionMask);
        $this->entityManager->flush();
    }

    private function grantGroupAccessToResource(UserGroup $group, int $permissionMask, Permissions $resource): void
    {
        PermissionManager::grantGroupPermission($resource->getPermissions(), $group, $permissionMask);
        $this->entityManager->flush();
    }

    private function verifyUserPermissionMap(CollectivePermission $collectivePermission, array $expectedUserToAccessMap): void
    {
        $actualPermissionMap = [];
        foreach ($collectivePermission->getUserPermissions() as $userPermission) {
            $actualPermissionMap[$userPermission->getUser()->getUserIdentifier()] = $userPermission->getMask();
        }

        $this->verifyPermissionMapMatchesExpected($expectedUserToAccessMap, $actualPermissionMap);
    }

    private function verifyGroupPermissionMap(CollectivePermission $collectivePermission, array $expectedGroupToAccessMap): void
    {
        $actualPermissionMap = [];
        foreach ($collectivePermission->getGroupPermissions() as $groupPermission) {
            $actualPermissionMap[$groupPermission->getGroup()->getName()] = $groupPermission->getMask();
        }

        $this->verifyPermissionMapMatchesExpected($expectedGroupToAccessMap, $actualPermissionMap);
    }

    /**
     * @throws \Exception
     */
    private function verifyPermissionMapMatchesExpected(array $expectedTable, array $actualTable): void
    {
        foreach ($actualTable as $name => $mask) {
            if (!\array_key_exists($name, $expectedTable)) {
                throw new \Exception("$name was not expected to be found in permission table");
            }
            if ($expectedTable[$name] !== $mask) {
                throw new \Exception("$name was expected to have $mask mask but {$expectedTable[$name]} was found");
            }

            unset($expectedTable[$name]);
        }

        $missingPermissionsCount = \count($expectedTable);
        if ($missingPermissionsCount > 0) {
            $names = implode(', ', array_keys($expectedTable));
            throw new \Exception("$missingPermissionsCount permissions for users: " . $names . ' were expected but were not found');
        }
    }

    private function findResource(string $resourceName, string $value): object
    {
        return $this->resourceHelper->findOneBy(
            StringManipulator::lowerStringAndReplaceSpaceWithDash($resourceName),
            ['name' => $value]
        );
    }

    private function compoundPermissions(string $permissionsString): int
    {
        $permissions = explode(',', str_replace('and', ',', $permissionsString));

        return MaskCompounder::compound(
            array_map(
                static fn ($mask): mixed => \constant('\Symfony\Component\Security\Acl\Permission\MaskBuilder::MASK_' . strtoupper(trim($mask))),
                $permissions
            )
        );
    }
}
