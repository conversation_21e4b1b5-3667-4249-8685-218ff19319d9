<?php

declare(strict_types=1);
namespace U2\Behat\Context;

use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ExpectationException;
use Behat\Mink\Mink;
use Behat\Mink\Session;
use Behat\MinkExtension\Context\MinkAwareContext;
use U2\Behat\Service\Utils\Asserter;
use U2\Behat\Service\Utils\TextFormatter;
use U2\Behat\Traits\GenericFunctionsTrait;
use U2\Util\StringManipulator;

class TableContext implements Context, MinkAwareContext
{
    use GenericFunctionsTrait;

    private Mink $mink;

    private ?array $minkParameters = null;

    public function setMink(Mink $mink): void
    {
        $this->mink = $mink;
    }

    public function setMinkParameters(array $parameters): void
    {
        $this->minkParameters = $parameters;
    }

    public function getSession(?string $name = null): Session
    {
        return $this->mink->getSession($name);
    }

    /**
     * @Then /^I should see a table with "([^"]*)" in the "([^"]*)" column$/
     */
    public function iShouldSeeATableWithInTheNamedColumn($list, $column): void
    {
        $cells = array_merge([$column], $this->getFormatter()->listToArray($list));
        $expected = new TableNode(
            array_map(
                static fn ($cell): array => [$cell],
                $cells
            )
        );

        $this->iShouldSeeTheFollowingTable($expected);
    }

    /**
     * @Given /^I should see the following table:?$/
     *
     * @throws \Exception
     */
    public function iShouldSeeTheFollowingTable($expected): void
    {
        if ($expected instanceof TableNode) {
            $expected = $expected->getTable();
        }

        $this->iShouldSeeATable();

        $tables = $this->findTables();
        $exceptions = [];

        foreach ($tables as $table) {
            try {
                if (false === $extraction = $this->extractColumns(current($expected), $table)) {
                    $this->getAsserter()->assertArrayEquals($expected, $table, true);

                    return;
                }
                $this->getAsserter()->assertArrayEquals($expected, $extraction, true);

                return;
            } catch (\Exception $e) {
                $exceptions[] = $e;
            }
        }

        $message = implode(
            "\n",
            array_map(
                static fn (\Exception $e): string => $e->getMessage(),
                $exceptions
            )
        );

        throw new \Exception($message);
    }

    /**
     * @Then /^I should see the following table portion:?$/
     *
     * @throws \Exception
     */
    public function iShouldSeeTheFollowingTablePortion($expected): void
    {
        if ($expected instanceof TableNode) {
            $expected = $this->reorderArrayKeys($expected->getTable());
        }

        $this->iShouldSeeATable();

        $tables = $this->findTables();
        $exceptions = [];

        foreach ($tables as $table) {
            try {
                if (false === $extraction = $this->extractColumns(current($expected), $table)) {
                    $this->getAsserter()->assertArrayContains($expected, $table);

                    return;
                }
                $this->getAsserter()->assertArrayContains($expected, $extraction);

                return;
            } catch (\Exception $e) {
                $exceptions[] = $e;
            }
        }

        $message = implode(
            "\n",
            array_map(
                static fn (\Exception $e): string => $e->getMessage(),
                $exceptions
            )
        );

        throw new \Exception($message);
    }

    /**
     * @Then /^I should see a table with ([^"]*) rows$/
     * @Then /^I should see a table with ([^"]*) row$/
     *
     * @throws \Exception
     */
    public function iShouldSeeATableWithRows($nbr): void
    {
        $nbr = (int) $nbr;

        $this->iShouldSeeATable();
        $exceptions = [];
        $tables = $this->getSession()->getPage()->findAll('css', 'table');

        foreach ($tables as $table) {
            try {
                if (null !== $body = $table->find('css', 'tbody')) {
                    $table = $body;
                }
                $rows = $table->findAll('css', 'tr');
                $this->getAsserter()
                    ->assertEquals($nbr, \count($rows), \sprintf('Table with %s rows expected, table with %s rows found.', $nbr, \count($rows)));

                return;
            } catch (\Exception $e) {
                $exceptions[] = $e;
            }
        }

        $message = implode(
            "\n",
            array_map(
                static fn (\Exception $e): string => $e->getMessage(),
                $exceptions
            )
        );

        throw new \Exception($message);
    }

    /**
     * @Then /^I should see a table$/
     */
    public function iShouldSeeATable(): void
    {
        $this->spin(
            function (): bool {
                $this->getSession()->wait(2000, '0 < document.getElementsByTagName("TABLE").length');

                return true;
            }
        );
    }

    /**
     * @Then /^the "([^"]*)" row in the table "([^"]*)" should contain "([^"]*)" in the column "([^"]*)"$/
     *
     * @throws \Exception
     */
    public function theRowOfTheTableShouldContainTextInTheNamedColumn(string $rowName, string $tableName, string $text, string $columnName): void
    {
        $tableId = StringManipulator::lowerStringAndReplaceSpaceWithDash($tableName);
        $columnIndex = $this->translateColumnNameIntoIndex($columnName, $tableId);
        $rowIndex = $this->translateRowNameIntoIndex($rowName);

        $cols = $this->getColumns($tableId, $rowIndex, $columnIndex);

        $actual = $cols[$columnIndex - 1]->getText();

        $this->assertContains($text, $actual);
    }

    private function extractColumns(array $headers, array $table): bool|array
    {
        if (0 === \count($table) || 0 === \count($headers)) {
            return false;
        }

        $columns = [];
        $tableHeaders = current($table);
        foreach ($headers as $header) {
            $inArray = false;
            foreach ($tableHeaders as $index => $thead) {
                if ($thead === $header) {
                    $columns[] = $index;
                    $inArray = true;
                }
            }
            if (false === $inArray) {
                return false;
            }
        }

        $result = [];
        foreach ($table as $row) {
            $node = [];
            foreach ($row as $index => $value) {
                if (\in_array($index, $columns)) {
                    $node[] = $value;
                }
            }
            $result[] = $node;
        }

        return $result;
    }

    /**
     * @return NodeElement[]
     */
    private function findTables(): array
    {
        $tables = $this->getSession()->getPage()->findAll('css', 'table');
        $result = [];

        foreach ($tables as $table) {
            $nodes = [];

            $head = $table->find('css', 'thead');
            if (null !== $head) {
                $this->extractDataFromPart($head, $nodes);
            }

            $body = $table->find('css', 'tbody');
            if (null !== $body) {
                $this->extractDataFromPart($body, $nodes);
            }

            // $foot = $table->find('css', 'tfoot');
            // if (null !== $foot) {
            //     $this->extractDataFromPart($foot, $node);
            // }

            if (0 === \count($nodes)) {
                $this->extractDataFromPart($table, $nodes);
            }

            $result[] = $nodes;
        }

        return $result;
    }

    private function extractDataFromPart(NodeElement $part, &$array): void
    {
        foreach ($part->findAll('css', 'tr') as $row) {
            $array[] = $this->extractDataFromRow($row);
        }
    }

    private function extractDataFromRow(NodeElement $row): array
    {
        $result = [];
        $elements = array_merge($row->findAll('css', 'th'), $row->findAll('css', 'td'));

        /** @var NodeElement $element */
        foreach ($elements as $element) {
            $result[] = preg_replace('!\s+!', ' ', $element->getText());
        }

        return $result;
    }

    private function reorderArrayKeys(array $subject): array
    {
        $orderedArray = [];

        foreach ($subject as $key => $value) {
            if (\is_int($key)) {
                $orderedArray[] = $value;
            } else {
                $orderedArray[$key] = $value;
            }
        }

        return $orderedArray;
    }

    private function getAsserter(): Asserter
    {
        return new Asserter($this->getFormatter());
    }

    private function getFormatter(): TextFormatter
    {
        return new TextFormatter();
    }

    private function translateRowNameIntoIndex(string $rowName): ?int
    {
        $rowIndexNames = [
            'first' => 1,
            'second' => 2,
            'third' => 3,
            'fourth' => 4,
            'fifth' => 5,
        ];

        $rowIndex = null;
        if (\array_key_exists($rowName, $rowIndexNames)) {
            $rowIndex = $rowIndexNames[$rowName];
        }

        return $rowIndex;
    }

    /**
     * @throws \Exception
     */
    private function translateColumnNameIntoIndex(string $columnName, string $tableId): int
    {
        $table = $this->findTableForId($tableId);

        $head = $table->find('css', 'thead');
        $tableHeaders = $this->extractTableHeaders($head);
        foreach ($tableHeaders as $index => $header) {
            if (str_contains($header, $columnName)) {
                return $index + 1;
            }
        }

        throw new \Exception('Column with name ' . $columnName . ' does not exists in table ' . $tableId . '. Possible values are: ' . implode(', ', $tableHeaders));
    }

    /**
     * @throws ExpectationException
     */
    private function assertContains(string $expected, string $actual, ?string $message = null): void
    {
        $regex = '/' . preg_quote($expected, '/') . '/ui';

        if (!preg_match($regex, $actual)) {
            if (null === $message) {
                $message = \sprintf('The string "%s" was not found.', $expected);
            }
            throw new ExpectationException($message, $this->getSession());
        }
    }

    /**
     * @return NodeElement[]
     */
    private function getRowsFromTable(string $tableId): array
    {
        return $this->findTableForId($tableId)->findAll('css', 'tr');
    }

    /**
     * @throws \Exception
     */
    private function getRow(string $tableId, int $rowIndex): NodeElement
    {
        $rows = $this->getRowsFromTable($tableId);

        $searchedRowExists = isset($rows[$rowIndex]);
        if (!$searchedRowExists) {
            throw new \Exception(\sprintf('The row %d was not found in the %s table', $rowIndex, $tableId));
        }

        return $rows[$rowIndex];
    }

    /**
     * @throws \Exception
     *
     * @return NodeElement[]
     */
    private function getColumns(string $tableId, int $rowIndex, int $columnIndex): array
    {
        $row = $this->getRow($tableId, $rowIndex);
        $cols = $row->findAll('css', 'td');
        if (!isset($cols[$columnIndex - 1])) {
            throw new \Exception(\sprintf('The column %d was not found in the row %d of the %s table', $columnIndex, $rowIndex, $tableId));
        }

        return $cols;
    }

    private function findTableForId(string $tableId): NodeElement
    {
        $elementWithId = $this->getSession()->getPage()->find('css', \sprintf('#%s', $tableId));
        if (null === $elementWithId) {
            throw new \Exception(\sprintf('The table with id "%s" was not found', $tableId));
        }

        return $elementWithId->find('css', 'table');
    }

    private function extractTableHeaders(NodeElement $head): array
    {
        $headerHtml = $head->getHtml();
        preg_match_all('/<th[^<>]*?>(.*?)<\/th>/', $headerHtml, $matches);

        return $matches[1];
    }
}
