<?php

declare(strict_types=1);
namespace U2\DataFixtures\Alice\Processor;

use Fidry\AliceDataFixtures\ProcessorInterface;
use U2\Entity\ItemFormula;
use U2\Form\DataTransformer\ReadableFormulaTransformer;

class ItemFormulaProcessor implements ProcessorInterface
{
    public function __construct(private readonly ReadableFormulaTransformer $readableFormulaTransformer)
    {
    }

    public function preProcess(string $id, object $object): void
    {
        if (!($object instanceof ItemFormula)) {
            return;
        }

        $formulaReadable = $object->getFormulaString();
        $object->setFormulaString($this->readableFormulaTransformer->reverseTransform($formulaReadable));
    }

    public function postProcess(string $id, object $object): void
    {
    }
}
