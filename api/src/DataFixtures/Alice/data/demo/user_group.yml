U2\Entity\UserGroup:

  group (template):
    description: "75%? <realText(100)>"
    units: "<numberBetween(1, 10)>x @unit_*"

  group_super_users:
    name: "Super User Group"
    description: "Super user group containing all permissions."
    users:
      - "@user_admin"
    authorizationProfiles:
      - "@authorization_profile_core_full_access"

  group_test_users:
    name: "Demo User Group"
    description: "Users for demo and testing features"
    users:
      - "@user_admin"

  group_demo_users_accounting (extends group):
    name: "Accounting"

  group_demo_users_customer_service (extends group):
    name: "Customer Service"

  group_demo_users_executives (extends group):
    name: "Executives"
    roles:
      - "ROLE_ADMIN"

  group_demo_users_human_resources (extends group):
    name: "Human Resources"
    roles:
      - "ROLE_USER_GROUP_ADMIN"

  group_demo_users_it (extends group):
    name: "IT Department"

  group_demo_users_logistics (extends group):
    name: "Logistics"

  group_demo_users_marketing (extends group):
    name: "Marketing"

  group_demo_users_public_relations (extends group):
    name: "Public Relations"

  group_demo_users_research (extends group):
    name: "Research"

  group_demo_users_retail (extends group):
    name: "Retail"
