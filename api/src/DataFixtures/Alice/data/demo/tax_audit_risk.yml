U2\Entity\Task\TaskType\TaxAuditRisk:

  tax_audit_risk_{1..20}:
    __factory:
      '@U2\DataFixtures\Example\TaxAuditRiskFactory::getObject':
        - status: "@status_in_progress"
          updatedBy: "@user_*"
          period: "@period_*"
          unit: "@unit_*"
          taxType: "@tax_type_*"
          riskType: "@risk_type_*"
    period: "@period_*"
    createdBy: "@user_*"
    createdAt: '<dateTimeBetween("-2 years", "now")>'
    taxYear: "<periodEndYear($period)>"
    updatedAt: "<dateTimeBetween($createdAt, 'now')>"
    task.reporter: "66%? $createdBy : @user_*"
    task.assignee: "@user_*"
    task.status: "@status_<randomElement(['in_progress', 'in_review', 'accepted', 'rejected'])>"
    task.description: "<realText(100)>"
