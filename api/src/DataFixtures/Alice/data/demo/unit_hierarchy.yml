U2\Entity\UnitHierarchy:

  hierarchy_1:
    description: "<realText(100)>"
    name: "Unit Hierarchy 1"

  hierarchy_2:
    description: "<realText(100)>"
    name: "Unit Hierarchy 2"


U2\Entity\UnitHierarchyDefinition:

  uh_definition_1_{@unit_organisational_group_*}:
    __construct: [ "@hierarchy_1", "<current()>" ]
    startDate: "<dateTimeBetween('-11 years', '-10 year')>"

  uh_definition_1_{@unit_legal_unit_*}:
    __construct: [ "@hierarchy_1", "<current()>" ]
    parent_unit: "@unit_organisational_group_*"
    startDate: "<dateTimeBetween('-10 years', '-5 year')>"
    endDate: "20%? <dateTimeBetween('-2 months', '-1 month')>"

  uh_definition_2_{@unit_organisational_group_*}:
    __construct: [ "@hierarchy_2", "<current()>" ]
    startDate: "<dateTimeBetween('-11 years', '-10 year')>"

  uh_definition_2_{@unit_legal_unit_*}:
    __construct: [ "@hierarchy_2", "<current()>" ]
    parent_unit: "@unit_organisational_group_*"
    startDate: "<dateTimeBetween('-10 years', '-5 year')>"
    endDate: "20%? <dateTimeBetween('-2 months', '-1 month')>"
