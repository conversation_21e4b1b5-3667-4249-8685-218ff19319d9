U2\Entity\Item:

  item (template):
    name: "80%? <sentence(4)>"
    description: "80%? <paragraph(2)>"

  item_TR0110020 (extends item):
    __construct:
      type: percent
    refId: TR0110020
    editable: true
    exchangeMethod: 2

  item_TR0110020P (extends item):
    __construct:
      type: percent
    refId: TR0110020P
    editable: false
    exchangeMethod: 2

  item_TR0120020 (extends item):
    __construct:
      type: percent
    refId: TR0120020
    editable: true
    exchangeMethod: 2

  item_TR0120020P (extends item):
    __construct:
      type: percent
    refId: TR0120020P
    editable: false
    exchangeMethod: 2

  item_TR0110010 (extends item):
    __construct:
      type: percent
    refId: TR0110010
    editable: true
    exchangeMethod: 2

  item_TR0120010 (extends item):
    __construct:
      type: percent
    refId: TR0120010
    editable: true
    exchangeMethod: 2

  item_TR0130010 (extends item):
    __construct:
      type: percent
    refId: TR0130010
    editable: false
    exchangeMethod: 2

  item_TR0130020 (extends item):
    __construct:
      type: percent
    refId: TR0130020
    editable: false
    exchangeMethod: 2

  item_TR0130020P (extends item):
    __construct:
      type: percent
    refId: TR0130020P
    editable: false
    exchangeMethod: 2

  item_TR0140020 (extends item):
    __construct:
      type: percent
    refId: TR0140020
    editable: true
    exchangeMethod: 2

  item_TR0140020P (extends item):
    __construct:
      type: percent
    refId: TR0140020P
    editable: false
    exchangeMethod: 2

  item_TR0180010 (extends item):
    __construct:
      type: percent
    refId: TR0180010
    editable: false
    exchangeMethod: 2

  item_TR0140010 (extends item):
    __construct:
      type: percent
    refId: TR0140010
    editable: true
    exchangeMethod: 2

  item_TR0150010 (extends item):
    __construct:
      type: percent
    refId: TR0150010
    editable: false
    exchangeMethod: 2

  item_TR0180020 (extends item):
    __construct:
      type: percent
    refId: TR0180020
    editable: false
    exchangeMethod: 2

  item_TR0150020 (extends item):
    __construct:
      type: percent
    refId: TR0150020
    editable: false
    exchangeMethod: 2

  item_TR0150020P (extends item):
    __construct:
      type: percent
    refId: TR0150020P
    editable: false
    exchangeMethod: 2

  item_TX0160010 (extends item):
    __construct:
      type: checkbox
    refId: TX0160010
    editable: true
    exchangeMethod: 2

  item_TX0160020 (extends item):
    __construct:
      type: checkbox
    refId: TX0160020
    editable: true
    exchangeMethod: 2

  item_TR0180020P (extends item):
    __construct:
      type: percent
    refId: TR0180020P
    editable: false
    exchangeMethod: 2

  item_TR0190010 (extends item):
    __construct:
      type: percent
    refId: TR0190010
    editable: false
    exchangeMethod: 2

  item_TR0190020 (extends item):
    __construct:
      type: percent
    refId: TR0190020
    editable: false
    exchangeMethod: 2

  item_TR0190020P (extends item):
    __construct:
      type: percent
    refId: TR0190020P
    editable: false
    exchangeMethod: 2

  item_TX3818010 (extends item):
    __construct:
      type: money
    refId: TX3818010
    editable: false
    exchangeMethod: 2

  item_TX3110020 (extends item):
    __construct:
      type: money
    refId: TX3110020
    editable: true
    exchangeMethod: 1

  item_TR3819010 (extends item):
    __construct:
      type: percent
    refId: TR3819010
    editable: false
    exchangeMethod: 2

  item_TX3910020 (extends item):
    __construct:
      type: money
    refId: TX3910020
    editable: true
    exchangeMethod: 1

  item_TX3923020 (extends item):
    __construct:
      type: money
    refId: TX3923020
    editable: false
    exchangeMethod: 1

  item_TR3910010 (extends item):
    __construct:
      type: percent
    refId: TR3910010
    editable: false
    exchangeMethod: 2

  item_TX02801010 (extends item):
    __construct:
      type: money
    refId: TX02801010
    editable: false
    exchangeMethod: 2

  item_TX02802010 (extends item):
    __construct:
      type: money
    refId: TX02802010
    editable: false
    exchangeMethod: 2

  item_TX02803010 (extends item):
    __construct:
      type: money
    refId: TX02803010
    editable: false
    exchangeMethod: 2

  item_TX02801020 (extends item):
    __construct:
      type: money
    refId: TX02801020
    editable: false
    exchangeMethod: 2

  item_TX02802020 (extends item):
    __construct:
      type: money
    refId: TX02802020
    editable: false
    exchangeMethod: 2

  item_TX02803020 (extends item):
    __construct:
      type: money
    refId: TX02803020
    editable: false
    exchangeMethod: 2

  item_TX02840010 (extends item):
    __construct:
      type: money
    refId: TX02840010
    editable: false
    exchangeMethod: 2

  item_TX02805010 (extends item):
    __construct:
      type: money
    refId: TX02805010
    editable: false
    exchangeMethod: 2

  item_TX02840020 (extends item):
    __construct:
      type: money
    refId: TX02840020
    editable: false
    exchangeMethod: 2

  item_TX02805020 (extends item):
    __construct:
      type: money
    refId: TX02805020
    editable: false
    exchangeMethod: 2

  item_TX02810010 (extends item):
    __construct:
      type: money
    refId: TX02810010
    editable: true
    exchangeMethod: 2

  item_TX02820010 (extends item):
    __construct:
      type: money
    refId: TX02820010
    editable: true
    exchangeMethod: 2

  item_TX02830010 (extends item):
    __construct:
      type: money
    refId: TX02830010
    editable: false
    exchangeMethod: 2

  item_TX02810020 (extends item):
    __construct:
      type: money
    refId: TX02810020
    editable: true
    exchangeMethod: 2

  item_TX02820020 (extends item):
    __construct:
      type: money
    refId: TX02820020
    editable: true
    exchangeMethod: 2

  item_TX02830020 (extends item):
    __construct:
      type: money
    refId: TX02830020
    editable: false
    exchangeMethod: 2

  item_TX02831010 (extends item):
    __construct:
      type: money
    refId: TX02831010
    editable: true
    exchangeMethod: 1

  item_TX02831010_comment (extends item):
    __construct:
      type: text
    refId: TX02831010_comment
    editable: true

  item_TX02840010P (extends item):
    __construct:
      type: money
    refId: TX02840010P
    editable: false
    exchangeMethod: 4

  item_TX02831020 (extends item):
    __construct:
      type: money
    refId: TX02831020
    editable: true
    exchangeMethod: 1

  item_TX02840020P (extends item):
    __construct:
      type: money
    refId: TX02840020P
    editable: false
    exchangeMethod: 4

  item_TX02850010 (extends item):
    __construct:
      type: money
    refId: TX02850010
    editable: false
    exchangeMethod: 2

  item_TX02850020 (extends item):
    __construct:
      type: money
    refId: TX02850020
    editable: false
    exchangeMethod: 2

  item_TX02870010 (extends item):
    __construct:
      type: money
    refId: TX02870010
    editable: false
    exchangeMethod: 1

  item_TX02880010 (extends item):
    __construct:
      type: money
    refId: TX02880010
    editable: false
    exchangeMethod: 2

  item_TX02880010P (extends item):
    __construct:
      type: money
    refId: TX02880010P
    editable: false
    exchangeMethod: 4

  item_TX3110030 (extends item):
    __construct:
      type: money
    refId: TX3110030
    editable: true
    exchangeMethod: 1

  item_TX3111030 (extends item):
    __construct:
      type: money
    refId: TX3111030
    editable: true
    exchangeMethod: 1

  item_TX3112030 (extends item):
    __construct:
      type: money
    refId: TX3112030
    editable: true
    exchangeMethod: 1

  item_TX3113030 (extends item):
    __construct:
      type: money
    refId: TX3113030
    editable: true
    exchangeMethod: 1

  item_TX3114030 (extends item):
    __construct:
      type: money
    refId: TX3114030
    editable: false
    exchangeMethod: 1

  item_TX3115020 (extends item):
    __construct:
      type: money
    refId: TX3115020
    editable: true
    exchangeMethod: 1

  item_TX3115021 (extends item):
    __construct:
      type: money
    refId: TX3115021
    editable: true
    exchangeMethod: 1

  item_TX3115022 (extends item):
    __construct:
      type: money
    refId: TX3115022
    editable: true
    exchangeMethod: 1

  item_TX3115023 (extends item):
    __construct:
      type: money
    refId: TX3115023
    editable: true
    exchangeMethod: 1

  item_TX3115024 (extends item):
    __construct:
      type: money
    refId: TX3115024
    editable: true
    exchangeMethod: 1

  item_TX3115025 (extends item):
    __construct:
      type: money
    refId: TX3115025
    editable: true
    exchangeMethod: 1

  item_TX3115026 (extends item):
    __construct:
      type: money
    refId: TX3115026
    editable: true
    exchangeMethod: 1

  item_TX3115027 (extends item):
    __construct:
      type: money
    refId: TX3115027
    editable: true
    exchangeMethod: 1

  item_TX3115028 (extends item):
    __construct:
      type: money
    refId: TX3115028
    editable: true
    exchangeMethod: 1

  item_TX3115029 (extends item):
    __construct:
      type: money
    refId: TX3115029
    editable: true
    exchangeMethod: 1

  item_TX3115121 (extends item):
    __construct:
      type: money
    refId: TX3115121
    editable: true
    exchangeMethod: 1

  item_TX3115122 (extends item):
    __construct:
      type: money
    refId: TX3115122
    editable: true
    exchangeMethod: 1

  item_TX3116020 (extends item):
    __construct:
      type: money
    refId: TX3116020
    editable: true
    exchangeMethod: 1

  item_TX3116020_comment (extends item):
    __construct:
      type: text
    refId: TX3116020_comment
    editable: true

  item_TX3116021 (extends item):
    __construct:
      type: money
    refId: TX3116021
    editable: true
    exchangeMethod: 1

  item_TX3116021_comment (extends item):
    __construct:
      type: text
    refId: TX3116021_comment
    editable: true

  item_TX3117020 (extends item):
    __construct:
      type: money
    refId: TX3117020
    editable: false
    exchangeMethod: 1

  item_TX3118020 (extends item):
    __construct:
      type: money
    refId: TX3118020
    editable: true
    exchangeMethod: 1

  item_TX3118030 (extends item):
    __construct:
      type: money
    refId: TX3118030
    editable: false
    exchangeMethod: 1

  item_TX3118021 (extends item):
    __construct:
      type: money
    refId: TX3118021
    editable: true
    exchangeMethod: 1

  item_TX3118031 (extends item):
    __construct:
      type: money
    refId: TX3118031
    editable: false
    exchangeMethod: 1

  item_TX3118022 (extends item):
    __construct:
      type: money
    refId: TX3118022
    editable: true
    exchangeMethod: 1

  item_TX3118032 (extends item):
    __construct:
      type: money
    refId: TX3118032
    editable: false
    exchangeMethod: 1

  item_TX3118023 (extends item):
    __construct:
      type: money
    refId: TX3118023
    editable: true
    exchangeMethod: 1

  item_TX3118033 (extends item):
    __construct:
      type: money
    refId: TX3118033
    editable: false
    exchangeMethod: 1

  item_TX3118024 (extends item):
    __construct:
      type: money
    refId: TX3118024
    editable: true
    exchangeMethod: 1

  item_TX3118034 (extends item):
    __construct:
      type: money
    refId: TX3118034
    editable: false
    exchangeMethod: 1

  item_TX3118025 (extends item):
    __construct:
      type: money
    refId: TX3118025
    editable: true
    exchangeMethod: 1

  item_TX3118035 (extends item):
    __construct:
      type: money
    refId: TX3118035
    editable: false
    exchangeMethod: 1

  item_TX3118026 (extends item):
    __construct:
      type: money
    refId: TX3118026
    editable: true
    exchangeMethod: 1

  item_TX3118036 (extends item):
    __construct:
      type: money
    refId: TX3118036
    editable: false
    exchangeMethod: 1

  item_TX3118027 (extends item):
    __construct:
      type: money
    refId: TX3118027
    editable: true
    exchangeMethod: 1

  item_TX3118037 (extends item):
    __construct:
      type: money
    refId: TX3118037
    editable: false
    exchangeMethod: 1

  item_TX3118028 (extends item):
    __construct:
      type: money
    refId: TX3118028
    editable: true
    exchangeMethod: 1

  item_TX3118038 (extends item):
    __construct:
      type: money
    refId: TX3118038
    editable: false
    exchangeMethod: 1

  item_TX3119020 (extends item):
    __construct:
      type: money
    refId: TX3119020
    editable: true
    exchangeMethod: 1

  item_TX3119020_comment (extends item):
    __construct:
      type: text
    refId: TX3119020_comment
    editable: true

  item_TX3119030 (extends item):
    __construct:
      type: money
    refId: TX3119030
    editable: false
    exchangeMethod: 1

  item_TX3119021 (extends item):
    __construct:
      type: money
    refId: TX3119021
    editable: true
    exchangeMethod: 1

  item_TX3119021_comment (extends item):
    __construct:
      type: text
    refId: TX3119021_comment
    editable: true

  item_TX3119031 (extends item):
    __construct:
      type: money
    refId: TX3119031
    editable: false
    exchangeMethod: 1

  item_TX3120020 (extends item):
    __construct:
      type: money
    refId: TX3120020
    editable: false
    exchangeMethod: 1

  item_TX3120021 (extends item):
    __construct:
      type: money
    refId: TX3120021
    editable: true
    exchangeMethod: 1

  item_TX3120022 (extends item):
    __construct:
      type: money
    refId: TX3120022
    editable: true
    exchangeMethod: 1

  item_TX3120023 (extends item):
    __construct:
      type: money
    refId: TX3120023
    editable: false
    exchangeMethod: 1

  item_TX3120024 (extends item):
    __construct:
      type: money
    refId: TX3120024
    editable: true
    exchangeMethod: 1

  item_TX3120025 (extends item):
    __construct:
      type: money
    refId: TX3120025
    editable: true
    exchangeMethod: 1

  item_TX3120026 (extends item):
    __construct:
      type: money
    refId: TX3120026
    editable: false
    exchangeMethod: 1

  item_TX3120030 (extends item):
    __construct:
      type: money
    refId: TX3120030
    editable: false
    exchangeMethod: 1

  item_TX3120031 (extends item):
    __construct:
      type: money
    refId: TX3120031
    editable: false
    exchangeMethod: 1

  item_TX3120032 (extends item):
    __construct:
      type: money
    refId: TX3120032
    editable: false
    exchangeMethod: 1

  item_TX3120033 (extends item):
    __construct:
      type: money
    refId: TX3120033
    editable: false
    exchangeMethod: 1

  item_TX3120036 (extends item):
    __construct:
      type: money
    refId: TX3120036
    editable: false
    exchangeMethod: 1

  item_TX3117030 (extends item):
    __construct:
      type: money
    refId: TX3117030
    editable: true
    exchangeMethod: 1

  item_TX3121030 (extends item):
    __construct:
      type: money
    refId: TX3121030
    editable: false
    exchangeMethod: 1

  item_TX3219030 (extends item):
    __construct:
      type: money
    refId: TX3219030
    editable: false
    exchangeMethod: 1

  item_TX3122020 (extends item):
    __construct:
      type: money
    refId: TX3122020
    editable: false
    exchangeMethod: 1

  item_TX3129030 (extends item):
    __construct:
      type: money
    refId: TX3129030
    editable: false
    exchangeMethod: 1

  item_TX3126020 (extends item):
    __construct:
      type: money
    refId: TX3126020
    editable: false
    exchangeMethod: 1

  item_TX3122030 (extends item):
    __construct:
      type: money
    refId: TX3122030
    editable: true
    exchangeMethod: 1

  item_TX3123030 (extends item):
    __construct:
      type: money
    refId: TX3123030
    editable: true
    exchangeMethod: 1

  item_TX3124030 (extends item):
    __construct:
      type: money
    refId: TX3124030
    editable: true
    exchangeMethod: 1

  item_TX3125030 (extends item):
    __construct:
      type: money
    refId: TX3125030
    editable: true
    exchangeMethod: 1

  item_TX3126030 (extends item):
    __construct:
      type: money
    refId: TX3126030
    editable: false
    exchangeMethod: 1

  item_TX3127020 (extends item):
    __construct:
      type: money
    refId: TX3127020
    editable: true
    exchangeMethod: 1

  item_TX3128020 (extends item):
    __construct:
      type: money
    refId: TX3128020
    editable: false
    exchangeMethod: 1

  item_TR0191010 (extends item):
    __construct:
      type: percent
    refId: TR0191010
    editable: true
    exchangeMethod: 2

  item_TR0192010 (extends item):
    __construct:
      type: percent
    refId: TR0192010
    editable: true
    exchangeMethod: 2

  item_TX3128021 (extends item):
    __construct:
      type: money
    refId: TX3128021
    editable: false
    exchangeMethod: 1

  item_TX3127030 (extends item):
    __construct:
      type: money
    refId: TX3127030
    editable: true
    exchangeMethod: 1

  item_TX3125031 (extends item):
    __construct:
      type: money
    refId: TX3125031
    editable: true
    exchangeMethod: 1

  item_TX3128030 (extends item):
    __construct:
      type: money
    refId: TX3128030
    editable: false
    exchangeMethod: 1

  item_TR3120010 (extends item):
    __construct:
      type: percent
    refId: TR3120010
    editable: true
    exchangeMethod: 2

  item_TX3129020 (extends item):
    __construct:
      type: money
    refId: TX3129020
    editable: true
    exchangeMethod: 1

  item_TX3130020 (extends item):
    __construct:
      type: money
    refId: TX3130020
    editable: true
    exchangeMethod: 1

  item_TX3130021 (extends item):
    __construct:
      type: money
    refId: TX3130021
    editable: false
    exchangeMethod: 1

  item_TX3130022 (extends item):
    __construct:
      type: money
    refId: TX3130022
    editable: true
    exchangeMethod: 1

  item_TX3130023 (extends item):
    __construct:
      type: money
    refId: TX3130023
    editable: true
    exchangeMethod: 1

  item_TX3130024 (extends item):
    __construct:
      type: money
    refId: TX3130024
    editable: true
    exchangeMethod: 1

  item_TX3130025 (extends item):
    __construct:
      type: money
    refId: TX3130025
    editable: false
    exchangeMethod: 1

  item_TR3130010 (extends item):
    __construct:
      type: percent
    refId: TR3130010
    editable: true
    exchangeMethod: 2

  item_TX3130030 (extends item):
    __construct:
      type: money
    refId: TX3130030
    editable: false
    exchangeMethod: 1

  item_TX3132032 (extends item):
    __construct:
      type: money
    refId: TX3132032
    editable: false
    exchangeMethod: 1

  item_TX3132033 (extends item):
    __construct:
      type: money
    refId: TX3132033
    editable: false
    exchangeMethod: 1

  item_TX3132034 (extends item):
    __construct:
      type: money
    refId: TX3132034
    editable: false
    exchangeMethod: 1

  item_TX3132035 (extends item):
    __construct:
      type: money
    refId: TX3132035
    editable: false
    exchangeMethod: 1

  item_TX3130031 (extends item):
    __construct:
      type: money
    refId: TX3130031
    editable: true
    exchangeMethod: 1

  item_TX3132031 (extends item):
    __construct:
      type: money
    refId: TX3132031
    editable: true
    exchangeMethod: 1

  item_TX3133030 (extends item):
    __construct:
      type: money
    refId: TX3133030
    editable: false
    exchangeMethod: 1

  item_TX3210010 (extends item):
    __construct:
      type: money
    refId: TX3210010
    editable: false
    exchangeMethod: 1

  item_TX3250020 (extends item):
    __construct:
      type: money
    refId: TX3250020
    editable: false
    exchangeMethod: 1

  item_TX3212030 (extends item):
    __construct:
      type: money
    refId: TX3212030
    editable: false
    exchangeMethod: 1

  item_TX3210030 (extends item):
    __construct:
      type: money
    refId: TX3210030
    editable: true
    exchangeMethod: 1

  item_TX3211030 (extends item):
    __construct:
      type: money
    refId: TX3211030
    editable: true
    exchangeMethod: 1

  item_TX3213030 (extends item):
    __construct:
      type: money
    refId: TX3213030
    editable: true
    exchangeMethod: 1

  item_TX3214030 (extends item):
    __construct:
      type: money
    refId: TX3214030
    editable: false
    exchangeMethod: 1

  item_TX3290020 (extends item):
    __construct:
      type: money
    refId: TX3290020
    editable: true
    exchangeMethod: 1

  item_TX3217030 (extends item):
    __construct:
      type: money
    refId: TX3217030
    editable: false
    exchangeMethod: 1

  item_TX3215030 (extends item):
    __construct:
      type: money
    refId: TX3215030
    editable: true
    exchangeMethod: 1

  item_TX3216030 (extends item):
    __construct:
      type: money
    refId: TX3216030
    editable: true
    exchangeMethod: 1

  item_TX3218030 (extends item):
    __construct:
      type: money
    refId: TX3218030
    editable: false
    exchangeMethod: 1

  item_TR3230010 (extends item):
    __construct:
      type: percent
    refId: TR3230010
    editable: true
    exchangeMethod: 2

  item_TX3220020 (extends item):
    __construct:
      type: money
    refId: TX3220020
    editable: true
    exchangeMethod: 1

  item_TX3220020_comment (extends item):
    __construct:
      type: text
    refId: TX3220020_comment
    editable: true

  item_TX3220021 (extends item):
    __construct:
      type: money
    refId: TX3220021
    editable: true
    exchangeMethod: 1

  item_TX3220021_comment (extends item):
    __construct:
      type: text
    refId: TX3220021_comment
    editable: true

  item_TX3220022 (extends item):
    __construct:
      type: money
    refId: TX3220022
    editable: true
    exchangeMethod: 1

  item_TX3220022_comment (extends item):
    __construct:
      type: text
    refId: TX3220022_comment
    editable: true

  item_TX3230020 (extends item):
    __construct:
      type: money
    refId: TX3230020
    editable: false
    exchangeMethod: 1

  item_TX3240020 (extends item):
    __construct:
      type: money
    refId: TX3240020
    editable: true
    exchangeMethod: 1

  item_TX3240020_comment (extends item):
    __construct:
      type: text
    refId: TX3240020_comment
    editable: true

  item_TX3240021 (extends item):
    __construct:
      type: money
    refId: TX3240021
    editable: true
    exchangeMethod: 1

  item_TX3240021_comment (extends item):
    __construct:
      type: text
    refId: TX3240021_comment
    editable: true

  item_TX3240022 (extends item):
    __construct:
      type: money
    refId: TX3240022
    editable: true
    exchangeMethod: 1

  item_TX3240022_comment (extends item):
    __construct:
      type: text
    refId: TX3240022_comment
    editable: true

  item_TX3317010 (extends item):
    __construct:
      type: money
    refId: TX3317010
    editable: false
    exchangeMethod: 2

  item_TX3311010 (extends item):
    __construct:
      type: money
    refId: TX3311010
    editable: true
    exchangeMethod: 2

  item_TX3312010 (extends item):
    __construct:
      type: money
    refId: TX3312010
    editable: true
    exchangeMethod: 2

  item_TX3312010_comment (extends item):
    __construct:
      type: text
    refId: TX3312010_comment
    editable: true

  item_TX3313010 (extends item):
    __construct:
      type: money
    refId: TX3313010
    editable: false
    exchangeMethod: 2

  item_TX3317020 (extends item):
    __construct:
      type: money
    refId: TX3317020
    editable: false
    exchangeMethod: 2

  item_TX3311020 (extends item):
    __construct:
      type: money
    refId: TX3311020
    editable: true
    exchangeMethod: 2

  item_TX3312020 (extends item):
    __construct:
      type: money
    refId: TX3312020
    editable: true
    exchangeMethod: 2

  item_TX3313020 (extends item):
    __construct:
      type: money
    refId: TX3313020
    editable: false
    exchangeMethod: 2

  item_TX3317030 (extends item):
    __construct:
      type: money
    refId: TX3317030
    editable: false
    exchangeMethod: 2

  item_TX3311030 (extends item):
    __construct:
      type: money
    refId: TX3311030
    editable: true
    exchangeMethod: 2

  item_TX3312030 (extends item):
    __construct:
      type: money
    refId: TX3312030
    editable: true
    exchangeMethod: 2

  item_TX3313030 (extends item):
    __construct:
      type: money
    refId: TX3313030
    editable: false
    exchangeMethod: 2

  item_TX3317040 (extends item):
    __construct:
      type: money
    refId: TX3317040
    editable: false
    exchangeMethod: 2

  item_TX3311040 (extends item):
    __construct:
      type: money
    refId: TX3311040
    editable: true
    exchangeMethod: 2

  item_TX3312040 (extends item):
    __construct:
      type: money
    refId: TX3312040
    editable: true
    exchangeMethod: 2

  item_TX3313040 (extends item):
    __construct:
      type: money
    refId: TX3313040
    editable: false
    exchangeMethod: 2

  item_TX3315010 (extends item):
    __construct:
      type: money
    refId: TX3315010
    editable: false
    exchangeMethod: 2

  item_TX3315020 (extends item):
    __construct:
      type: money
    refId: TX3315020
    editable: false
    exchangeMethod: 2

  item_TX3314010 (extends item):
    __construct:
      type: money
    refId: TX3314010
    editable: true
    exchangeMethod: 2

  item_TX3316010 (extends item):
    __construct:
      type: money
    refId: TX3316010
    editable: true
    exchangeMethod: 2

  item_TX3317010P (extends item):
    __construct:
      type: money
    refId: TX3317010P
    editable: false
    exchangeMethod: 4

  item_TX3314020 (extends item):
    __construct:
      type: money
    refId: TX3314020
    editable: true
    exchangeMethod: 2

  item_TX3316020 (extends item):
    __construct:
      type: money
    refId: TX3316020
    editable: true
    exchangeMethod: 2

  item_TX3317020P (extends item):
    __construct:
      type: money
    refId: TX3317020P
    editable: false
    exchangeMethod: 4

  item_TX3314030 (extends item):
    __construct:
      type: money
    refId: TX3314030
    editable: true
    exchangeMethod: 2

  item_TX3315030 (extends item):
    __construct:
      type: money
    refId: TX3315030
    editable: true
    exchangeMethod: 2

  item_TX3316030 (extends item):
    __construct:
      type: money
    refId: TX3316030
    editable: true
    exchangeMethod: 2

  item_TX3317030P (extends item):
    __construct:
      type: money
    refId: TX3317030P
    editable: false
    exchangeMethod: 4

  item_TX3314040 (extends item):
    __construct:
      type: money
    refId: TX3314040
    editable: true
    exchangeMethod: 2

  item_TX3315040 (extends item):
    __construct:
      type: money
    refId: TX3315040
    editable: true
    exchangeMethod: 2

  item_TX3316040 (extends item):
    __construct:
      type: money
    refId: TX3316040
    editable: true
    exchangeMethod: 2

  item_TX3317040P (extends item):
    __construct:
      type: money
    refId: TX3317040P
    editable: false
    exchangeMethod: 4

  item_TX3320010 (extends item):
    __construct:
      type: money
    refId: TX3320010
    editable: false
    exchangeMethod: 2

  item_TX3319010 (extends item):
    __construct:
      type: money
    refId: TX3319010
    editable: true
    exchangeMethod: 1

  item_TX3319010_comment (extends item):
    __construct:
      type: text
    refId: TX3319010_comment
    editable: true

  item_TX3320010P (extends item):
    __construct:
      type: money
    refId: TX3320010P
    editable: false
    exchangeMethod: 4

  item_TX3320020 (extends item):
    __construct:
      type: money
    refId: TX3320020
    editable: false
    exchangeMethod: 2

  item_TX3319020 (extends item):
    __construct:
      type: money
    refId: TX3319020
    editable: true
    exchangeMethod: 1

  item_TX3320020P (extends item):
    __construct:
      type: money
    refId: TX3320020P
    editable: false
    exchangeMethod: 4

  item_TX3320030 (extends item):
    __construct:
      type: money
    refId: TX3320030
    editable: false
    exchangeMethod: 2

  item_TX3319030 (extends item):
    __construct:
      type: money
    refId: TX3319030
    editable: true
    exchangeMethod: 1

  item_TX3320030P (extends item):
    __construct:
      type: money
    refId: TX3320030P
    editable: false
    exchangeMethod: 4

  item_TX3320040 (extends item):
    __construct:
      type: money
    refId: TX3320040
    editable: false
    exchangeMethod: 2

  item_TX3319040 (extends item):
    __construct:
      type: money
    refId: TX3319040
    editable: true
    exchangeMethod: 1

  item_TX3320040P (extends item):
    __construct:
      type: money
    refId: TX3320040P
    editable: false
    exchangeMethod: 4

  item_TX3321010 (extends item):
    __construct:
      type: money
    refId: TX3321010
    editable: false
    exchangeMethod: 2

  item_TX3321020 (extends item):
    __construct:
      type: money
    refId: TX3321020
    editable: false
    exchangeMethod: 2

  item_TX3321030 (extends item):
    __construct:
      type: money
    refId: TX3321030
    editable: false
    exchangeMethod: 2

  item_TX3321040 (extends item):
    __construct:
      type: money
    refId: TX3321040
    editable: false
    exchangeMethod: 2

  item_TX3323010 (extends item):
    __construct:
      type: money
    refId: TX3323010
    editable: true
    exchangeMethod: 2

  item_TX3324010 (extends item):
    __construct:
      type: money
    refId: TX3324010
    editable: true
    exchangeMethod: 2

  item_TX3325010 (extends item):
    __construct:
      type: money
    refId: TX3325010
    editable: true
    exchangeMethod: 2

  item_TX3326010 (extends item):
    __construct:
      type: money
    refId: TX3326010
    editable: true
    exchangeMethod: 2

  item_TX3327010 (extends item):
    __construct:
      type: money
    refId: TX3327010
    editable: true
    exchangeMethod: 2

  item_TX3328010 (extends item):
    __construct:
      type: money
    refId: TX3328010
    editable: true
    exchangeMethod: 2

  item_TX3329010 (extends item):
    __construct:
      type: money
    refId: TX3329010
    editable: true
    exchangeMethod: 2

  item_TX3330010 (extends item):
    __construct:
      type: money
    refId: TX3330010
    editable: true
    exchangeMethod: 2

  item_TX3331010 (extends item):
    __construct:
      type: money
    refId: TX3331010
    editable: true
    exchangeMethod: 2

  item_TX3332010 (extends item):
    __construct:
      type: money
    refId: TX3332010
    editable: true
    exchangeMethod: 2

  item_TX3333010 (extends item):
    __construct:
      type: money
    refId: TX3333010
    editable: true
    exchangeMethod: 2

  item_TX3334010 (extends item):
    __construct:
      type: money
    refId: TX3334010
    editable: false
    exchangeMethod: 2

  item_TX3323020 (extends item):
    __construct:
      type: money
    refId: TX3323020
    editable: true
    exchangeMethod: 2

  item_TX3324020 (extends item):
    __construct:
      type: money
    refId: TX3324020
    editable: true
    exchangeMethod: 2

  item_TX3325020 (extends item):
    __construct:
      type: money
    refId: TX3325020
    editable: true
    exchangeMethod: 2

  item_TX3326020 (extends item):
    __construct:
      type: money
    refId: TX3326020
    editable: true
    exchangeMethod: 2

  item_TX3327020 (extends item):
    __construct:
      type: money
    refId: TX3327020
    editable: true
    exchangeMethod: 2

  item_TX3328020 (extends item):
    __construct:
      type: money
    refId: TX3328020
    editable: true
    exchangeMethod: 2

  item_TX3329020 (extends item):
    __construct:
      type: money
    refId: TX3329020
    editable: true
    exchangeMethod: 2

  item_TX3330020 (extends item):
    __construct:
      type: money
    refId: TX3330020
    editable: true
    exchangeMethod: 2

  item_TX3331020 (extends item):
    __construct:
      type: money
    refId: TX3331020
    editable: true
    exchangeMethod: 2

  item_TX3332020 (extends item):
    __construct:
      type: money
    refId: TX3332020
    editable: true
    exchangeMethod: 2

  item_TX3333020 (extends item):
    __construct:
      type: money
    refId: TX3333020
    editable: true
    exchangeMethod: 2

  item_TX3334020 (extends item):
    __construct:
      type: money
    refId: TX3334020
    editable: false
    exchangeMethod: 2

  item_TX3323030 (extends item):
    __construct:
      type: money
    refId: TX3323030
    editable: true
    exchangeMethod: 2

  item_TX3324030 (extends item):
    __construct:
      type: money
    refId: TX3324030
    editable: true
    exchangeMethod: 2

  item_TX3325030 (extends item):
    __construct:
      type: money
    refId: TX3325030
    editable: true
    exchangeMethod: 2

  item_TX3326030 (extends item):
    __construct:
      type: money
    refId: TX3326030
    editable: true
    exchangeMethod: 2

  item_TX3327030 (extends item):
    __construct:
      type: money
    refId: TX3327030
    editable: true
    exchangeMethod: 2

  item_TX3328030 (extends item):
    __construct:
      type: money
    refId: TX3328030
    editable: true
    exchangeMethod: 2

  item_TX3329030 (extends item):
    __construct:
      type: money
    refId: TX3329030
    editable: true
    exchangeMethod: 2

  item_TX3330030 (extends item):
    __construct:
      type: money
    refId: TX3330030
    editable: true
    exchangeMethod: 2

  item_TX3331030 (extends item):
    __construct:
      type: money
    refId: TX3331030
    editable: true
    exchangeMethod: 2

  item_TX3332030 (extends item):
    __construct:
      type: money
    refId: TX3332030
    editable: true
    exchangeMethod: 2

  item_TX3333030 (extends item):
    __construct:
      type: money
    refId: TX3333030
    editable: true
    exchangeMethod: 2

  item_TX3334030 (extends item):
    __construct:
      type: money
    refId: TX3334030
    editable: false
    exchangeMethod: 2

  item_TX3323040 (extends item):
    __construct:
      type: money
    refId: TX3323040
    editable: true
    exchangeMethod: 2

  item_TX3324040 (extends item):
    __construct:
      type: money
    refId: TX3324040
    editable: true
    exchangeMethod: 2

  item_TX3325040 (extends item):
    __construct:
      type: money
    refId: TX3325040
    editable: true
    exchangeMethod: 2

  item_TX3326040 (extends item):
    __construct:
      type: money
    refId: TX3326040
    editable: true
    exchangeMethod: 2

  item_TX3327040 (extends item):
    __construct:
      type: money
    refId: TX3327040
    editable: true
    exchangeMethod: 2

  item_TX3328040 (extends item):
    __construct:
      type: money
    refId: TX3328040
    editable: true
    exchangeMethod: 2

  item_TX3329040 (extends item):
    __construct:
      type: money
    refId: TX3329040
    editable: true
    exchangeMethod: 2

  item_TX3330040 (extends item):
    __construct:
      type: money
    refId: TX3330040
    editable: true
    exchangeMethod: 2

  item_TX3331040 (extends item):
    __construct:
      type: money
    refId: TX3331040
    editable: true
    exchangeMethod: 2

  item_TX3332040 (extends item):
    __construct:
      type: money
    refId: TX3332040
    editable: true
    exchangeMethod: 2

  item_TX3333040 (extends item):
    __construct:
      type: money
    refId: TX3333040
    editable: true
    exchangeMethod: 2

  item_TX3334040 (extends item):
    __construct:
      type: money
    refId: TX3334040
    editable: false
    exchangeMethod: 2

  item_TX3414020 (extends item):
    __construct:
      type: money
    refId: TX3414020
    editable: true
    exchangeMethod: 2

  item_TX3415040 (extends item):
    __construct:
      type: money
    refId: TX3415040
    editable: true
    exchangeMethod: 2

  item_TX3413020 (extends item):
    __construct:
      type: money
    refId: TX3413020
    editable: false
    exchangeMethod: 2

  item_TX3414040 (extends item):
    __construct:
      type: money
    refId: TX3414040
    editable: false
    exchangeMethod: 2

  item_TX3416020 (extends item):
    __construct:
      type: money
    refId: TX3416020
    editable: false
    exchangeMethod: 2

  item_TX3411020 (extends item):
    __construct:
      type: money
    refId: TX3411020
    editable: true
    exchangeMethod: 2

  item_TX3412020 (extends item):
    __construct:
      type: money
    refId: TX3412020
    editable: true
    exchangeMethod: 1

  item_TX3415020 (extends item):
    __construct:
      type: money
    refId: TX3415020
    editable: true
    exchangeMethod: 2

  item_TX3416020P (extends item):
    __construct:
      type: money
    refId: TX3416020P
    editable: false
    exchangeMethod: 4

  item_TX3417040 (extends item):
    __construct:
      type: money
    refId: TX3417040
    editable: false
    exchangeMethod: 2

  item_TX3411040 (extends item):
    __construct:
      type: money
    refId: TX3411040
    editable: true
    exchangeMethod: 2

  item_TX3412040 (extends item):
    __construct:
      type: money
    refId: TX3412040
    editable: true
    exchangeMethod: 1

  item_TX3416040 (extends item):
    __construct:
      type: money
    refId: TX3416040
    editable: true
    exchangeMethod: 2

  item_TX3417040P (extends item):
    __construct:
      type: money
    refId: TX3417040P
    editable: false
    exchangeMethod: 4

  item_TX3421020 (extends item):
    __construct:
      type: money
    refId: TX3421020
    editable: true
    exchangeMethod: 2

  item_TX3423040 (extends item):
    __construct:
      type: money
    refId: TX3423040
    editable: true
    exchangeMethod: 2

  item_TX3420020 (extends item):
    __construct:
      type: money
    refId: TX3420020
    editable: false
    exchangeMethod: 2

  item_TX3422040 (extends item):
    __construct:
      type: money
    refId: TX3422040
    editable: false
    exchangeMethod: 2

  item_TX3423020 (extends item):
    __construct:
      type: money
    refId: TX3423020
    editable: false
    exchangeMethod: 2

  item_TX3418020 (extends item):
    __construct:
      type: money
    refId: TX3418020
    editable: true
    exchangeMethod: 2

  item_TX3419020 (extends item):
    __construct:
      type: money
    refId: TX3419020
    editable: true
    exchangeMethod: 1

  item_TX3422020 (extends item):
    __construct:
      type: money
    refId: TX3422020
    editable: true
    exchangeMethod: 2

  item_TX3423020P (extends item):
    __construct:
      type: money
    refId: TX3423020P
    editable: false
    exchangeMethod: 4

  item_TX3425040 (extends item):
    __construct:
      type: money
    refId: TX3425040
    editable: false
    exchangeMethod: 2

  item_TX3419040 (extends item):
    __construct:
      type: money
    refId: TX3419040
    editable: true
    exchangeMethod: 2

  item_TX3420040 (extends item):
    __construct:
      type: money
    refId: TX3420040
    editable: true
    exchangeMethod: 1

  item_TX3424040 (extends item):
    __construct:
      type: money
    refId: TX3424040
    editable: true
    exchangeMethod: 2

  item_TX3425040P (extends item):
    __construct:
      type: money
    refId: TX3425040P
    editable: false
    exchangeMethod: 4

  item_TX3510010 (extends item):
    __construct:
      type: money
    refId: TX3510010
    editable: true
    exchangeMethod: 2

  item_TX3520010 (extends item):
    __construct:
      type: money
    refId: TX3520010
    editable: true
    exchangeMethod: 2

  item_TX3521010 (extends item):
    __construct:
      type: money
    refId: TX3521010
    editable: false
    exchangeMethod: 2

  item_TX3510020 (extends item):
    __construct:
      type: money
    refId: TX3510020
    editable: true
    exchangeMethod: 2

  item_TX3520020 (extends item):
    __construct:
      type: money
    refId: TX3520020
    editable: true
    exchangeMethod: 2

  item_TX3521020 (extends item):
    __construct:
      type: money
    refId: TX3521020
    editable: false
    exchangeMethod: 2

  item_TX42770090 (extends item):
    __construct:
      type: money
    refId: TX42770090
    editable: false
    exchangeMethod: 2

  item_TX42770100 (extends item):
    __construct:
      type: money
    refId: TX42770100
    editable: false
    exchangeMethod: 2

  item_TX42770110 (extends item):
    __construct:
      type: money
    refId: TX42770110
    editable: false
    exchangeMethod: 2

  item_TX42770120 (extends item):
    __construct:
      type: money
    refId: TX42770120
    editable: true
    exchangeMethod: 2

  item_TX3531116 (extends item):
    __construct:
      type: money
    refId: TX3531116
    editable: true
    exchangeMethod: 2

  item_TX3530010 (extends item):
    __construct:
      type: money
    refId: TX3530010
    editable: false
    exchangeMethod: 2

  item_TX3531126 (extends item):
    __construct:
      type: money
    refId: TX3531126
    editable: true
    exchangeMethod: 2

  item_TX3530020 (extends item):
    __construct:
      type: money
    refId: TX3530020
    editable: false
    exchangeMethod: 2

  item_TX3550020 (extends item):
    __construct:
      type: money
    refId: TX3550020
    editable: false
    exchangeMethod: 2

  item_TX3560020 (extends item):
    __construct:
      type: money
    refId: TX3560020
    editable: false
    exchangeMethod: 2

  item_TX3540020 (extends item):
    __construct:
      type: money
    refId: TX3540020
    editable: false
    exchangeMethod: 2

  item_TX3560010 (extends item):
    __construct:
      type: money
    refId: TX3560010
    editable: false
    exchangeMethod: 1

  item_TX3570010 (extends item):
    __construct:
      type: money
    refId: TX3570010
    editable: false
    exchangeMethod: 1

  item_TX3580010 (extends item):
    __construct:
      type: money
    refId: TX3580010
    editable: false
    exchangeMethod: 1

  item_TX3550010 (extends item):
    __construct:
      type: money
    refId: TX3550010
    editable: false
    exchangeMethod: 1

  item_TX3817010 (extends item):
    __construct:
      type: money
    refId: TX3817010
    editable: false
    exchangeMethod: 2

  item_TX3815010 (extends item):
    __construct:
      type: money
    refId: TX3815010
    editable: false
    exchangeMethod: 2

  item_TX91450090 (extends item):
    __construct:
      type: money
    refId: TX91450090
    editable: false
    exchangeMethod: 2

  item_TX91450095 (extends item):
    __construct:
      type: money
    refId: TX91450095
    editable: false
    exchangeMethod: 2

  item_TX3571010 (extends item):
    __construct:
      type: money
    refId: TX3571010
    editable: false
    exchangeMethod: 1

  item_TX42770160 (extends item):
    __construct:
      type: money
    refId: TX42770160
    editable: false
    exchangeMethod: 2

  item_TX42770170 (extends item):
    __construct:
      type: money
    refId: TX42770170
    editable: false
    exchangeMethod: 2

  item_TX42770189 (extends item):
    __construct:
      type: money
    refId: TX42770189
    editable: false
    exchangeMethod: 2

  item_TX3131030 (extends item):
    __construct:
      type: money
    refId: TX3131030
    editable: true
    exchangeMethod: 1

  item_TX3132030 (extends item):
    __construct:
      type: money
    refId: TX3132030
    editable: true
    exchangeMethod: 1

  item_TX3810010 (extends item):
    __construct:
      type: money
    refId: TX3810010
    editable: false
    exchangeMethod: 2

  item_TX3810110 (extends item):
    __construct:
      type: money
    refId: TX3810110
    editable: false
    exchangeMethod: 2

  item_TX3812020 (extends item):
    __construct:
      type: money
    refId: TX3812020
    editable: false
    exchangeMethod: 2

  item_TX3813010 (extends item):
    __construct:
      type: money
    refId: TX3813010
    editable: false
    exchangeMethod: 2

  item_TX3813011 (extends item):
    __construct:
      type: money
    refId: TX3813011
    editable: false
    exchangeMethod: 2

  item_TX3813021 (extends item):
    __construct:
      type: money
    refId: TX3813021
    editable: false
    exchangeMethod: 2

  item_TX3816022 (extends item):
    __construct:
      type: money
    refId: TX3816022
    editable: false
    exchangeMethod: 2

  item_TX3816021 (extends item):
    __construct:
      type: money
    refId: TX3816021
    editable: true
    exchangeMethod: 2

  item_TX3813020 (extends item):
    __construct:
      type: money
    refId: TX3813020
    editable: false
    exchangeMethod: 2

  item_TX42711189 (extends item):
    __construct:
      type: money
    refId: TX42711189
    editable: false
    exchangeMethod: 2

  item_TX3412030 (extends item):
    __construct:
      type: money
    refId: TX3412030
    editable: true
    exchangeMethod: 2

  item_TX3420030 (extends item):
    __construct:
      type: money
    refId: TX3420030
    editable: true
    exchangeMethod: 2

  item_TX3412010 (extends item):
    __construct:
      type: money
    refId: TX3412010
    editable: true
    exchangeMethod: 2

  item_TX3419010 (extends item):
    __construct:
      type: money
    refId: TX3419010
    editable: true
    exchangeMethod: 2

  item_TX3816010 (extends item):
    __construct:
      type: money
    refId: TX3816010
    editable: false
    exchangeMethod: 2

  item_TX3811010 (extends item):
    __construct:
      type: money
    refId: TX3811010
    editable: true
    exchangeMethod: 2

  item_TX3812010 (extends item):
    __construct:
      type: money
    refId: TX3812010
    editable: true
    exchangeMethod: 2

  item_TX3814010 (extends item):
    __construct:
      type: money
    refId: TX3814010
    editable: true
    exchangeMethod: 2

  item_TR3911020 (extends item):
    __construct:
      type: percent
    refId: TR3911020
    editable: true
    exchangeMethod: 2

  item_TX3912020 (extends item):
    __construct:
      type: money
    refId: TX3912020
    editable: false
    exchangeMethod: 1

  item_TX3913011 (extends item):
    __construct:
      type: money
    refId: TX3913011
    editable: false
    exchangeMethod: 1

  item_TR0200110 (extends item):
    __construct:
      type: percent
    refId: TR0200110
    editable: true
    exchangeMethod: 2

  item_TX3913012 (extends item):
    __construct:
      type: money
    refId: TX3913012
    editable: false
    exchangeMethod: 1

  item_TX3913013 (extends item):
    __construct:
      type: money
    refId: TX3913013
    editable: false
    exchangeMethod: 1

  item_TX3913020 (extends item):
    __construct:
      type: money
    refId: TX3913020
    editable: false
    exchangeMethod: 1

  item_TX3914011 (extends item):
    __construct:
      type: money
    refId: TX3914011
    editable: false
    exchangeMethod: 1

  item_TX71450070 (extends item):
    __construct:
      type: money
    refId: TX71450070
    editable: false
    exchangeMethod: 2

  item_TX3914012 (extends item):
    __construct:
      type: money
    refId: TX3914012
    editable: false
    exchangeMethod: 1

  item_TX42680170 (extends item):
    __construct:
      type: money
    refId: TX42680170
    editable: false
    exchangeMethod: 2

  item_TX3914014 (extends item):
    __construct:
      type: money
    refId: TX3914014
    editable: false
    exchangeMethod: 1

  item_TX42690170 (extends item):
    __construct:
      type: money
    refId: TX42690170
    editable: false
    exchangeMethod: 2

  item_TX3914015 (extends item):
    __construct:
      type: money
    refId: TX3914015
    editable: false
    exchangeMethod: 1

  item_TX42700170 (extends item):
    __construct:
      type: money
    refId: TX42700170
    editable: false
    exchangeMethod: 2

  item_TX3914016 (extends item):
    __construct:
      type: money
    refId: TX3914016
    editable: false
    exchangeMethod: 1

  item_TX42711170 (extends item):
    __construct:
      type: money
    refId: TX42711170
    editable: true
    exchangeMethod: 2

  item_TX3914018 (extends item):
    __construct:
      type: money
    refId: TX3914018
    editable: false
    exchangeMethod: 1

  item_TX3914017 (extends item):
    __construct:
      type: money
    refId: TX3914017
    editable: false
    exchangeMethod: 1

  item_TX3914020 (extends item):
    __construct:
      type: money
    refId: TX3914020
    editable: false
    exchangeMethod: 1

  item_TX3112020 (extends item):
    __construct:
      type: money
    refId: TX3112020
    editable: true
    exchangeMethod: 1

  item_TX3915011 (extends item):
    __construct:
      type: money
    refId: TX3915011
    editable: false
    exchangeMethod: 1

  item_TX3113020 (extends item):
    __construct:
      type: money
    refId: TX3113020
    editable: true
    exchangeMethod: 1

  item_TX3113020_comment (extends item):
    __construct:
      type: text
    refId: TX3113020_comment
    editable: true

  item_TX3915012 (extends item):
    __construct:
      type: money
    refId: TX3915012
    editable: false
    exchangeMethod: 1

  item_TX3915020 (extends item):
    __construct:
      type: money
    refId: TX3915020
    editable: false
    exchangeMethod: 1

  item_TX71450060 (extends item):
    __construct:
      type: money
    refId: TX71450060
    editable: false
    exchangeMethod: 2

  item_TX3916011 (extends item):
    __construct:
      type: money
    refId: TX3916011
    editable: false
    exchangeMethod: 1

  item_TX91450101 (extends item):
    __construct:
      type: money
    refId: TX91450101
    editable: false
    exchangeMethod: 2

  item_TX3916012 (extends item):
    __construct:
      type: money
    refId: TX3916012
    editable: false
    exchangeMethod: 1

  item_TX42710160 (extends item):
    __construct:
      type: money
    refId: TX42710160
    editable: false
    exchangeMethod: 2

  item_TX42780160 (extends item):
    __construct:
      type: money
    refId: TX42780160
    editable: false
    exchangeMethod: 2

  item_TX3916013 (extends item):
    __construct:
      type: money
    refId: TX3916013
    editable: false
    exchangeMethod: 1

  item_TX3916020 (extends item):
    __construct:
      type: money
    refId: TX3916020
    editable: false
    exchangeMethod: 1

  item_TX3917012 (extends item):
    __construct:
      type: money
    refId: TX3917012
    editable: false
    exchangeMethod: 1

  item_TX3917011 (extends item):
    __construct:
      type: money
    refId: TX3917011
    editable: false
    exchangeMethod: 1

  item_TX3917020 (extends item):
    __construct:
      type: money
    refId: TX3917020
    editable: false
    exchangeMethod: 1

  item_TX3115039 (extends item):
    __construct:
      type: money
    refId: TX3115039
    editable: true
    exchangeMethod: 1

  item_TX3918011 (extends item):
    __construct:
      type: money
    refId: TX3918011
    editable: false
    exchangeMethod: 1

  item_TX3918012 (extends item):
    __construct:
      type: money
    refId: TX3918012
    editable: false
    exchangeMethod: 1

  item_TX3918020 (extends item):
    __construct:
      type: money
    refId: TX3918020
    editable: false
    exchangeMethod: 1

  item_TX3919011 (extends item):
    __construct:
      type: money
    refId: TX3919011
    editable: false
    exchangeMethod: 1

  item_TX3919012 (extends item):
    __construct:
      type: money
    refId: TX3919012
    editable: true
    exchangeMethod: 1

  item_TX3919020 (extends item):
    __construct:
      type: money
    refId: TX3919020
    editable: false
    exchangeMethod: 1

  item_TX3920011 (extends item):
    __construct:
      type: money
    refId: TX3920011
    editable: false
    exchangeMethod: 1

  item_TX3920012 (extends item):
    __construct:
      type: money
    refId: TX3920012
    editable: false
    exchangeMethod: 1

  item_TX3920013 (extends item):
    __construct:
      type: money
    refId: TX3920013
    editable: false
    exchangeMethod: 1

  item_TX3920014 (extends item):
    __construct:
      type: money
    refId: TX3920014
    editable: false
    exchangeMethod: 1

  item_TX3922014 (extends item):
    __construct:
      type: money
    refId: TX3922014
    editable: false
    exchangeMethod: 1

  item_TX3920020 (extends item):
    __construct:
      type: money
    refId: TX3920020
    editable: false
    exchangeMethod: 1

  item_TX3921011 (extends item):
    __construct:
      type: money
    refId: TX3921011
    editable: false
    exchangeMethod: 1

  item_TX3921012 (extends item):
    __construct:
      type: money
    refId: TX3921012
    editable: false
    exchangeMethod: 1

  item_TX3921013 (extends item):
    __construct:
      type: money
    refId: TX3921013
    editable: false
    exchangeMethod: 1

  item_TX3921014 (extends item):
    __construct:
      type: money
    refId: TX3921014
    editable: false
    exchangeMethod: 1

  item_TX3921015 (extends item):
    __construct:
      type: money
    refId: TX3921015
    editable: false
    exchangeMethod: 1

  item_TX3921016 (extends item):
    __construct:
      type: money
    refId: TX3921016
    editable: false
    exchangeMethod: 1

  item_TX42750189 (extends item):
    __construct:
      type: money
    refId: TX42750189
    editable: true
    exchangeMethod: 2

  item_TX3921017 (extends item):
    __construct:
      type: money
    refId: TX3921017
    editable: false
    exchangeMethod: 1

  item_TX3921018 (extends item):
    __construct:
      type: money
    refId: TX3921018
    editable: false
    exchangeMethod: 1

  item_TX3921019 (extends item):
    __construct:
      type: money
    refId: TX3921019
    editable: false
    exchangeMethod: 1

  item_TX3921020 (extends item):
    __construct:
      type: money
    refId: TX3921020
    editable: false
    exchangeMethod: 1

  item_TX3922011 (extends item):
    __construct:
      type: money
    refId: TX3922011
    editable: false
    exchangeMethod: 1

  item_TX3922012 (extends item):
    __construct:
      type: money
    refId: TX3922012
    editable: false
    exchangeMethod: 1

  item_TX3922013 (extends item):
    __construct:
      type: money
    refId: TX3922013
    editable: false
    exchangeMethod: 1

  item_TX3210020 (extends item):
    __construct:
      type: money
    refId: TX3210020
    editable: true
    exchangeMethod: 1

  item_TX3922015 (extends item):
    __construct:
      type: money
    refId: TX3922015
    editable: false
    exchangeMethod: 1

  item_TX3922016 (extends item):
    __construct:
      type: money
    refId: TX3922016
    editable: false
    exchangeMethod: 1

  item_TX3922017 (extends item):
    __construct:
      type: money
    refId: TX3922017
    editable: false
    exchangeMethod: 1

  item_TX3922018 (extends item):
    __construct:
      type: money
    refId: TX3922018
    editable: false
    exchangeMethod: 2

  item_TX3922020 (extends item):
    __construct:
      type: money
    refId: TX3922020
    editable: false
    exchangeMethod: 1

  item_TX3924020 (extends item):
    __construct:
      type: money
    refId: TX3924020
    editable: false
    exchangeMethod: 1

  item_TX3925020 (extends item):
    __construct:
      type: money
    refId: TX3925020
    editable: false
    exchangeMethod: 1

  item_TX42680010 (extends item):
    __construct:
      type: money
    refId: TX42680010
    editable: false
    exchangeMethod: 4

  item_TX42680090 (extends item):
    __construct:
      type: money
    refId: TX42680090
    editable: false
    exchangeMethod: 2

  item_TX42680160 (extends item):
    __construct:
      type: money
    refId: TX42680160
    editable: false
    exchangeMethod: 2

  item_TX42680180 (extends item):
    __construct:
      type: money
    refId: TX42680180
    editable: false
    exchangeMethod: 2

  item_TX42680189 (extends item):
    __construct:
      type: money
    refId: TX42680189
    editable: false
    exchangeMethod: 2

  item_TX42690010 (extends item):
    __construct:
      type: money
    refId: TX42690010
    editable: false
    exchangeMethod: 4

  item_TX42690090 (extends item):
    __construct:
      type: money
    refId: TX42690090
    editable: false
    exchangeMethod: 2

  item_TX42690160 (extends item):
    __construct:
      type: money
    refId: TX42690160
    editable: false
    exchangeMethod: 2

  item_TX42690180 (extends item):
    __construct:
      type: money
    refId: TX42690180
    editable: false
    exchangeMethod: 2

  item_TX42690189 (extends item):
    __construct:
      type: money
    refId: TX42690189
    editable: false
    exchangeMethod: 2

  item_TX42700010 (extends item):
    __construct:
      type: money
    refId: TX42700010
    editable: false
    exchangeMethod: 4

  item_TX42700090 (extends item):
    __construct:
      type: money
    refId: TX42700090
    editable: false
    exchangeMethod: 2

  item_TX42700160 (extends item):
    __construct:
      type: money
    refId: TX42700160
    editable: false
    exchangeMethod: 2

  item_TX42700180 (extends item):
    __construct:
      type: money
    refId: TX42700180
    editable: false
    exchangeMethod: 2

  item_TX42700189 (extends item):
    __construct:
      type: money
    refId: TX42700189
    editable: false
    exchangeMethod: 2

  item_TX42711010 (extends item):
    __construct:
      type: money
    refId: TX42711010
    editable: true
    exchangeMethod: 2

  item_TX42710010 (extends item):
    __construct:
      type: money
    refId: TX42710010
    editable: false
    exchangeMethod: 4

  item_TX42711090 (extends item):
    __construct:
      type: money
    refId: TX42711090
    editable: true
    exchangeMethod: 2

  item_TX42710090 (extends item):
    __construct:
      type: money
    refId: TX42710090
    editable: false
    exchangeMethod: 2

  item_TX42680150 (extends item):
    __construct:
      type: diff
    refId: TX42680150
    editable: false
    exchangeMethod: 2

  item_TX42690150 (extends item):
    __construct:
      type: diff
    refId: TX42690150
    editable: false
    exchangeMethod: 2

  item_TX427000150 (extends item):
    __construct:
      type: money
    refId: TX427000150
    editable: true
    exchangeMethod: 2

  item_TX42711150 (extends item):
    __construct:
      type: diff
    refId: TX42711150
    editable: false
    exchangeMethod: 2

  item_TX42710150 (extends item):
    __construct:
      type: diff
    refId: TX42710150
    editable: false
    exchangeMethod: 2

  item_TX42710170 (extends item):
    __construct:
      type: money
    refId: TX42710170
    editable: false
    exchangeMethod: 2

  item_TX42711180 (extends item):
    __construct:
      type: money
    refId: TX42711180
    editable: true
    exchangeMethod: 2

  item_TX42710180 (extends item):
    __construct:
      type: money
    refId: TX42710180
    editable: false
    exchangeMethod: 2

  item_TX42710189 (extends item):
    __construct:
      type: money
    refId: TX42710189
    editable: false
    exchangeMethod: 2

  item_TX42720010 (extends item):
    __construct:
      type: money
    refId: TX42720010
    editable: false
    exchangeMethod: 4

  item_TX42720090 (extends item):
    __construct:
      type: money
    refId: TX42720090
    editable: false
    exchangeMethod: 2

  item_TX42720160 (extends item):
    __construct:
      type: money
    refId: TX42720160
    editable: false
    exchangeMethod: 2

  item_TX42720189 (extends item):
    __construct:
      type: money
    refId: TX42720189
    editable: false
    exchangeMethod: 2

  item_TX42730010 (extends item):
    __construct:
      type: money
    refId: TX42730010
    editable: false
    exchangeMethod: 4

  item_TX42730090 (extends item):
    __construct:
      type: money
    refId: TX42730090
    editable: false
    exchangeMethod: 2

  item_TX42730160 (extends item):
    __construct:
      type: money
    refId: TX42730160
    editable: false
    exchangeMethod: 2

  item_TX42730189 (extends item):
    __construct:
      type: money
    refId: TX42730189
    editable: false
    exchangeMethod: 2

  item_TX42740010 (extends item):
    __construct:
      type: money
    refId: TX42740010
    editable: false
    exchangeMethod: 4

  item_TX42740090 (extends item):
    __construct:
      type: money
    refId: TX42740090
    editable: false
    exchangeMethod: 2

  item_TX42740160 (extends item):
    __construct:
      type: money
    refId: TX42740160
    editable: false
    exchangeMethod: 2

  item_TX42740189 (extends item):
    __construct:
      type: money
    refId: TX42740189
    editable: false
    exchangeMethod: 2

  item_TX42770010 (extends item):
    __construct:
      type: money
    refId: TX42770010
    editable: false
    exchangeMethod: 4

  item_TX42770020 (extends item):
    __construct:
      type: money
    refId: TX42770020
    editable: false
    exchangeMethod: 4

  item_TX42770030 (extends item):
    __construct:
      type: money
    refId: TX42770030
    editable: false
    exchangeMethod: 4

  item_TX42770040 (extends item):
    __construct:
      type: money
    refId: TX42770040
    editable: false
    exchangeMethod: 4

  item_TX71450101 (extends item):
    __construct:
      type: money
    refId: TX71450101
    editable: false
    exchangeMethod: 2

  item_TX42780090 (extends item):
    __construct:
      type: money
    refId: TX42780090
    editable: false
    exchangeMethod: 2

  item_TX71450111 (extends item):
    __construct:
      type: money
    refId: TX71450111
    editable: false
    exchangeMethod: 2

  item_TX42760100 (extends item):
    __construct:
      type: money
    refId: TX42760100
    editable: true
    exchangeMethod: 2

  item_TX71450112 (extends item):
    __construct:
      type: money
    refId: TX71450112
    editable: false
    exchangeMethod: 2

  item_TX42790110 (extends item):
    __construct:
      type: money
    refId: TX42790110
    editable: true
    exchangeMethod: 2

  item_TX42770011 (extends item):
    __construct:
      type: money
    refId: TX42770011
    editable: true
    exchangeMethod: 2

  item_TX42770021 (extends item):
    __construct:
      type: money
    refId: TX42770021
    editable: true
    exchangeMethod: 2

  item_TX42770031 (extends item):
    __construct:
      type: money
    refId: TX42770031
    editable: true
    exchangeMethod: 2

  item_TX42770041 (extends item):
    __construct:
      type: money
    refId: TX42770041
    editable: true
    exchangeMethod: 2

  item_TX42770150 (extends item):
    __construct:
      type: diff
    refId: TX42770150
    editable: false
    exchangeMethod: 2

  item_TX42670170 (extends item):
    __construct:
      type: money
    refId: TX42670170
    editable: true
    exchangeMethod: 2

  item_TX71450080 (extends item):
    __construct:
      type: money
    refId: TX71450080
    editable: false
    exchangeMethod: 2

  item_TX42670180 (extends item):
    __construct:
      type: money
    refId: TX42670180
    editable: true
    exchangeMethod: 2

  item_TX42780180 (extends item):
    __construct:
      type: money
    refId: TX42780180
    editable: true
    exchangeMethod: 2

  item_TX42770180 (extends item):
    __construct:
      type: money
    refId: TX42770180
    editable: false
    exchangeMethod: 2

  item_TX71450090 (extends item):
    __construct:
      type: money
    refId: TX71450090
    editable: false
    exchangeMethod: 2

  item_TX42670189 (extends item):
    __construct:
      type: money
    refId: TX42670189
    editable: true
    exchangeMethod: 2

  item_TX42780189 (extends item):
    __construct:
      type: money
    refId: TX42780189
    editable: false
    exchangeMethod: 2

  item_TX42790189 (extends item):
    __construct:
      type: money
    refId: TX42790189
    editable: true
    exchangeMethod: 2

  item_TX42750010 (extends item):
    __construct:
      type: money
    refId: TX42750010
    editable: true
    exchangeMethod: 2

  item_TX42780010 (extends item):
    __construct:
      type: money
    refId: TX42780010
    editable: false
    exchangeMethod: 4

  item_TX42750090 (extends item):
    __construct:
      type: money
    refId: TX42750090
    editable: true
    exchangeMethod: 2

  item_TX42720150 (extends item):
    __construct:
      type: diff
    refId: TX42720150
    editable: false
    exchangeMethod: 2

  item_TX42730150 (extends item):
    __construct:
      type: diff
    refId: TX42730150
    editable: false
    exchangeMethod: 2

  item_TX42740150 (extends item):
    __construct:
      type: diff
    refId: TX42740150
    editable: false
    exchangeMethod: 2

  item_TX42750150 (extends item):
    __construct:
      type: diff
    refId: TX42750150
    editable: false
    exchangeMethod: 2

  item_TX42760150 (extends item):
    __construct:
      type: diff
    refId: TX42760150
    editable: false
    exchangeMethod: 2

  item_TX42780150 (extends item):
    __construct:
      type: diff
    refId: TX42780150
    editable: false
    exchangeMethod: 2

  item_TX42750160 (extends item):
    __construct:
      type: money
    refId: TX42750160
    editable: true
    exchangeMethod: 2

  item_TX42900100 (extends item):
    __construct:
      type: money
    refId: TX42900100
    editable: false
    exchangeMethod: 2

  item_TX42900020 (extends item):
    __construct:
      type: money
    refId: TX42900020
    editable: false
    exchangeMethod: 4

  item_TX42900120 (extends item):
    __construct:
      type: money
    refId: TX42900120
    editable: false
    exchangeMethod: 2

  item_TX42900040 (extends item):
    __construct:
      type: money
    refId: TX42900040
    editable: false
    exchangeMethod: 4

  item_TX42910100 (extends item):
    __construct:
      type: money
    refId: TX42910100
    editable: false
    exchangeMethod: 2

  item_TX42910020 (extends item):
    __construct:
      type: money
    refId: TX42910020
    editable: false
    exchangeMethod: 4

  item_TX42910120 (extends item):
    __construct:
      type: money
    refId: TX42910120
    editable: false
    exchangeMethod: 2

  item_TX42910040 (extends item):
    __construct:
      type: money
    refId: TX42910040
    editable: false
    exchangeMethod: 4

  item_TX42920100 (extends item):
    __construct:
      type: money
    refId: TX42920100
    editable: false
    exchangeMethod: 2

  item_TX42920020 (extends item):
    __construct:
      type: money
    refId: TX42920020
    editable: false
    exchangeMethod: 4

  item_TX42920120 (extends item):
    __construct:
      type: money
    refId: TX42920120
    editable: false
    exchangeMethod: 2

  item_TX42920040 (extends item):
    __construct:
      type: money
    refId: TX42920040
    editable: false
    exchangeMethod: 4

  item_TX71200101 (extends item):
    __construct:
      type: money
    refId: TX71200101
    editable: false
    exchangeMethod: 2

  item_TX71200010 (extends item):
    __construct:
      type: money
    refId: TX71200010
    editable: false
    exchangeMethod: 4

  item_TX71200111 (extends item):
    __construct:
      type: money
    refId: TX71200111
    editable: false
    exchangeMethod: 2

  item_TX71200020 (extends item):
    __construct:
      type: money
    refId: TX71200020
    editable: false
    exchangeMethod: 4

  item_TX71200112 (extends item):
    __construct:
      type: money
    refId: TX71200112
    editable: false
    exchangeMethod: 2

  item_TX71200030 (extends item):
    __construct:
      type: money
    refId: TX71200030
    editable: false
    exchangeMethod: 4

  item_TX71200113 (extends item):
    __construct:
      type: money
    refId: TX71200113
    editable: false
    exchangeMethod: 2

  item_TX71200040 (extends item):
    __construct:
      type: money
    refId: TX71200040
    editable: false
    exchangeMethod: 4

  item_TX71200011 (extends item):
    __construct:
      type: money
    refId: TX71200011
    editable: true
    exchangeMethod: 2

  item_TX71200021 (extends item):
    __construct:
      type: money
    refId: TX71200021
    editable: true
    exchangeMethod: 2

  item_TX71200031 (extends item):
    __construct:
      type: money
    refId: TX71200031
    editable: true
    exchangeMethod: 2

  item_TX71200041 (extends item):
    __construct:
      type: money
    refId: TX71200041
    editable: true
    exchangeMethod: 2

  item_TX71200050 (extends item):
    __construct:
      type: diff
    refId: TX71200050
    editable: false
    exchangeMethod: 2

  item_TX91200070 (extends item):
    __construct:
      type: money
    refId: TX91200070
    editable: false
    exchangeMethod: 2

  item_TX91200090 (extends item):
    __construct:
      type: money
    refId: TX91200090
    editable: true
    exchangeMethod: 2

  item_TX71200060 (extends item):
    __construct:
      type: money
    refId: TX71200060
    editable: false
    exchangeMethod: 2

  item_TX71200070 (extends item):
    __construct:
      type: money
    refId: TX71200070
    editable: false
    exchangeMethod: 2

  item_TX91200060 (extends item):
    __construct:
      type: money
    refId: TX91200060
    editable: true
    exchangeMethod: 2

  item_TX91200095 (extends item):
    __construct:
      type: money
    refId: TX91200095
    editable: true
    exchangeMethod: 2

  item_TX71200080 (extends item):
    __construct:
      type: money
    refId: TX71200080
    editable: false
    exchangeMethod: 2

  item_TX91200101 (extends item):
    __construct:
      type: money
    refId: TX91200101
    editable: false
    exchangeMethod: 2

  item_TX71200090 (extends item):
    __construct:
      type: money
    refId: TX71200090
    editable: false
    exchangeMethod: 2

  item_TX71210101 (extends item):
    __construct:
      type: money
    refId: TX71210101
    editable: false
    exchangeMethod: 2

  item_TX71210010 (extends item):
    __construct:
      type: money
    refId: TX71210010
    editable: false
    exchangeMethod: 4

  item_TX71210111 (extends item):
    __construct:
      type: money
    refId: TX71210111
    editable: false
    exchangeMethod: 2

  item_TX71210020 (extends item):
    __construct:
      type: money
    refId: TX71210020
    editable: false
    exchangeMethod: 4

  item_TX71210112 (extends item):
    __construct:
      type: money
    refId: TX71210112
    editable: false
    exchangeMethod: 2

  item_TX71210030 (extends item):
    __construct:
      type: money
    refId: TX71210030
    editable: false
    exchangeMethod: 4

  item_TX71210113 (extends item):
    __construct:
      type: money
    refId: TX71210113
    editable: false
    exchangeMethod: 2

  item_TX71210040 (extends item):
    __construct:
      type: money
    refId: TX71210040
    editable: false
    exchangeMethod: 4

  item_TX71210011 (extends item):
    __construct:
      type: money
    refId: TX71210011
    editable: true
    exchangeMethod: 2

  item_TX71210021 (extends item):
    __construct:
      type: money
    refId: TX71210021
    editable: true
    exchangeMethod: 2

  item_TX71210031 (extends item):
    __construct:
      type: money
    refId: TX71210031
    editable: true
    exchangeMethod: 2

  item_TX71210041 (extends item):
    __construct:
      type: money
    refId: TX71210041
    editable: true
    exchangeMethod: 2

  item_TX71210050 (extends item):
    __construct:
      type: diff
    refId: TX71210050
    editable: false
    exchangeMethod: 2

  item_TX91210070 (extends item):
    __construct:
      type: money
    refId: TX91210070
    editable: false
    exchangeMethod: 2

  item_TX91210090 (extends item):
    __construct:
      type: money
    refId: TX91210090
    editable: true
    exchangeMethod: 2

  item_TX71210060 (extends item):
    __construct:
      type: money
    refId: TX71210060
    editable: false
    exchangeMethod: 2

  item_TX71210070 (extends item):
    __construct:
      type: money
    refId: TX71210070
    editable: false
    exchangeMethod: 2

  item_TX91210060 (extends item):
    __construct:
      type: money
    refId: TX91210060
    editable: true
    exchangeMethod: 2

  item_TX91210095 (extends item):
    __construct:
      type: money
    refId: TX91210095
    editable: true
    exchangeMethod: 2

  item_TX71210080 (extends item):
    __construct:
      type: money
    refId: TX71210080
    editable: false
    exchangeMethod: 2

  item_TX91210101 (extends item):
    __construct:
      type: money
    refId: TX91210101
    editable: false
    exchangeMethod: 2

  item_TX71210090 (extends item):
    __construct:
      type: money
    refId: TX71210090
    editable: false
    exchangeMethod: 2

  item_TX71220101 (extends item):
    __construct:
      type: money
    refId: TX71220101
    editable: false
    exchangeMethod: 2

  item_TX71220010 (extends item):
    __construct:
      type: money
    refId: TX71220010
    editable: false
    exchangeMethod: 4

  item_TX71220111 (extends item):
    __construct:
      type: money
    refId: TX71220111
    editable: false
    exchangeMethod: 2

  item_TX71220020 (extends item):
    __construct:
      type: money
    refId: TX71220020
    editable: false
    exchangeMethod: 4

  item_TX71220112 (extends item):
    __construct:
      type: money
    refId: TX71220112
    editable: false
    exchangeMethod: 2

  item_TX71220030 (extends item):
    __construct:
      type: money
    refId: TX71220030
    editable: false
    exchangeMethod: 4

  item_TX71220113 (extends item):
    __construct:
      type: money
    refId: TX71220113
    editable: false
    exchangeMethod: 2

  item_TX71220040 (extends item):
    __construct:
      type: money
    refId: TX71220040
    editable: false
    exchangeMethod: 4

  item_TX71220011 (extends item):
    __construct:
      type: money
    refId: TX71220011
    editable: true
    exchangeMethod: 2

  item_TX71220021 (extends item):
    __construct:
      type: money
    refId: TX71220021
    editable: true
    exchangeMethod: 2

  item_TX71220031 (extends item):
    __construct:
      type: money
    refId: TX71220031
    editable: true
    exchangeMethod: 2

  item_TX71220041 (extends item):
    __construct:
      type: money
    refId: TX71220041
    editable: true
    exchangeMethod: 2

  item_TX71220050 (extends item):
    __construct:
      type: diff
    refId: TX71220050
    editable: false
    exchangeMethod: 2

  item_TX91220070 (extends item):
    __construct:
      type: money
    refId: TX91220070
    editable: false
    exchangeMethod: 2

  item_TX91220090 (extends item):
    __construct:
      type: money
    refId: TX91220090
    editable: false
    exchangeMethod: 2

  item_TX71220060 (extends item):
    __construct:
      type: money
    refId: TX71220060
    editable: false
    exchangeMethod: 2

  item_TX71220070 (extends item):
    __construct:
      type: money
    refId: TX71220070
    editable: false
    exchangeMethod: 2

  item_TX91220060 (extends item):
    __construct:
      type: money
    refId: TX91220060
    editable: false
    exchangeMethod: 2

  item_TX91220095 (extends item):
    __construct:
      type: money
    refId: TX91220095
    editable: false
    exchangeMethod: 2

  item_TX71220080 (extends item):
    __construct:
      type: money
    refId: TX71220080
    editable: false
    exchangeMethod: 2

  item_TX91220101 (extends item):
    __construct:
      type: money
    refId: TX91220101
    editable: false
    exchangeMethod: 2

  item_TX71220090 (extends item):
    __construct:
      type: money
    refId: TX71220090
    editable: false
    exchangeMethod: 2

  item_TX71230101 (extends item):
    __construct:
      type: money
    refId: TX71230101
    editable: false
    exchangeMethod: 2

  item_TX71230010 (extends item):
    __construct:
      type: money
    refId: TX71230010
    editable: false
    exchangeMethod: 4

  item_TX71230111 (extends item):
    __construct:
      type: money
    refId: TX71230111
    editable: false
    exchangeMethod: 2

  item_TX71230020 (extends item):
    __construct:
      type: money
    refId: TX71230020
    editable: false
    exchangeMethod: 4

  item_TX71230112 (extends item):
    __construct:
      type: money
    refId: TX71230112
    editable: false
    exchangeMethod: 2

  item_TX71230030 (extends item):
    __construct:
      type: money
    refId: TX71230030
    editable: false
    exchangeMethod: 4

  item_TX71230113 (extends item):
    __construct:
      type: money
    refId: TX71230113
    editable: false
    exchangeMethod: 2

  item_TX71230040 (extends item):
    __construct:
      type: money
    refId: TX71230040
    editable: false
    exchangeMethod: 4

  item_TX71230011 (extends item):
    __construct:
      type: money
    refId: TX71230011
    editable: true
    exchangeMethod: 2

  item_TX71230021 (extends item):
    __construct:
      type: money
    refId: TX71230021
    editable: true
    exchangeMethod: 2

  item_TX71230031 (extends item):
    __construct:
      type: money
    refId: TX71230031
    editable: true
    exchangeMethod: 2

  item_TX71230041 (extends item):
    __construct:
      type: money
    refId: TX71230041
    editable: true
    exchangeMethod: 2

  item_TX71230050 (extends item):
    __construct:
      type: diff
    refId: TX71230050
    editable: false
    exchangeMethod: 2

  item_TX91230070 (extends item):
    __construct:
      type: money
    refId: TX91230070
    editable: false
    exchangeMethod: 2

  item_TX91230090 (extends item):
    __construct:
      type: money
    refId: TX91230090
    editable: true
    exchangeMethod: 2

  item_TX71230060 (extends item):
    __construct:
      type: money
    refId: TX71230060
    editable: false
    exchangeMethod: 2

  item_TX71230070 (extends item):
    __construct:
      type: money
    refId: TX71230070
    editable: false
    exchangeMethod: 2

  item_TX91230060 (extends item):
    __construct:
      type: money
    refId: TX91230060
    editable: true
    exchangeMethod: 2

  item_TX91230095 (extends item):
    __construct:
      type: money
    refId: TX91230095
    editable: true
    exchangeMethod: 2

  item_TX71230080 (extends item):
    __construct:
      type: money
    refId: TX71230080
    editable: false
    exchangeMethod: 2

  item_TX91230101 (extends item):
    __construct:
      type: money
    refId: TX91230101
    editable: false
    exchangeMethod: 2

  item_TX71230090 (extends item):
    __construct:
      type: money
    refId: TX71230090
    editable: false
    exchangeMethod: 2

  item_TX71240101 (extends item):
    __construct:
      type: money
    refId: TX71240101
    editable: false
    exchangeMethod: 2

  item_TX71240010 (extends item):
    __construct:
      type: money
    refId: TX71240010
    editable: false
    exchangeMethod: 4

  item_TX71240111 (extends item):
    __construct:
      type: money
    refId: TX71240111
    editable: false
    exchangeMethod: 2

  item_TX71240020 (extends item):
    __construct:
      type: money
    refId: TX71240020
    editable: false
    exchangeMethod: 4

  item_TX71240112 (extends item):
    __construct:
      type: money
    refId: TX71240112
    editable: false
    exchangeMethod: 2

  item_TX71240030 (extends item):
    __construct:
      type: money
    refId: TX71240030
    editable: false
    exchangeMethod: 4

  item_TX71240113 (extends item):
    __construct:
      type: money
    refId: TX71240113
    editable: false
    exchangeMethod: 2

  item_TX71240040 (extends item):
    __construct:
      type: money
    refId: TX71240040
    editable: false
    exchangeMethod: 4

  item_TX71240011 (extends item):
    __construct:
      type: money
    refId: TX71240011
    editable: true
    exchangeMethod: 2

  item_TX71240021 (extends item):
    __construct:
      type: money
    refId: TX71240021
    editable: true
    exchangeMethod: 2

  item_TX71240031 (extends item):
    __construct:
      type: money
    refId: TX71240031
    editable: true
    exchangeMethod: 2

  item_TX71240041 (extends item):
    __construct:
      type: money
    refId: TX71240041
    editable: true
    exchangeMethod: 2

  item_TX71240050 (extends item):
    __construct:
      type: diff
    refId: TX71240050
    editable: false
    exchangeMethod: 2

  item_TX91240070 (extends item):
    __construct:
      type: money
    refId: TX91240070
    editable: false
    exchangeMethod: 2

  item_TX91240090 (extends item):
    __construct:
      type: money
    refId: TX91240090
    editable: true
    exchangeMethod: 2

  item_TX71240060 (extends item):
    __construct:
      type: money
    refId: TX71240060
    editable: false
    exchangeMethod: 2

  item_TX71240070 (extends item):
    __construct:
      type: money
    refId: TX71240070
    editable: false
    exchangeMethod: 2

  item_TX91240060 (extends item):
    __construct:
      type: money
    refId: TX91240060
    editable: true
    exchangeMethod: 2

  item_TX91240095 (extends item):
    __construct:
      type: money
    refId: TX91240095
    editable: true
    exchangeMethod: 2

  item_TX71240080 (extends item):
    __construct:
      type: money
    refId: TX71240080
    editable: false
    exchangeMethod: 2

  item_TX91240101 (extends item):
    __construct:
      type: money
    refId: TX91240101
    editable: false
    exchangeMethod: 2

  item_TX71240090 (extends item):
    __construct:
      type: money
    refId: TX71240090
    editable: false
    exchangeMethod: 2

  item_TX71250101 (extends item):
    __construct:
      type: money
    refId: TX71250101
    editable: false
    exchangeMethod: 2

  item_TX71250010 (extends item):
    __construct:
      type: money
    refId: TX71250010
    editable: false
    exchangeMethod: 4

  item_TX71250111 (extends item):
    __construct:
      type: money
    refId: TX71250111
    editable: false
    exchangeMethod: 2

  item_TX71250020 (extends item):
    __construct:
      type: money
    refId: TX71250020
    editable: false
    exchangeMethod: 4

  item_TX71250112 (extends item):
    __construct:
      type: money
    refId: TX71250112
    editable: false
    exchangeMethod: 2

  item_TX71250030 (extends item):
    __construct:
      type: money
    refId: TX71250030
    editable: false
    exchangeMethod: 4

  item_TX71250113 (extends item):
    __construct:
      type: money
    refId: TX71250113
    editable: false
    exchangeMethod: 2

  item_TX71250040 (extends item):
    __construct:
      type: money
    refId: TX71250040
    editable: false
    exchangeMethod: 4

  item_TX71250011 (extends item):
    __construct:
      type: money
    refId: TX71250011
    editable: true
    exchangeMethod: 2

  item_TX71250021 (extends item):
    __construct:
      type: money
    refId: TX71250021
    editable: true
    exchangeMethod: 2

  item_TX71250031 (extends item):
    __construct:
      type: money
    refId: TX71250031
    editable: true
    exchangeMethod: 2

  item_TX71250041 (extends item):
    __construct:
      type: money
    refId: TX71250041
    editable: true
    exchangeMethod: 2

  item_TX71250050 (extends item):
    __construct:
      type: diff
    refId: TX71250050
    editable: false
    exchangeMethod: 2

  item_TX91250070 (extends item):
    __construct:
      type: money
    refId: TX91250070
    editable: false
    exchangeMethod: 2

  item_TX91250090 (extends item):
    __construct:
      type: money
    refId: TX91250090
    editable: true
    exchangeMethod: 2

  item_TX71250060 (extends item):
    __construct:
      type: money
    refId: TX71250060
    editable: false
    exchangeMethod: 2

  item_TX71250070 (extends item):
    __construct:
      type: money
    refId: TX71250070
    editable: false
    exchangeMethod: 2

  item_TX91250060 (extends item):
    __construct:
      type: money
    refId: TX91250060
    editable: true
    exchangeMethod: 2

  item_TX91250095 (extends item):
    __construct:
      type: money
    refId: TX91250095
    editable: true
    exchangeMethod: 2

  item_TX71250080 (extends item):
    __construct:
      type: money
    refId: TX71250080
    editable: false
    exchangeMethod: 2

  item_TX91250101 (extends item):
    __construct:
      type: money
    refId: TX91250101
    editable: false
    exchangeMethod: 2

  item_TX71250090 (extends item):
    __construct:
      type: money
    refId: TX71250090
    editable: false
    exchangeMethod: 2

  item_TX71260101 (extends item):
    __construct:
      type: money
    refId: TX71260101
    editable: false
    exchangeMethod: 2

  item_TX71260010 (extends item):
    __construct:
      type: money
    refId: TX71260010
    editable: false
    exchangeMethod: 4

  item_TX71260111 (extends item):
    __construct:
      type: money
    refId: TX71260111
    editable: false
    exchangeMethod: 2

  item_TX71260020 (extends item):
    __construct:
      type: money
    refId: TX71260020
    editable: false
    exchangeMethod: 4

  item_TX71260112 (extends item):
    __construct:
      type: money
    refId: TX71260112
    editable: false
    exchangeMethod: 2

  item_TX71260030 (extends item):
    __construct:
      type: money
    refId: TX71260030
    editable: false
    exchangeMethod: 4

  item_TX71260113 (extends item):
    __construct:
      type: money
    refId: TX71260113
    editable: false
    exchangeMethod: 2

  item_TX71260040 (extends item):
    __construct:
      type: money
    refId: TX71260040
    editable: false
    exchangeMethod: 4

  item_TX71260011 (extends item):
    __construct:
      type: money
    refId: TX71260011
    editable: true
    exchangeMethod: 2

  item_TX71260021 (extends item):
    __construct:
      type: money
    refId: TX71260021
    editable: true
    exchangeMethod: 2

  item_TX71260031 (extends item):
    __construct:
      type: money
    refId: TX71260031
    editable: true
    exchangeMethod: 2

  item_TX71260041 (extends item):
    __construct:
      type: money
    refId: TX71260041
    editable: true
    exchangeMethod: 2

  item_TX71260050 (extends item):
    __construct:
      type: diff
    refId: TX71260050
    editable: false
    exchangeMethod: 2

  item_TX91260070 (extends item):
    __construct:
      type: money
    refId: TX91260070
    editable: false
    exchangeMethod: 2

  item_TX91260090 (extends item):
    __construct:
      type: money
    refId: TX91260090
    editable: false
    exchangeMethod: 2

  item_TX71260060 (extends item):
    __construct:
      type: money
    refId: TX71260060
    editable: false
    exchangeMethod: 2

  item_TX71260070 (extends item):
    __construct:
      type: money
    refId: TX71260070
    editable: false
    exchangeMethod: 2

  item_TX91260060 (extends item):
    __construct:
      type: money
    refId: TX91260060
    editable: false
    exchangeMethod: 2

  item_TX91260095 (extends item):
    __construct:
      type: money
    refId: TX91260095
    editable: false
    exchangeMethod: 2

  item_TX71260080 (extends item):
    __construct:
      type: money
    refId: TX71260080
    editable: false
    exchangeMethod: 2

  item_TX91260101 (extends item):
    __construct:
      type: money
    refId: TX91260101
    editable: false
    exchangeMethod: 2

  item_TX71260090 (extends item):
    __construct:
      type: money
    refId: TX71260090
    editable: false
    exchangeMethod: 2

  item_TX71300101 (extends item):
    __construct:
      type: money
    refId: TX71300101
    editable: false
    exchangeMethod: 2

  item_TX71300010 (extends item):
    __construct:
      type: money
    refId: TX71300010
    editable: false
    exchangeMethod: 4

  item_TX71300111 (extends item):
    __construct:
      type: money
    refId: TX71300111
    editable: false
    exchangeMethod: 2

  item_TX71300020 (extends item):
    __construct:
      type: money
    refId: TX71300020
    editable: false
    exchangeMethod: 4

  item_TX71300112 (extends item):
    __construct:
      type: money
    refId: TX71300112
    editable: false
    exchangeMethod: 2

  item_TX71300030 (extends item):
    __construct:
      type: money
    refId: TX71300030
    editable: false
    exchangeMethod: 4

  item_TX71300113 (extends item):
    __construct:
      type: money
    refId: TX71300113
    editable: false
    exchangeMethod: 2

  item_TX71300040 (extends item):
    __construct:
      type: money
    refId: TX71300040
    editable: false
    exchangeMethod: 4

  item_TX71300011 (extends item):
    __construct:
      type: money
    refId: TX71300011
    editable: true
    exchangeMethod: 2

  item_TX71300021 (extends item):
    __construct:
      type: money
    refId: TX71300021
    editable: true
    exchangeMethod: 2

  item_TX71300031 (extends item):
    __construct:
      type: money
    refId: TX71300031
    editable: true
    exchangeMethod: 2

  item_TX71300041 (extends item):
    __construct:
      type: money
    refId: TX71300041
    editable: true
    exchangeMethod: 2

  item_TX71300050 (extends item):
    __construct:
      type: diff
    refId: TX71300050
    editable: false
    exchangeMethod: 2

  item_TX91300070 (extends item):
    __construct:
      type: money
    refId: TX91300070
    editable: false
    exchangeMethod: 2

  item_TX91300090 (extends item):
    __construct:
      type: money
    refId: TX91300090
    editable: true
    exchangeMethod: 2

  item_TX71300060 (extends item):
    __construct:
      type: money
    refId: TX71300060
    editable: false
    exchangeMethod: 2

  item_TX71300070 (extends item):
    __construct:
      type: money
    refId: TX71300070
    editable: false
    exchangeMethod: 2

  item_TX91300060 (extends item):
    __construct:
      type: money
    refId: TX91300060
    editable: true
    exchangeMethod: 2

  item_TX91300095 (extends item):
    __construct:
      type: money
    refId: TX91300095
    editable: true
    exchangeMethod: 2

  item_TX71300080 (extends item):
    __construct:
      type: money
    refId: TX71300080
    editable: false
    exchangeMethod: 2

  item_TX91300101 (extends item):
    __construct:
      type: money
    refId: TX91300101
    editable: false
    exchangeMethod: 2

  item_TX71300090 (extends item):
    __construct:
      type: money
    refId: TX71300090
    editable: false
    exchangeMethod: 2

  item_TX71310101 (extends item):
    __construct:
      type: money
    refId: TX71310101
    editable: false
    exchangeMethod: 2

  item_TX71310010 (extends item):
    __construct:
      type: money
    refId: TX71310010
    editable: false
    exchangeMethod: 4

  item_TX71310111 (extends item):
    __construct:
      type: money
    refId: TX71310111
    editable: false
    exchangeMethod: 2

  item_TX71310020 (extends item):
    __construct:
      type: money
    refId: TX71310020
    editable: false
    exchangeMethod: 4

  item_TX71310112 (extends item):
    __construct:
      type: money
    refId: TX71310112
    editable: false
    exchangeMethod: 2

  item_TX71310030 (extends item):
    __construct:
      type: money
    refId: TX71310030
    editable: false
    exchangeMethod: 4

  item_TX71310113 (extends item):
    __construct:
      type: money
    refId: TX71310113
    editable: false
    exchangeMethod: 2

  item_TX71310040 (extends item):
    __construct:
      type: money
    refId: TX71310040
    editable: false
    exchangeMethod: 4

  item_TX71310011 (extends item):
    __construct:
      type: money
    refId: TX71310011
    editable: true
    exchangeMethod: 2

  item_TX71310021 (extends item):
    __construct:
      type: money
    refId: TX71310021
    editable: true
    exchangeMethod: 2

  item_TX71310031 (extends item):
    __construct:
      type: money
    refId: TX71310031
    editable: true
    exchangeMethod: 2

  item_TX71310041 (extends item):
    __construct:
      type: money
    refId: TX71310041
    editable: true
    exchangeMethod: 2

  item_TX71310050 (extends item):
    __construct:
      type: diff
    refId: TX71310050
    editable: false
    exchangeMethod: 2

  item_TX91310070 (extends item):
    __construct:
      type: money
    refId: TX91310070
    editable: false
    exchangeMethod: 2

  item_TX91310090 (extends item):
    __construct:
      type: money
    refId: TX91310090
    editable: true
    exchangeMethod: 2

  item_TX71310060 (extends item):
    __construct:
      type: money
    refId: TX71310060
    editable: false
    exchangeMethod: 2

  item_TX71310070 (extends item):
    __construct:
      type: money
    refId: TX71310070
    editable: false
    exchangeMethod: 2

  item_TX91310060 (extends item):
    __construct:
      type: money
    refId: TX91310060
    editable: true
    exchangeMethod: 2

  item_TX91310095 (extends item):
    __construct:
      type: money
    refId: TX91310095
    editable: true
    exchangeMethod: 2

  item_TX71310080 (extends item):
    __construct:
      type: money
    refId: TX71310080
    editable: false
    exchangeMethod: 2

  item_TX91310101 (extends item):
    __construct:
      type: money
    refId: TX91310101
    editable: false
    exchangeMethod: 2

  item_TX71310090 (extends item):
    __construct:
      type: money
    refId: TX71310090
    editable: false
    exchangeMethod: 2

  item_TX71320101 (extends item):
    __construct:
      type: money
    refId: TX71320101
    editable: false
    exchangeMethod: 2

  item_TX71320010 (extends item):
    __construct:
      type: money
    refId: TX71320010
    editable: false
    exchangeMethod: 4

  item_TX71320111 (extends item):
    __construct:
      type: money
    refId: TX71320111
    editable: false
    exchangeMethod: 2

  item_TX71320020 (extends item):
    __construct:
      type: money
    refId: TX71320020
    editable: false
    exchangeMethod: 4

  item_TX71320112 (extends item):
    __construct:
      type: money
    refId: TX71320112
    editable: false
    exchangeMethod: 2

  item_TX71320030 (extends item):
    __construct:
      type: money
    refId: TX71320030
    editable: false
    exchangeMethod: 4

  item_TX71320113 (extends item):
    __construct:
      type: money
    refId: TX71320113
    editable: false
    exchangeMethod: 2

  item_TX71320040 (extends item):
    __construct:
      type: money
    refId: TX71320040
    editable: false
    exchangeMethod: 4

  item_TX71320011 (extends item):
    __construct:
      type: money
    refId: TX71320011
    editable: true
    exchangeMethod: 2

  item_TX71320021 (extends item):
    __construct:
      type: money
    refId: TX71320021
    editable: true
    exchangeMethod: 2

  item_TX71320031 (extends item):
    __construct:
      type: money
    refId: TX71320031
    editable: true
    exchangeMethod: 2

  item_TX71320041 (extends item):
    __construct:
      type: money
    refId: TX71320041
    editable: true
    exchangeMethod: 2

  item_TX71320050 (extends item):
    __construct:
      type: diff
    refId: TX71320050
    editable: false
    exchangeMethod: 2

  item_TX91320070 (extends item):
    __construct:
      type: money
    refId: TX91320070
    editable: false
    exchangeMethod: 2

  item_TX91320090 (extends item):
    __construct:
      type: money
    refId: TX91320090
    editable: true
    exchangeMethod: 2

  item_TX71320060 (extends item):
    __construct:
      type: money
    refId: TX71320060
    editable: false
    exchangeMethod: 2

  item_TX71320070 (extends item):
    __construct:
      type: money
    refId: TX71320070
    editable: false
    exchangeMethod: 2

  item_TX91320060 (extends item):
    __construct:
      type: money
    refId: TX91320060
    editable: true
    exchangeMethod: 2

  item_TX91320095 (extends item):
    __construct:
      type: money
    refId: TX91320095
    editable: true
    exchangeMethod: 2

  item_TX71320080 (extends item):
    __construct:
      type: money
    refId: TX71320080
    editable: false
    exchangeMethod: 2

  item_TX91320101 (extends item):
    __construct:
      type: money
    refId: TX91320101
    editable: false
    exchangeMethod: 2

  item_TX71320090 (extends item):
    __construct:
      type: money
    refId: TX71320090
    editable: false
    exchangeMethod: 2

  item_TX71330101 (extends item):
    __construct:
      type: money
    refId: TX71330101
    editable: false
    exchangeMethod: 2

  item_TX71330010 (extends item):
    __construct:
      type: money
    refId: TX71330010
    editable: false
    exchangeMethod: 4

  item_TX71330111 (extends item):
    __construct:
      type: money
    refId: TX71330111
    editable: false
    exchangeMethod: 2

  item_TX71330020 (extends item):
    __construct:
      type: money
    refId: TX71330020
    editable: false
    exchangeMethod: 4

  item_TX71330112 (extends item):
    __construct:
      type: money
    refId: TX71330112
    editable: false
    exchangeMethod: 2

  item_TX71330030 (extends item):
    __construct:
      type: money
    refId: TX71330030
    editable: false
    exchangeMethod: 4

  item_TX71330113 (extends item):
    __construct:
      type: money
    refId: TX71330113
    editable: false
    exchangeMethod: 2

  item_TX71330040 (extends item):
    __construct:
      type: money
    refId: TX71330040
    editable: false
    exchangeMethod: 4

  item_TX71330011 (extends item):
    __construct:
      type: money
    refId: TX71330011
    editable: true
    exchangeMethod: 2

  item_TX71330021 (extends item):
    __construct:
      type: money
    refId: TX71330021
    editable: true
    exchangeMethod: 2

  item_TX71330031 (extends item):
    __construct:
      type: money
    refId: TX71330031
    editable: true
    exchangeMethod: 2

  item_TX71330041 (extends item):
    __construct:
      type: money
    refId: TX71330041
    editable: true
    exchangeMethod: 2

  item_TX71330050 (extends item):
    __construct:
      type: diff
    refId: TX71330050
    editable: false
    exchangeMethod: 2

  item_TX91330070 (extends item):
    __construct:
      type: money
    refId: TX91330070
    editable: false
    exchangeMethod: 2

  item_TX91330090 (extends item):
    __construct:
      type: money
    refId: TX91330090
    editable: true
    exchangeMethod: 2

  item_TX71330060 (extends item):
    __construct:
      type: money
    refId: TX71330060
    editable: false
    exchangeMethod: 2

  item_TX71330070 (extends item):
    __construct:
      type: money
    refId: TX71330070
    editable: false
    exchangeMethod: 2

  item_TX91330060 (extends item):
    __construct:
      type: money
    refId: TX91330060
    editable: true
    exchangeMethod: 2

  item_TX91330095 (extends item):
    __construct:
      type: money
    refId: TX91330095
    editable: true
    exchangeMethod: 2

  item_TX71330080 (extends item):
    __construct:
      type: money
    refId: TX71330080
    editable: false
    exchangeMethod: 2

  item_TX91330101 (extends item):
    __construct:
      type: money
    refId: TX91330101
    editable: false
    exchangeMethod: 2

  item_TX71330090 (extends item):
    __construct:
      type: money
    refId: TX71330090
    editable: false
    exchangeMethod: 2

  item_TX71360101 (extends item):
    __construct:
      type: money
    refId: TX71360101
    editable: false
    exchangeMethod: 2

  item_TX71360010 (extends item):
    __construct:
      type: money
    refId: TX71360010
    editable: false
    exchangeMethod: 4

  item_TX71360111 (extends item):
    __construct:
      type: money
    refId: TX71360111
    editable: false
    exchangeMethod: 2

  item_TX71360020 (extends item):
    __construct:
      type: money
    refId: TX71360020
    editable: false
    exchangeMethod: 4

  item_TX71360112 (extends item):
    __construct:
      type: money
    refId: TX71360112
    editable: false
    exchangeMethod: 2

  item_TX71360030 (extends item):
    __construct:
      type: money
    refId: TX71360030
    editable: false
    exchangeMethod: 4

  item_TX71360113 (extends item):
    __construct:
      type: money
    refId: TX71360113
    editable: false
    exchangeMethod: 2

  item_TX71360040 (extends item):
    __construct:
      type: money
    refId: TX71360040
    editable: false
    exchangeMethod: 4

  item_TX71360011 (extends item):
    __construct:
      type: money
    refId: TX71360011
    editable: true
    exchangeMethod: 2

  item_TX71360021 (extends item):
    __construct:
      type: money
    refId: TX71360021
    editable: true
    exchangeMethod: 2

  item_TX71360031 (extends item):
    __construct:
      type: money
    refId: TX71360031
    editable: true
    exchangeMethod: 2

  item_TX71360041 (extends item):
    __construct:
      type: money
    refId: TX71360041
    editable: true
    exchangeMethod: 2

  item_TX71360050 (extends item):
    __construct:
      type: diff
    refId: TX71360050
    editable: false
    exchangeMethod: 2

  item_TX91360070 (extends item):
    __construct:
      type: money
    refId: TX91360070
    editable: false
    exchangeMethod: 2

  item_TX91360090 (extends item):
    __construct:
      type: money
    refId: TX91360090
    editable: true
    exchangeMethod: 2

  item_TX71360060 (extends item):
    __construct:
      type: money
    refId: TX71360060
    editable: false
    exchangeMethod: 2

  item_TX71360070 (extends item):
    __construct:
      type: money
    refId: TX71360070
    editable: false
    exchangeMethod: 2

  item_TX91360060 (extends item):
    __construct:
      type: money
    refId: TX91360060
    editable: true
    exchangeMethod: 2

  item_TX91360095 (extends item):
    __construct:
      type: money
    refId: TX91360095
    editable: true
    exchangeMethod: 2

  item_TX71360080 (extends item):
    __construct:
      type: money
    refId: TX71360080
    editable: false
    exchangeMethod: 2

  item_TX91360101 (extends item):
    __construct:
      type: money
    refId: TX91360101
    editable: false
    exchangeMethod: 2

  item_TX71360090 (extends item):
    __construct:
      type: money
    refId: TX71360090
    editable: false
    exchangeMethod: 2

  item_TX71370101 (extends item):
    __construct:
      type: money
    refId: TX71370101
    editable: false
    exchangeMethod: 2

  item_TX71370010 (extends item):
    __construct:
      type: money
    refId: TX71370010
    editable: false
    exchangeMethod: 4

  item_TX71370111 (extends item):
    __construct:
      type: money
    refId: TX71370111
    editable: false
    exchangeMethod: 2

  item_TX71370020 (extends item):
    __construct:
      type: money
    refId: TX71370020
    editable: false
    exchangeMethod: 4

  item_TX71370112 (extends item):
    __construct:
      type: money
    refId: TX71370112
    editable: false
    exchangeMethod: 2

  item_TX71370030 (extends item):
    __construct:
      type: money
    refId: TX71370030
    editable: false
    exchangeMethod: 4

  item_TX71370113 (extends item):
    __construct:
      type: money
    refId: TX71370113
    editable: false
    exchangeMethod: 2

  item_TX71370040 (extends item):
    __construct:
      type: money
    refId: TX71370040
    editable: false
    exchangeMethod: 4

  item_TX71370011 (extends item):
    __construct:
      type: money
    refId: TX71370011
    editable: true
    exchangeMethod: 2

  item_TX71370021 (extends item):
    __construct:
      type: money
    refId: TX71370021
    editable: true
    exchangeMethod: 2

  item_TX71370031 (extends item):
    __construct:
      type: money
    refId: TX71370031
    editable: true
    exchangeMethod: 2

  item_TX71370041 (extends item):
    __construct:
      type: money
    refId: TX71370041
    editable: true
    exchangeMethod: 2

  item_TX71370050 (extends item):
    __construct:
      type: diff
    refId: TX71370050
    editable: false
    exchangeMethod: 2

  item_TX91370070 (extends item):
    __construct:
      type: money
    refId: TX91370070
    editable: false
    exchangeMethod: 2

  item_TX91370090 (extends item):
    __construct:
      type: money
    refId: TX91370090
    editable: true
    exchangeMethod: 2

  item_TX71370060 (extends item):
    __construct:
      type: money
    refId: TX71370060
    editable: false
    exchangeMethod: 2

  item_TX71370070 (extends item):
    __construct:
      type: money
    refId: TX71370070
    editable: false
    exchangeMethod: 2

  item_TX91370060 (extends item):
    __construct:
      type: money
    refId: TX91370060
    editable: true
    exchangeMethod: 2

  item_TX91370095 (extends item):
    __construct:
      type: money
    refId: TX91370095
    editable: true
    exchangeMethod: 2

  item_TX71370080 (extends item):
    __construct:
      type: money
    refId: TX71370080
    editable: false
    exchangeMethod: 2

  item_TX91370101 (extends item):
    __construct:
      type: money
    refId: TX91370101
    editable: false
    exchangeMethod: 2

  item_TX71370090 (extends item):
    __construct:
      type: money
    refId: TX71370090
    editable: false
    exchangeMethod: 2

  item_TX71380101 (extends item):
    __construct:
      type: money
    refId: TX71380101
    editable: false
    exchangeMethod: 2

  item_TX71380010 (extends item):
    __construct:
      type: money
    refId: TX71380010
    editable: false
    exchangeMethod: 4

  item_TX71380111 (extends item):
    __construct:
      type: money
    refId: TX71380111
    editable: false
    exchangeMethod: 2

  item_TX71380020 (extends item):
    __construct:
      type: money
    refId: TX71380020
    editable: false
    exchangeMethod: 4

  item_TX71380112 (extends item):
    __construct:
      type: money
    refId: TX71380112
    editable: false
    exchangeMethod: 2

  item_TX71380030 (extends item):
    __construct:
      type: money
    refId: TX71380030
    editable: false
    exchangeMethod: 4

  item_TX71380113 (extends item):
    __construct:
      type: money
    refId: TX71380113
    editable: false
    exchangeMethod: 2

  item_TX71380040 (extends item):
    __construct:
      type: money
    refId: TX71380040
    editable: false
    exchangeMethod: 4

  item_TX71380011 (extends item):
    __construct:
      type: money
    refId: TX71380011
    editable: true
    exchangeMethod: 2

  item_TX71380021 (extends item):
    __construct:
      type: money
    refId: TX71380021
    editable: true
    exchangeMethod: 2

  item_TX71380031 (extends item):
    __construct:
      type: money
    refId: TX71380031
    editable: true
    exchangeMethod: 2

  item_TX71380041 (extends item):
    __construct:
      type: money
    refId: TX71380041
    editable: true
    exchangeMethod: 2

  item_TX71380050 (extends item):
    __construct:
      type: diff
    refId: TX71380050
    editable: false
    exchangeMethod: 2

  item_TX91380070 (extends item):
    __construct:
      type: money
    refId: TX91380070
    editable: false
    exchangeMethod: 2

  item_TX91380090 (extends item):
    __construct:
      type: money
    refId: TX91380090
    editable: true
    exchangeMethod: 2

  item_TX71380060 (extends item):
    __construct:
      type: money
    refId: TX71380060
    editable: false
    exchangeMethod: 2

  item_TX71380070 (extends item):
    __construct:
      type: money
    refId: TX71380070
    editable: false
    exchangeMethod: 2

  item_TX91380060 (extends item):
    __construct:
      type: money
    refId: TX91380060
    editable: true
    exchangeMethod: 2

  item_TX91380095 (extends item):
    __construct:
      type: money
    refId: TX91380095
    editable: true
    exchangeMethod: 2

  item_TX71380080 (extends item):
    __construct:
      type: money
    refId: TX71380080
    editable: false
    exchangeMethod: 2

  item_TX91380101 (extends item):
    __construct:
      type: money
    refId: TX91380101
    editable: false
    exchangeMethod: 2

  item_TX71380090 (extends item):
    __construct:
      type: money
    refId: TX71380090
    editable: false
    exchangeMethod: 2

  item_TX71400101 (extends item):
    __construct:
      type: money
    refId: TX71400101
    editable: false
    exchangeMethod: 2

  item_TX71400010 (extends item):
    __construct:
      type: money
    refId: TX71400010
    editable: false
    exchangeMethod: 4

  item_TX71400111 (extends item):
    __construct:
      type: money
    refId: TX71400111
    editable: false
    exchangeMethod: 2

  item_TX71400020 (extends item):
    __construct:
      type: money
    refId: TX71400020
    editable: false
    exchangeMethod: 4

  item_TX71400112 (extends item):
    __construct:
      type: money
    refId: TX71400112
    editable: false
    exchangeMethod: 2

  item_TX71400030 (extends item):
    __construct:
      type: money
    refId: TX71400030
    editable: false
    exchangeMethod: 4

  item_TX71400113 (extends item):
    __construct:
      type: money
    refId: TX71400113
    editable: false
    exchangeMethod: 2

  item_TX71400040 (extends item):
    __construct:
      type: money
    refId: TX71400040
    editable: false
    exchangeMethod: 4

  item_TX71400011 (extends item):
    __construct:
      type: money
    refId: TX71400011
    editable: true
    exchangeMethod: 2

  item_TX71400021 (extends item):
    __construct:
      type: money
    refId: TX71400021
    editable: true
    exchangeMethod: 2

  item_TX71400031 (extends item):
    __construct:
      type: money
    refId: TX71400031
    editable: true
    exchangeMethod: 2

  item_TX71400041 (extends item):
    __construct:
      type: money
    refId: TX71400041
    editable: true
    exchangeMethod: 2

  item_TX71400050 (extends item):
    __construct:
      type: diff
    refId: TX71400050
    editable: false
    exchangeMethod: 2

  item_TX91400070 (extends item):
    __construct:
      type: money
    refId: TX91400070
    editable: false
    exchangeMethod: 2

  item_TX91400090 (extends item):
    __construct:
      type: money
    refId: TX91400090
    editable: true
    exchangeMethod: 2

  item_TX71400060 (extends item):
    __construct:
      type: money
    refId: TX71400060
    editable: false
    exchangeMethod: 2

  item_TX71400070 (extends item):
    __construct:
      type: money
    refId: TX71400070
    editable: false
    exchangeMethod: 2

  item_TX91400060 (extends item):
    __construct:
      type: money
    refId: TX91400060
    editable: true
    exchangeMethod: 2

  item_TX91400095 (extends item):
    __construct:
      type: money
    refId: TX91400095
    editable: true
    exchangeMethod: 2

  item_TX71400080 (extends item):
    __construct:
      type: money
    refId: TX71400080
    editable: false
    exchangeMethod: 2

  item_TX91400101 (extends item):
    __construct:
      type: money
    refId: TX91400101
    editable: false
    exchangeMethod: 2

  item_TX71400090 (extends item):
    __construct:
      type: money
    refId: TX71400090
    editable: false
    exchangeMethod: 2

  item_TX71420101 (extends item):
    __construct:
      type: money
    refId: TX71420101
    editable: false
    exchangeMethod: 2

  item_TX71420010 (extends item):
    __construct:
      type: money
    refId: TX71420010
    editable: false
    exchangeMethod: 4

  item_TX71420111 (extends item):
    __construct:
      type: money
    refId: TX71420111
    editable: false
    exchangeMethod: 2

  item_TX71420020 (extends item):
    __construct:
      type: money
    refId: TX71420020
    editable: false
    exchangeMethod: 4

  item_TX71420112 (extends item):
    __construct:
      type: money
    refId: TX71420112
    editable: false
    exchangeMethod: 2

  item_TX71420030 (extends item):
    __construct:
      type: money
    refId: TX71420030
    editable: false
    exchangeMethod: 4

  item_TX71420113 (extends item):
    __construct:
      type: money
    refId: TX71420113
    editable: false
    exchangeMethod: 2

  item_TX71420040 (extends item):
    __construct:
      type: money
    refId: TX71420040
    editable: false
    exchangeMethod: 4

  item_TX71420011 (extends item):
    __construct:
      type: money
    refId: TX71420011
    editable: true
    exchangeMethod: 2

  item_TX71420021 (extends item):
    __construct:
      type: money
    refId: TX71420021
    editable: true
    exchangeMethod: 2

  item_TX71420031 (extends item):
    __construct:
      type: money
    refId: TX71420031
    editable: true
    exchangeMethod: 2

  item_TX71420041 (extends item):
    __construct:
      type: money
    refId: TX71420041
    editable: true
    exchangeMethod: 2

  item_TX71420050 (extends item):
    __construct:
      type: diff
    refId: TX71420050
    editable: false
    exchangeMethod: 2

  item_TX91420070 (extends item):
    __construct:
      type: money
    refId: TX91420070
    editable: false
    exchangeMethod: 2

  item_TX91420090 (extends item):
    __construct:
      type: money
    refId: TX91420090
    editable: true
    exchangeMethod: 2

  item_TX71420060 (extends item):
    __construct:
      type: money
    refId: TX71420060
    editable: false
    exchangeMethod: 2

  item_TX71420070 (extends item):
    __construct:
      type: money
    refId: TX71420070
    editable: false
    exchangeMethod: 2

  item_TX91420060 (extends item):
    __construct:
      type: money
    refId: TX91420060
    editable: true
    exchangeMethod: 2

  item_TX91420095 (extends item):
    __construct:
      type: money
    refId: TX91420095
    editable: true
    exchangeMethod: 2

  item_TX71420080 (extends item):
    __construct:
      type: money
    refId: TX71420080
    editable: false
    exchangeMethod: 2

  item_TX91420101 (extends item):
    __construct:
      type: money
    refId: TX91420101
    editable: false
    exchangeMethod: 2

  item_TX71420090 (extends item):
    __construct:
      type: money
    refId: TX71420090
    editable: false
    exchangeMethod: 2

  item_TX71430101 (extends item):
    __construct:
      type: money
    refId: TX71430101
    editable: false
    exchangeMethod: 2

  item_TX71430010 (extends item):
    __construct:
      type: money
    refId: TX71430010
    editable: false
    exchangeMethod: 4

  item_TX71430111 (extends item):
    __construct:
      type: money
    refId: TX71430111
    editable: false
    exchangeMethod: 2

  item_TX71430020 (extends item):
    __construct:
      type: money
    refId: TX71430020
    editable: false
    exchangeMethod: 4

  item_TX71430112 (extends item):
    __construct:
      type: money
    refId: TX71430112
    editable: false
    exchangeMethod: 2

  item_TX71430030 (extends item):
    __construct:
      type: money
    refId: TX71430030
    editable: false
    exchangeMethod: 4

  item_TX71430113 (extends item):
    __construct:
      type: money
    refId: TX71430113
    editable: false
    exchangeMethod: 2

  item_TX71430040 (extends item):
    __construct:
      type: money
    refId: TX71430040
    editable: false
    exchangeMethod: 4

  item_TX71430011 (extends item):
    __construct:
      type: money
    refId: TX71430011
    editable: true
    exchangeMethod: 2

  item_TX71430021 (extends item):
    __construct:
      type: money
    refId: TX71430021
    editable: true
    exchangeMethod: 2

  item_TX71430031 (extends item):
    __construct:
      type: money
    refId: TX71430031
    editable: true
    exchangeMethod: 2

  item_TX71430041 (extends item):
    __construct:
      type: money
    refId: TX71430041
    editable: true
    exchangeMethod: 2

  item_TX71430050 (extends item):
    __construct:
      type: diff
    refId: TX71430050
    editable: false
    exchangeMethod: 2

  item_TX91430070 (extends item):
    __construct:
      type: money
    refId: TX91430070
    editable: false
    exchangeMethod: 2

  item_TX91430090 (extends item):
    __construct:
      type: money
    refId: TX91430090
    editable: true
    exchangeMethod: 2

  item_TX71430060 (extends item):
    __construct:
      type: money
    refId: TX71430060
    editable: false
    exchangeMethod: 2

  item_TX71430070 (extends item):
    __construct:
      type: money
    refId: TX71430070
    editable: false
    exchangeMethod: 2

  item_TX91430060 (extends item):
    __construct:
      type: money
    refId: TX91430060
    editable: true
    exchangeMethod: 2

  item_TX91430095 (extends item):
    __construct:
      type: money
    refId: TX91430095
    editable: true
    exchangeMethod: 2

  item_TX71430080 (extends item):
    __construct:
      type: money
    refId: TX71430080
    editable: false
    exchangeMethod: 2

  item_TX91430101 (extends item):
    __construct:
      type: money
    refId: TX91430101
    editable: false
    exchangeMethod: 2

  item_TX71430090 (extends item):
    __construct:
      type: money
    refId: TX71430090
    editable: false
    exchangeMethod: 2

  item_TX71450010 (extends item):
    __construct:
      type: money
    refId: TX71450010
    editable: false
    exchangeMethod: 4

  item_TX71450020 (extends item):
    __construct:
      type: money
    refId: TX71450020
    editable: false
    exchangeMethod: 4

  item_TX71450030 (extends item):
    __construct:
      type: money
    refId: TX71450030
    editable: false
    exchangeMethod: 4

  item_TX71450040 (extends item):
    __construct:
      type: money
    refId: TX71450040
    editable: false
    exchangeMethod: 4

  item_TX71450011 (extends item):
    __construct:
      type: money
    refId: TX71450011
    editable: true
    exchangeMethod: 2

  item_TX71450021 (extends item):
    __construct:
      type: money
    refId: TX71450021
    editable: true
    exchangeMethod: 2

  item_TX71450031 (extends item):
    __construct:
      type: money
    refId: TX71450031
    editable: true
    exchangeMethod: 2

  item_TX71450041 (extends item):
    __construct:
      type: money
    refId: TX71450041
    editable: true
    exchangeMethod: 2

  item_TX71450050 (extends item):
    __construct:
      type: diff
    refId: TX71450050
    editable: false
    exchangeMethod: 2

  item_TX71450113 (extends item):
    __construct:
      type: money
    refId: TX71450113
    editable: false
    exchangeMethod: 2

  item_TX91200010 (extends item):
    __construct:
      type: money
    refId: TX91200010
    editable: true
    exchangeMethod: 2

  item_TX91200010P (extends item):
    __construct:
      type: money
    refId: TX91200010P
    editable: false
    exchangeMethod: 4

  item_TX91200020 (extends item):
    __construct:
      type: money
    refId: TX91200020
    editable: true
    exchangeMethod: 2

  item_TX91200020P (extends item):
    __construct:
      type: money
    refId: TX91200020P
    editable: false
    exchangeMethod: 4

  item_TX91200030 (extends item):
    __construct:
      type: money
    refId: TX91200030
    editable: false
    exchangeMethod: 2

  item_TX91200030P (extends item):
    __construct:
      type: money
    refId: TX91200030P
    editable: false
    exchangeMethod: 4

  item_TX91200040 (extends item):
    __construct:
      type: money
    refId: TX91200040
    editable: true
    exchangeMethod: 2

  item_TX91200040P (extends item):
    __construct:
      type: money
    refId: TX91200040P
    editable: false
    exchangeMethod: 4

  item_TX91200050 (extends item):
    __construct:
      type: money
    refId: TX91200050
    editable: true
    exchangeMethod: 2

  item_TX91200050P (extends item):
    __construct:
      type: money
    refId: TX91200050P
    editable: false
    exchangeMethod: 4

  item_TX91200060P (extends item):
    __construct:
      type: money
    refId: TX91200060P
    editable: false
    exchangeMethod: 4

  item_TX91200070P (extends item):
    __construct:
      type: money
    refId: TX91200070P
    editable: false
    exchangeMethod: 4

  item_TX91200080 (extends item):
    __construct:
      type: money
    refId: TX91200080
    editable: true
    exchangeMethod: 2

  item_TX91200100 (extends item):
    __construct:
      type: money
    refId: TX91200100
    editable: false
    exchangeMethod: 2

  item_TX91200085 (extends item):
    __construct:
      type: money
    refId: TX91200085
    editable: true
    exchangeMethod: 2

  item_TX91200111 (extends item):
    __construct:
      type: money
    refId: TX91200111
    editable: false
    exchangeMethod: 2

  item_TX91210010 (extends item):
    __construct:
      type: money
    refId: TX91210010
    editable: true
    exchangeMethod: 2

  item_TX91210010P (extends item):
    __construct:
      type: money
    refId: TX91210010P
    editable: false
    exchangeMethod: 4

  item_TX91210020 (extends item):
    __construct:
      type: money
    refId: TX91210020
    editable: true
    exchangeMethod: 2

  item_TX91210020P (extends item):
    __construct:
      type: money
    refId: TX91210020P
    editable: false
    exchangeMethod: 4

  item_TX91210030 (extends item):
    __construct:
      type: money
    refId: TX91210030
    editable: false
    exchangeMethod: 2

  item_TX91210030P (extends item):
    __construct:
      type: money
    refId: TX91210030P
    editable: false
    exchangeMethod: 4

  item_TX91210040 (extends item):
    __construct:
      type: money
    refId: TX91210040
    editable: true
    exchangeMethod: 2

  item_TX91210040P (extends item):
    __construct:
      type: money
    refId: TX91210040P
    editable: false
    exchangeMethod: 4

  item_TX91210050 (extends item):
    __construct:
      type: money
    refId: TX91210050
    editable: true
    exchangeMethod: 2

  item_TX91210050P (extends item):
    __construct:
      type: money
    refId: TX91210050P
    editable: false
    exchangeMethod: 4

  item_TX91210060P (extends item):
    __construct:
      type: money
    refId: TX91210060P
    editable: false
    exchangeMethod: 4

  item_TX91210070P (extends item):
    __construct:
      type: money
    refId: TX91210070P
    editable: false
    exchangeMethod: 4

  item_TX91210080 (extends item):
    __construct:
      type: money
    refId: TX91210080
    editable: true
    exchangeMethod: 2

  item_TX91210100 (extends item):
    __construct:
      type: money
    refId: TX91210100
    editable: false
    exchangeMethod: 2

  item_TX91210085 (extends item):
    __construct:
      type: money
    refId: TX91210085
    editable: true
    exchangeMethod: 2

  item_TX91210111 (extends item):
    __construct:
      type: money
    refId: TX91210111
    editable: false
    exchangeMethod: 2

  item_TX91220010 (extends item):
    __construct:
      type: money
    refId: TX91220010
    editable: false
    exchangeMethod: 2

  item_TX91220010P (extends item):
    __construct:
      type: money
    refId: TX91220010P
    editable: false
    exchangeMethod: 4

  item_TX91220020 (extends item):
    __construct:
      type: money
    refId: TX91220020
    editable: false
    exchangeMethod: 2

  item_TX91220020P (extends item):
    __construct:
      type: money
    refId: TX91220020P
    editable: false
    exchangeMethod: 4

  item_TX91220030 (extends item):
    __construct:
      type: money
    refId: TX91220030
    editable: false
    exchangeMethod: 2

  item_TX91220030P (extends item):
    __construct:
      type: money
    refId: TX91220030P
    editable: false
    exchangeMethod: 4

  item_TX91220040 (extends item):
    __construct:
      type: money
    refId: TX91220040
    editable: false
    exchangeMethod: 2

  item_TX91220040P (extends item):
    __construct:
      type: money
    refId: TX91220040P
    editable: false
    exchangeMethod: 4

  item_TX91220050 (extends item):
    __construct:
      type: money
    refId: TX91220050
    editable: false
    exchangeMethod: 2

  item_TX91220050P (extends item):
    __construct:
      type: money
    refId: TX91220050P
    editable: false
    exchangeMethod: 4

  item_TX91220060P (extends item):
    __construct:
      type: money
    refId: TX91220060P
    editable: false
    exchangeMethod: 4

  item_TX91220070P (extends item):
    __construct:
      type: money
    refId: TX91220070P
    editable: false
    exchangeMethod: 4

  item_TX91220080 (extends item):
    __construct:
      type: money
    refId: TX91220080
    editable: false
    exchangeMethod: 2

  item_TX91220085 (extends item):
    __construct:
      type: money
    refId: TX91220085
    editable: false
    exchangeMethod: 2

  item_TX91220100 (extends item):
    __construct:
      type: money
    refId: TX91220100
    editable: false
    exchangeMethod: 2

  item_TX91220111 (extends item):
    __construct:
      type: money
    refId: TX91220111
    editable: false
    exchangeMethod: 2

  item_TX91230010 (extends item):
    __construct:
      type: money
    refId: TX91230010
    editable: true
    exchangeMethod: 2

  item_TX91230010P (extends item):
    __construct:
      type: money
    refId: TX91230010P
    editable: false
    exchangeMethod: 4

  item_TX91230020 (extends item):
    __construct:
      type: money
    refId: TX91230020
    editable: true
    exchangeMethod: 2

  item_TX91230020P (extends item):
    __construct:
      type: money
    refId: TX91230020P
    editable: false
    exchangeMethod: 4

  item_TX91230030 (extends item):
    __construct:
      type: money
    refId: TX91230030
    editable: false
    exchangeMethod: 2

  item_TX91230030P (extends item):
    __construct:
      type: money
    refId: TX91230030P
    editable: false
    exchangeMethod: 4

  item_TX91230040 (extends item):
    __construct:
      type: money
    refId: TX91230040
    editable: true
    exchangeMethod: 2

  item_TX91230040P (extends item):
    __construct:
      type: money
    refId: TX91230040P
    editable: false
    exchangeMethod: 4

  item_TX91230050 (extends item):
    __construct:
      type: money
    refId: TX91230050
    editable: true
    exchangeMethod: 2

  item_TX91230050P (extends item):
    __construct:
      type: money
    refId: TX91230050P
    editable: false
    exchangeMethod: 4

  item_TX91230060P (extends item):
    __construct:
      type: money
    refId: TX91230060P
    editable: false
    exchangeMethod: 4

  item_TX91230070P (extends item):
    __construct:
      type: money
    refId: TX91230070P
    editable: false
    exchangeMethod: 4

  item_TX91230080 (extends item):
    __construct:
      type: money
    refId: TX91230080
    editable: true
    exchangeMethod: 2

  item_TX91230100 (extends item):
    __construct:
      type: money
    refId: TX91230100
    editable: false
    exchangeMethod: 2

  item_TX91230085 (extends item):
    __construct:
      type: money
    refId: TX91230085
    editable: true
    exchangeMethod: 2

  item_TX91230111 (extends item):
    __construct:
      type: money
    refId: TX91230111
    editable: false
    exchangeMethod: 2

  item_TX91240010 (extends item):
    __construct:
      type: money
    refId: TX91240010
    editable: true
    exchangeMethod: 2

  item_TX91240010P (extends item):
    __construct:
      type: money
    refId: TX91240010P
    editable: false
    exchangeMethod: 4

  item_TX91240020 (extends item):
    __construct:
      type: money
    refId: TX91240020
    editable: true
    exchangeMethod: 2

  item_TX91240020P (extends item):
    __construct:
      type: money
    refId: TX91240020P
    editable: false
    exchangeMethod: 4

  item_TX91240030 (extends item):
    __construct:
      type: money
    refId: TX91240030
    editable: false
    exchangeMethod: 2

  item_TX91240030P (extends item):
    __construct:
      type: money
    refId: TX91240030P
    editable: false
    exchangeMethod: 4

  item_TX91240040 (extends item):
    __construct:
      type: money
    refId: TX91240040
    editable: true
    exchangeMethod: 2

  item_TX91240040P (extends item):
    __construct:
      type: money
    refId: TX91240040P
    editable: false
    exchangeMethod: 4

  item_TX91240050 (extends item):
    __construct:
      type: money
    refId: TX91240050
    editable: true
    exchangeMethod: 2

  item_TX91240050P (extends item):
    __construct:
      type: money
    refId: TX91240050P
    editable: false
    exchangeMethod: 4

  item_TX91240060P (extends item):
    __construct:
      type: money
    refId: TX91240060P
    editable: false
    exchangeMethod: 4

  item_TX91240070P (extends item):
    __construct:
      type: money
    refId: TX91240070P
    editable: false
    exchangeMethod: 4

  item_TX91240080 (extends item):
    __construct:
      type: money
    refId: TX91240080
    editable: true
    exchangeMethod: 2

  item_TX91240100 (extends item):
    __construct:
      type: money
    refId: TX91240100
    editable: false
    exchangeMethod: 2

  item_TX91240085 (extends item):
    __construct:
      type: money
    refId: TX91240085
    editable: true
    exchangeMethod: 2

  item_TX91240111 (extends item):
    __construct:
      type: money
    refId: TX91240111
    editable: false
    exchangeMethod: 2

  item_TX91250010 (extends item):
    __construct:
      type: money
    refId: TX91250010
    editable: true
    exchangeMethod: 2

  item_TX91250010P (extends item):
    __construct:
      type: money
    refId: TX91250010P
    editable: false
    exchangeMethod: 4

  item_TX91250020 (extends item):
    __construct:
      type: money
    refId: TX91250020
    editable: true
    exchangeMethod: 2

  item_TX91250020P (extends item):
    __construct:
      type: money
    refId: TX91250020P
    editable: false
    exchangeMethod: 4

  item_TX91250030 (extends item):
    __construct:
      type: money
    refId: TX91250030
    editable: false
    exchangeMethod: 2

  item_TX91250030P (extends item):
    __construct:
      type: money
    refId: TX91250030P
    editable: false
    exchangeMethod: 4

  item_TX91250040 (extends item):
    __construct:
      type: money
    refId: TX91250040
    editable: true
    exchangeMethod: 2

  item_TX91250040P (extends item):
    __construct:
      type: money
    refId: TX91250040P
    editable: false
    exchangeMethod: 4

  item_TX91250050 (extends item):
    __construct:
      type: money
    refId: TX91250050
    editable: true
    exchangeMethod: 2

  item_TX91250050P (extends item):
    __construct:
      type: money
    refId: TX91250050P
    editable: false
    exchangeMethod: 4

  item_TX91250060P (extends item):
    __construct:
      type: money
    refId: TX91250060P
    editable: false
    exchangeMethod: 4

  item_TX91250070P (extends item):
    __construct:
      type: money
    refId: TX91250070P
    editable: false
    exchangeMethod: 4

  item_TX91250080 (extends item):
    __construct:
      type: money
    refId: TX91250080
    editable: true
    exchangeMethod: 2

  item_TX91250100 (extends item):
    __construct:
      type: money
    refId: TX91250100
    editable: false
    exchangeMethod: 2

  item_TX91250085 (extends item):
    __construct:
      type: money
    refId: TX91250085
    editable: true
    exchangeMethod: 2

  item_TX91250111 (extends item):
    __construct:
      type: money
    refId: TX91250111
    editable: false
    exchangeMethod: 2

  item_TX91260010 (extends item):
    __construct:
      type: money
    refId: TX91260010
    editable: false
    exchangeMethod: 2

  item_TX91260010P (extends item):
    __construct:
      type: money
    refId: TX91260010P
    editable: false
    exchangeMethod: 4

  item_TX91260020 (extends item):
    __construct:
      type: money
    refId: TX91260020
    editable: false
    exchangeMethod: 2

  item_TX91260020P (extends item):
    __construct:
      type: money
    refId: TX91260020P
    editable: false
    exchangeMethod: 4

  item_TX91260030 (extends item):
    __construct:
      type: money
    refId: TX91260030
    editable: false
    exchangeMethod: 2

  item_TX91260030P (extends item):
    __construct:
      type: money
    refId: TX91260030P
    editable: false
    exchangeMethod: 4

  item_TX91260040 (extends item):
    __construct:
      type: money
    refId: TX91260040
    editable: false
    exchangeMethod: 2

  item_TX91260040P (extends item):
    __construct:
      type: money
    refId: TX91260040P
    editable: false
    exchangeMethod: 4

  item_TX91260050 (extends item):
    __construct:
      type: money
    refId: TX91260050
    editable: false
    exchangeMethod: 2

  item_TX91260050P (extends item):
    __construct:
      type: money
    refId: TX91260050P
    editable: false
    exchangeMethod: 4

  item_TX91260060P (extends item):
    __construct:
      type: money
    refId: TX91260060P
    editable: false
    exchangeMethod: 4

  item_TX91260070P (extends item):
    __construct:
      type: money
    refId: TX91260070P
    editable: false
    exchangeMethod: 4

  item_TX91260080 (extends item):
    __construct:
      type: money
    refId: TX91260080
    editable: false
    exchangeMethod: 2

  item_TX91260085 (extends item):
    __construct:
      type: money
    refId: TX91260085
    editable: false
    exchangeMethod: 2

  item_TX91260100 (extends item):
    __construct:
      type: money
    refId: TX91260100
    editable: false
    exchangeMethod: 2

  item_TX91260111 (extends item):
    __construct:
      type: money
    refId: TX91260111
    editable: false
    exchangeMethod: 2

  item_TX91300010 (extends item):
    __construct:
      type: money
    refId: TX91300010
    editable: true
    exchangeMethod: 2

  item_TX91300010P (extends item):
    __construct:
      type: money
    refId: TX91300010P
    editable: false
    exchangeMethod: 4

  item_TX91300020 (extends item):
    __construct:
      type: money
    refId: TX91300020
    editable: true
    exchangeMethod: 2

  item_TX91300020P (extends item):
    __construct:
      type: money
    refId: TX91300020P
    editable: false
    exchangeMethod: 4

  item_TX91300030 (extends item):
    __construct:
      type: money
    refId: TX91300030
    editable: false
    exchangeMethod: 2

  item_TX91300030P (extends item):
    __construct:
      type: money
    refId: TX91300030P
    editable: false
    exchangeMethod: 4

  item_TX91300040 (extends item):
    __construct:
      type: money
    refId: TX91300040
    editable: true
    exchangeMethod: 2

  item_TX91300040P (extends item):
    __construct:
      type: money
    refId: TX91300040P
    editable: false
    exchangeMethod: 4

  item_TX91300050 (extends item):
    __construct:
      type: money
    refId: TX91300050
    editable: true
    exchangeMethod: 2

  item_TX91300050P (extends item):
    __construct:
      type: money
    refId: TX91300050P
    editable: false
    exchangeMethod: 4

  item_TX91300060P (extends item):
    __construct:
      type: money
    refId: TX91300060P
    editable: false
    exchangeMethod: 4

  item_TX91300070P (extends item):
    __construct:
      type: money
    refId: TX91300070P
    editable: false
    exchangeMethod: 4

  item_TX91300080 (extends item):
    __construct:
      type: money
    refId: TX91300080
    editable: true
    exchangeMethod: 2

  item_TX91300100 (extends item):
    __construct:
      type: money
    refId: TX91300100
    editable: false
    exchangeMethod: 2

  item_TX91300085 (extends item):
    __construct:
      type: money
    refId: TX91300085
    editable: true
    exchangeMethod: 2

  item_TX91300111 (extends item):
    __construct:
      type: money
    refId: TX91300111
    editable: false
    exchangeMethod: 2

  item_TX91310010 (extends item):
    __construct:
      type: money
    refId: TX91310010
    editable: true
    exchangeMethod: 2

  item_TX91310010P (extends item):
    __construct:
      type: money
    refId: TX91310010P
    editable: false
    exchangeMethod: 4

  item_TX91310020 (extends item):
    __construct:
      type: money
    refId: TX91310020
    editable: true
    exchangeMethod: 2

  item_TX91310020P (extends item):
    __construct:
      type: money
    refId: TX91310020P
    editable: false
    exchangeMethod: 4

  item_TX91310030 (extends item):
    __construct:
      type: money
    refId: TX91310030
    editable: false
    exchangeMethod: 2

  item_TX91310030P (extends item):
    __construct:
      type: money
    refId: TX91310030P
    editable: false
    exchangeMethod: 4

  item_TX91310040 (extends item):
    __construct:
      type: money
    refId: TX91310040
    editable: true
    exchangeMethod: 2

  item_TX91310040P (extends item):
    __construct:
      type: money
    refId: TX91310040P
    editable: false
    exchangeMethod: 4

  item_TX91310050 (extends item):
    __construct:
      type: money
    refId: TX91310050
    editable: true
    exchangeMethod: 2

  item_TX91310050P (extends item):
    __construct:
      type: money
    refId: TX91310050P
    editable: false
    exchangeMethod: 4

  item_TX91310060P (extends item):
    __construct:
      type: money
    refId: TX91310060P
    editable: false
    exchangeMethod: 4

  item_TX91310070P (extends item):
    __construct:
      type: money
    refId: TX91310070P
    editable: false
    exchangeMethod: 4

  item_TX91310080 (extends item):
    __construct:
      type: money
    refId: TX91310080
    editable: true
    exchangeMethod: 2

  item_TX91310100 (extends item):
    __construct:
      type: money
    refId: TX91310100
    editable: false
    exchangeMethod: 2

  item_TX91310085 (extends item):
    __construct:
      type: money
    refId: TX91310085
    editable: true
    exchangeMethod: 2

  item_TX91310111 (extends item):
    __construct:
      type: money
    refId: TX91310111
    editable: false
    exchangeMethod: 2

  item_TX91320010 (extends item):
    __construct:
      type: money
    refId: TX91320010
    editable: true
    exchangeMethod: 2

  item_TX91320010P (extends item):
    __construct:
      type: money
    refId: TX91320010P
    editable: false
    exchangeMethod: 4

  item_TX91320020 (extends item):
    __construct:
      type: money
    refId: TX91320020
    editable: true
    exchangeMethod: 2

  item_TX91320020P (extends item):
    __construct:
      type: money
    refId: TX91320020P
    editable: false
    exchangeMethod: 4

  item_TX91320030 (extends item):
    __construct:
      type: money
    refId: TX91320030
    editable: false
    exchangeMethod: 2

  item_TX91320030P (extends item):
    __construct:
      type: money
    refId: TX91320030P
    editable: false
    exchangeMethod: 4

  item_TX91320040 (extends item):
    __construct:
      type: money
    refId: TX91320040
    editable: true
    exchangeMethod: 2

  item_TX91320040P (extends item):
    __construct:
      type: money
    refId: TX91320040P
    editable: false
    exchangeMethod: 4

  item_TX91320050 (extends item):
    __construct:
      type: money
    refId: TX91320050
    editable: true
    exchangeMethod: 2

  item_TX91320050P (extends item):
    __construct:
      type: money
    refId: TX91320050P
    editable: false
    exchangeMethod: 4

  item_TX91320060P (extends item):
    __construct:
      type: money
    refId: TX91320060P
    editable: false
    exchangeMethod: 4

  item_TX91320070P (extends item):
    __construct:
      type: money
    refId: TX91320070P
    editable: false
    exchangeMethod: 4

  item_TX91320080 (extends item):
    __construct:
      type: money
    refId: TX91320080
    editable: true
    exchangeMethod: 2

  item_TX91320100 (extends item):
    __construct:
      type: money
    refId: TX91320100
    editable: false
    exchangeMethod: 2

  item_TX91320085 (extends item):
    __construct:
      type: money
    refId: TX91320085
    editable: true
    exchangeMethod: 2

  item_TX91320111 (extends item):
    __construct:
      type: money
    refId: TX91320111
    editable: false
    exchangeMethod: 2

  item_TX91330010 (extends item):
    __construct:
      type: money
    refId: TX91330010
    editable: true
    exchangeMethod: 2

  item_TX91330010P (extends item):
    __construct:
      type: money
    refId: TX91330010P
    editable: false
    exchangeMethod: 4

  item_TX91330020 (extends item):
    __construct:
      type: money
    refId: TX91330020
    editable: true
    exchangeMethod: 2

  item_TX91330020P (extends item):
    __construct:
      type: money
    refId: TX91330020P
    editable: false
    exchangeMethod: 4

  item_TX91330030 (extends item):
    __construct:
      type: money
    refId: TX91330030
    editable: false
    exchangeMethod: 2

  item_TX91330030P (extends item):
    __construct:
      type: money
    refId: TX91330030P
    editable: false
    exchangeMethod: 4

  item_TX91330040 (extends item):
    __construct:
      type: money
    refId: TX91330040
    editable: true
    exchangeMethod: 2

  item_TX91330040P (extends item):
    __construct:
      type: money
    refId: TX91330040P
    editable: false
    exchangeMethod: 4

  item_TX91330050 (extends item):
    __construct:
      type: money
    refId: TX91330050
    editable: true
    exchangeMethod: 2

  item_TX91330050P (extends item):
    __construct:
      type: money
    refId: TX91330050P
    editable: false
    exchangeMethod: 4

  item_TX91330060P (extends item):
    __construct:
      type: money
    refId: TX91330060P
    editable: false
    exchangeMethod: 4

  item_TX91330070P (extends item):
    __construct:
      type: money
    refId: TX91330070P
    editable: false
    exchangeMethod: 4

  item_TX91330080 (extends item):
    __construct:
      type: money
    refId: TX91330080
    editable: true
    exchangeMethod: 2

  item_TX91330100 (extends item):
    __construct:
      type: money
    refId: TX91330100
    editable: false
    exchangeMethod: 2

  item_TX91330085 (extends item):
    __construct:
      type: money
    refId: TX91330085
    editable: true
    exchangeMethod: 2

  item_TX91330111 (extends item):
    __construct:
      type: money
    refId: TX91330111
    editable: false
    exchangeMethod: 2

  item_TX91360010 (extends item):
    __construct:
      type: money
    refId: TX91360010
    editable: true
    exchangeMethod: 2

  item_TX91360010P (extends item):
    __construct:
      type: money
    refId: TX91360010P
    editable: false
    exchangeMethod: 4

  item_TX91360020 (extends item):
    __construct:
      type: money
    refId: TX91360020
    editable: true
    exchangeMethod: 2

  item_TX91360020P (extends item):
    __construct:
      type: money
    refId: TX91360020P
    editable: false
    exchangeMethod: 4

  item_TX91360030 (extends item):
    __construct:
      type: money
    refId: TX91360030
    editable: false
    exchangeMethod: 2

  item_TX91360030P (extends item):
    __construct:
      type: money
    refId: TX91360030P
    editable: false
    exchangeMethod: 4

  item_TX91360040 (extends item):
    __construct:
      type: money
    refId: TX91360040
    editable: true
    exchangeMethod: 2

  item_TX91360040P (extends item):
    __construct:
      type: money
    refId: TX91360040P
    editable: false
    exchangeMethod: 4

  item_TX91360050 (extends item):
    __construct:
      type: money
    refId: TX91360050
    editable: true
    exchangeMethod: 2

  item_TX91360050P (extends item):
    __construct:
      type: money
    refId: TX91360050P
    editable: false
    exchangeMethod: 4

  item_TX91360060P (extends item):
    __construct:
      type: money
    refId: TX91360060P
    editable: false
    exchangeMethod: 4

  item_TX91360070P (extends item):
    __construct:
      type: money
    refId: TX91360070P
    editable: false
    exchangeMethod: 4

  item_TX91360080 (extends item):
    __construct:
      type: money
    refId: TX91360080
    editable: true
    exchangeMethod: 2

  item_TX91360100 (extends item):
    __construct:
      type: money
    refId: TX91360100
    editable: false
    exchangeMethod: 2

  item_TX91360085 (extends item):
    __construct:
      type: money
    refId: TX91360085
    editable: true
    exchangeMethod: 2

  item_TX91360111 (extends item):
    __construct:
      type: money
    refId: TX91360111
    editable: false
    exchangeMethod: 2

  item_TX91370010 (extends item):
    __construct:
      type: money
    refId: TX91370010
    editable: true
    exchangeMethod: 2

  item_TX91370010P (extends item):
    __construct:
      type: money
    refId: TX91370010P
    editable: false
    exchangeMethod: 4

  item_TX91370020 (extends item):
    __construct:
      type: money
    refId: TX91370020
    editable: true
    exchangeMethod: 2

  item_TX91370020P (extends item):
    __construct:
      type: money
    refId: TX91370020P
    editable: false
    exchangeMethod: 4

  item_TX91370030 (extends item):
    __construct:
      type: money
    refId: TX91370030
    editable: false
    exchangeMethod: 2

  item_TX91370030P (extends item):
    __construct:
      type: money
    refId: TX91370030P
    editable: false
    exchangeMethod: 4

  item_TX91370040 (extends item):
    __construct:
      type: money
    refId: TX91370040
    editable: true
    exchangeMethod: 2

  item_TX91370040P (extends item):
    __construct:
      type: money
    refId: TX91370040P
    editable: false
    exchangeMethod: 4

  item_TX91370050 (extends item):
    __construct:
      type: money
    refId: TX91370050
    editable: true
    exchangeMethod: 2

  item_TX91370050P (extends item):
    __construct:
      type: money
    refId: TX91370050P
    editable: false
    exchangeMethod: 4

  item_TX91370060P (extends item):
    __construct:
      type: money
    refId: TX91370060P
    editable: false
    exchangeMethod: 4

  item_TX91370070P (extends item):
    __construct:
      type: money
    refId: TX91370070P
    editable: false
    exchangeMethod: 4

  item_TX91370080 (extends item):
    __construct:
      type: money
    refId: TX91370080
    editable: true
    exchangeMethod: 2

  item_TX91370100 (extends item):
    __construct:
      type: money
    refId: TX91370100
    editable: false
    exchangeMethod: 2

  item_TX91370085 (extends item):
    __construct:
      type: money
    refId: TX91370085
    editable: true
    exchangeMethod: 2

  item_TX91370111 (extends item):
    __construct:
      type: money
    refId: TX91370111
    editable: false
    exchangeMethod: 2

  item_TX91380010 (extends item):
    __construct:
      type: money
    refId: TX91380010
    editable: true
    exchangeMethod: 2

  item_TX91380010P (extends item):
    __construct:
      type: money
    refId: TX91380010P
    editable: false
    exchangeMethod: 4

  item_TX91380020 (extends item):
    __construct:
      type: money
    refId: TX91380020
    editable: true
    exchangeMethod: 2

  item_TX91380020P (extends item):
    __construct:
      type: money
    refId: TX91380020P
    editable: false
    exchangeMethod: 4

  item_TX91380030 (extends item):
    __construct:
      type: money
    refId: TX91380030
    editable: false
    exchangeMethod: 2

  item_TX91380030P (extends item):
    __construct:
      type: money
    refId: TX91380030P
    editable: false
    exchangeMethod: 4

  item_TX91380040 (extends item):
    __construct:
      type: money
    refId: TX91380040
    editable: true
    exchangeMethod: 2

  item_TX91380040P (extends item):
    __construct:
      type: money
    refId: TX91380040P
    editable: false
    exchangeMethod: 4

  item_TX91380050 (extends item):
    __construct:
      type: money
    refId: TX91380050
    editable: true
    exchangeMethod: 2

  item_TX91380050P (extends item):
    __construct:
      type: money
    refId: TX91380050P
    editable: false
    exchangeMethod: 4

  item_TX91380060P (extends item):
    __construct:
      type: money
    refId: TX91380060P
    editable: false
    exchangeMethod: 4

  item_TX91380070P (extends item):
    __construct:
      type: money
    refId: TX91380070P
    editable: false
    exchangeMethod: 4

  item_TX91380080 (extends item):
    __construct:
      type: money
    refId: TX91380080
    editable: true
    exchangeMethod: 2

  item_TX91380100 (extends item):
    __construct:
      type: money
    refId: TX91380100
    editable: false
    exchangeMethod: 2

  item_TX91380085 (extends item):
    __construct:
      type: money
    refId: TX91380085
    editable: true
    exchangeMethod: 2

  item_TX91380111 (extends item):
    __construct:
      type: money
    refId: TX91380111
    editable: false
    exchangeMethod: 2

  item_TX91400010 (extends item):
    __construct:
      type: money
    refId: TX91400010
    editable: true
    exchangeMethod: 2

  item_TX91400010P (extends item):
    __construct:
      type: money
    refId: TX91400010P
    editable: false
    exchangeMethod: 4

  item_TX91400020 (extends item):
    __construct:
      type: money
    refId: TX91400020
    editable: true
    exchangeMethod: 2

  item_TX91400020P (extends item):
    __construct:
      type: money
    refId: TX91400020P
    editable: false
    exchangeMethod: 4

  item_TX91400030 (extends item):
    __construct:
      type: money
    refId: TX91400030
    editable: false
    exchangeMethod: 2

  item_TX91400030P (extends item):
    __construct:
      type: money
    refId: TX91400030P
    editable: false
    exchangeMethod: 4

  item_TX91400040 (extends item):
    __construct:
      type: money
    refId: TX91400040
    editable: true
    exchangeMethod: 2

  item_TX91400040P (extends item):
    __construct:
      type: money
    refId: TX91400040P
    editable: false
    exchangeMethod: 4

  item_TX91400050 (extends item):
    __construct:
      type: money
    refId: TX91400050
    editable: true
    exchangeMethod: 2

  item_TX91400050P (extends item):
    __construct:
      type: money
    refId: TX91400050P
    editable: false
    exchangeMethod: 4

  item_TX91400060P (extends item):
    __construct:
      type: money
    refId: TX91400060P
    editable: false
    exchangeMethod: 4

  item_TX91400070P (extends item):
    __construct:
      type: money
    refId: TX91400070P
    editable: false
    exchangeMethod: 4

  item_TX91400080 (extends item):
    __construct:
      type: money
    refId: TX91400080
    editable: true
    exchangeMethod: 2

  item_TX91400100 (extends item):
    __construct:
      type: money
    refId: TX91400100
    editable: false
    exchangeMethod: 2

  item_TX91400085 (extends item):
    __construct:
      type: money
    refId: TX91400085
    editable: true
    exchangeMethod: 2

  item_TX91400111 (extends item):
    __construct:
      type: money
    refId: TX91400111
    editable: false
    exchangeMethod: 2

  item_TX91420010 (extends item):
    __construct:
      type: money
    refId: TX91420010
    editable: true
    exchangeMethod: 2

  item_TX91420010P (extends item):
    __construct:
      type: money
    refId: TX91420010P
    editable: false
    exchangeMethod: 4

  item_TX91420020 (extends item):
    __construct:
      type: money
    refId: TX91420020
    editable: true
    exchangeMethod: 2

  item_TX91420020P (extends item):
    __construct:
      type: money
    refId: TX91420020P
    editable: false
    exchangeMethod: 4

  item_TX91420030 (extends item):
    __construct:
      type: money
    refId: TX91420030
    editable: false
    exchangeMethod: 2

  item_TX91420030P (extends item):
    __construct:
      type: money
    refId: TX91420030P
    editable: false
    exchangeMethod: 4

  item_TX91420040 (extends item):
    __construct:
      type: money
    refId: TX91420040
    editable: true
    exchangeMethod: 2

  item_TX91420040P (extends item):
    __construct:
      type: money
    refId: TX91420040P
    editable: false
    exchangeMethod: 4

  item_TX91420050 (extends item):
    __construct:
      type: money
    refId: TX91420050
    editable: true
    exchangeMethod: 2

  item_TX91420050P (extends item):
    __construct:
      type: money
    refId: TX91420050P
    editable: false
    exchangeMethod: 4

  item_TX91420060P (extends item):
    __construct:
      type: money
    refId: TX91420060P
    editable: false
    exchangeMethod: 4

  item_TX91420070P (extends item):
    __construct:
      type: money
    refId: TX91420070P
    editable: false
    exchangeMethod: 4

  item_TX91420080 (extends item):
    __construct:
      type: money
    refId: TX91420080
    editable: true
    exchangeMethod: 2

  item_TX91420100 (extends item):
    __construct:
      type: money
    refId: TX91420100
    editable: false
    exchangeMethod: 2

  item_TX91420085 (extends item):
    __construct:
      type: money
    refId: TX91420085
    editable: true
    exchangeMethod: 2

  item_TX91420111 (extends item):
    __construct:
      type: money
    refId: TX91420111
    editable: false
    exchangeMethod: 2

  item_TX91430010 (extends item):
    __construct:
      type: money
    refId: TX91430010
    editable: true
    exchangeMethod: 2

  item_TX91430010P (extends item):
    __construct:
      type: money
    refId: TX91430010P
    editable: false
    exchangeMethod: 4

  item_TX91430020 (extends item):
    __construct:
      type: money
    refId: TX91430020
    editable: true
    exchangeMethod: 2

  item_TX91430020P (extends item):
    __construct:
      type: money
    refId: TX91430020P
    editable: false
    exchangeMethod: 4

  item_TX91430030 (extends item):
    __construct:
      type: money
    refId: TX91430030
    editable: false
    exchangeMethod: 2

  item_TX91430030P (extends item):
    __construct:
      type: money
    refId: TX91430030P
    editable: false
    exchangeMethod: 4

  item_TX91430040 (extends item):
    __construct:
      type: money
    refId: TX91430040
    editable: true
    exchangeMethod: 2

  item_TX91430040P (extends item):
    __construct:
      type: money
    refId: TX91430040P
    editable: false
    exchangeMethod: 4

  item_TX91430050 (extends item):
    __construct:
      type: money
    refId: TX91430050
    editable: true
    exchangeMethod: 2

  item_TX91430050P (extends item):
    __construct:
      type: money
    refId: TX91430050P
    editable: false
    exchangeMethod: 4

  item_TX91430060P (extends item):
    __construct:
      type: money
    refId: TX91430060P
    editable: false
    exchangeMethod: 4

  item_TX91430070P (extends item):
    __construct:
      type: money
    refId: TX91430070P
    editable: false
    exchangeMethod: 4

  item_TX91430080 (extends item):
    __construct:
      type: money
    refId: TX91430080
    editable: true
    exchangeMethod: 2

  item_TX91430100 (extends item):
    __construct:
      type: money
    refId: TX91430100
    editable: false
    exchangeMethod: 2

  item_TX91430085 (extends item):
    __construct:
      type: money
    refId: TX91430085
    editable: true
    exchangeMethod: 2

  item_TX91430111 (extends item):
    __construct:
      type: money
    refId: TX91430111
    editable: false
    exchangeMethod: 2

  item_TX91450010 (extends item):
    __construct:
      type: money
    refId: TX91450010
    editable: false
    exchangeMethod: 2

  item_TX91450010P (extends item):
    __construct:
      type: money
    refId: TX91450010P
    editable: false
    exchangeMethod: 4

  item_TX91450020 (extends item):
    __construct:
      type: money
    refId: TX91450020
    editable: false
    exchangeMethod: 2

  item_TX91450020P (extends item):
    __construct:
      type: money
    refId: TX91450020P
    editable: false
    exchangeMethod: 4

  item_TX91450030 (extends item):
    __construct:
      type: money
    refId: TX91450030
    editable: false
    exchangeMethod: 2

  item_TX91450030P (extends item):
    __construct:
      type: money
    refId: TX91450030P
    editable: false
    exchangeMethod: 4

  item_TX91450040 (extends item):
    __construct:
      type: money
    refId: TX91450040
    editable: false
    exchangeMethod: 2

  item_TX91450040P (extends item):
    __construct:
      type: money
    refId: TX91450040P
    editable: false
    exchangeMethod: 4

  item_TX91450050 (extends item):
    __construct:
      type: money
    refId: TX91450050
    editable: false
    exchangeMethod: 2

  item_TX91450050P (extends item):
    __construct:
      type: money
    refId: TX91450050P
    editable: false
    exchangeMethod: 4

  item_TX91450060 (extends item):
    __construct:
      type: money
    refId: TX91450060
    editable: false
    exchangeMethod: 2

  item_TX91450060P (extends item):
    __construct:
      type: money
    refId: TX91450060P
    editable: false
    exchangeMethod: 4

  item_TX91450070 (extends item):
    __construct:
      type: money
    refId: TX91450070
    editable: false
    exchangeMethod: 2

  item_TX91450070P (extends item):
    __construct:
      type: money
    refId: TX91450070P
    editable: false
    exchangeMethod: 4

  item_TX91450080 (extends item):
    __construct:
      type: money
    refId: TX91450080
    editable: false
    exchangeMethod: 2

  item_TX91450085 (extends item):
    __construct:
      type: money
    refId: TX91450085
    editable: false
    exchangeMethod: 2

  item_TX91450100 (extends item):
    __construct:
      type: money
    refId: TX91450100
    editable: false
    exchangeMethod: 2

  item_TX91450111 (extends item):
    __construct:
      type: money
    refId: TX91450111
    editable: false
    exchangeMethod: 2

  item_TX3121020 (extends item):
    __construct:
      type: money
    refId: TX3121020
    editable: true
    exchangeMethod: 1

  item_TX3123020 (extends item):
    __construct:
      type: money
    refId: TX3123020
    editable: true
    exchangeMethod: 1

  item_TX3124020 (extends item):
    __construct:
      type: money
    refId: TX3124020
    editable: true
    exchangeMethod: 1

  item_TX3125020 (extends item):
    __construct:
      type: money
    refId: TX3125020
    editable: true
    exchangeMethod: 1

  item_TX3125021 (extends item):
    __construct:
      type: money
    refId: TX3125021
    editable: true
    exchangeMethod: 1

  item_TX3260020 (extends item):
    __construct:
      type: money
    refId: TX3260020
    editable: true
    exchangeMethod: 1

  item_TX3270020 (extends item):
    __construct:
      type: money
    refId: TX3270020
    editable: true
    exchangeMethod: 1

  item_TX3280020 (extends item):
    __construct:
      type: money
    refId: TX3280020
    editable: true
    exchangeMethod: 1

  item_TX3311110 (extends item):
    __construct:
      type: diff
    refId: TX3311110
    editable: false
    exchangeMethod: 2

  item_TX3311120 (extends item):
    __construct:
      type: diff
    refId: TX3311120
    editable: false
    exchangeMethod: 2

  item_TX3311130 (extends item):
    __construct:
      type: diff
    refId: TX3311130
    editable: false
    exchangeMethod: 2

  item_TX3311140 (extends item):
    __construct:
      type: diff
    refId: TX3311140
    editable: false
    exchangeMethod: 2

  item_TX3320110 (extends item):
    __construct:
      type: diff
    refId: TX3320110
    editable: false
    exchangeMethod: 2

  item_TX3320120 (extends item):
    __construct:
      type: diff
    refId: TX3320120
    editable: false
    exchangeMethod: 2

  item_TX3320130 (extends item):
    __construct:
      type: diff
    refId: TX3320130
    editable: false
    exchangeMethod: 2

  item_TX3320140 (extends item):
    __construct:
      type: diff
    refId: TX3320140
    editable: false
    exchangeMethod: 2

  item_TX3410110 (extends item):
    __construct:
      type: diff
    refId: TX3410110
    editable: false
    exchangeMethod: 2

  item_TX3410120 (extends item):
    __construct:
      type: diff
    refId: TX3410120
    editable: false
    exchangeMethod: 2

  item_TX3410130 (extends item):
    __construct:
      type: diff
    refId: TX3410130
    editable: false
    exchangeMethod: 2

  item_TX3410140 (extends item):
    __construct:
      type: diff
    refId: TX3410140
    editable: false
    exchangeMethod: 2

  item_TX3417110 (extends item):
    __construct:
      type: diff
    refId: TX3417110
    editable: false
    exchangeMethod: 2

  item_TX3417120 (extends item):
    __construct:
      type: diff
    refId: TX3417120
    editable: false
    exchangeMethod: 2

  item_TX3417130 (extends item):
    __construct:
      type: diff
    refId: TX3417130
    editable: false
    exchangeMethod: 2

  item_TX3417140 (extends item):
    __construct:
      type: diff
    refId: TX3417140
    editable: false
    exchangeMethod: 2

  item_TX02841010 (extends item):
    __construct:
      type: diff
    refId: TX02841010
    editable: false
    exchangeMethod: 2

  item_TX02841020 (extends item):
    __construct:
      type: diff
    refId: TX02841020
    editable: false
    exchangeMethod: 2

  item_TX02881010 (extends item):
    __construct:
      type: diff
    refId: TX02881010
    editable: false
    exchangeMethod: 2

  item_TX42700150 (extends item):
    __construct:
      type: diff
    refId: TX42700150
    editable: false
    exchangeMethod: 2

  item_TX3313011 (extends item):
    __construct:
      type: money
    refId: TX3313011
    editable: false
    exchangeMethod: 2

  item_TX3412220 (extends item):
    __construct:
      type: money
    refId: TX3412220
    editable: true
    exchangeMethod: 1

  item_TX3412420 (extends item):
    __construct:
      type: money
    refId: TX3412420
    editable: true
    exchangeMethod: 1

  item_TX3412620 (extends item):
    __construct:
      type: money
    refId: TX3412620
    editable: true
    exchangeMethod: 1

  item_TX3412240 (extends item):
    __construct:
      type: money
    refId: TX3412240
    editable: true
    exchangeMethod: 1

  item_TX3412440 (extends item):
    __construct:
      type: money
    refId: TX3412440
    editable: true
    exchangeMethod: 1

  item_TX3412640 (extends item):
    __construct:
      type: money
    refId: TX3412640
    editable: true
    exchangeMethod: 1

  item_TX3419220 (extends item):
    __construct:
      type: money
    refId: TX3419220
    editable: true
    exchangeMethod: 1

  item_TX3419420 (extends item):
    __construct:
      type: money
    refId: TX3419420
    editable: true
    exchangeMethod: 1

  item_TX3419620 (extends item):
    __construct:
      type: money
    refId: TX3419620
    editable: true
    exchangeMethod: 1

  item_TX3420240 (extends item):
    __construct:
      type: money
    refId: TX3420240
    editable: true
    exchangeMethod: 1

  item_TX3420440 (extends item):
    __construct:
      type: money
    refId: TX3420440
    editable: true
    exchangeMethod: 1

  item_TX3420640 (extends item):
    __construct:
      type: money
    refId: TX3420640
    editable: true
    exchangeMethod: 1

  item_TX02881210 (extends item):
    __construct:
      type: money
    refId: TX02881210
    editable: true
    exchangeMethod: 1

  item_TX02881410 (extends item):
    __construct:
      type: money
    refId: TX02881410
    editable: true
    exchangeMethod: 1

  item_TX02881610 (extends item):
    __construct:
      type: money
    refId: TX02881610
    editable: true
    exchangeMethod: 1

  item_TX02881810 (extends item):
    __construct:
      type: money
    refId: TX02881810
    editable: true
    exchangeMethod: 1

  item_TX02871010 (extends item):
    __construct:
      type: money
    refId: TX02871010
    editable: false
    exchangeMethod: 1

  item_TX3317011P (extends item):
    __construct:
      type: money
    refId: TX3317011P
    editable: false
    exchangeMethod: 2

  item_TX3317021P (extends item):
    __construct:
      type: money
    refId: TX3317021P
    editable: false
    exchangeMethod: 2

  item_TX3317031P (extends item):
    __construct:
      type: money
    refId: TX3317031P
    editable: false
    exchangeMethod: 2

  item_TX3317041P (extends item):
    __construct:
      type: money
    refId: TX3317041P
    editable: false
    exchangeMethod: 2

  item_TX3320011P (extends item):
    __construct:
      type: money
    refId: TX3320011P
    editable: false
    exchangeMethod: 2

  item_TX3320021P (extends item):
    __construct:
      type: money
    refId: TX3320021P
    editable: false
    exchangeMethod: 2

  item_TX3320031P (extends item):
    __construct:
      type: money
    refId: TX3320031P
    editable: false
    exchangeMethod: 2

  item_TX3320041P (extends item):
    __construct:
      type: money
    refId: TX3320041P
    editable: false
    exchangeMethod: 2

  item_TX02840011P (extends item):
    __construct:
      type: money
    refId: TX02840011P
    editable: false
    exchangeMethod: 2

  item_TX02840021P (extends item):
    __construct:
      type: money
    refId: TX02840021P
    editable: false
    exchangeMethod: 2

  item_TX3416021P (extends item):
    __construct:
      type: money
    refId: TX3416021P
    editable: false
    exchangeMethod: 2

  item_TX3417041P (extends item):
    __construct:
      type: money
    refId: TX3417041P
    editable: false
    exchangeMethod: 2

  item_TX3423021P (extends item):
    __construct:
      type: money
    refId: TX3423021P
    editable: false
    exchangeMethod: 2

  item_TX3425041P (extends item):
    __construct:
      type: money
    refId: TX3425041P
    editable: false
    exchangeMethod: 2

  item_TX02880011P (extends item):
    __construct:
      type: money
    refId: TX02880011P
    editable: false
    exchangeMethod: 2

  item_TX42680011 (extends item):
    __construct:
      type: money
    refId: TX42680011
    editable: false
    exchangeMethod: 2

  item_TX42690011 (extends item):
    __construct:
      type: money
    refId: TX42690011
    editable: false
    exchangeMethod: 2

  item_TX42700011 (extends item):
    __construct:
      type: money
    refId: TX42700011
    editable: false
    exchangeMethod: 2

  item_TX42710011 (extends item):
    __construct:
      type: money
    refId: TX42710011
    editable: false
    exchangeMethod: 2

  item_TX42720011 (extends item):
    __construct:
      type: money
    refId: TX42720011
    editable: false
    exchangeMethod: 2

  item_TX42730011 (extends item):
    __construct:
      type: money
    refId: TX42730011
    editable: false
    exchangeMethod: 2

  item_TX42740011 (extends item):
    __construct:
      type: money
    refId: TX42740011
    editable: false
    exchangeMethod: 2

  item_TX42750011 (extends item):
    __construct:
      type: money
    refId: TX42750011
    editable: false
    exchangeMethod: 2

  item_TX42760011 (extends item):
    __construct:
      type: money
    refId: TX42760011
    editable: false
    exchangeMethod: 2

  item_TX3816210 (extends item):
    __construct:
      type: money
    refId: TX3816210
    editable: false
    exchangeMethod: 2

  item_TX3816410 (extends item):
    __construct:
      type: money
    refId: TX3816410
    editable: false
    exchangeMethod: 2

  item_TX3816610 (extends item):
    __construct:
      type: money
    refId: TX3816610
    editable: false
    exchangeMethod: 2
