<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\File;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use Zenst<PERSON>ck\Foundry\LazyValue;
use Zenst<PERSON>ck\Foundry\Persistence\Proxy;

/**
 * @extends ModelFactory<TaxAssessmentMonitor>
 */
final class TaxAssessmentMonitorFactory extends ModelFactory
{
    protected function defaults(): array
    {
        $currency = LazyValue::new(fn (): Proxy => CurrencyFactory::getApplicationCurrency());

        return [
            'status' => StatusFactory::new()->notComplete(),
            'unit' => LegalUnitFactory::new(['currency' => $currency]),
            'taxYear' => self::faker()->numberBetween(1980, 2100),
            'specification' => self::faker()->realText(20),
            'taxMonth' => self::faker()->numberBetween(1, 12),
            'taxType' => TaxTypeFactory::new(),
            'assessmentType' => AssessmentTypeFactory::new(),
        ];
    }

    protected function initialize(): static
    {
        return $this->afterInstantiate(function (TaxAssessmentMonitor $taxAssessmentMonitor, array $attributes): void {
            /** @var array{
             *      "files"?: array<File>,
             *  } $attributes
             */
            if (\array_key_exists('files', $attributes)) {
                foreach ($attributes['files'] as $file) {
                    $taxAssessmentMonitor->getTask()->addFile($file);
                }
            }
        });
    }

    public static function class(): string
    {
        return TaxAssessmentMonitor::class;
    }
}
