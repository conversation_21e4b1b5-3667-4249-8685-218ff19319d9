<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\Workflow\Action\RecalculateItemValuesAction;

/**
 * @extends ModelFactory<RecalculateItemValuesAction>
 */
final class RecalculateItemValuesActionFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'transition' => TransitionFactory::new(),
        ];
    }

    public static function class(): string
    {
        return RecalculateItemValuesAction::class;
    }
}
