<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use Tests\U2\TestUtils;
use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Dto\Permission\GroupPermissionInput;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\File;
use U2\Entity\User;
use U2\Security\Permissions\MaskCompounder;
use U2\Security\Permissions\PermissionManager;

/**
 * @extends ModelFactory<File>
 */
final class FileFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'name' => self::faker()->name(),
            'path' => self::faker()->filePath(),
            'description' => self::faker()->text(),
            'accessType' => File::SMART_ACCESS,
            'mimeType' => 'text/plain',
            'fileSize' => self::faker()->numberBetween('100', '999999999'),
            'createdAt' => new \DateTime(),
            // This can be brought back when the UserFactory does not require the service container anymore.
            // 'createdBy' => UserFactory::randomOrCreate(),
            'updatedAt' => new \DateTime(),
        ];
    }

    protected function initialize(): static
    {
        return $this
            ->afterInstantiate(function (File $file, array $attributes): void {
                /** @var array{
                 *      "id"?: int,
                 *      "createdBy"?: User,
                 *      "userPermissions"?: array<UserPermissionInput>,
                 *      "groupPermissions"?: array<GroupPermissionInput>,
                 *  } $attributes
                 */
                if (\array_key_exists('id', $attributes)) {
                    TestUtils::setId($file, $attributes['id']);
                }

                if (\array_key_exists('createdBy', $attributes)) {
                    $user = $attributes['createdBy'];
                    PermissionManager::grantUserPermission(
                        $file->getPermissions(),
                        $user,
                        MaskCompounder::compound($file->getMasks())
                    );
                }
                if (\array_key_exists('userPermissions', $attributes)) {
                    foreach ($attributes['userPermissions'] as $userPermission) {
                        $file->getPermissions()->addUserPermission($userPermission);
                    }
                }

                if (\array_key_exists('groupPermissions', $attributes)) {
                    foreach ($attributes['groupPermissions'] as $groupPermission) {
                        $file->getPermissions()->addGroupPermission($groupPermission);
                    }
                }
            });
    }

    public static function class(): string
    {
        return File::class;
    }
}
