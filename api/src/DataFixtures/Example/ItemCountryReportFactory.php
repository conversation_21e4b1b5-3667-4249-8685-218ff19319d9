<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\ItemCountryReport;
use Zenstruck\Foundry\LazyValue;
use Zenstruck\Foundry\Persistence\Proxy;

/**
 * @extends ModelFactory<ItemCountryReport>
 */
final class ItemCountryReportFactory extends ModelFactory
{
    protected function defaults(): array
    {
        $admin = LazyValue::new(fn (): Proxy => UserFactory::findOrCreate(['username' => 'admin']));

        return [
            'name' => self::faker()->unique()->word(),
            'createdBy' => $admin,
            'updatedBy' => $admin,
            'createdAt' => $created = self::faker()->dateTimeBetween('-2 year', 'now'),
            'updatedAt' => self::faker()->dateTimeBetween($created, 'now'),
        ];
    }

    public static function class(): string
    {
        return ItemCountryReport::class;
    }
}
