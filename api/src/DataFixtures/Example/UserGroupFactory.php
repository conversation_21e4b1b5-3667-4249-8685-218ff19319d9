<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\UserGroup;

/**
 * @extends ModelFactory<UserGroup>
 */
final class UserGroupFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'name' => self::faker()->name(),
            'description' => self::faker()->text(),
        ];
    }

    public static function class(): string
    {
        return UserGroup::class;
    }
}
