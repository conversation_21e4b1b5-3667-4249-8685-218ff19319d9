<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\LocalFileSection;

/**
 * @extends ModelFactory<LocalFileSection>
 */
final class LocalFileSectionFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'content' => self::faker()->text(),
            'editable' => true,
            'include' => self::faker()->boolean(),
            'name' => self::faker()->text(),
            'orderPosition' => self::faker()->randomNumber(),
            'required' => false,
        ];
    }

    public static function class(): string
    {
        return LocalFileSection::class;
    }
}
