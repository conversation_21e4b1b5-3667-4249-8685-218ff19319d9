<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\DiffItemUnitValue;
use U2\Entity\Item;
use U2\Entity\Period;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\Unit;
use Zenstruck\Foundry\Persistence\Proxy;

/**
 * @extends ModelFactory<DiffItemUnitValue>
 */
final class DiffItemUnitValueFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'item' => ItemFactory::new()->readonly()->diff(),
            'unit' => UnitFactory::new(),
            'period' => PeriodFactory::new(),
        ];
    }

    /**
     * @param Item|Proxy<Item> $item
     */
    public function withItem(Item|Proxy $item): self
    {
        return $this->with(['item' => $item]);
    }

    /**
     * @param Unit|Proxy<covariant Unit> $unit
     */
    public function withUnit(Unit|Proxy $unit): self
    {
        return $this->with(['unit' => $unit]);
    }

    /**
     * @param Period|Proxy<Period> $period
     */
    public function withPeriod(Period|Proxy $period): self
    {
        return $this->with(['period' => $period]);
    }

    /**
     * @param UnitPeriod|Proxy<UnitPeriod> $unitPeriod
     */
    public function withUnitPeriod(UnitPeriod|Proxy $unitPeriod): self
    {
        return $this->with(['unit' => $unitPeriod->getUnit(), 'period' => $unitPeriod->getPeriod()]);
    }

    public function withDiff(string $diff): self
    {
        return $this->with(['diff' => $diff]);
    }

    public static function class(): string
    {
        return DiffItemUnitValue::class;
    }
}
