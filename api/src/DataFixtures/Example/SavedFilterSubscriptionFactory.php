<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\Frequency;
use U2\Entity\SavedFilterSubscription;

/**
 * @extends ModelFactory<SavedFilterSubscription>
 */
final class SavedFilterSubscriptionFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'name' => self::faker()->text(),
            'active' => self::faker()->boolean(),
            'frequency' => new Frequency('0 6 * * *'),
            'savedFilter' => SavedFilterFactory::new(),
        ];
    }

    public static function class(): string
    {
        return SavedFilterSubscription::class;
    }
}
