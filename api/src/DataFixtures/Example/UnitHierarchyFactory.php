<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\UnitHierarchy;

/**
 * @extends ModelFactory<UnitHierarchy>
 */
final class UnitHierarchyFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'name' => self::faker()->text(),
            'description' => self::faker()->text(),
        ];
    }

    public static function class(): string
    {
        return UnitHierarchy::class;
    }
}
