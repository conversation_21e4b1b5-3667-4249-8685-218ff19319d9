<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\Comment;
use U2\Entity\CommentState;

/**
 * @extends ModelFactory<Comment>
 */
final class CommentFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'body' => self::faker()->text(),
            'createdAt' => self::faker()->dateTime(),
            'depth' => self::faker()->randomNumber(),
            'state' => CommentState::Visible,
            'updatedAt' => self::faker()->dateTime(),
        ];
    }

    public static function class(): string
    {
        return Comment::class;
    }
}
