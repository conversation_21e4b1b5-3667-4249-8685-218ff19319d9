<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\LegalForm;

/**
 * @extends ModelFactory<LegalForm>
 */
final class LegalFormFactory extends ModelFactory
{
    /**
     * @return array<string, string|true>
     */
    protected function defaults(): array
    {
        return [
            'name' => self::faker()->text(20),
            'enabled' => true,
        ];
    }

    public static function class(): string
    {
        return LegalForm::class;
    }
}
