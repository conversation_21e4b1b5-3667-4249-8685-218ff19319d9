<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\Dashboard;

/**
 * @extends ModelFactory<Dashboard>
 */
final class DashboardFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'title' => self::faker()->text(),
            'public' => self::faker()->boolean(),
            'widgets' => [],
        ];
    }

    public static function class(): string
    {
        return Dashboard::class;
    }
}
