<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\File;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Igt\Igt2\Igt2Types;
use Zenstruck\Foundry\LazyValue;
use Zenstruck\Foundry\Persistence\Proxy;

/**
 * @extends ModelFactory<Igt2Transaction>
 */
final class Igt2TransactionFactory extends ModelFactory
{
    protected function defaults(): array
    {
        $currency = LazyValue::new(fn (): Proxy => CurrencyFactory::getApplicationCurrency());

        return [
            'type' => Igt2Types::derivativesForwards,
            'status' => StatusFactory::new()->notComplete(),
            'period' => PeriodFactory::new(),
            'unit' => LegalUnitFactory::new(['currency' => $currency]),
            'partnerUnit' => LegalUnitFactory::new(),
            'assetOrLiabilityUnderlyingDerivative' => 'text',
            'assetOrLiabilityUnderlyingDerivativeIdType' => InstrumentIdTypeFactory::new(),
            'counterPartyName' => 'text',
            'swapDeliveredCurrency' => CurrencyFactory::new(),
            'swapReceivedCurrency' => CurrencyFactory::new(),
            'swapReceivedInterestRate' => '1',
            'swapDeliveredInterestRate' => '1',
            's2CollateralValue' => BaseLocalGroupMoneyFactory::new(['currency' => $currency]),
        ];
    }

    protected function initialize(): static
    {
        return $this->afterInstantiate(function (Igt2Transaction $otherDeadline, array $attributes): void {
            /** @var array{
             *      "files"?: array<File>,
             *  } $attributes
             */
            if (\array_key_exists('files', $attributes)) {
                foreach ($attributes['files'] as $file) {
                    $otherDeadline->getTask()->addFile($file);
                }
            }
        });
    }

    public static function class(): string
    {
        return Igt2Transaction::class;
    }
}
