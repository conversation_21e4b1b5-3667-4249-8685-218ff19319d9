<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\File;
use U2\Entity\Task\TaskType\LossCarryForward;
use Zenstruck\Foundry\LazyValue;
use Zenstruck\Foundry\Persistence\Proxy;

/**
 * @extends ModelFactory<LossCarryForward>
 */
final class LossCarryForwardFactory extends ModelFactory
{
    protected function defaults(): array
    {
        $currency = LazyValue::new(fn (): Proxy => CurrencyFactory::getApplicationCurrency());

        return [
            'unit' => LegalUnitFactory::new(['currency' => $currency]),
            'status' => StatusFactory::new()->notComplete(),
            'period' => PeriodFactory::new(),
            'beginningOfYear' => self::faker()->numberBetween(0, 9999),
            'correction' => self::faker()->numberBetween(-999, 9999),
            'consumption' => self::faker()->numberBetween(0, 9999),
            'addition' => self::faker()->numberBetween(0, 9999),
            'lossType' => LossTypeFactory::new(),
            'taxType' => TaxTypeFactory::new(),
            'restrictions' => LossRestrictionFactory::new(),
        ];
    }

    protected function initialize(): static
    {
        return $this->afterInstantiate(function (LossCarryForward $lossCarryForward, array $attributes): void {
            /** @var array{
             *      "files"?: array<File>,
             *  } $attributes
             */
            if (\array_key_exists('files', $attributes)) {
                foreach ($attributes['files'] as $file) {
                    $lossCarryForward->getTask()->addFile($file);
                }
            }
        });
    }

    public static function class(): string
    {
        return LossCarryForward::class;
    }
}
