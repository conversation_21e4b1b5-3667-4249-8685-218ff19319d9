<?php

declare(strict_types=1);
namespace U2\DataFixtures\Example;

use U2\DataFixtures\Example\Foundry\ModelFactory;
use U2\Entity\Configuration\Field\BillingType;

/**
 * @extends ModelFactory<BillingType>
 */
final class BillingTypeFactory extends ModelFactory
{
    protected function defaults(): array
    {
        return [
            'name' => self::faker()->text(),
            'enabled' => true,
        ];
    }

    public static function class(): string
    {
        return BillingType::class;
    }
}
