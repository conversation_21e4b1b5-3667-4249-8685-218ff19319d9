<?php

declare(strict_types=1);
namespace U2\Datasheets\Item\Formula;

use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\DependencyInjection\Attribute\AutowireLocator;
use Symfony\Contracts\Service\ServiceCollectionInterface;
use U2\Datasheets\Item\Formula\Element\ElementEvaluator;
use U2\Datasheets\Item\Formula\Element\ElementInterface;
use U2\Datasheets\Item\ItemTypes;
use U2\Exception\FormulaNotFoundException;

#[Autoconfigure(lazy: true)]
class FormulaCompiler
{
    /**
     * @param ServiceCollectionInterface<ElementEvaluator> $evaluatorServiceLocator
     */
    public function __construct(
        #[AutowireLocator(services: ElementEvaluator::class, )]
        private readonly ServiceCollectionInterface $evaluatorServiceLocator,
    ) {
    }

    public function compile(FormulaWithElements $formula, FormulaEvaluationContext $context): string
    {
        $pairs = [];
        foreach ($formula->getElements() as $element) {
            $pairs[\sprintf('{%s}', $element->getId())] = $this->getCompiledElement($formula, $element, $context);
        }

        $itemFormula = $formula->getItem()->getFormula();
        $formulaString = $itemFormula?->getFormulaString() ?? throw new FormulaNotFoundException('Formula not found for item', 0, null, $formula->getItem());

        return strtr($formulaString, $pairs);
    }

    protected function getCompiledElement(FormulaWithElements $formula, ElementInterface $element, FormulaEvaluationContext $context): string
    {
        $type = $element->getItem()->getType();
        $value = $this->evaluatorServiceLocator->get($type)->getValue($formula->getItem(), $element, $context);

        return match ($type) {
            ItemTypes::TEXT => \sprintf("'%s'", addcslashes($value, "'")),
            default => \sprintf('(%s)', $value),
        };
    }
}
