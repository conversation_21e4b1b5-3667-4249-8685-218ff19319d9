<?php

declare(strict_types=1);
namespace U2\Datasheets\View\Unit;

use U2\Entity\Datasheet;
use U2\Entity\Field;
use U2\Entity\Period;
use U2\Entity\Task\Task;
use U2\Entity\Unit;
use U2\Repository\CachedItemUnitValueRepository;

class UnitViewFactory
{
    public function __construct(
        private readonly CachedItemUnitValueRepository $itemUnitValueRepository,
    ) {
    }

    public function create(
        Datasheet $layout,
        Unit $unit,
        Period $period,
        ?Task $task = null,
    ): UnitView {
        return new UnitView($layout, $unit, $period, $this->createData($layout, $unit, $period), $task);
    }

    /**
     * @return UnitViewFieldData[]
     */
    private function createData(Datasheet $layout, Unit $unit, Period $period): array
    {
        /** @var Field[] $fieldArray */
        $fieldArray = $layout->getFields()->toArray();
        $this->itemUnitValueRepository->findBy([
            'item' => array_map(
                static fn (Field $field): int => $field->getItem()->getId(),
                $fieldArray
            ),
            'unit' => $unit,
            'period' => $period,
        ]);

        return array_map(
            fn (Field $field): UnitViewFieldData => new UnitViewFieldData(
                $field,
                $this->itemUnitValueRepository->getFromCacheOrAdd($field->getItem(), $unit, $period)
            ),
            $fieldArray
        );
    }
}
