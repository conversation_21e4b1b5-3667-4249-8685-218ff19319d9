<?php

declare(strict_types=1);
namespace U2\Datasheets\View\UnitHierarchy\Breakdown;

use U2\Api\Resource\Breakdown;
use U2\Datasheets\Item\UnitHierarchyValue\UnitHierarchyValueCalculator;
use U2\Datasheets\Item\UnitValue\ItemUnitValueFactory;
use U2\Entity\CheckboxItemUnitValue;
use U2\Entity\DiffItemUnitValue;
use U2\Entity\Item;
use U2\Entity\ItemUnitValue;
use U2\Entity\MoneyItemUnitValue;
use U2\Entity\NumberItemUnitValue;
use U2\Entity\PercentItemUnitValue;
use U2\Entity\Period;
use U2\Entity\TextItemUnitValue;
use U2\Entity\UnitHierarchy;
use U2\Exception\ItemValueCalculationNotPossibleException;
use U2\Repository\CachedItemUnitValueRepository;
use U2\Repository\UnitRepository;

class BreakdownFactory
{
    public function __construct(
        private readonly CachedItemUnitValueRepository $itemUnitValueRepository,
        private readonly ItemUnitValueFactory $itemUnitValueFactory,
        private readonly UnitHierarchyValueCalculator $groupValueCalculator,
        private readonly UnitRepository $unitRepository,
    ) {
    }

    public function create(
        Period $period,
        UnitHierarchy $unitHierarchy,
        Item $item,
    ): Breakdown {
        return new Breakdown(
            $period,
            $unitHierarchy,
            $item,
            $this->getBreakdownValues($item, $unitHierarchy, $period),
            $this->getGroupValue($item, $unitHierarchy, $period),
        );
    }

    /**
     * @return BreakdownValue[]
     */
    private function getBreakdownValues(Item $item, UnitHierarchy $unitHierarchy, Period $period): array
    {
        $units = $this->unitRepository->findUnitsForUnitHierarchyDate($unitHierarchy, $period->getEndDate());
        $itemUnitValues = $this->itemUnitValueRepository->findBy(['item' => $item, 'unit' => $units, 'period' => $period]);

        $itemUnitValueMap = [];
        foreach ($itemUnitValues as $itemUnitValue) {
            $itemUnitValueMap[$itemUnitValue->getUnit()->getId()] = $itemUnitValue;
        }

        $breakdownValues = [];
        foreach ($units as $unit) {
            $itemUnitValue = $itemUnitValueMap[$unit->getId()] ?? $this->itemUnitValueFactory->create($item, $unit, $period);
            $breakdownValues[] = $this->getBreakdownValue($itemUnitValue);
        }

        usort(
            $breakdownValues,
            static fn (BreakdownValue $a, BreakdownValue $b): int => strnatcasecmp($a->getUnit()->getCountry()?->getNameShort() ?? '', $b->getUnit()->getCountry()?->getNameShort() ?? ''),
        );

        return $breakdownValues;
    }

    protected function getBreakdownValue(ItemUnitValue $itemUnitValue): BreakdownValue
    {
        return match (true) {
            $itemUnitValue instanceof CheckboxItemUnitValue => CheckboxValue::fromCheckboxItemUnitValue($itemUnitValue),
            $itemUnitValue instanceof DiffItemUnitValue => DiffValue::fromDiffItemUnitValue($itemUnitValue),
            $itemUnitValue instanceof MoneyItemUnitValue => MoneyValue::fromMoneyItemUnitValue($itemUnitValue),
            $itemUnitValue instanceof NumberItemUnitValue => NumberValue::fromNumberItemUnitValue($itemUnitValue),
            $itemUnitValue instanceof PercentItemUnitValue => PercentValue::fromPercentItemUnitValue($itemUnitValue),
            $itemUnitValue instanceof TextItemUnitValue => TextValue::fromTextItemUnitValue($itemUnitValue),
            default => throw new \UnexpectedValueException(\sprintf('Unknown item unit value type %s: ', $itemUnitValue::class)),
        };
    }

    private function getGroupValue(Item $item, UnitHierarchy $unitHierarchy, Period $period): bool|string|null
    {
        try {
            return $this->groupValueCalculator->calculate($item, $unitHierarchy, $period);
        } catch (ItemValueCalculationNotPossibleException) {
            return null;
        }
    }
}
