<?php

declare(strict_types=1);
namespace U2\Datasheets\View\UnitHierarchy\CountryBreakdown;

use Symfony\Component\Serializer\Attribute\Groups;
use U2\Entity\Country;

class CountryTextValue implements CountryBreakdownValue
{
    public function __construct(
        #[Groups(groups: ['country-breakdown:read'])]
        private readonly Country $country,
        #[Groups(groups: ['country-breakdown:read'])]
        private readonly ?string $value,
    ) {
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public static function fromMixedValue(Country $country, mixed $value): self
    {
        \assert(\is_string($value) || null === $value);

        return new self($country, $value);
    }
}
