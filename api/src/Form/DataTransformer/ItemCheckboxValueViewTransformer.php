<?php

declare(strict_types=1);
namespace U2\Form\DataTransformer;

use Symfony\Component\Form\DataTransformerInterface;

class ItemCheckboxValueViewTransformer implements DataTransformerInterface
{
    /**
     * @param string $value
     */
    public function transform($value): ?bool
    {
        if (0 == $value) {
            return null;
        }

        return true;
    }

    /**
     * @param string|null $value
     */
    public function reverseTransform($value): int
    {
        if (null === $value) {
            return 0;
        }

        return 1;
    }
}
