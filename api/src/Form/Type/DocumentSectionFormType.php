<?php

declare(strict_types=1);
namespace U2\Form\Type;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use function Symfony\Component\Translation\t;

use U2\Entity\DocumentSection;

class DocumentSectionFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add(
                'content',
                TextareaType::class,
                [
                    'label' => t('u2_structureddocument.content'),
                    'required' => false,
                ]
            )
            ->add(
                'name',
                null,
                [
                    'label' => t('u2_structureddocument.title'),
                    'required' => true,
                    'disabled' => !$options['canEditTitle'],
                ]
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => DocumentSection::class,
                'canEditTitle' => true,
                'save_prompt' => true,
            ]
        );
    }
}
