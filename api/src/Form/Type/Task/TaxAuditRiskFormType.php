<?php

declare(strict_types=1);
namespace U2\Form\Type\Task;

use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

use function Symfony\Component\Translation\t;

use U2\Entity\Configuration\Field\RiskType;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Form\Type\MoneyType;
use U2\Form\Type\PercentType;
use U2\Form\Type\TaskChoiceFieldType;

class TaxAuditRiskFormType extends AbstractTamIssueFormType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                'taxType',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tam.tax_type'),
                    'class' => TaxType::class,
                ]
            )
            ->add(
                'riskType',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tam.type_of_risk'),
                    'class' => RiskType::class,
                ]
            )
            ->add(
                'permanent',
                ChoiceType::class,
                [
                    'choices' => [
                        'u2_tam.temporary' => 0,
                        'u2_tam.permanent' => 1,
                    ],
                    'expanded' => true,
                    'multiple' => false,
                    'empty_data' => null,
                    'label' => t('u2_tam.temporary_permanent_risk'),
                ]
            )
            ->add(
                'taxYear',
                IntegerType::class,
                [
                    'label' => t('u2_tam.taxation_year'),
                ]
            )
            ->add(
                'grossRiskBoy',
                MoneyType::class,
                [
                    'label' => t('u2_tam.gross_risk_boy'),
                    'attr' => [
                        'readonly' => !$this->systemSettings->getTaxAssessmentOpenBoy(),
                    ],
                ]
            )
            ->add(
                'identifiedByTaxAdmin',
                MoneyType::class,
                [
                    'label' => t('u2_tam.identified_by_tax_admin'),
                    'required' => false,
                ]
            )
            ->add(
                'additions',
                MoneyType::class,
                [
                    'label' => t('u2_tam.additions'),
                    'required' => false,
                ]
            )
            ->add(
                'less',
                MoneyType::class,
                [
                    'label' => t('u2_tam.less'),
                    'required' => false,
                ]
            )
            ->add(
                'grossRiskEoy',
                MoneyType::class,
                [
                    'label' => t('u2_tam.gross_risk_eoy'),
                    'attr' => [
                        'readonly' => true,
                    ],
                ]
            )
            ->add(
                'riskProbability',
                PercentType::class,
                [
                    'label' => t('u2_tam.risk_probability'),
                    'required' => false,
                ]
            )
            ->add(
                'accruedBoy',
                MoneyType::class,
                [
                    'label' => t('u2_tam.accrued_boy'),
                    'attr' => [
                        'readonly' => !$this->systemSettings->getTaxAssessmentOpenBoy(),
                    ],
                ]
            )
            ->add(
                'plEffectCy',
                MoneyType::class,
                [
                    'label' => t('u2_tam.p_l_effect_cy'),
                    'required' => false,
                ]
            )
            ->add(
                'accruedEoy',
                MoneyType::class,
                [
                    'label' => t('u2_tam.accrued_eoy'),
                    'attr' => [
                        'readonly' => true,
                    ],
                ]
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => TaxAuditRisk::class,
                'openBoy' => false,
                'save_prompt' => true,
            ]
        );
    }
}
