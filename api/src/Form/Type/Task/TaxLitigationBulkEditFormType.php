<?php

declare(strict_types=1);
namespace U2\Form\Type\Task;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\RequestStack;

use function Symfony\Component\Translation\t;

use U2\Entity\Configuration\Field\RiskType;
use U2\Entity\Configuration\Field\Specification;
use U2\Entity\Configuration\Field\TaxType;
use U2\Form\Type\MoneyType;
use U2\Form\Type\PercentType;
use U2\Form\Type\PeriodType;
use U2\Form\Type\TaskChoiceFieldType;
use U2\Form\Type\UnitType;
use U2\Repository\UnitRepository;
use U2\Task\BulkAction\Edit\BulkChangeFactory;
use U2\Task\FieldStateResolver;
use U2\User\CurrentUserProvider;

class TaxLitigationBulkEditFormType extends TaskBulkChangeFormType
{
    public function __construct(
        BulkChangeFactory $bulkChangeFactory,
        RequestStack $requestStack,
        EntityManagerInterface $entityManager,
        FieldStateResolver $fieldStateResolver,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly UnitRepository $unitRepository,
    ) {
        parent::__construct($bulkChangeFactory, $requestStack, $entityManager, $fieldStateResolver);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                'period',
                PeriodType::class,
                [
                    'enableable' => true,
                ]
            )
            ->add(
                'unit',
                UnitType::class,
                [
                    'label' => t('u2.unit'),
                    'choices' => $this->unitRepository->findUserAssigned($this->currentUserProvider->get()),
                    'enableable' => true,
                ]
            )
            ->add(
                'taxType',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tam.tax_type'),
                    'class' => TaxType::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'specification',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tam.specification'),
                    'class' => Specification::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'riskType',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tam.type_of_risk'),
                    'class' => RiskType::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'permanent',
                ChoiceType::class,
                [
                    'choices' => [
                        'u2_tam.temporary' => 0,
                        'u2_tam.permanent' => 1,
                    ],
                    'expanded' => true,
                    'empty_data' => null,
                    'multiple' => false,
                    'label' => t('u2_tam.temporary_permanent_risk'),
                    'enableable' => true,
                ]
            )
            ->add(
                'taxYear',
                IntegerType::class,
                [
                    'label' => t('u2_tam.taxation_year'),
                    'enableable' => true,
                    'attr' => [
                        'maxlength' => 4,
                    ],
                ]
            )
            ->add(
                'amount',
                MoneyType::class,
                [
                    'label' => t('u2_tam.amount'),
                    'required' => false,
                    'enableable' => true,
                ]
            )
            ->add(
                'riskProbability',
                PercentType::class,
                [
                    'label' => t('u2_tam.risk_probability'),
                    'required' => false,
                    'enableable' => true,
                ]
            )
            ->add(
                'plEffectCy',
                MoneyType::class,
                [
                    'label' => t('u2_tam.p_l_effect_cy'),
                    'required' => false,
                    'enableable' => true,
                ]
            );
    }
}
