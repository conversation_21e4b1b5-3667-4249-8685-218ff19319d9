<?php

declare(strict_types=1);
namespace U2\Form\Type\Task;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\RequestStack;

use function Symfony\Component\Translation\t;

use U2\Entity\Configuration\Field\BillingType;
use U2\Entity\Configuration\Field\TransactionType;
use U2\Entity\Currency;
use U2\Form\Type\CurrencyType;
use U2\Form\Type\DatepickerType;
use U2\Form\Type\MoneyType;
use U2\Form\Type\PercentType;
use U2\Form\Type\TaskChoiceFieldType;
use U2\Form\Type\ToggleButtonType;
use U2\Form\Type\UnitType;
use U2\Repository\UnitRepository;
use U2\Task\BulkAction\Edit\BulkChangeFactory;
use U2\Task\FieldStateResolver;
use U2\User\CurrentUserProvider;

class TransactionBulkEditFormType extends TaskBulkChangeFormType
{
    public function __construct(
        BulkChangeFactory $bulkChangeFactory,
        RequestStack $requestStack,
        EntityManagerInterface $entityManager,
        FieldStateResolver $fieldStateResolver,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly UnitRepository $unitRepository,
    ) {
        parent::__construct($bulkChangeFactory, $requestStack, $entityManager, $fieldStateResolver);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                'subType',
                TextType::class,
                [
                    'label' => t('u2_tpm.sub_type'),
                    'enableable' => true,
                    'required' => false,
                ]
            )
            ->add(
                'type',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tpm.type'),
                    'class' => TransactionType::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'billingType',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tpm.billing_type'),
                    'class' => BillingType::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'unit',
                UnitType::class,
                [
                    'label' => t('u2.unit'),
                    'choices' => $this->unitRepository->findUserAssigned($this->currentUserProvider->get()),
                    'enableable' => true,
                    'help' => t('u2.unit.help'),
                ]
            )
            ->add(
                'unitRequiresDocumentation',
                ChoiceType::class,
                [
                    'expanded' => true,
                    'multiple' => false,
                    'label' => t('u2_tpm.documentation'),
                    'required' => false,
                    'enableable' => true,
                    'placeholder' => false,
                    'choices' => [
                        'u2_tpm.not_required' => false,
                        'u2_tpm.required' => true,
                    ],
                    'help' => t('u2_tpm.select_whether_the_unit_needs_to_be_documented'),
                ]
            )
            ->add(
                'partnerUnit',
                UnitType::class,
                [
                    'label' => t('u2.partner_unit'),
                    'enableable' => true,
                    'help' => t('u2_tpm.partner_unit.help'),
                ]
            )
            ->add(
                'partnerUnitRequiresDocumentation',
                ChoiceType::class,
                [
                    'expanded' => true,
                    'multiple' => false,
                    'label' => t('u2_tpm.documentation'),
                    'enableable' => true,
                    'required' => false,
                    'placeholder' => false,
                    'choices' => [
                        'u2_tpm.not_required' => false,
                        'u2_tpm.required' => true,
                    ],
                    'help' => t('u2_tpm.select_whether_the_partner_unit_needs_to_be_documented'),
                ]
            )
            ->add(
                'transactionAmount',
                MoneyType::class,
                [
                    'label' => t('u2_tpm.transaction_amount'),
                    'enableable' => true,
                ]
            )
            ->add(
                'transactionCurrency',
                CurrencyType::class,
                [
                    'class' => Currency::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'armsLength',
                ToggleButtonType::class,
                [
                    'enableable' => true,
                    'help' => t('u2_tpm.arms_length.help'),
                    'label' => t('u2_tpm.arms_length'),
                    'required' => true,
                ]
            )
            ->add(
                'contractDate',
                DatepickerType::class,
                [
                    'enableable' => true,
                    'label' => t('u2.contract_date'),
                    'required' => false,
                ]
            )
            ->add(
                'contractExpiryDate',
                DatepickerType::class,
                [
                    'enableable' => true,
                    'label' => t('u2.contract_expiry_date'),
                    'required' => false,
                ]
            )
            ->add(
                'maturityDate',
                DatepickerType::class,
                [
                    'enableable' => true,
                    'label' => t('u2.maturity_date'),
                    'required' => false,
                ]
            )
            ->add(
                'guaranteeFeeAmount',
                MoneyType::class,
                [
                    'enableable' => true,
                    'required' => false,
                    'label' => t('u2.transaction.guarantee_fee_amount'),
                ]
            )
            ->add(
                'guaranteeFeeCurrency',
                CurrencyType::class,
                [
                    'enableable' => true,
                    'required' => false,
                ]
            )
            ->add(
                'couponInterestRate',
                PercentType::class,
                [
                    'enableable' => true,
                    'label' => t('u2.transaction.coupon_interest_rate'),
                    'required' => false,
                ]
            )
            ->add(
                'forwardRate',
                null,
                [
                    'enableable' => true,
                    'label' => t('u2.transaction.forward_rate'),
                    'required' => false,
                ]
            )
            ->add(
                'assetLiabilityId',
                null,
                [
                    'enableable' => true,
                    'label' => t('u2.transaction.asset_liability_id'),
                    'required' => false,
                ]
            );
    }
}
