<?php

declare(strict_types=1);
namespace U2\Form\Type\Task;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Extension\Core\Type\IntegerType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\HttpFoundation\RequestStack;

use function Symfony\Component\Translation\t;

use U2\Entity\Configuration\Field\TaxType;
use U2\Form\Type\PeriodType;
use U2\Form\Type\TaskChoiceFieldType;
use U2\Form\Type\UnitType;
use U2\Repository\UnitRepository;
use U2\Task\BulkAction\Edit\BulkChangeFactory;
use U2\Task\FieldStateResolver;
use U2\User\CurrentUserProvider;

class TaxAssessmentStatusBulkEditFormType extends TaskBulkChangeFormType
{
    public function __construct(
        BulkChangeFactory $bulkChangeFactory,
        RequestStack $requestStack,
        EntityManagerInterface $entityManager,
        FieldStateResolver $fieldStateResolver,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly UnitRepository $unitRepository,
    ) {
        parent::__construct($bulkChangeFactory, $requestStack, $entityManager, $fieldStateResolver);
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                'period',
                PeriodType::class,
                [
                    'enableable' => true,
                ]
            )
            ->add(
                'unit',
                UnitType::class,
                [
                    'label' => t('u2.unit'),
                    'choices' => $this->unitRepository->findUserAssigned(
                        $this->currentUserProvider->get()
                    ),
                    'enableable' => true,
                ]
            )
            ->add(
                'taxType',
                TaskChoiceFieldType::class,
                [
                    'label' => t('u2_tam.tax_type'),
                    'class' => TaxType::class,
                    'enableable' => true,
                ]
            )
            ->add(
                'taxYear',
                IntegerType::class,
                [
                    'label' => t('u2_tam.taxation_year'),
                    'enableable' => true,
                    'attr' => [
                        'maxlength' => 4,
                    ],
                ]
            )
            ->add(
                'taxMonth',
                IntegerType::class,
                [
                    'label' => t('u2_tam.taxation_month'),
                    'required' => false,
                    'enableable' => true,
                    'attr' => [
                        'maxlength' => 2,
                    ],
                ]
            );
    }
}
