<?php

declare(strict_types=1);
namespace U2\Form\Type;

use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use U2\Entity\Workflow\Transition;
use U2\Task\BulkAction\Transition\BulkTransitionMapping;

class BulkTransitionMappingFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->addEventListener(
                FormEvents::PRE_SET_DATA,
                function (FormEvent $event): void {
                    /** @var BulkTransitionMapping $mapping */
                    $mapping = $event->getData();
                    $form = $event->getForm();

                    $form
                        ->add(
                            'transition',
                            EntityType::class,
                            [
                                'choices' => $this->getChoices($mapping),
                                'class' => Transition::class,
                                'choice_label' => 'name',
                                'expanded' => true,
                                'multiple' => false,
                            ]
                        );
                }
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => BulkTransitionMapping::class,
            ]
        );
    }

    /**
     * @return Transition[]
     */
    private function getChoices(BulkTransitionMapping $mapping): array
    {
        $originStatus = $mapping->getOriginStatus();
        $availableTransitions = [];
        foreach ($mapping->getWorkflow()->getTransitions() as $transition) {
            if ($transition->getOriginStatus()->getId() === $originStatus->getId()) {
                $availableTransitions[] = $transition;
            }
        }

        return $availableTransitions;
    }
}
