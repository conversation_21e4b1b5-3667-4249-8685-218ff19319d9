<?php

declare(strict_types=1);
namespace U2\Form\Type;

use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\OptionsResolver\OptionsResolver;

use function Symfony\Component\Translation\t;

use U2\Entity\Country;

class CountryType extends AbstractType
{
    public function getParent(): string
    {
        return EntityType::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'class' => Country::class,
                'query_builder' => fn (EntityRepository $repository): QueryBuilder => $repository->createQueryBuilder('c')->orderBy('c.nameShort', 'ASC'),
                'choice_label' => 'nameShort',
                'label' => t('u2.country'),
                'placeholder' => t('u2.select_country'),
            ]
        );
    }
}
