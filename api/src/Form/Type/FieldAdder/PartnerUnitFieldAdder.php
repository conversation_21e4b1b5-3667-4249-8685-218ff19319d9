<?php

declare(strict_types=1);
namespace U2\Form\Type\FieldAdder;

use Symfony\Component\Form\FormBuilderInterface;
use U2\Form\Type\CountryType;
use U2\Form\Type\ToggleButtonType;
use U2\Form\Type\UnitType;

class PartnerUnitFieldAdder
{
    /**
     * @param array<string, mixed> $options
     */
    public static function add(FormBuilderInterface $builder, array $options = []): void
    {
        $builder
            ->add(
                'partnerUnit',
                UnitType::class,
                [
                    'required' => true,
                    'label' => 'Partner Unit',
                    'help' => null,
                ]
            )
            ->add(
                'partnerIsThirdParty',
                ToggleButtonType::class,
                [
                    'required' => false,
                    'label' => 'Partner is Third Party',
                ]
            )
            ->add(
                'thirdPartyName',
                null,
                [
                    'label' => 'Third Party Name',
                ]
            )
            ->add(
                'thirdPartyCountry',
                CountryType::class,
                [
                    'label' => 'Third Party Country',
                ]
            );
    }
}
