<?php

declare(strict_types=1);
namespace U2\Form\Type\FieldAdder;

use Symfony\Component\Form\FormBuilderInterface;

class InstrumentIdFieldAdder
{
    /**
     * @param array<string, mixed> $options
     */
    public static function add(FormBuilderInterface $builder, array $options = []): void
    {
        $builder->add(
            'instrumentId',
            null,
            $options
        );
    }
}
