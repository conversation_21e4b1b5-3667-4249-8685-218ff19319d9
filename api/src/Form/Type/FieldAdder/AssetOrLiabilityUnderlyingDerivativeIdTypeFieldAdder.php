<?php

declare(strict_types=1);
namespace U2\Form\Type\FieldAdder;

use Symfony\Component\Form\FormBuilderInterface;
use U2\Entity\Configuration\Field\InstrumentIdType;
use U2\Form\Type\TaskChoiceFieldType;

class AssetOrLiabilityUnderlyingDerivativeIdTypeFieldAdder
{
    /**
     * @param array<string, mixed> $options
     */
    public static function add(FormBuilderInterface $builder, array $options = []): void
    {
        $builder
            ->add(
                'assetOrLiabilityUnderlyingDerivativeIdType',
                TaskChoiceFieldType::class,
                [
                    ...$options,
                    'class' => InstrumentIdType::class,
                ]
            );
    }
}
