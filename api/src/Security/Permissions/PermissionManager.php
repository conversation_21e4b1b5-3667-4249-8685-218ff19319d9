<?php

declare(strict_types=1);
namespace U2\Security\Permissions;

use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use U2\Dto\Permission\GroupPermissionInput;
use U2\Dto\Permission\UserPermissionInput;
use U2\Entity\CollectivePermission;
use U2\Entity\User;
use U2\Entity\UserGroup;

final class PermissionManager
{
    public static function grantUserPermission(CollectivePermission $collectivePermission, User $user, int $mask): void
    {
        $currentPermission = $collectivePermission->getUserPermissionFor($user);
        if (null === $currentPermission) {
            $collectivePermission->addUserPermission(new UserPermissionInput($user, self::extendMask(0, $mask)));

            return;
        }

        $currentPermission->setMask(self::extendMask($currentPermission->getMask(), $mask));
    }

    public static function grantGroupPermission(CollectivePermission $collectivePermission, UserGroup $group, int $mask): void
    {
        $currentPermission = $collectivePermission->getGroupPermissionFor($group);
        if (null === $currentPermission) {
            $collectivePermission->addGroupPermission(new GroupPermissionInput($group, self::extendMask(0, $mask)));

            return;
        }

        $currentPermission->setMask(self::extendMask($currentPermission->getMask(), $mask));
    }

    private static function extendMask(int $maskToExtend, int $extensionMask): int
    {
        $maskBuilder = new MaskBuilder($maskToExtend);
        $maskBuilder->add($extensionMask);

        return $maskBuilder->get();
    }
}
