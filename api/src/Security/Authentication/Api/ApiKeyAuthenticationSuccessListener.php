<?php

declare(strict_types=1);
namespace U2\Security\Authentication\Api;

use Lexik\Bundle\JWTAuthenticationBundle\TokenExtractor\AuthorizationHeaderTokenExtractor;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Security\Core\Event\AuthenticationSuccessEvent;
use U2\Messenger\ApiKeyAuthenticationSuccessMessage;

#[AsEventListener(event: AuthenticationSuccessEvent::class, method: 'onAuthenticationSuccess')]
readonly class ApiKeyAuthenticationSuccessListener
{
    public function __construct(
        private RequestStack $requestStack,
        private MessageBusInterface $messageBus,
    ) {
    }

    public function onAuthenticationSuccess(AuthenticationSuccessEvent $event): void
    {
        $currentRequest = $this->requestStack->getCurrentRequest();
        if (null === $currentRequest) {
            return;
        }

        $tokenExtractor = new AuthorizationHeaderTokenExtractor('ApiKey', 'Authorization');
        $apiKey = $tokenExtractor->extract($currentRequest);
        if (false === $apiKey) {
            return;
        }
        $this->messageBus->dispatch(new ApiKeyAuthenticationSuccessMessage($apiKey));
    }
}
