<?php

declare(strict_types=1);
namespace U2\Security\Authentication;

use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\Security\Core\Event\AuthenticationSuccessEvent;
use U2\Entity\User;
use U2\Sentry\Sentry;

#[AsEventListener(event: AuthenticationSuccessEvent::class, method: 'onAuthenticationSuccess')]
readonly class AuthenticationSuccessListener
{
    public function onAuthenticationSuccess(AuthenticationSuccessEvent $event): void
    {
        /** @var User $user */
        $user = $event->getAuthenticationToken()->getUser();

        Sentry::addUserToScope($user);
    }
}
