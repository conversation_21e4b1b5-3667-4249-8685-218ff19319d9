<?php

declare(strict_types=1);
namespace U2\Security\Password;

use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;
use U2\Email\EmailHelper;
use U2\Entity\User;
use U2\User\Settings\UserSettingTypes;

class PasswordResetMailer
{
    public function __construct(
        private readonly EmailHelper $emailHelper,
        private readonly TranslatorInterface $translator,
        private readonly Environment $renderer,
        private readonly string $defaultLocale,
        private readonly string $mailFrom,
    ) {
    }

    public function sendMail(User $user): void
    {
        $this->send([$user]);
    }

    /**
     * @param User[] $users
     */
    public function sendMailWithResetLinkPerUser(array $users): void
    {
        if (0 === \count($users)) {
            return;
        }

        $this->send($users);
    }

    private function send(array $users): void
    {
        $mailTo = reset($users)->getContact()->getEmail();
        $locales = array_unique(array_map(fn ($user): string => $this->getLocaleForUser($user), $users));
        $this->emailHelper->sendEmail(
            $this->mailFrom,
            $mailTo,
            $this->renderer->render('email/password_reset.email.html.twig', [
                'email' => $mailTo,
                'users' => array_map(fn ($user): array => [
                    'token' => base64_encode(json_encode([
                        'token' => $user->getPasswordResetConfirmationToken(),
                        'username' => $user->getUsername(),
                    ], \JSON_THROW_ON_ERROR)),
                    'username' => $user->getUsername(),
                ], $users),
                'locales' => $locales,
            ]),
            $this->translator->trans('u2.password_reset', [], null, $locales[0])
        );
    }

    private function getLocaleForUser(User $user): string
    {
        $userSettings = $user->getSettings();
        if (!$userSettings->offsetExists(UserSettingTypes::LOCALE)) {
            return $this->defaultLocale;
        }

        $setting = $userSettings->get(UserSettingTypes::LOCALE);

        return $setting?->getValue() ?? $this->defaultLocale;
    }
}
