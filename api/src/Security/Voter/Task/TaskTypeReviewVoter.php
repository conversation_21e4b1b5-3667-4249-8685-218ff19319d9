<?php

declare(strict_types=1);
namespace U2\Security\Voter\Task;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\Task\TaskType;
use U2\Entity\User;
use U2\Security\Voter\VoterAttributes;

/**
 * @extends Voter<VoterAttributes::review|VoterAttributes::removeReview, TaskType>
 */
class TaskTypeReviewVoter extends Voter
{
    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            VoterAttributes::review,
            VoterAttributes::removeReview,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, TaskType::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();
        if (!$user instanceof User) {
            return false;
        }

        /** @var TaskType $taskType */
        $taskType = $subject;

        switch ($attribute) {
            case VoterAttributes::review:
                return false === $taskType->getTask()->reviewExistsForUser($user);
            case VoterAttributes::removeReview:
                return $taskType->getTask()->reviewExistsForUser($user);
        }
    }
}
