<?php

declare(strict_types=1);
namespace U2\Security\Voter\Task;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\User;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\Voter\VoterAttributes;
use U2\Task\TaskType\TransactionInterface;
use U2\Unit\Assignment\UserUnitAssignmentChecker;

/**
 * @extends Voter<VoterAttributes::read|VoterAttributes::write|VoterAttributes::delete|VoterAttributes::addAttachment|VoterAttributes::removeAttachment, TaskType>
 */
class TaskTypeVoter extends Voter
{
    public function __construct(
        private readonly TaskTypeVoterHelper $helper,
        private readonly UserUnitAssignmentChecker $userUnitAssignmentChecker,
    ) {
    }

    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            VoterAttributes::addAttachment,
            VoterAttributes::removeAttachment,
            VoterAttributes::delete,
            VoterAttributes::read,
            VoterAttributes::write,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    public function supportsType(string $subjectType): bool
    {
        return is_a($subjectType, TaskType::class, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return true;
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();

        if (!$user instanceof User) {
            return false;
        }

        /** @var TaskType $taskType */
        $taskType = $subject;

        return match ($attribute) {
            VoterAttributes::removeAttachment => $this->canRemoveAttachment($taskType, $user),
            VoterAttributes::addAttachment, VoterAttributes::write => $this->canEdit($taskType, $user),
            VoterAttributes::read => $this->canRead($taskType, $user),
            VoterAttributes::delete => $this->canDelete($taskType, $user),
        };
    }

    private function canRemoveAttachment(TaskType $taskType, User $user): bool
    {
        return $this->canEdit($taskType, $user);
    }

    private function canRead(TaskType $taskType, User $user): bool
    {
        $right = $taskType instanceof StructuredDocumentInterface ? AuthorizationRight::ACCESS->value : AuthorizationRight::READ->value;
        if (!$this->helper->hasAuthorizationRight($taskType, $user, $right)) {
            return false;
        }

        // Allow read of new task types
        if (null === $taskType->getId()) {
            return true;
        }

        // Allow read of task types without a unit
        if (null === $taskType->getUnit()) {
            return true;
        }

        // Allow read of task types if the user is assigned to the unit
        if ($this->userUnitAssignmentChecker->check($user, [$taskType->getUnit()])) {
            return true;
        }

        // Special checks for general transaction task types
        if ($taskType instanceof TransactionInterface) {
            // Prevent read if there is no partner unit
            if (null === $taskType->getPartnerUnit()) {
                return false;
            }

            // Allow read if the user is assigned to the partner unit
            if ($this->userUnitAssignmentChecker->check($user, [$taskType->getPartnerUnit()])) {
                return true;
            }
        }

        return false;
    }

    private function canDelete(TaskType $taskType, User $user): bool
    {
        $right = $taskType instanceof StructuredDocumentInterface ? AuthorizationRight::ACCESS->value : AuthorizationRight::DELETE->value;
        if (!$this->helper->hasAuthorizationRight($taskType, $user, $right)) {
            return false;
        }

        // Allow delete of new task types
        if (null === $taskType->getId()) {
            return true;
        }

        // Allow delete of task types without a unit
        if (null === $taskType->getUnit()) {
            return true;
        }

        // Allow delete of task types if the user is assigned to the unit
        if ($this->userUnitAssignmentChecker->check($user, [$taskType->getUnit()])) {
            return true;
        }

        // Prevent delete of transaction task types if there is no partner unit
        if ($taskType instanceof TransactionInterface && null === $taskType->getPartnerUnit()) {
            return false;
        }

        // Allow delete of TPM transactions task types if the user is assigned to the partner unit
        if ($taskType instanceof Transaction && $this->userUnitAssignmentChecker->check($user, [$taskType->getPartnerUnit()])) {
            return true;
        }

        return false;
    }

    private function canEdit(TaskType $taskType, User $user): bool
    {
        $right = $taskType instanceof StructuredDocumentInterface ? AuthorizationRight::ACCESS->value : AuthorizationRight::UPDATE->value;
        if (!$this->helper->hasAuthorizationRight($taskType, $user, $right)) {
            return false;
        }

        // Allow edit of new task types
        if (null === $taskType->getId()) {
            return true;
        }

        // Allow edit of task types without a unit
        if (null === $taskType->getUnit()) {
            return true;
        }

        // Allow edit of task types if the user is assigned to the unit
        if ($this->userUnitAssignmentChecker->check($user, [$taskType->getUnit()])) {
            return true;
        }

        // Prevent edit of transaction task types if there is no partner unit
        if ($taskType instanceof TransactionInterface && null === $taskType->getPartnerUnit()) {
            return false;
        }

        // Allow edit of TPM transactions task types if the user is assigned to the partner unit
        if ($taskType instanceof Transaction && $this->userUnitAssignmentChecker->check($user, [$taskType->getPartnerUnit()])) {
            return true;
        }

        return false;
    }
}
