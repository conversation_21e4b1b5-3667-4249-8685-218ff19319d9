<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\Authorization\Voter\Voter as BaseVoter;
use U2\Entity\AuthorizationItem;
use U2\Entity\ExchangeRate;
use U2\Entity\Import;
use U2\Entity\User;
use U2\Security\Authorization\AuthorizationManager;
use U2\Security\Authorization\AuthorizationRight;

/**
 * @extends Voter<VoterAttributes::import, class-string<ExchangeRate>>
 */ class ExchangeRateImportVoter extends BaseVoter
{
    public function __construct(private readonly AuthorizationManager $authorizationManager)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        return VoterAttributes::import === $attribute;
    }

    public function supportsType(string $subjectType): bool
    {
        return 'string' === $subjectType;
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return \is_string($subject) && is_a($subject, ExchangeRate::class, true);
    }

    protected function voteOnAttribute(string $attribute, mixed $object, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();

        if (false === ($user instanceof User)) {
            return false;
        }

        return $this->authorizationManager->isAuthorized($user, AuthorizationItem::Period->value, AuthorizationRight::UPDATE->value);
    }
}
