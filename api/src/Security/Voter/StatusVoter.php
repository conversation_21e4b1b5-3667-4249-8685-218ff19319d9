<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Security\UserRoles;

/**
 * @extends Voter<VoterAttributes::read|VoterAttributes::write|VoterAttributes::delete, Status>
 */
class StatusVoter extends Voter
{
    public function __construct(private readonly AuthorizationCheckerInterface $authorizationChecker)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        $supportedAttributes = [
            VoterAttributes::write,
            VoterAttributes::read,
            VoterAttributes::delete,
        ];

        return \in_array($attribute, $supportedAttributes, true);
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        return is_a($subject, Status::class, true);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        $user = $token->getUser();

        if (false === ($user instanceof User)) {
            return false;
        }

        if (VoterAttributes::read === $attribute) {
            return true;
        }

        return $this->authorizationChecker->isGranted(UserRoles::Admin->value);
    }
}
