<?php

declare(strict_types=1);
namespace U2\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Vote;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use U2\Entity\Datasheet;
use U2\Entity\Field;
use U2\Entity\Item;
use U2\Entity\ItemFormula;
use U2\Security\UserRoles;

/**
 * @extends Voter<VoterAttributes::import, class-string>
 */
class ConfigurationImportVoter extends Voter
{
    public function __construct(private readonly AuthorizationCheckerInterface $authorizationChecker)
    {
    }

    public function supportsAttribute(string $attribute): bool
    {
        return VoterAttributes::import === $attribute;
    }

    public function supportsType(string $subjectType): bool
    {
        return 'string' === $subjectType;
    }

    protected function supports(string $attribute, mixed $subject): bool
    {
        $supportedClasses = [
            Field::class,
            ItemFormula::class,
            Item::class,
            Datasheet::class,
        ];

        return \in_array($subject, $supportedClasses, true);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token, ?Vote $vote = null): bool
    {
        return $this->authorizationChecker->isGranted(UserRoles::Admin->value);
    }
}
