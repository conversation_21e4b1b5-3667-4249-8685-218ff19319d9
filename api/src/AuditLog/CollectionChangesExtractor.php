<?php

declare(strict_types=1);
namespace U2\AuditLog;

use Doctrine\ORM\Mapping\AssociationMapping;
use Doctrine\ORM\Mapping\ToManyAssociationMapping;
use Doctrine\ORM\PersistentCollection;

class CollectionChangesExtractor
{
    public function __construct(
        private readonly DeletedCollectionOriginalValueExtractor $deletedCollectionOriginalValueExtractor,
    ) {
    }

    /**
     * @param PersistentCollection[] $scheduledCollectionUpdates
     * @param PersistentCollection[] $scheduledCollectionDeletions
     *
     * @return list<array{
     *     collection: PersistentCollection,
     *     oldValue: array<string|int, mixed>,
     *     newValue: list<mixed>,
     *     owner: object|null,
     *     association: AssociationMapping&ToManyAssociationMapping
     * }>
     */
    public function extractCollectionChanges(array $scheduledCollectionUpdates, array $scheduledCollectionDeletions): array
    {
        $collectionChanges = [];

        foreach ($scheduledCollectionUpdates as $updatedCollection) {
            $collectionChanges[] = [
                'collection' => $updatedCollection,
                'oldValue' => $updatedCollection->getSnapshot(),
                'newValue' => $updatedCollection->getValues(),
                'owner' => $updatedCollection->getOwner(),
                'association' => $updatedCollection->getMapping(),
            ];
        }

        foreach ($scheduledCollectionDeletions as $deletedCollection) {
            $collectionChanges[] = [
                'collection' => $deletedCollection,
                'oldValue' => $this->deletedCollectionOriginalValueExtractor->getOriginalValue($deletedCollection),
                'newValue' => [],
                'owner' => $deletedCollection->getOwner(),
                'association' => $deletedCollection->getMapping(),
            ];
        }

        return $collectionChanges;
    }
}
