<?php

declare(strict_types=1);
namespace U2\AuditLog;

class AuditedEntityAssociatedProperties
{
    public function __construct(private readonly array $reflectionProperties, private readonly \ReflectionClass $auditedClassReflection)
    {
    }

    /**
     * @return \ReflectionProperty[]
     */
    public function getReflectionProperties(): array
    {
        return $this->reflectionProperties;
    }

    public function getAuditedClassReflection(): \ReflectionClass
    {
        return $this->auditedClassReflection;
    }
}
