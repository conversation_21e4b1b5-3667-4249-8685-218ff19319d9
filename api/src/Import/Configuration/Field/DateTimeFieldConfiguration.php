<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field;

use Symfony\Component\Serializer\Attribute\Groups;

readonly class DateTimeFieldConfiguration extends AbstractFieldConfiguration
{
    #[Groups(['import_type:read'])]
    private string $format;

    public function __construct(
        string $id,
        string $label,
        ?string $help = null,
        ?string $format = null,
    ) {
        parent::__construct($id, $label, $help);
        $this->format = $format ?? 'Y-m-d H:i:s';
    }

    public function getFormat(): string
    {
        return $this->format;
    }
}
