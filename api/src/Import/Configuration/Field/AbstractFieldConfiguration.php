<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Field;

use Symfony\Component\Serializer\Attribute\Groups;

abstract readonly class AbstractFieldConfiguration implements FieldConfigurationInterface
{
    public function __construct(
        #[Groups(['import_type:read'])]
        private string $id,
        #[Groups(['import_type:read'])]
        private string $label,
        #[Groups(['import_type:read'])]
        private ?string $help = null,
    ) {
    }

    public function getId(): string
    {
        return $this->id;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getHelp(): ?string
    {
        return $this->help;
    }
}
