<?php

declare(strict_types=1);
namespace U2\Import\Configuration\Collection;

use U2\Entity\Datasheet;
use U2\Import\Configuration\Field\LiteralFieldConfiguration;
use U2\Import\Configuration\ImportConfig;

class DatasheetConfigurationFactory implements ConfigurationFactory
{
    public static function create(): ImportConfig
    {
        return new ImportConfig(
            class: Datasheet::class,
            fields: [
                new LiteralFieldConfiguration(
                    id: 'group',
                    label: 'Group',
                ),
                new LiteralFieldConfiguration(
                    id: 'name',
                    label: 'Name',
                ),
            ],
            updateMatchFields: [],
            factory: null,
            factoryArguments: [],
            help: null
        );
    }
}
