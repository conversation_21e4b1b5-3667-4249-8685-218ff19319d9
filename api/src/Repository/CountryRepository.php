<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Country;
use U2\Entity\Unit;
use U2\Entity\UnitHierarchy;
use U2\Entity\UnitHierarchyDefinition;

/**
 * @extends ServiceEntityRepository<Country>
 */
class CountryRepository extends ServiceEntityRepository
{
    /**
     * @return Country[]
     */
    public function findCountriesForUnitHierarchyDate(UnitHierarchy $unitHierarchy, \DateTime $date): array
    {
        $qb = $this->createQueryBuilder('country');

        $qb->select('country')
            ->join(Unit::class, 'unit', Join::WITH, 'country.id = unit.country')
            ->join(UnitHierarchyDefinition::class, 'uhd', Join::WITH, 'unit.id = uhd.unit')
            ->where(
                'uhd.unitHierarchy = :unitHierarchy
                AND ( uhd.startDate <= :searchDate OR uhd.startDate is null )
                AND ( uhd.endDate   >= :searchDate OR uhd.endDate   is null )
                '
            );

        /** @var Country[] $units */
        $units = $qb->getQuery()
            ->setParameter('unitHierarchy', $unitHierarchy)
            ->setParameter('searchDate', $date)
            ->getResult();

        return $units;
    }

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Country::class);
    }
}
