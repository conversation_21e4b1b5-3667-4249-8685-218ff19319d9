<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\DatasheetCollection;
use U2\Entity\Period;
use U2\Entity\Task\Task;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\Unit;

/**
 * @extends ServiceEntityRepository<UnitPeriod>
 */
class UnitPeriodRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, UnitPeriod::class);
    }

    /**
     * @return array<int,UnitPeriod>
     */
    public function findByLayoutCollectionPeriodAndUnit(
        Unit $unit,
        Period $period,
        DatasheetCollection $layoutCollection,
    ): array {
        $queryBuilder = $this->createQueryBuilder('up');

        /** @var array<int,UnitPeriod> $taskWithLayoutCollectionAssigned */
        $taskWithLayoutCollectionAssigned = $queryBuilder
            ->leftJoin(Task::class, 't', Join::WITH, 'up.task = t.id')
            ->where('up.unit = :unit')
            ->andWhere('up.period = :period')
            ->andWhere(
                $queryBuilder->expr()->orX(
                    ':layoutCollection MEMBER OF t.layoutCollections',
                    't.layoutCollections IS EMPTY',
                )
            )
            ->setParameter('unit', $unit->getId())
            ->setParameter('period', $period->getId())
            ->setParameter('layoutCollection', $layoutCollection->getId(), 'ulid')
            ->getQuery()
            ->getResult();

        return $taskWithLayoutCollectionAssigned;
    }
}
