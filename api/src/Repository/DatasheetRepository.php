<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\NoResultException;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use U2\Entity\Datasheet;
use U2\Entity\DatasheetCollection;
use U2\Entity\DatasheetCollectionEntry;
use U2\Security\Permissions\Assignable\QueryBuilderHelper;
use U2\Security\UserRoles;

/**
 * @extends ServiceEntityRepository<Datasheet>
 */
class DatasheetRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
        private Security $security,
        private readonly QueryBuilderHelper $queryBuilderHelper,
    ) {
        parent::__construct($registry, Datasheet::class);
    }

    public function addSecurityConditions(QueryBuilder $queryBuilder, ?int $mask = MaskBuilder::MASK_VIEW): void
    {
        $rootAlias = $queryBuilder->getRootAliases()[0];
        $this->queryBuilderHelper->addSecurityCondition(
            $queryBuilder,
            $rootAlias,
            $mask ?? MaskBuilder::MASK_VIEW,
            [
                $rootAlias . '.public = 1',
            ]);
    }

    public function findByIdAndMask(int $id, ?int $mask = null): ?Datasheet
    {
        $queryBuilder = $this->createQueryBuilder('datasheet');
        $queryBuilder
            ->andWhere('datasheet.id = :layoutId')
            ->setParameter('layoutId', $id);

        if (null !== $mask || false === $this->security->isGranted(UserRoles::Admin->value)) {
            $this->addSecurityConditions($queryBuilder, $mask);
        }

        try {
            /** @var Datasheet $result */
            $result = $queryBuilder->getQuery()->getSingleResult();

            return $result;
        } catch (NoResultException) {
            return null;
        }
    }

    public function findEager(int $layoutId): ?Datasheet
    {
        $queryBuilder = $this->createQueryBuilder('layout');
        $queryBuilder
            ->addSelect('fields')
            ->addSelect('item')
            ->addSelect('formula')
            ->leftJoin('layout.fields', 'fields')
            ->leftJoin('fields.item', 'item')
            ->leftJoin('item.formula', 'formula')
            ->where('layout.id = :layoutId')
            ->setParameter('layoutId', $layoutId);

        try {
            /** @var Datasheet $result */
            $result = $queryBuilder->getQuery()->getSingleResult();

            return $result;
        } catch (NoResultException) {
            return null;
        }
    }

    /**
     * @return \Traversable<Datasheet>
     */
    public function findAllAssignedToCollectionByMask(DatasheetCollection $layoutCollection, ?int $mask = null): \Traversable
    {
        $queryBuilder = $this->createQueryBuilder('layout');
        $queryBuilder
            ->leftJoin(DatasheetCollectionEntry::class, 'entry', 'WITH', 'entry.layoutCollection = :layoutCollectionId')
            ->andWhere('entry.layout = layout.id')
            ->setParameter('layoutCollectionId', $layoutCollection->getId(), 'ulid');

        // Only default to MASK_VIEW if the user is not an admin
        if (!$this->security->isGranted(UserRoles::Admin->value) && null === $mask) {
            $mask = MaskBuilder::MASK_VIEW;
        }

        if (null !== $mask) {
            $this->queryBuilderHelper->addSecurityCondition(
                $queryBuilder,
                $queryBuilder->getRootAliases()[0],
                $mask,
                [$queryBuilder->getRootAliases()[0] . '.public = 1']
            );
        }

        $queryBuilder->orderBy('entry.position');

        /** @var array<int, Datasheet> $result */
        $result = $queryBuilder->getQuery()->getResult();

        foreach ($result as $layout) {
            yield $layout;
        }
    }
}
