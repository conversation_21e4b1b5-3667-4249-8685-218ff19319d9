<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Task\TaskType\Igt5Transaction;

/**
 * @extends ServiceEntityRepository<Igt5Transaction>
 */
class Igt5TransactionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Igt5Transaction::class);
    }
}
