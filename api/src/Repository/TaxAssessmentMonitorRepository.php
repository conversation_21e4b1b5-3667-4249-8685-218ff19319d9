<?php

declare(strict_types=1);
namespace U2\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;

/**
 * @extends ServiceEntityRepository<TaxAssessmentMonitor>
 */
class TaxAssessmentMonitorRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TaxAssessmentMonitor::class);
    }
}
