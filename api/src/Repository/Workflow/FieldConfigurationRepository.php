<?php

declare(strict_types=1);
namespace U2\Repository\Workflow;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Workflow\FieldConfiguration;

/**
 * @extends ServiceEntityRepository<FieldConfiguration>
 */
class FieldConfigurationRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FieldConfiguration::class);
    }
}
