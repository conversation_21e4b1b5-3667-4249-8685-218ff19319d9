<?php

declare(strict_types=1);
namespace U2\Repository\Workflow;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\Workflow\FieldConfigurationStatuses;

/**
 * @extends ServiceEntityRepository<FieldConfigurationStatuses>
 */
class FieldConfigurationStatusesRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FieldConfigurationStatuses::class);
    }
}
