<?php

declare(strict_types=1);
namespace U2\Repository;

use U2\Datasheets\Item\UnitValue\ItemUnitValueFactory;
use U2\Entity\Item;
use U2\Entity\ItemUnitValue;
use U2\Entity\Period;
use U2\Entity\Unit;

class CachedItemUnitValueRepository
{
    /**
     * @var array<string, ItemUnitValue>
     */
    private array $itemUnitValues = [];

    public function __construct(
        private readonly ItemUnitValueRepository $itemUnitValueRepository,
        private readonly ItemUnitValueFactory $factory,
    ) {
    }

    public function getFromCacheOrAdd(Item $item, Unit $unit, Period $period): ItemUnitValue
    {
        $key = $this->generateKey($item, $unit, $period);
        if (!$this->isCached($key)) {
            $itemUnitValue = $this->factory->create($item, $unit, $period);
            $this->add($itemUnitValue);
        }

        return $this->itemUnitValues[$key];
    }

    public function findOrAdd(Item $item, Unit $unit, Period $period): ItemUnitValue
    {
        $key = $this->generateKey($item, $unit, $period);
        if (!$this->isCached($key)) {
            $itemUnitValue = $this->fetchOneAndAddToCache($item, $unit, $period);
            if (null === $itemUnitValue) {
                $itemUnitValue = $this->factory->create($item, $unit, $period);
                $this->add($itemUnitValue);
            }
        }

        return $this->itemUnitValues[$key];
    }

    /**
     * @return ItemUnitValue[]
     */
    public function all(): array
    {
        return array_values($this->itemUnitValues);
    }

    public function add(ItemUnitValue $itemUnitValue): void
    {
        $this->cache($itemUnitValue);
    }

    /**
     * @return ItemUnitValue[]
     */
    public function findBy(array $criteria, ?array $orderBy = null, ?int $limit = null, ?int $offset = null): array
    {
        /** @var ItemUnitValue[] $foundRecords */
        $foundRecords = $this->itemUnitValueRepository->findBy($criteria, $orderBy, $limit, $offset);
        foreach ($foundRecords as $itemUnitValue) {
            $this->cacheIfNotYetCached($itemUnitValue);
        }

        return $foundRecords;
    }

    protected function isCached(string $key): bool
    {
        return \array_key_exists($key, $this->itemUnitValues);
    }

    private function cacheIfNotYetCached(ItemUnitValue $itemUnitValue): void
    {
        $key = $this->generateKey($itemUnitValue->getItem(), $itemUnitValue->getUnit(), $itemUnitValue->getPeriod());
        if (false === $this->isCached($key)) {
            $this->itemUnitValues[$key] = $itemUnitValue;
        }
    }

    private function cache(ItemUnitValue $itemUnitValue): void
    {
        $key = $this->generateKey($itemUnitValue->getItem(), $itemUnitValue->getUnit(), $itemUnitValue->getPeriod());
        $this->itemUnitValues[$key] = $itemUnitValue;
    }

    private function fetchOneAndAddToCache(Item $item, Unit $unit, Period $period): ?ItemUnitValue
    {
        $itemUnitValue = $this->itemUnitValueRepository->findOneBy([
            'item' => $item,
            'unit' => $unit,
            'period' => $period,
        ]);

        if (null === $itemUnitValue) {
            return null;
        }

        $this->cacheIfNotYetCached($itemUnitValue);

        return $itemUnitValue;
    }

    private function generateKey(Item $item, Unit $unit, Period $period): string
    {
        return \sprintf('%d-%d-%d', $item->getId(), $unit->getId(), $period->getId());
    }
}
