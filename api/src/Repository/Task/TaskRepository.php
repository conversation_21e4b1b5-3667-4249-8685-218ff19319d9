<?php

declare(strict_types=1);
namespace U2\Repository\Task;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use U2\Entity\File;
use U2\Entity\Task\Task;

/**
 * @extends ServiceEntityRepository<Task>
 */
class TaskRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Task::class);
    }

    /**
     * @return array<int, Task>
     */
    public function findLinkedToFile(File $file): array
    {
        $qb = $this->createQueryBuilder('task');
        $qb->join('task.files', 'f')->where($qb->expr()->eq('f.id', $file->getId()));
        $qb->orderBy('task.type', 'ASC');

        /** @var array<int, Task> $result */
        $result = $qb->getQuery()->getResult();

        return $result;
    }
}
