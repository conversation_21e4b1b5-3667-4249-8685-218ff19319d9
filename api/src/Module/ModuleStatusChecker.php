<?php

declare(strict_types=1);
namespace U2\Module;

use U2\MultiTenancy\CurrentTenantProvider;

class ModuleStatusChecker
{
    public function __construct(
        private readonly CurrentTenantProvider $currentTenantProvider,
        private readonly EntityResolver $entityResolver,
    ) {
    }

    public function isEnabled(Module $module): bool
    {
        $tenantModules = $this->currentTenantProvider->getCurrentTenant()?->getModules();

        return $tenantModules[$module->value] ?? false;
    }

    public function isEnabledForEntity(string $entityClass): bool
    {
        $module = $this->entityResolver->resolve($entityClass);
        if (null === $module) {
            return true;
        }

        return $this->isEnabled($module);
    }
}
