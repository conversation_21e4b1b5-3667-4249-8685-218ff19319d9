<?php

declare(strict_types=1);
namespace U2\Serializer;

use ApiPlatform\Metadata\IriConverterInterface;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareInterface;
use Symfony\Component\Serializer\Normalizer\DenormalizerAwareTrait;
use Symfony\Component\Serializer\Normalizer\DenormalizerInterface;
use U2\Api\Resource\UpdateWorkflowBinding;
use U2\Entity\Workflow\Status;
use U2\Entity\Workflow\Workflow;
use U2\Repository\WorkflowBindingRepository;
use U2\Workflow\StatusMapping;

class UpdateWorkflowBindingDenormalizer implements DenormalizerAwareInterface, DenormalizerInterface
{
    use DenormalizerAwareTrait;

    public function __construct(
        private readonly WorkflowBindingRepository $workflowBindingRepository,
        private readonly IriConverterInterface $iriConverter,
    ) {
    }

    private const string ALREADY_CALLED = 'UPDATE_WORKFLOW_BINDING_ATTRIBUTE_DENORMALIZER_ALREADY_CALLED';

    public function denormalize(mixed $data, string $type, ?string $format = null, array $context = []): UpdateWorkflowBinding
    {
        $context[self::ALREADY_CALLED] = true;
        if (!\array_key_exists('uri_variables', $context)
            || !\array_key_exists('bindingId', $context['uri_variables'])
        ) {
            throw new BadRequestHttpException('Missing required parameter "bindingId"');
        }

        $binding = $this->workflowBindingRepository->findOneBy(['bindingId' => $context['uri_variables']['bindingId']]);
        if (null === $binding) {
            throw new NotFoundHttpException("Binding with bindingId {$context['uri_variables']['bindingId']} not found.");
        }

        /** @var array{targetWorkflow: string, statusMappings: array<array{source: string, destination: string}>} $updateWorkflowBinding */
        $updateWorkflowBinding = $data;

        return new UpdateWorkflowBinding(
            $binding,
            $this->getWorkflow($updateWorkflowBinding),
            $this->getStatusMappings($updateWorkflowBinding),
        );
    }

    /**
     * @param array<mixed> $context
     */
    public function supportsDenormalization($data, string $type, ?string $format = null, array $context = []): bool
    {
        if (isset($context[self::ALREADY_CALLED])) {
            return false;
        }

        if (UpdateWorkflowBinding::class !== $type) {
            return false;
        }

        return \is_array($data) && \array_key_exists('operation_name', $context) && 'u2_update_workflow_binding' === $context['operation_name'];
    }

    /**
     * @return array<class-string|'*'|'object'|string, bool|null>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [
            UpdateWorkflowBinding::class => false,
        ];
    }

    /**
     * @param array{targetWorkflow: string} $data
     */
    private function getWorkflow(array $data): Workflow
    {
        $targetWorkflow = $this->iriConverter->getResourceFromIri($data['targetWorkflow']);
        \assert($targetWorkflow instanceof Workflow);

        return $targetWorkflow;
    }

    /**
     * @param array{statusMappings: array<array{source: string, destination: string}>} $data
     *
     * @return array<StatusMapping>
     */
    private function getStatusMappings(array $data): array
    {
        $statusMappings = [];
        foreach ($data['statusMappings'] as $statusMapping) {
            /** @var string $sourceIri */
            $sourceIri = $statusMapping['source'];

            /** @var string $destinationIri */
            $destinationIri = $statusMapping['destination'];

            /** @var Status $source */
            $source = $this->iriConverter->getResourceFromIri($sourceIri);
            /** @var Status $destination */
            $destination = $this->iriConverter->getResourceFromIri($destinationIri);
            $statusMappings[] = new StatusMapping($source, $destination);
        }

        return $statusMappings;
    }
}
