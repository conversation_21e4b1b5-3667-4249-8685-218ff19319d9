<?php

declare(strict_types=1);
namespace U2\TransferPricing;

use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;
use U2\Document\DocumentTcpdf;
use U2\Document\Structure\ExcludedSectionsFilter;
use U2\Document\Structure\SectionNumberer;
use U2\Entity\DocumentSection;
use U2\Entity\StructuredDocumentInterface;
use U2\Entity\Task\TaskType\AbstractDocument;
use U2\FileSystem\TenantFilesystemOperator;
use U2\SystemSettings\SystemImagesHandler;
use U2\Util\NumberManipulator;

class DocumentPdfRenderer
{
    private const string DEFAULT_FONT = 'freesans';

    private const int FONT_SIZE = 10;

    private const int MARGIN = 20;

    public function __construct(
        private readonly Environment $templatingEngine,
        private readonly ExcludedSectionsFilter $excludedSectionsFilter,
        private readonly SectionNumberer $sectionNumberer,
        private readonly TenantFilesystemOperator $tenantFilesystem,
        private readonly string $defaultCoverPageImage,
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function generatePdf(AbstractDocument $document): string
    {
        $pdf = $this->initialisePdf($document);
        $this->insertCoverPage($document, $pdf);
        $this->insertContent($document, $pdf);
        $this->insertTableOfContents($pdf);

        return $pdf->Output('', 'S');
    }

    private function initialisePdf(AbstractDocument $document): DocumentTcpdf
    {
        \define('K_TCPDF_EXTERNAL_CONFIG', true);
        if (!\defined('K_TCPDF_THROW_EXCEPTION_ERROR')) {
            \define('K_TCPDF_THROW_EXCEPTION_ERROR', true);
        }

        $pdf = new DocumentTcpdf();

        $pdf->setImageScale(1);
        $pdf->setMargins(self::MARGIN, self::MARGIN);

        $pdf->setPrintHeader(true);
        $pdf->setHeaderMargin(self::MARGIN / 2);
        $pdf->setHeaderFont([self::DEFAULT_FONT, '', self::FONT_SIZE - 2]);

        $pdf->setPrintFooter(true);
        $pdf->setFooterMargin(self::MARGIN / 2);
        $pdf->setFooterFont([self::DEFAULT_FONT, '', self::FONT_SIZE - 2]);

        $pdf->setDefaultMonospacedFont(PDF_FONT_MONOSPACED);
        $pdf->setFont(self::DEFAULT_FONT, '', self::FONT_SIZE);
        $pdf->setFontSubsetting(false);
        $pdf->setAutoPageBreak(true, self::MARGIN);

        $pdf->setCreator('U2 Transfer Pricing PDF Export');

        $pdf->setAuthor($document->getTask()->getCreatedBy()?->getUsername() ?? 'Unknown');

        $documentName = $document->getName();
        \assert(null !== $documentName);
        $pdf->setTitle(trim($documentName));
        $pdf->setHeaderTitle(trim($documentName));

        $pdf->setHeaderImage('@' . $this->getHeaderImageContent());
        $pdf->setSubject('Subject not set.');
        $pdf->setKeywords('Keywords not set.');

        // set image scale factor
        $pdf->setImageScale(1.47);

        $pdf->setHtmlVSpace([
            'div' => [0 => ['h' => 1, 'n' => 1], 1 => ['h' => 1, 'n' => 4]],
            'h1' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 2]],
            'h2' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 2]],
            'h3' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 2]],
            'h4' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 2]],
            'h5' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 2]],
            'h6' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 2]],
            'p' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 4]],
            'ol' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 4]],
            'ul' => [0 => ['h' => 0, 'n' => 0], 1 => ['h' => 1, 'n' => 4]],
        ]);

        return $pdf;
    }

    private function insertCoverPage(AbstractDocument $document, DocumentTcpdf $pdfGenerator): void
    {
        $pdfGenerator->AddPage('P', 'A4');
        $pdfGenerator->Ln();
        $pdfGenerator->Ln();

        $positionOfTitleUpFromPageCenter = 30; // mm
        $coverPage = $this->templatingEngine
            ->render(
                'document/pdf/_cover.html.twig',
                [
                    'document' => $document,
                    'now' => new \DateTime(),
                ]
            );

        $coverImageFileContent = $this->getCoverImageContent();
        if ($coverImageFileContent) {
            $dpi = 120;
            $spaceBetweenMargins = $pdfGenerator->getPageWidth() - 2 * $pdfGenerator->GetX();
            $imageDimensions = $this->calculateCoverImageDisplayDimensions($spaceBetweenMargins, $dpi);

            $halfPageHeight = $pdfGenerator->getPageHeight() / 2;
            $paddingFromTitle = 15; // mm
            $positionFromTop = $halfPageHeight - $imageDimensions[1] - $paddingFromTitle - $positionOfTitleUpFromPageCenter;
            $positionFromTop = $positionFromTop > 0 ? $positionFromTop : $pdfGenerator->getHeaderMargin();

            $pdfGenerator->Image(
                '@' . $coverImageFileContent,
                $pdfGenerator->GetX(),
                $positionFromTop,
                $imageDimensions[0],
                $imageDimensions[1],
                '',
                '',
                '',
                false,
                $dpi,
                '',
                false,
                false,
                0,
                'CM', // FitBox
                false,
                false,
                false,
                []
            );
        }

        $pdfGenerator->setY(($pdfGenerator->getPageHeight() / 2) - $positionOfTitleUpFromPageCenter);
        $pdfGenerator->writeHTML($coverPage);

        $pdfGenerator->endPage();
    }

    private function insertContent(AbstractDocument $document, DocumentTcpdf $pdfGenerator): void
    {
        $sections = $document->getSections()->toArray();

        $numbering = $this->sectionNumberer->getNumbering($sections);
        $pdfGenerator->AddPage('P', 'A4');
        foreach ($this->excludedSectionsFilter->filter($sections) as $section) {
            $this->insertSection($pdfGenerator, $section, $numbering[$section->getId()] ?? []);
        }

        $pdfGenerator->AddPage('P', 'A4');

        $this->insertAttachmentsSection($pdfGenerator, $document, $numbering);
    }

    private function insertTableOfContents(DocumentTcpdf $pdfGenerator): void
    {
        $pdfGenerator->addTOCPage('P', 'A4');
        $pdfGenerator->setFont(self::DEFAULT_FONT, '', self::FONT_SIZE + 2);
        $pdfGenerator->addTOC(2, 'courier', '_', 'Table of Contents');
        $pdfGenerator->endTOCPage();
    }

    /**
     * @param array<int,array<int,string>> $numbering
     */
    private function insertSection(
        DocumentTcpdf $pdfGenerator,
        DocumentSection $section,
        ?array $numbering,
    ): void {
        if (null === $numbering) {
            $numbering = [];
        }

        $sectionHeader = implode('.', $numbering) . ' ' . trim($section->getName());
        $sectionContent = $this->templatingEngine->render(
            'document/pdf/_section.html.twig',
            [
                'section' => $section,
                'numbering' => $numbering,
            ]
        );

        $pdfGenerator->Bookmark($sectionHeader, $section->getLevel() - 1);
        $pdfGenerator->writeHTML($sectionContent);
    }

    /**
     * @param array<int, array<int, string>> $numbering
     */
    private function insertAttachmentsSection(
        DocumentTcpdf $pdfGenerator,
        StructuredDocumentInterface $document,
        array $numbering,
    ): void {
        $nameWithNumber = NumberManipulator::numberToRomanNumeral(1) . ' ' . $this->translator->trans('u2_core.attachments');
        $pdfGenerator->Bookmark($nameWithNumber);
        $pdfGenerator->writeHTML(
            $this->templatingEngine->render(
                'document/pdf/_attachments_appendix_section.html.twig',
                [
                    'document' => $document,
                    'name_with_number' => $nameWithNumber,
                    'numbering' => $numbering,
                ]
            )
        );
    }

    private function getHeaderImageContent(): string
    {
        $headerImage = SystemImagesHandler::APPLICATION_DOCUMENTATION_CORP_LOGO;
        if ($this->tenantFilesystem->fileExists($headerImage)) {
            return $this->tenantFilesystem->read($headerImage);
        }

        /** @var string $defaultHeaderImageContent */
        $defaultHeaderImageContent = file_get_contents($this->defaultCoverPageImage);

        return $defaultHeaderImageContent;
    }

    private function getCoverImageContent(): string
    {
        $coverImage = SystemImagesHandler::APPLICATION_DOCUMENTATION_COVER_PICTURE;
        if ($this->tenantFilesystem->fileExists($coverImage)) {
            return $this->tenantFilesystem->read($coverImage);
        }

        /** @var string $defaultCoverImageContent */
        $defaultCoverImageContent = file_get_contents($this->defaultCoverPageImage);

        return $defaultCoverImageContent;
    }

    private function calculateCoverImageDisplayDimensions(float $maximumImageWidthInMillimetres, int $dpi = 300): array
    {
        $pixelsPerInch = $dpi; // We approximate 1 pixel = 1 dot
        $pixelsPerMillimetre = $pixelsPerInch / 25.4;
        $maximumImageWidthInPixels = $pixelsPerMillimetre * $maximumImageWidthInMillimetres;
        $coverImageFinalWidthInMillimetres = $maximumImageWidthInMillimetres; // Maximum, so it centers
        $coverImageFinalHeightInMillimetres = $this->calculateCoverImageFinalHeightInMillimetres(
            $maximumImageWidthInMillimetres,
            $maximumImageWidthInPixels,
            $pixelsPerMillimetre
        );

        return [
            $coverImageFinalWidthInMillimetres,
            $coverImageFinalHeightInMillimetres,
        ];
    }

    private function calculateCoverImageFinalHeightInMillimetres(float $maximumImageWidthInMillimetres, float $maximumImageWidthInPixels, float $pixelsPerMillimetre): float
    {
        $result = getimagesizefromstring($this->getCoverImageContent());
        [$width, $height] = false === $result ? [0, 0] : $result;

        if ($width > $maximumImageWidthInPixels) {
            $ratio = $width / $height;

            return $maximumImageWidthInMillimetres / $ratio;
        }

        return $height / $pixelsPerMillimetre;
    }
}
