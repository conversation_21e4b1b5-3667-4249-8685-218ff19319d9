<?php

declare(strict_types=1);
namespace U2\TransferPricing\FinancialData;

use function Symfony\Component\Translation\t;

use U2\Entity\Task\TaskType\FinancialData;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\Task\TableType\AbstractTaskTypeTableType;
use U2\Task\TableType\PeriodColumnAdder;

class FinancialDataTableType extends AbstractTaskTypeTableType
{
    public function getName(): string
    {
        return 'u2_transfer_pricing_tpm_data_table';
    }

    public function buildTable(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        parent::buildTable($columnDefinitionCollection);

        PeriodColumnAdder::add($columnDefinitionCollection);

        $columnDefinitionCollection
            ->addAfter(
                'Period',
                'UnitRefId',
                null,
                [
                    'sortable' => true,
                    'filterable' => true,
                    'name' => t('u2.unit_ref_id'),
                ]
            )
            ->addAfter(
                'UnitRefId',
                'UnitName',
                null,
                [
                    'label' => t('u2.unit_name'),
                    'name' => t('u2.unit_name'),
                    'sortable' => true,
                    'filterable' => true,
                ]
            )
            ->addAfter(
                'UnitName',
                'UnitCountry',
                null,
                [
                    'label' => t('u2_tpm.unit_country'),
                    'name' => t('u2_tpm.unit_country'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'UnitCountry',
                'CarryForward',
                'boolean',
                [
                    'filterable' => true,
                    'name' => t('u2_tpm.carry_forward'),
                    'sortable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'CarryForward',
                'Currency',
                'currency',
                [
                    'sortable' => true,
                    'searchable' => true,
                    'selectedByDefault' => true,
                    'filterable' => true,
                    'label' => t('u2_tpm.base_currency.short'),
                    'name' => t('u2_tpm.base_currency'),
                ]
            )->addAfter(
                'Currency',
                'LocalCurrency',
                'currency',
                [
                    'sortable' => true,
                    'searchable' => true,
                    'selectedByDefault' => false,
                    'filterable' => true,
                    'label' => t('u2_tpm.local_currency.short'),
                    'name' => t('u2_tpm.local_currency'),
                ]
            )->addAfter(
                'LocalCurrency',
                'GroupCurrency',
                'currency',
                [
                    'sortable' => true,
                    'searchable' => true,
                    'selectedByDefault' => false,
                    'filterable' => true,
                    'label' => t('u2_tpm.group_currency.short'),
                    'name' => t('u2_tpm.group_currency'),
                ]
            )
            ->addAfter(
                'GroupCurrency',
                'TotalRevenueUnrelatedValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_unrelated_value_base_value.short'),
                    'name' => t('u2_tpm.total_revenue_unrelated_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'TotalRevenueUnrelatedValue',
                'TotalRevenueUnrelatedGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_unrelated_value_group_value.short'),
                    'name' => t('u2_tpm.total_revenue_unrelated_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'TotalRevenueUnrelatedGroupValue',
                'TotalRevenueUnrelatedLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_unrelated_value_local_value.short'),
                    'name' => t('u2_tpm.total_revenue_unrelated_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'TotalRevenueUnrelatedLocalValue',
                'TotalRevenueRelatedValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_related_value_base_value.short'),
                    'name' => t('u2_tpm.total_revenue_related_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'TotalRevenueRelatedValue',
                'TotalRevenueRelatedGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_related_value_group_value.short'),
                    'name' => t('u2_tpm.total_revenue_related_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'TotalRevenueRelatedGroupValue',
                'TotalRevenueRelatedLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_related_value_local_value.short'),
                    'name' => t('u2_tpm.total_revenue_related_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'TotalRevenueRelatedLocalValue',
                'TotalRevenueValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_value_base_value.short'),
                    'name' => t('u2_tpm.total_revenue_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'TotalRevenueValue',
                'TotalRevenueGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.total_revenue_value_group_value.short'),
                    'name' => t('u2_tpm.total_revenue_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'TotalRevenueGroupValue',
                'TotalRevenueLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.totalRevenue_value_local_value.short'),
                    'name' => t('u2_tpm.totalRevenue_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'TotalRevenueLocalValue',
                'ProfitLossBeforeIncomeTaxValue',
                'number',
                [
                    'label' => t('u2_tpm.profit_loss_before_income_tax_value_base_value.short'),
                    'name' => t('u2_tpm.profit_loss_before_income_tax_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'ProfitLossBeforeIncomeTaxValue',
                'ProfitLossBeforeIncomeTaxGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.profit_loss_before_income_tax_value_group_value.short'),
                    'name' => t('u2_tpm.profit_loss_before_income_tax_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'ProfitLossBeforeIncomeTaxGroupValue',
                'ProfitLossBeforeIncomeTaxLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.profit_loss_before_income_tax_value_local_value.short'),
                    'name' => t('u2_tpm.profit_loss_before_income_tax_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'ProfitLossBeforeIncomeTaxLocalValue',
                'IncomeTaxPaidValue',
                'number',
                [
                    'label' => t('u2_tpm.income_tax_paid_value_base_value.short'),
                    'name' => t('u2_tpm.income_tax_paid_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'IncomeTaxPaidValue',
                'IncomeTaxPaidGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.income_tax_paid_value_group_value.short'),
                    'name' => t('u2_tpm.income_tax_paid_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'IncomeTaxPaidGroupValue',
                'IncomeTaxPaidLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.income_tax_paid_value_local_value.short'),
                    'name' => t('u2_tpm.income_tax_paid_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'IncomeTaxPaidLocalValue',
                'IncomeTaxAccruedValue',
                'number',
                [
                    'label' => t('u2_tpm.income_tax_accrued_value_base_value.short'),
                    'name' => t('u2_tpm.income_tax_accrued_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'IncomeTaxAccruedValue',
                'IncomeTaxAccruedGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.income_tax_accrued_value_group_value.short'),
                    'name' => t('u2_tpm.income_tax_accrued_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'IncomeTaxAccruedGroupValue',
                'IncomeTaxAccruedLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.income_tax_accrued_value_local_value.short'),
                    'name' => t('u2_tpm.income_tax_accrued_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'IncomeTaxAccruedLocalValue',
                'StatedCapitalValue',
                'number',
                [
                    'label' => t('u2_tpm.stated_capital_value_base_value.short'),
                    'name' => t('u2_tpm.stated_capital_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'StatedCapitalValue',
                'StatedCapitalGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.stated_capital_value_group_value.short'),
                    'name' => t('u2_tpm.stated_capital_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'StatedCapitalGroupValue',
                'StatedCapitalLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.stated_capital_value_local_value.short'),
                    'name' => t('u2_tpm.stated_capital_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'StatedCapitalLocalValue',
                'AccumulatedEarningsValue',
                'number',
                [
                    'label' => t('u2_tpm.accumulated_earnings_value_base_value.short'),
                    'name' => t('u2_tpm.accumulated_earnings_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'AccumulatedEarningsValue',
                'AccumulatedEarningsGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.accumulated_earnings_value_group_value.short'),
                    'name' => t('u2_tpm.accumulated_earnings_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'AccumulatedEarningsGroupValue',
                'AccumulatedEarningsLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.accumulated_earnings_value_local_value.short'),
                    'name' => t('u2_tpm.accumulated_earnings_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'AccumulatedEarningsLocalValue',
                'TangibleAssetsValue',
                'number',
                [
                    'label' => t('u2_tpm.tangible_assets_value_base_value.short'),
                    'name' => t('u2_tpm.tangible_assets_value_base_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => true,
                ]
            )->addAfter(
                'TangibleAssetsValue',
                'TangibleAssetsGroupValue',
                'number',
                [
                    'label' => t('u2_tpm.tangible_assets_value_group_value.short'),
                    'name' => t('u2_tpm.tangible_assets_value_group_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addAfter(
                'TangibleAssetsGroupValue',
                'TangibleAssetsLocalValue',
                'number',
                [
                    'label' => t('u2_tpm.tangible_assets_value_local_value.short'),
                    'name' => t('u2_tpm.tangible_assets_value_local_value'),
                    'sortable' => true,
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )->addBefore(
                'Description',
                'NumberOfEmployees',
                'decimals',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'searchable' => true,
                    'label' => t('u2_tpm.employees'),
                    'name' => t('u2_tpm.number_of_employees'),
                ]
            );
    }

    public static function getEntityClass(): string
    {
        return FinancialData::class;
    }
}
