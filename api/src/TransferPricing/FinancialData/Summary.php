<?php

declare(strict_types=1);
namespace U2\TransferPricing\FinancialData;

use U2\Entity\Country;

final class Summary
{
    /**
     * @param Country[] $countries
     */
    public function __construct(
        private readonly array $countries,
        private readonly int $totalRevenueUnrelatedValue,
        private readonly int $totalRevenueRelatedValue,
        private readonly int $totalRevenueValue,
        private readonly int $profitLossBeforeIncomeTaxValue,
        private readonly int $incomeTaxPaidValue,
        private readonly int $incomeTaxAccruedValue,
        private readonly int $statedCapitalValue,
        private readonly int $accumulatedEarningsValue,
        private readonly int $tangibleAssetsValue,
        private readonly float $numberOfEmployees,
        private readonly string $groupCurrency,
    ) {
    }

    /**
     * @return Country[]
     */
    public function getCountries(): array
    {
        return $this->countries;
    }

    public function getTotalRevenueUnrelatedValue(): int
    {
        return $this->totalRevenueUnrelatedValue;
    }

    public function getTotalRevenueRelatedValue(): int
    {
        return $this->totalRevenueRelatedValue;
    }

    public function getTotalRevenueValue(): int
    {
        return $this->totalRevenueValue;
    }

    public function getProfitLossBeforeIncomeTaxValue(): int
    {
        return $this->profitLossBeforeIncomeTaxValue;
    }

    public function getIncomeTaxPaidValue(): int
    {
        return $this->incomeTaxPaidValue;
    }

    public function getIncomeTaxAccruedValue(): int
    {
        return $this->incomeTaxAccruedValue;
    }

    public function getStatedCapitalValue(): int
    {
        return $this->statedCapitalValue;
    }

    public function getAccumulatedEarningsValue(): int
    {
        return $this->accumulatedEarningsValue;
    }

    public function getTangibleAssetsValue(): int
    {
        return $this->tangibleAssetsValue;
    }

    public function getNumberOfEmployees(): float
    {
        return $this->numberOfEmployees;
    }

    public function getGroupCurrency(): string
    {
        return $this->groupCurrency;
    }
}
