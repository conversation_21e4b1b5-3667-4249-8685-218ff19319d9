<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240813113656 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove unit/period unique constraint from unit_period task type';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DROP INDEX unique_unit_period ON unit_period');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE UNIQUE INDEX unique_unit_period ON unit_period (unit_id, period_id)');
    }
}
