<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;

final class Version20240109082705 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add delete cascade to document section tables so that sections are deleted when the document is deleted';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE document_template_section DROP FOREIGN KEY FK_1FA93204877338D2');
        $this->addSql('ALTER TABLE document_template_section ADD CONSTRAINT FK_1FA93204877338D2 FOREIGN KEY (document_template_id) REFERENCES document_template (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tpm_country_by_country_report_section DROP FOREIGN KEY FK_5970990324DC16B');
        $this->addSql('ALTER TABLE tpm_country_by_country_report_section ADD CONSTRAINT FK_5970990324DC16B FOREIGN KEY (country_by_country_report_id) REFERENCES tpm_country_by_country_report (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tpm_local_file_section DROP FOREIGN KEY FK_93E89F38E1E7CE1');
        $this->addSql('ALTER TABLE tpm_local_file_section ADD CONSTRAINT FK_93E89F38E1E7CE1 FOREIGN KEY (local_file_id) REFERENCES tpm_local_file (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE tpm_master_file_section DROP FOREIGN KEY FK_CC1B05BA4FEC9404');
        $this->addSql('ALTER TABLE tpm_master_file_section ADD CONSTRAINT FK_CC1B05BA4FEC9404 FOREIGN KEY (master_file_id) REFERENCES tpm_master_file (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE document_template_section DROP FOREIGN KEY FK_1FA93204877338D2');
        $this->addSql('ALTER TABLE document_template_section ADD CONSTRAINT FK_1FA93204877338D2 FOREIGN KEY (document_template_id) REFERENCES document_template (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE tpm_country_by_country_report_section DROP FOREIGN KEY FK_5970990324DC16B');
        $this->addSql('ALTER TABLE tpm_country_by_country_report_section ADD CONSTRAINT FK_5970990324DC16B FOREIGN KEY (country_by_country_report_id) REFERENCES tpm_country_by_country_report (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE tpm_local_file_section DROP FOREIGN KEY FK_93E89F38E1E7CE1');
        $this->addSql('ALTER TABLE tpm_local_file_section ADD CONSTRAINT FK_93E89F38E1E7CE1 FOREIGN KEY (local_file_id) REFERENCES tpm_local_file (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE tpm_master_file_section DROP FOREIGN KEY FK_CC1B05BA4FEC9404');
        $this->addSql('ALTER TABLE tpm_master_file_section ADD CONSTRAINT FK_CC1B05BA4FEC9404 FOREIGN KEY (master_file_id) REFERENCES tpm_master_file (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
