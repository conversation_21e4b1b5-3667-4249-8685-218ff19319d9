<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;

final class Version20250516104928 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Increate the length of item formulas';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
                ALTER TABLE dtm_item_formula CHANGE formula_string formula_string VARCHAR(500) NOT NULL
            SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
                ALTER TABLE dtm_item_formula CHANGE formula_string formula_string VARCHAR(255) NOT NULL
            SQL);
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
