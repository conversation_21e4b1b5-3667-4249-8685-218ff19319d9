<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230524155453 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE dtm_item ADD description VARCHAR(255) DEFAULT NULL');
        $this->addSql('ALTER TABLE dtm_field ADD help_text VARCHAR(500) DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE dtm_item DROP description');
        $this->addSql('ALTER TABLE dtm_field DROP help_text');
    }
}
