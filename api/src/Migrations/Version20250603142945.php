<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250603142945 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove data transfer data';
    }

    public function up(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
                ALTER TABLE data_transfer DROP FOREIGN KEY FK_8AEA6A1C8415E910
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE data_transfer DROP FOREIGN KEY FK_8AEA6A1CA76ED395
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE data_transfer DROP FOREIGN KEY FK_8AEA6A1CD83F08F2
            SQL);
        $this->addSql(<<<'SQL'
                DROP TABLE data_transfer
            SQL);
    }

    public function down(Schema $schema): void
    {
        $this->addSql(<<<'SQL'
                CREATE TABLE data_transfer (id INT AUTO_INCREMENT NOT NULL, status VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_0900_ai_ci`, progress SMALLINT DEFAULT NULL, started_at DATETIME DEFAULT NULL, completed_at DATETIME DEFAULT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, metadata VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_0900_ai_ci`, dry_run TINYINT(1) NOT NULL, clear_status TINYINT(1) NOT NULL, statistics_count_source INT DEFAULT NULL, statistics_count_transfer INT DEFAULT NULL, statistics_count_target_before INT DEFAULT NULL, statistics_count_target_after INT DEFAULT NULL, statistics_warnings JSON NOT NULL, statistics_errors JSON NOT NULL, user_id INT DEFAULT NULL, source_period_id INT NOT NULL, target_period_id INT NOT NULL, INDEX IDX_8AEA6A1CA76ED395 (user_id), INDEX IDX_8AEA6A1C8415E910 (source_period_id), INDEX IDX_8AEA6A1CD83F08F2 (target_period_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_0900_ai_ci` ENGINE = InnoDB COMMENT = ''
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE data_transfer ADD CONSTRAINT FK_8AEA6A1C8415E910 FOREIGN KEY (source_period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE CASCADE
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE data_transfer ADD CONSTRAINT FK_8AEA6A1CA76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION
            SQL);
        $this->addSql(<<<'SQL'
                ALTER TABLE data_transfer ADD CONSTRAINT FK_8AEA6A1CD83F08F2 FOREIGN KEY (target_period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE CASCADE
            SQL);
    }
}
