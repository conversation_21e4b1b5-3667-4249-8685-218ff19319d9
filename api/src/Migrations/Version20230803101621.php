<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20230803101621 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Delete all igt/apm tables';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('DELETE FROM task where type = "apm_transaction"');
        $this->addSql('DELETE FROM task where type = "igt_igt1_transaction"');
        $this->addSql('DELETE FROM task where type = "igt_igt2_transaction"');
        $this->addSql('DELETE FROM task where type = "igt_igt3_transaction"');
        $this->addSql('DELETE FROM task where type = "igt_igt4_transaction"');
        $this->addSql('DROP TABLE apm_transaction');
        $this->addSql('DROP TABLE igt_igt1_transaction');
        $this->addSql('DROP TABLE igt_igt2_transaction');
        $this->addSql('DROP TABLE igt_igt3_transaction');
        $this->addSql('DROP TABLE igt_igt4_transaction');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('CREATE TABLE apm_transaction (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, third_party_country_id INT DEFAULT NULL, collateralisation_type_id INT DEFAULT NULL, business_case_id INT DEFAULT NULL, instrument_id_type_id INT DEFAULT NULL, transfer_pricing_method_id INT DEFAULT NULL, partner_unit_id INT DEFAULT NULL, transaction_currency_id INT NOT NULL, task_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', unit_id INT NOT NULL, local_currency_id INT DEFAULT NULL, group_currency_id INT DEFAULT NULL, period_id INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, reporting_date DATE NOT NULL, partner_is_third_party TINYINT(1) NOT NULL, third_party_name VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, booking_date DATE DEFAULT NULL, contract_date DATE NOT NULL, contract_party VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, seniority VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, interest_rate_type VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, interest_rate NUMERIC(7, 6) DEFAULT NULL, contract_expiry_date DATE NOT NULL, repayment_conditions LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, financing_purpose LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, overdraft_limit TINYINT(1) DEFAULT NULL, internal_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, instrument_id VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, trigger_event_if_applicable LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, max_letter_value_credit_or_guarantees NUMERIC(14, 0) DEFAULT NULL, guaranteed_assets_value NUMERIC(14, 0) DEFAULT NULL, previous_period_book_value NUMERIC(14, 0) DEFAULT NULL, current_period_interest_expenses NUMERIC(14, 0) DEFAULT NULL, current_period_book_value NUMERIC(14, 0) DEFAULT NULL, s2value_collateral_at_reporting_date NUMERIC(14, 0) DEFAULT NULL, redemption_value NUMERIC(14, 0) DEFAULT NULL, interest_coupon_value NUMERIC(14, 0) DEFAULT NULL, interest_rate_details LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, shareholder_interest TINYINT(1) DEFAULT NULL, arms_length TINYINT(1) DEFAULT NULL, unit_tp_documentation_required TINYINT(1) DEFAULT NULL, partner_unit_tp_documentation_required TINYINT(1) DEFAULT NULL, carry_forward TINYINT(1) NOT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, transaction_amount NUMERIC(14, 0) NOT NULL, local_amount NUMERIC(14, 0) NOT NULL, group_amount NUMERIC(14, 0) NOT NULL, local_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, transaction_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, UNIQUE INDEX UNIQ_D8F9E5C98DB60186 (task_id), INDEX IDX_D8F9E5C9B03A8386 (created_by_id), INDEX IDX_D8F9E5C9896DBBDE (updated_by_id), INDEX IDX_D8F9E5C9FFAE5AB7 (third_party_country_id), INDEX IDX_D8F9E5C9DF1882CC (collateralisation_type_id), INDEX IDX_D8F9E5C992236FB8 (business_case_id), INDEX IDX_D8F9E5C9D41C16E3 (instrument_id_type_id), INDEX IDX_D8F9E5C9473DBD53 (transfer_pricing_method_id), INDEX IDX_D8F9E5C93F571310 (partner_unit_id), INDEX IDX_D8F9E5C984AD945B (transaction_currency_id), INDEX IDX_D8F9E5C9F8BD700D (unit_id), INDEX IDX_D8F9E5C9532DF3D8 (local_currency_id), INDEX IDX_D8F9E5C91D9C9EB7 (group_currency_id), INDEX IDX_D8F9E5C9EC8B7ADE (period_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE igt_igt1_transaction (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, instrument_id_type_id INT DEFAULT NULL, collateralisation_type_id INT DEFAULT NULL, partner_unit_id INT DEFAULT NULL, transaction_currency_id INT NOT NULL, task_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', unit_id INT NOT NULL, local_currency_id INT DEFAULT NULL, group_currency_id INT DEFAULT NULL, transfer_pricing_method_id INT DEFAULT NULL, period_id INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, instrument_id VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, date DATE NOT NULL, contract_expiry_date DATE DEFAULT NULL, contract_date DATE NOT NULL, s2value_collateral_at_reporting_date NUMERIC(14, 0) DEFAULT NULL, redemption_value NUMERIC(14, 0) DEFAULT NULL, other_payment NUMERIC(14, 0) DEFAULT NULL, coupon_interest_rate NUMERIC(7, 6) DEFAULT NULL, coupon_interest_rate_type LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, previous_period_book_value NUMERIC(14, 0) DEFAULT NULL, current_period_interest_expenses NUMERIC(14, 0) DEFAULT NULL, current_period_book_value NUMERIC(14, 0) DEFAULT NULL, repayment_condition LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, transaction_amount NUMERIC(14, 0) NOT NULL, local_amount NUMERIC(14, 0) NOT NULL, group_amount NUMERIC(14, 0) NOT NULL, local_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, transaction_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, arms_length TINYINT(1) DEFAULT NULL, internal_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, unit_tp_documentation_required TINYINT(1) DEFAULT NULL, partner_unit_tp_documentation_required TINYINT(1) DEFAULT NULL, UNIQUE INDEX UNIQ_F4FCAB158DB60186 (task_id), INDEX IDX_F4FCAB15B03A8386 (created_by_id), INDEX IDX_F4FCAB15896DBBDE (updated_by_id), INDEX IDX_F4FCAB15D41C16E3 (instrument_id_type_id), INDEX IDX_F4FCAB15DF1882CC (collateralisation_type_id), INDEX IDX_F4FCAB153F571310 (partner_unit_id), INDEX IDX_F4FCAB1584AD945B (transaction_currency_id), INDEX IDX_F4FCAB15F8BD700D (unit_id), INDEX IDX_F4FCAB15532DF3D8 (local_currency_id), INDEX IDX_F4FCAB151D9C9EB7 (group_currency_id), INDEX IDX_F4FCAB15473DBD53 (transfer_pricing_method_id), INDEX IDX_F4FCAB15EC8B7ADE (period_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE igt_igt2_transaction (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, asset_or_liability_underlying_derivative_id_type_id INT NOT NULL, instrument_id_type_id INT NOT NULL, swap_delivered_currency_id INT DEFAULT NULL, swap_received_currency_id INT DEFAULT NULL, unit_for_credit_protection_id INT DEFAULT NULL, partner_unit_id INT DEFAULT NULL, transaction_currency_id INT NOT NULL, task_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', unit_id INT NOT NULL, local_currency_id INT DEFAULT NULL, group_currency_id INT DEFAULT NULL, transfer_pricing_method_id INT DEFAULT NULL, period_id INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, asset_or_liability_underlying_derivative LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, collateral_value_at_reporting_date NUMERIC(14, 0) DEFAULT NULL, date DATE NOT NULL, derivatives_use VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, instrument_id VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, maturity_date DATE NOT NULL, notional_value_at_reporting_date NUMERIC(14, 0) NOT NULL, swap_delivered_interest_rate NUMERIC(7, 6) DEFAULT NULL, swap_received_interest_rate NUMERIC(7, 6) DEFAULT NULL, rate_type VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, rate NUMERIC(20, 10) DEFAULT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, transaction_amount NUMERIC(14, 0) NOT NULL, local_amount NUMERIC(14, 0) NOT NULL, group_amount NUMERIC(14, 0) NOT NULL, local_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, transaction_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, arms_length TINYINT(1) DEFAULT NULL, internal_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, unit_tp_documentation_required TINYINT(1) DEFAULT NULL, partner_unit_tp_documentation_required TINYINT(1) DEFAULT NULL, UNIQUE INDEX UNIQ_4936C7DB8DB60186 (task_id), INDEX IDX_4936C7DBB03A8386 (created_by_id), INDEX IDX_4936C7DB896DBBDE (updated_by_id), INDEX IDX_4936C7DBFFABFB8F (asset_or_liability_underlying_derivative_id_type_id), INDEX IDX_4936C7DBD41C16E3 (instrument_id_type_id), INDEX IDX_4936C7DBD51AA2C3 (swap_delivered_currency_id), INDEX IDX_4936C7DB5CCD6FF6 (swap_received_currency_id), INDEX IDX_4936C7DBE3B74172 (unit_for_credit_protection_id), INDEX IDX_4936C7DB3F571310 (partner_unit_id), INDEX IDX_4936C7DB84AD945B (transaction_currency_id), INDEX IDX_4936C7DBF8BD700D (unit_id), INDEX IDX_4936C7DB532DF3D8 (local_currency_id), INDEX IDX_4936C7DB1D9C9EB7 (group_currency_id), INDEX IDX_4936C7DB473DBD53 (transfer_pricing_method_id), INDEX IDX_4936C7DBEC8B7ADE (period_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE igt_igt3_transaction (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, line_of_business_id INT NOT NULL, partner_unit_id INT DEFAULT NULL, transaction_currency_id INT NOT NULL, task_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', unit_id INT NOT NULL, local_currency_id INT DEFAULT NULL, group_currency_id INT DEFAULT NULL, transfer_pricing_method_id INT DEFAULT NULL, period_id INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, maximum_cover_by_reinsurer NUMERIC(14, 0) NOT NULL, reinsurance_recoverable_total_value NUMERIC(14, 0) NOT NULL, reinsurance_result_value NUMERIC(14, 0) DEFAULT NULL, net_receivables NUMERIC(14, 0) NOT NULL, validity_period_start_date DATE NOT NULL, validity_period_expiry_date DATE NOT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, transaction_amount NUMERIC(14, 0) NOT NULL, local_amount NUMERIC(14, 0) NOT NULL, group_amount NUMERIC(14, 0) NOT NULL, local_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, transaction_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, arms_length TINYINT(1) DEFAULT NULL, internal_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, unit_tp_documentation_required TINYINT(1) DEFAULT NULL, partner_unit_tp_documentation_required TINYINT(1) DEFAULT NULL, UNIQUE INDEX UNIQ_94A01E5E8DB60186 (task_id), INDEX IDX_94A01E5EB03A8386 (created_by_id), INDEX IDX_94A01E5E896DBBDE (updated_by_id), INDEX IDX_94A01E5E1CE7B5B5 (line_of_business_id), INDEX IDX_94A01E5E3F571310 (partner_unit_id), INDEX IDX_94A01E5E84AD945B (transaction_currency_id), INDEX IDX_94A01E5EF8BD700D (unit_id), INDEX IDX_94A01E5E532DF3D8 (local_currency_id), INDEX IDX_94A01E5E1D9C9EB7 (group_currency_id), INDEX IDX_94A01E5E473DBD53 (transfer_pricing_method_id), INDEX IDX_94A01E5EEC8B7ADE (period_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('CREATE TABLE igt_igt4_transaction (id INT AUTO_INCREMENT NOT NULL, created_by_id INT DEFAULT NULL, updated_by_id INT DEFAULT NULL, service_type_id INT DEFAULT NULL, partner_unit_id INT DEFAULT NULL, transaction_currency_id INT NOT NULL, task_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', unit_id INT NOT NULL, local_currency_id INT DEFAULT NULL, group_currency_id INT DEFAULT NULL, transfer_pricing_method_id INT DEFAULT NULL, period_id INT NOT NULL, created_at DATETIME NOT NULL, updated_at DATETIME NOT NULL, contract_date DATE NOT NULL, contract_expiry_date DATE NOT NULL, date DATE DEFAULT NULL, max_contingent_liabilities_in_s2bs NUMERIC(14, 0) DEFAULT NULL, max_contingent_liabilities_not_in_s2bs NUMERIC(14, 0) DEFAULT NULL, trigger_event_if_applicable LONGTEXT CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, max_letter_value_credit_or_guarantees NUMERIC(14, 0) DEFAULT NULL, guaranteed_assets_value NUMERIC(14, 0) DEFAULT NULL, shareholder_interest TINYINT(1) DEFAULT NULL, carry_forward TINYINT(1) NOT NULL, guarantee_fee_amount NUMERIC(14, 0) DEFAULT NULL, type VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, transaction_amount NUMERIC(14, 0) NOT NULL, local_amount NUMERIC(14, 0) NOT NULL, group_amount NUMERIC(14, 0) NOT NULL, local_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, transaction_to_group_exchange_rate NUMERIC(20, 10) NOT NULL, arms_length TINYINT(1) DEFAULT NULL, internal_id VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL COLLATE `utf8mb4_unicode_ci`, unit_tp_documentation_required TINYINT(1) DEFAULT NULL, partner_unit_tp_documentation_required TINYINT(1) DEFAULT NULL, UNIQUE INDEX UNIQ_E9D318068DB60186 (task_id), INDEX IDX_E9D31806B03A8386 (created_by_id), INDEX IDX_E9D31806896DBBDE (updated_by_id), INDEX IDX_E9D31806AC8DE0F (service_type_id), INDEX IDX_E9D318063F571310 (partner_unit_id), INDEX IDX_E9D3180684AD945B (transaction_currency_id), INDEX IDX_E9D31806F8BD700D (unit_id), INDEX IDX_E9D31806532DF3D8 (local_currency_id), INDEX IDX_E9D318061D9C9EB7 (group_currency_id), INDEX IDX_E9D31806473DBD53 (transfer_pricing_method_id), INDEX IDX_E9D31806EC8B7ADE (period_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C91D9C9EB7 FOREIGN KEY (group_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C93F571310 FOREIGN KEY (partner_unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9473DBD53 FOREIGN KEY (transfer_pricing_method_id) REFERENCES pricing_method (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9532DF3D8 FOREIGN KEY (local_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C984AD945B FOREIGN KEY (transaction_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C98DB60186 FOREIGN KEY (task_id) REFERENCES task (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C992236FB8 FOREIGN KEY (business_case_id) REFERENCES apm_business_case (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9D41C16E3 FOREIGN KEY (instrument_id_type_id) REFERENCES igt_instrument_id_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9DF1882CC FOREIGN KEY (collateralisation_type_id) REFERENCES collateralisation_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9EC8B7ADE FOREIGN KEY (period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9F8BD700D FOREIGN KEY (unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE apm_transaction ADD CONSTRAINT FK_D8F9E5C9FFAE5AB7 FOREIGN KEY (third_party_country_id) REFERENCES country (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB151D9C9EB7 FOREIGN KEY (group_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB153F571310 FOREIGN KEY (partner_unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15473DBD53 FOREIGN KEY (transfer_pricing_method_id) REFERENCES pricing_method (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15532DF3D8 FOREIGN KEY (local_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB1584AD945B FOREIGN KEY (transaction_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB158DB60186 FOREIGN KEY (task_id) REFERENCES task (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15D41C16E3 FOREIGN KEY (instrument_id_type_id) REFERENCES igt_instrument_id_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15DF1882CC FOREIGN KEY (collateralisation_type_id) REFERENCES collateralisation_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15EC8B7ADE FOREIGN KEY (period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt1_transaction ADD CONSTRAINT FK_F4FCAB15F8BD700D FOREIGN KEY (unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB1D9C9EB7 FOREIGN KEY (group_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB3F571310 FOREIGN KEY (partner_unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB473DBD53 FOREIGN KEY (transfer_pricing_method_id) REFERENCES pricing_method (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB532DF3D8 FOREIGN KEY (local_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB5CCD6FF6 FOREIGN KEY (swap_received_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB84AD945B FOREIGN KEY (transaction_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DB8DB60186 FOREIGN KEY (task_id) REFERENCES task (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBD41C16E3 FOREIGN KEY (instrument_id_type_id) REFERENCES igt_instrument_id_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBD51AA2C3 FOREIGN KEY (swap_delivered_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBE3B74172 FOREIGN KEY (unit_for_credit_protection_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBEC8B7ADE FOREIGN KEY (period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBF8BD700D FOREIGN KEY (unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt2_transaction ADD CONSTRAINT FK_4936C7DBFFABFB8F FOREIGN KEY (asset_or_liability_underlying_derivative_id_type_id) REFERENCES igt_instrument_id_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E1CE7B5B5 FOREIGN KEY (line_of_business_id) REFERENCES igt_line_of_business (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E1D9C9EB7 FOREIGN KEY (group_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E3F571310 FOREIGN KEY (partner_unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E473DBD53 FOREIGN KEY (transfer_pricing_method_id) REFERENCES pricing_method (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E532DF3D8 FOREIGN KEY (local_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E84AD945B FOREIGN KEY (transaction_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5E8DB60186 FOREIGN KEY (task_id) REFERENCES task (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5EB03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5EEC8B7ADE FOREIGN KEY (period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt3_transaction ADD CONSTRAINT FK_94A01E5EF8BD700D FOREIGN KEY (unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D318061D9C9EB7 FOREIGN KEY (group_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D318063F571310 FOREIGN KEY (partner_unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806473DBD53 FOREIGN KEY (transfer_pricing_method_id) REFERENCES pricing_method (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806532DF3D8 FOREIGN KEY (local_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D3180684AD945B FOREIGN KEY (transaction_currency_id) REFERENCES currency (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806896DBBDE FOREIGN KEY (updated_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D318068DB60186 FOREIGN KEY (task_id) REFERENCES task (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806AC8DE0F FOREIGN KEY (service_type_id) REFERENCES igt_service_type (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806B03A8386 FOREIGN KEY (created_by_id) REFERENCES user (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806EC8B7ADE FOREIGN KEY (period_id) REFERENCES period (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('ALTER TABLE igt_igt4_transaction ADD CONSTRAINT FK_E9D31806F8BD700D FOREIGN KEY (unit_id) REFERENCES unit (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
