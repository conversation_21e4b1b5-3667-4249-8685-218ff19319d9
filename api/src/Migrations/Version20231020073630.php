<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;

final class Version20231020073630 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add rules to choice types to allow validation against task content';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('ALTER TABLE apm_business_case ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE billing_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE collateralisation_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE contract_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE deadline_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE igt_instrument_id_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE igt_line_of_business ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE igt_service_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE pricing_method ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_advice_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_loss_restriction ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_loss_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_restriction_reason ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_risk_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_specification ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tam_tax_credit_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tax_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tcm_assessment_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tcm_declaration_type ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE tpm_business_activity ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE trace_id ADD rules JSON DEFAULT NULL');
        $this->addSql('ALTER TABLE transaction_type ADD rules JSON DEFAULT NULL');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('ALTER TABLE apm_business_case DROP rules');
        $this->addSql('ALTER TABLE billing_type DROP rules');
        $this->addSql('ALTER TABLE collateralisation_type DROP rules');
        $this->addSql('ALTER TABLE contract_type DROP rules');
        $this->addSql('ALTER TABLE deadline_type DROP rules');
        $this->addSql('ALTER TABLE igt_instrument_id_type DROP rules');
        $this->addSql('ALTER TABLE igt_line_of_business DROP rules');
        $this->addSql('ALTER TABLE igt_service_type DROP rules');
        $this->addSql('ALTER TABLE pricing_method DROP rules');
        $this->addSql('ALTER TABLE tam_advice_type DROP rules');
        $this->addSql('ALTER TABLE tam_loss_restriction DROP rules');
        $this->addSql('ALTER TABLE tam_loss_type DROP rules');
        $this->addSql('ALTER TABLE tam_restriction_reason DROP rules');
        $this->addSql('ALTER TABLE tam_risk_type DROP rules');
        $this->addSql('ALTER TABLE tam_specification DROP rules');
        $this->addSql('ALTER TABLE tam_tax_credit_type DROP rules');
        $this->addSql('ALTER TABLE tax_type DROP rules');
        $this->addSql('ALTER TABLE tcm_assessment_type DROP rules');
        $this->addSql('ALTER TABLE tcm_declaration_type DROP rules');
        $this->addSql('ALTER TABLE tpm_business_activity DROP rules');
        $this->addSql('ALTER TABLE trace_id DROP rules');
        $this->addSql('ALTER TABLE transaction_type DROP rules');
    }

    public function isTransactional(): bool
    {
        return false;
    }
}
