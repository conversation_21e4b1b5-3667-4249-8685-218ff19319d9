<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;
use Symfony\Component\Uid\Ulid;

final class Version20230811110050 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add "Tax Accounting" layout collection with all existing layouts inside';
    }

    public function up(Schema $schema): void
    {
        // Add a new collective permission entity
        $this->connection->executeQuery('INSERT INTO collective_permission (id) VALUES (null);');

        $layoutCollectionId = (new Ulid())->toBinary();
        $this->connection->executeQuery("INSERT INTO dtm_layout_collection
            (id, permissions_id, created_by_id, updated_by_id, created_at, updated_at, name, public)
            VALUES
            (:layoutCollectionId, :collectivePermissionId, null, null, NOW(), NOW(), 'Tax Accounting', 1);
        ",
            ['layoutCollectionId' => $layoutCollectionId, 'collectivePermissionId' => $this->connection->lastInsertId()],
            ['ulid']
        );

        $allLayouts = $this->connection->executeQuery('select id, group_id from dtm_layout')->fetchAllAssociative();
        $layoutsByGroup = [];
        foreach ($allLayouts as $layout) {
            $layoutsByGroup[$layout['group_id']][] = $layout;
        }

        $layoutsIdAndPositionMap = [];
        foreach ($layoutsByGroup as $layouts) {
            foreach ($layouts as $index => $layout) {
                $layoutsIdAndPositionMap[] = ['id' => $layout['id'], 'position' => $index + 1];
            }
        }

        foreach ($layoutsIdAndPositionMap as $layoutIdAndPosition) {
            $this->addSql('INSERT INTO dtm_layout_collection_entry
                (id, layout_id, layout_collection_id, position)
                VALUES
                (:layoutCollectionEntryId, :layoutId, :layoutCollectionId, :positionInCollection);
            ',
                [
                    'layoutCollectionEntryId' => (new Ulid())->toBinary(),
                    'layoutId' => $layoutIdAndPosition['id'],
                    'layoutCollectionId' => $layoutCollectionId,
                    'positionInCollection' => $layoutIdAndPosition['position'],
                ],
                ['ulid']
            );
        }
    }

    public function down(Schema $schema): void
    {
    }

    public function isTransactional(): bool
    {
        return true;
    }
}
