<?php

declare(strict_types=1);
namespace U2\Migrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20240813123336 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Add layout collections to task';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE task_layout_collection (task_id BINARY(16) NOT NULL COMMENT \'(DC2Type:uuid)\', layout_collection_id BINARY(16) NOT NULL COMMENT \'(DC2Type:ulid)\', INDEX IDX_8FB9290F8DB60186 (task_id), INDEX IDX_8FB9290FCD381AB5 (layout_collection_id), PRIMARY KEY(task_id, layout_collection_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE task_layout_collection ADD CONSTRAINT FK_8FB9290F8DB60186 FOREIGN KEY (task_id) REFERENCES task (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE task_layout_collection ADD CONSTRAINT FK_8FB9290FCD381AB5 FOREIGN KEY (layout_collection_id) REFERENCES dtm_layout_collection (id) ON DELETE CASCADE');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE task_layout_collection DROP FOREIGN KEY FK_8FB9290F8DB60186');
        $this->addSql('ALTER TABLE task_layout_collection DROP FOREIGN KEY FK_8FB9290FCD381AB5');
        $this->addSql('DROP TABLE task_layout_collection');
    }
}
