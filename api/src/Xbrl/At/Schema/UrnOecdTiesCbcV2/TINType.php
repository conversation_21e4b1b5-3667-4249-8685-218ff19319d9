<?php

declare(strict_types=1);
namespace U2\Xbrl\At\Schema\UrnOecdTiesCbcV2;

class TINType
{
    private string $__value;

    private ?string $issuedBy = null;

    public function __construct(string $value, string $issuedBy)
    {
        $this->value($value);
        $this->issuedBy = $issuedBy;
    }

    public function __toString(): string
    {
        return $this->__value;
    }

    public function value(): string
    {
        if (0 < \count($args = \func_get_args())) {
            $this->__value = $args[0];
        }

        return $this->__value;
    }

    public function getIssuedBy(): ?string
    {
        return $this->issuedBy;
    }

    public function setIssuedBy(string $issuedBy): self
    {
        $this->issuedBy = $issuedBy;

        return $this;
    }
}
