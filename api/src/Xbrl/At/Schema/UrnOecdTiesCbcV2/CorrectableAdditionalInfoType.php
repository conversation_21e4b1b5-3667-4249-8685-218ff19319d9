<?php

declare(strict_types=1);
namespace U2\Xbrl\At\Schema\UrnOecdTiesCbcV2;

use U2\Xbrl\At\Schema\UrnOecdTiesCbcstfV5\DocSpecType;
use U2\Xbrl\At\Schema\UrnOecdTiesCbcstfV5\StringMin1Max4000WithLangType;

class CorrectableAdditionalInfoType
{
    /**
     * @var array<int, string>
     */
    private array $summaryRef = [];

    /**
     * @param array<int, StringMin1Max4000WithLangType> $otherInfo
     * @param array<int, string>                        $resCountryCode
     */
    public function __construct(private DocSpecType $docSpec, private array $otherInfo, private array $resCountryCode)
    {
    }

    public function getDocSpec(): ?DocSpecType
    {
        return $this->docSpec;
    }

    public function setDocSpec(DocSpecType $docSpec): self
    {
        $this->docSpec = $docSpec;

        return $this;
    }

    public function addToOtherInfo(StringMin1Max4000WithLangType $otherInfo): void
    {
        $this->otherInfo[] = $otherInfo;
    }

    public function issetOtherInfo(int $index): bool
    {
        return isset($this->otherInfo[$index]);
    }

    public function unsetOtherInfo(int $index): void
    {
        unset($this->otherInfo[$index]);
    }

    /**
     * @return array<int, StringMin1Max4000WithLangType>
     */
    public function getOtherInfo(): array
    {
        return $this->otherInfo;
    }

    /**
     * @param array<int, StringMin1Max4000WithLangType> $otherInfo
     */
    public function setOtherInfo(array $otherInfo): void
    {
        $this->otherInfo = $otherInfo;
    }

    public function addToResCountryCode(string $resCountryCode): self
    {
        $this->resCountryCode[] = $resCountryCode;

        return $this;
    }

    public function issetResCountryCode(int $index): bool
    {
        return isset($this->resCountryCode[$index]);
    }

    public function unsetResCountryCode(int $index): void
    {
        unset($this->resCountryCode[$index]);
    }

    /**
     * @return array<int, string>
     */
    public function getResCountryCode(): array
    {
        return $this->resCountryCode;
    }

    /**
     * @param array<int, string> $resCountryCode
     */
    public function setResCountryCode(array $resCountryCode): self
    {
        $this->resCountryCode = $resCountryCode;

        return $this;
    }

    public function addToSummaryRef(string $summaryRef): self
    {
        $this->summaryRef[] = $summaryRef;

        return $this;
    }

    public function issetSummaryRef(int $index): bool
    {
        return isset($this->summaryRef[$index]);
    }

    public function unsetSummaryRef(int $index): void
    {
        unset($this->summaryRef[$index]);
    }

    /**
     * @return array<int, string>
     */
    public function getSummaryRef(): array
    {
        return $this->summaryRef;
    }

    /**
     * @param array<int, string> $summaryRef
     */
    public function setSummaryRef(array $summaryRef): self
    {
        $this->summaryRef = $summaryRef;

        return $this;
    }
}
