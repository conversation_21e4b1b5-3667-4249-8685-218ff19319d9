<?php

declare(strict_types=1);
namespace U2\Xbrl\At\Schema\UrnOecdTiesNationalcbcV2;

use U2\Xbrl\At\NationalData;
use U2\Xbrl\At\Schema\UrnOecdTiesCbcV2\CBCOECD;
use U2\Xbrl\XbrlObjectInterface;

class CbcNational implements XbrlObjectInterface
{
    public function __construct(private InfoDatenType $infoDaten, private CBCOECD $cBCOECD)
    {
    }

    public static function createFromNationalData(NationalData $nationalData): self
    {
        $reportingCompany = $nationalData->getCountryByCountryReport()->getReportingCompany();

        return new self(
            new InfoDatenType(null !== $reportingCompany ? $reportingCompany->getDefaultTaxNumber() ?? '' : ''),
            CBCOECD::createFromOecdData($nationalData)
        );
    }

    public function getSchemaFile(): string
    {
        return 'assets/xbrl/at/Cbc_National_v2.0.xsd';
    }

    public function getInfoDaten(): ?InfoDatenType
    {
        return $this->infoDaten;
    }

    public function setInfoDaten(InfoDatenType $infoDaten): self
    {
        $this->infoDaten = $infoDaten;

        return $this;
    }

    public function getCBCOECD(): ?CBCOECD
    {
        return $this->cBCOECD;
    }

    public function setCBCOECD(CBCOECD $cBCOECD): self
    {
        $this->cBCOECD = $cBCOECD;

        return $this;
    }
}
