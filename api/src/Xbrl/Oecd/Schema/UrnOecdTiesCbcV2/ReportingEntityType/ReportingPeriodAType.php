<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2\ReportingEntityType;

class ReportingPeriodAType
{
    public function __construct(private \DateTime $startDate, private \DateTime $endDate)
    {
    }

    public function getStartDate(): \DateTime
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTime $startDate): void
    {
        $this->startDate = $startDate;
    }

    public function getEndDate(): \DateTime
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTime $endDate): void
    {
        $this->endDate = $endDate;
    }
}
