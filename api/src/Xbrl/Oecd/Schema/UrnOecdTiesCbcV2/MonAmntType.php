<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2;

use U2\Exception\XbrlCurrencyException;
use U2\Xbrl\Oecd\Enum\ValidCurrencyCodes;

class MonAmntType
{
    private int $__value;

    private string $currCode;

    public function __construct(int $value, string $currencyCode)
    {
        if (!\in_array($currencyCode, (new ValidCurrencyCodes())->all(), true)) {
            throw new XbrlCurrencyException($currencyCode);
        }

        $this->value($value);

        $this->currCode = $currencyCode;
    }

    public function __toString(): string
    {
        return (string) $this->__value;
    }

    public function value(): int
    {
        if (0 < \count($args = \func_get_args())) {
            $this->__value = $args[0];
        }

        return $this->__value;
    }

    public function getCurrCode(): ?string
    {
        return $this->currCode;
    }

    public function setCurrCode(string $currCode): self
    {
        $this->currCode = $currCode;

        return $this;
    }
}
