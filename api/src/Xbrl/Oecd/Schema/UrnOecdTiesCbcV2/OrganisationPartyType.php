<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2;

use U2\Entity\LegalUnit;
use U2\Entity\PermanentEstablishment;
use U2\Entity\Unit;
use U2\Exception\XbrlException;
use U2\Xbrl\Oecd\CountryCodeExtractor;

class OrganisationPartyType
{
    /**
     * @var array<int, string>
     */
    private array $resCountryCode = [];

    private ?TINType $tIN = null;

    /**
     * @var array<int, OrganisationINType>
     */
    private array $iN = [];

    /**
     * @var array<int, NameOrganisationType>
     */
    private array $name = [];

    /**
     * @var array<int, AddressType>
     */
    private array $address = [];

    public static function createFromUnit(Unit $unit): self
    {
        $organisationPartyType = new self();

        $country = $unit->getCountry();
        if (null === $country) {
            throw new XbrlException(\sprintf('Unit %s must have country assigned', $unit->getRefIdAndName()));
        }
        $organisationPartyType->setResCountryCode([CountryCodeExtractor::extract($country)]);

        if (false === $unit instanceof LegalUnit && false === $unit instanceof PermanentEstablishment) {
            throw new XbrlException(\sprintf('Unit %s must have Tax Number assigned', $unit->getRefIdAndName()));
        }

        if (null === $unit->getDefaultTaxNumber()) {
            throw new XbrlException(\sprintf('Unit %s must have Tax Number assigned', $unit->getRefIdAndName()));
        }

        $organisationPartyType->setName(
            [
                new NameOrganisationType($unit->getLegalName()),
            ]
        );

        $unitCountry = $unit->getCountry();
        \assert(null !== $unitCountry);

        $organisationPartyType->setTIN(
            new TINType($unit->getDefaultTaxNumber(), CountryCodeExtractor::extract($unitCountry))
        );

        if (null !== $unit->getVatNumber()) {
            $organisationPartyType->addToIN(
                new OrganisationINType($unit->getVatNumber(), 'VAT-ID', CountryCodeExtractor::extract($unitCountry)),
            );
        }
        if (null !== $unit->getRegisterNumber()) {
            $organisationPartyType->addToIN(
                new OrganisationINType($unit->getRegisterNumber(), 'Registry number', CountryCodeExtractor::extract($unitCountry))
            );
        }

        if (null !== $unit->getPostalAddress()) {
            $organisationPartyType->addToAddress(
                AddressType::createFromAddress($unit->getPostalAddress())
            );
        }

        return $organisationPartyType;
    }

    public function addToResCountryCode(string $resCountryCode): self
    {
        $this->resCountryCode[] = $resCountryCode;

        return $this;
    }

    public function issetResCountryCode(int $index): bool
    {
        return isset($this->resCountryCode[$index]);
    }

    public function unsetResCountryCode(int $index): void
    {
        unset($this->resCountryCode[$index]);
    }

    /**
     * @return array<int, string>
     */
    public function getResCountryCode(): array
    {
        return $this->resCountryCode;
    }

    /**
     * @param array<int, string> $resCountryCode
     */
    public function setResCountryCode(array $resCountryCode): self
    {
        $this->resCountryCode = $resCountryCode;

        return $this;
    }

    public function getTIN(): ?TINType
    {
        return $this->tIN;
    }

    public function setTIN(TINType $tIN): self
    {
        $this->tIN = $tIN;

        return $this;
    }

    public function addToIN(OrganisationINType $iN): self
    {
        $this->iN[] = $iN;

        return $this;
    }

    public function issetIN(int $index): bool
    {
        return isset($this->iN[$index]);
    }

    public function unsetIN(int $index): void
    {
        unset($this->iN[$index]);
    }

    /**
     * @return array<int, OrganisationINType>
     */
    public function getIN(): array
    {
        return $this->iN;
    }

    /**
     * @param array<int, OrganisationINType> $iN
     */
    public function setIN(array $iN): self
    {
        $this->iN = $iN;

        return $this;
    }

    public function addToName(NameOrganisationType $name): self
    {
        $this->name[] = $name;

        return $this;
    }

    public function issetName(int $index): bool
    {
        return isset($this->name[$index]);
    }

    public function unsetName(int $index): void
    {
        unset($this->name[$index]);
    }

    /**
     * @return array<int, NameOrganisationType>
     */
    public function getName(): array
    {
        return $this->name;
    }

    /**
     * @param array<int, NameOrganisationType> $name
     */
    public function setName(array $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function addToAddress(AddressType $address): self
    {
        $this->address[] = $address;

        return $this;
    }

    public function issetAddress(int $index): bool
    {
        return isset($this->address[$index]);
    }

    public function unsetAddress(int $index): void
    {
        unset($this->address[$index]);
    }

    /**
     * @return array<int, AddressType>
     */
    public function getAddress(): array
    {
        return $this->address;
    }

    /**
     * @param array<int, AddressType> $address
     */
    public function setAddress(array $address): self
    {
        $this->address = $address;

        return $this;
    }
}
