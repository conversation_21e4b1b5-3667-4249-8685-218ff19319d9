<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd\Schema\UrnOecdTiesCbcV2;

use U2\Xbrl\Oecd\OecdData;
use U2\Xbrl\XbrlObjectInterface;

class CBCOECD implements XbrlObjectInterface
{
    private ?string $version = null;

    /**
     * @param array<int, CbcBodyType> $cbcBody
     */
    public function __construct(private MessageSpecType $messageSpec, private array $cbcBody)
    {
    }

    public static function createFromOecdData(OecdData $oecdData): self
    {
        return new self(
            MessageSpecType::createFromXbrlData($oecdData),
            [CbcBodyType::createFromXbrlData($oecdData)]
        );
    }

    public function getSchemaFile(): string
    {
        return 'assets/xbrl/oecd_next/CbcXML_v2.0.xsd';
    }

    public function getVersion(): ?string
    {
        return $this->version;
    }

    public function setVersion(string $version): self
    {
        $this->version = $version;

        return $this;
    }

    public function getMessageSpec(): ?MessageSpecType
    {
        return $this->messageSpec;
    }

    public function setMessageSpec(MessageSpecType $messageSpec): self
    {
        $this->messageSpec = $messageSpec;

        return $this;
    }

    public function addToCbcBody(CbcBodyType $cbcBody): self
    {
        $this->cbcBody[] = $cbcBody;

        return $this;
    }

    public function issetCbcBody(int $index): bool
    {
        return isset($this->cbcBody[$index]);
    }

    public function unsetCbcBody(int $index): void
    {
        unset($this->cbcBody[$index]);
    }

    /**
     * @return array<int, CbcBodyType>
     */
    public function getCbcBody(): array
    {
        return $this->cbcBody;
    }

    /**
     * @param array<int, CbcBodyType> $cbcBody
     */
    public function setCbcBody(array $cbcBody): self
    {
        $this->cbcBody = $cbcBody;

        return $this;
    }
}
