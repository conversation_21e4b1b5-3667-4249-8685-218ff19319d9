<?php

declare(strict_types=1);
namespace U2\Xbrl\Oecd;

use U2\Entity\Configuration\Field\BusinessActivity;
use U2\Exception\XbrlException;
use U2\Xbrl\Oecd\Enum\CbcBizActivityType;

class BusinessActivityCodeProvider
{
    /**
     * @throws XbrlException
     */
    public static function get(BusinessActivity $businessActivity): string
    {
        $businessActivityName = $businessActivity->getName();
        if (!\array_key_exists($businessActivityName, CbcBizActivityType::NAME_TO_CODE_MAP)) {
            throw new XbrlException(\sprintf('Business activity "%s" is not in the approved set provided by the XBRL standards', $businessActivityName));
        }

        return CbcBizActivityType::NAME_TO_CODE_MAP[$businessActivityName];
    }
}
