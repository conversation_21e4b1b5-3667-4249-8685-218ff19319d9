<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\UrnOecdTiesCbcV2;

class NameOrganisationType
{
    private string $__value;

    public function __construct(string $value)
    {
        $this->value($value);
    }

    public function __toString(): string
    {
        return $this->__value;
    }

    public function value(): string
    {
        if (0 < \count($args = \func_get_args())) {
            $this->__value = $args[0];
        }

        return $this->__value;
    }
}
