<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\UrnOecdTiesCbcV2;

use U2\Entity\Address;
use U2\Xbrl\De\Enum\OecdLegalAddressType;

class AddressType
{
    public function __construct(private readonly string $countryCode, private readonly string $addressFree, private readonly string $legalAddressType)
    {
    }

    public static function createFromAddress(Address $address): self
    {
        $addressCountry = $address->getCountry();
        \assert(null !== $addressCountry);

        return new self(
            $addressCountry->getIso3166code(),
            $address->getLine1() . \PHP_EOL .
            ($address->getLine2() ? $address->getLine2() . \PHP_EOL : '') .
            ($address->getLine3() ? $address->getLine3() . \PHP_EOL : '') .
            ($address->getPostcode() ? $address->getPostcode() . ' ' : '') . $address->getCity() . \PHP_EOL .
            ($address->getState() ? $address->getState() . \PHP_EOL : '') .
            $addressCountry->getNameShort(),
            OecdLegalAddressType::BUSINESS
        );
    }

    public function getLegalAddressType(): string
    {
        return $this->legalAddressType;
    }

    public function getCountryCode(): string
    {
        return $this->countryCode;
    }

    public function getAddressFree(): string
    {
        return $this->addressFree;
    }
}
