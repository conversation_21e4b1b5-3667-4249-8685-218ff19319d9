<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\WwwItzbundDeElanCbCR01\CbCR;

use U2\Entity\LegalUnit;
use U2\Exception\XbrlException;
use U2\Xbrl\XbrlDataInterface;

class CbCRHeaderAType
{
    public function __construct(
        private string $authSteuernummerMNU,
        private string $nameMNU,
        private string $nameELANNutzer,
    ) {
    }

    public static function fromOecdData(XbrlDataInterface $oecdData): self
    {
        $countryByCountryReport = $oecdData->getCountryByCountryReport();

        $reportingCompany = $countryByCountryReport->getReportingCompany();
        if (!$reportingCompany instanceof LegalUnit) {
            throw new XbrlException('Document Reporting Company must be set.');
        }

        $headquarters = $countryByCountryReport->getHeadquarters();

        if (null !== $headquarters) {
            $headquarterCountry = $headquarters->getCountry();
            \assert(null !== $headquarterCountry);
            if ($headquarters === $reportingCompany || 'DE' === $headquarterCountry->getIso3166code()) {
                return new self(
                    $headquarters->getDefaultTaxNumber() ?? '',
                    $headquarters->getLegalName(),
                    $countryByCountryReport->getServiceProvider() ?? $reportingCompany->getLegalName()
                );
            }
        }

        return new self(
            $reportingCompany->getDefaultTaxNumber() ?? '',
            $reportingCompany->getLegalName(),
            $countryByCountryReport->getServiceProvider() ?? $reportingCompany->getLegalName()
        );
    }

    public function getAuthSteuernummerMNU(): string
    {
        return $this->authSteuernummerMNU;
    }

    public function getNameMNU(): string
    {
        return $this->nameMNU;
    }

    public function getNameELANNutzer(): string
    {
        return $this->nameELANNutzer;
    }

    public function setAuthSteuernummerMNU(string $authSteuernummerMNU): void
    {
        $this->authSteuernummerMNU = $authSteuernummerMNU;
    }

    public function setNameMNU(string $nameMNU): void
    {
        $this->nameMNU = $nameMNU;
    }

    public function setNameELANNutzer(string $nameELANNutzer): void
    {
        $this->nameELANNutzer = $nameELANNutzer;
    }
}
