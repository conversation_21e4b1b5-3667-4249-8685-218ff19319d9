<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\UrnOecdTiesCbcstmV2;

class OriginalMessageType
{
    private ?string $originalMessageRefID = null;

    private ?FileMetaDataType $fileMetaData = null;

    public function getOriginalMessageRefID(): ?string
    {
        return $this->originalMessageRefID;
    }

    public function setOriginalMessageRefID(string $originalMessageRefID): self
    {
        $this->originalMessageRefID = $originalMessageRefID;

        return $this;
    }

    public function getFileMetaData(): ?FileMetaDataType
    {
        return $this->fileMetaData;
    }

    public function setFileMetaData(FileMetaDataType $fileMetaData): self
    {
        $this->fileMetaData = $fileMetaData;

        return $this;
    }
}
