<?php

declare(strict_types=1);
namespace U2\Xbrl\De\Schema\WwwItzbundDeElan01;

class ELMAProtokoll
{
    private ?string $dateiname = null;

    private ?string $authSteuernummer = null;

    private ?string $accountID = null;

    private ?\DateTime $verarbeitungsDatum = null;

    private ?\DateTime $verarbeitungsZeit = null;

    private ?string $status = null;

    private ?string $statusText = null;

    private ?string $statusDetail = null;

    private ?string $kundeneigeneID = null;

    private ?string $uUID = null;

    public function getDateiname(): ?string
    {
        return $this->dateiname;
    }

    public function setDateiname(?string $dateiname): self
    {
        $this->dateiname = $dateiname;

        return $this;
    }

    public function getAuthSteuernummer(): ?string
    {
        return $this->authSteuernummer;
    }

    public function setAuthSteuernummer(string $authSteuernummer): self
    {
        $this->authSteuernummer = $authSteuernummer;

        return $this;
    }

    public function getAccountID(): ?string
    {
        return $this->accountID;
    }

    public function setAccountID(?string $accountID): self
    {
        $this->accountID = $accountID;

        return $this;
    }

    public function getVerarbeitungsDatum(): ?\DateTime
    {
        return $this->verarbeitungsDatum;
    }

    public function setVerarbeitungsDatum(\DateTime $verarbeitungsDatum): self
    {
        $this->verarbeitungsDatum = $verarbeitungsDatum;

        return $this;
    }

    public function getVerarbeitungsZeit(): ?\DateTime
    {
        return $this->verarbeitungsZeit;
    }

    public function setVerarbeitungsZeit(\DateTime $verarbeitungsZeit): self
    {
        $this->verarbeitungsZeit = $verarbeitungsZeit;

        return $this;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function setStatus(string $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function getStatusText(): ?string
    {
        return $this->statusText;
    }

    public function setStatusText(string $statusText): static
    {
        $this->statusText = $statusText;

        return $this;
    }

    public function getStatusDetail(): ?string
    {
        return $this->statusDetail;
    }

    public function setStatusDetail(string $statusDetail): static
    {
        $this->statusDetail = $statusDetail;

        return $this;
    }

    public function getKundeneigeneID(): ?string
    {
        return $this->kundeneigeneID;
    }

    public function setKundeneigeneID(string $kundeneigeneID): self
    {
        $this->kundeneigeneID = $kundeneigeneID;

        return $this;
    }

    public function getUUID(): ?string
    {
        return $this->uUID;
    }

    public function setUUID(string $uUID): self
    {
        $this->uUID = $uUID;

        return $this;
    }
}
