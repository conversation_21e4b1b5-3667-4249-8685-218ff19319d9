<?php

declare(strict_types=1);
namespace U2\Document\Structure\SectionListManipulation;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Document\Structure\HierarchicalBrowser;
use U2\Entity\DocumentSection;
use U2\Entity\StructuredDocumentInterface;

class InitialAddition extends AbstractManipulation
{
    protected ?StructuredDocumentInterface $document = null;

    public function __construct(
        DocumentSection $sectionToAdd,
        StructuredDocumentInterface $document,
        HierarchicalBrowser $hierarchicalBrowser,
    ) {
        $sectionToAdd->setDocument($document);

        parent::__construct($sectionToAdd, $hierarchicalBrowser);
    }

    #[Assert\Callback]
    public function validateDocumentHasNoSections(?ExecutionContextInterface $context = null): bool
    {
        $sectionCount = \count($this->document->getSections());
        if ($sectionCount > 0) {
            if (null !== $context) {
                $context
                    ->buildViolation('u2_structureddocument.create_initial_section.document_already_has_sections')
                    ->atPath('document')
                    ->addViolation();
            }

            return false;
        }

        return true;
    }

    protected function doManipulate(Collection $sections): Collection
    {
        $this->sectionToManipulate->setLevel(1);

        $newSections = new ArrayCollection([$this->sectionToManipulate]);

        $this->updateOrderPosition($newSections);

        return $newSections;
    }
}
