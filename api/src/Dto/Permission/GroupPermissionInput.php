<?php

declare(strict_types=1);
namespace U2\Dto\Permission;

use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\UserGroup;

class GroupPermissionInput
{
    public function __construct(
        #[Groups(groups: ['group-permission:write'])]
        public UserGroup $group,

        #[Assert\Choice(choices: [
            MaskBuilder::MASK_VIEW,
            MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT,
            MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE,
            MaskBuilder::MASK_VIEW | MaskBuilder::MASK_EDIT | MaskBuilder::MASK_DELETE | MaskBuilder::MASK_OWNER,
        ])]
        #[Groups(groups: ['group-permission:write'])]
        public int $mask,
    ) {
    }
}
