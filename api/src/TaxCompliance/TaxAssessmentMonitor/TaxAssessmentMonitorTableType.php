<?php

declare(strict_types=1);
namespace U2\TaxCompliance\TaxAssessmentMonitor;

use function Symfony\Component\Translation\t;

use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\TaxCompliance\AbstractTaskTypeTableType;

class TaxAssessmentMonitorTableType extends AbstractTaskTypeTableType
{
    public function getName(): string
    {
        return 'u2_tcm_tax_assessment_monitor_table_type';
    }

    public function buildTable(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        parent::buildTable($columnDefinitionCollection);

        $columnDefinitionCollection
            ->addAfter(
                'UnitCountry',
                'Name',
                null,
                [
                    'label' => t('u2.name'),
                    'name' => t('u2.name'),
                    'filterable' => true,
                ]
            )
            ->addAfter(
                'Name',
                'TaxYear',
                null,
                [
                    'label' => t('u2_tcm.taxation_year'),
                    'name' => t('u2_tcm.taxation_year'),
                    'filterable' => true,
                ]
            )
            ->addAfter(
                'TaxYear',
                'TaxMonth',
                null,
                [
                    'sortable' => true,
                    'filterable' => true,
                    'label' => t('u2_tcm.tax_month'),
                    'name' => t('u2_tcm.taxation_month'),
                ]
            )
            ->addAfter(
                'UnitCountry',
                'TaxType',
                null,
                [
                    'label' => t('u2_tcm.tax_type'),
                    'name' => t('u2_tcm.tax_type'),
                    'filterable' => true,
                ]
            )
            ->addAfter(
                'TaxType',
                'AssessmentType',
                null,
                [
                    'label' => t('u2_tcm.type_of_assessment'),
                    'name' => t('u2_tcm.type_of_assessment'),
                    'filterable' => true,
                ]
            )
            ->addAfter(
                'TaxMonth',
                'DateReceived',
                'date',
                [
                    'label' => t('u2_tcm.date_received'),
                    'name' => t('u2_tcm.date_received'),
                    'filterable' => true,
                    'selectedByDefault' => false,
                ]
            )
            ->addAfter(
                'DateReceived',
                'DocumentDate',
                'date',
                [
                    'label' => t('u2_tcm.document_date'),
                    'name' => t('u2_tcm.document_date'),
                    'filterable' => true,
                ]
            );
    }

    public static function getEntityClass(): string
    {
        return TaxAssessmentMonitor::class;
    }
}
