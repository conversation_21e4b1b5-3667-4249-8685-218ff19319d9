<?php

declare(strict_types=1);
namespace U2\TaxCompliance\TaxAuthorityAuditObjection;

use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Task\TaskType\TaxAuthorityAuditObjection;
use U2\Task\DataSource\AbstractTaskTypeDataSourceConfiguration;
use U2\Task\DataSource\UnitCurrencyAsCurrencyFieldAdder;

class TaxAuthorityAuditObjectionDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);
        UnitCurrencyAsCurrencyFieldAdder::add($builder);

        $builder
            ->addField(
                'Name',
                'string',
                'name'
            )
            ->addField(
                'TaxYear',
                'string',
                'taxYear'
            )
            ->addField(
                'TaxMonth',
                'number',
                'taxMonth'
            )
            ->addField(
                'TaxType',
                'string',
                'taxType.name',
                [
                    'choices' => [
                        'repository' => TaxType::class,
                        'field' => 'name',
                    ],
                ]
            )
            ->addField(
                'DateReceived',
                'date',
                'dateReceived'
            );
    }

    public static function getEntityClass(): string
    {
        return TaxAuthorityAuditObjection::class;
    }
}
