<?php

declare(strict_types=1);
namespace U2\TaxCompliance\TaxAuditRisk;

use function Symfony\Component\Translation\t;

use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\Task\TableType\PeriodColumnAdder;
use U2\TaxCompliance\AbstractTaskTypeTableType;

class TaxAuditRiskTableType extends AbstractTaskTypeTableType
{
    public function getName(): string
    {
        return 'u2_tam_tax_audit_risk';
    }

    public function buildTable(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        parent::buildTable($columnDefinitionCollection);

        PeriodColumnAdder::add($columnDefinitionCollection);

        $columnDefinitionCollection
            ->addAfter(
                'UnitCountry',
                'TaxType',
                null,
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.tax_type'),
                    'name' => t('u2_tam.tax_type'),
                ]
            )
            ->addAfter(
                'TaxType',
                'RiskType',
                null,
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.type_of_risk'),
                    'name' => t('u2_tam.type_of_risk'),
                ]
            )
            ->addAfter(
                'RiskType',
                'Permanent',
                'boolean',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.permanent'),
                    'name' => t('u2_tam.permanent'),
                ]
            )
            ->addAfter(
                'Permanent',
                'TaxYear',
                null,
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.taxation_year'),
                    'name' => t('u2_tam.taxation_year'),
                ]
            )
            ->addAfter(
                'TaxYear',
                'GrossRiskBoy',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.gross_risk_boy'),
                    'name' => t('u2_tam.gross_risk_boy'),
                ]
            )
            ->addAfter(
                'GrossRiskBoy',
                'IdentifiedByTaxAdmin',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.identified_by_tax_admin.short'),
                    'name' => t('u2_tam.identified_by_tax_admin'),
                ]
            )
            ->addAfter(
                'IdentifiedByTaxAdmin',
                'Additions',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.additions'),
                    'name' => t('u2_tam.additions'),
                ]
            )
            ->addAfter(
                'Additions',
                'Less',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.less'),
                    'name' => t('u2_tam.less'),
                ]
            )
            ->addAfter(
                'Less',
                'GrossRiskEoy',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.gross_risk_eoy'),
                    'name' => t('u2_tam.gross_risk_eoy'),
                ]
            )
            ->addAfter(
                'GrossRiskEoy',
                'RiskProbability',
                'percentage',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.risk_probability.short'),
                    'name' => t('u2_tam.risk_probability'),
                    'unit' => '%',
                ]
            )
            ->addAfter(
                'RiskProbability',
                'AccruedBoy',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.accrued_boy'),
                    'name' => t('u2_tam.accrued_boy'),
                ]
            )
            ->addAfter(
                'AccruedBoy',
                'PlEffectCy',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.p_l_effect_cy'),
                    'name' => t('u2_tam.p_l_effect_cy'),
                ]
            )
            ->addAfter(
                'PlEffectCy',
                'AccruedEoy',
                'money',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => t('u2_tam.accrued_eoy'),
                    'name' => t('u2_tam.accrued_eoy'),
                ]
            )
            ->addAfter(
                'AccruedEoy',
                'Currency',
                'currency',
                [
                    'filterable' => true,
                    'sortable' => true,
                    'label' => false,
                    'name' => t('u2_tam.currency'),
                ]
            );
    }

    public static function getEntityClass(): string
    {
        return TaxAuditRisk::class;
    }
}
