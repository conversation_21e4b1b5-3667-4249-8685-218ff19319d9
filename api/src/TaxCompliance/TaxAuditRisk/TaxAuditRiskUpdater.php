<?php

declare(strict_types=1);
namespace U2\TaxCompliance\TaxAuditRisk;

use U2\Entity\Task\TaskType\TaxAuditRisk;

class TaxAuditRiskUpdater
{
    private TaxAuditRiskCalculator $calculator;

    public function __construct(TaxAuditRiskCalculator $taxAuditRiskCalculator)
    {
        $this->calculator = $taxAuditRiskCalculator;
    }

    public function update(TaxAuditRisk $taxAuditRisk): void
    {
        $this->updateAccruedEoy($taxAuditRisk);
        $this->updateGrossRiskEoy($taxAuditRisk);
    }

    private function updateAccruedEoy(TaxAuditRisk $taxAuditRisk): void
    {
        $accruedEoy = $this->calculator->calculateAccruedEoy($taxAuditRisk);
        $taxAuditRisk->setAccruedEoy((int) $accruedEoy);
    }

    private function updateGrossRiskEoy(TaxAuditRisk $taxAuditRisk): void
    {
        $grossRiskEoy = $this->calculator->calculateGrossRiskEoy($taxAuditRisk);
        $taxAuditRisk->setGrossRiskEoy((int) $grossRiskEoy);
    }
}
