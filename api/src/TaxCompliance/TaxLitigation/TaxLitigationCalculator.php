<?php

declare(strict_types=1);
namespace U2\TaxCompliance\TaxLitigation;

use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Util\BcNumberManipulator;

class TaxLitigationCalculator
{
    public function calculateAccruedEoy(TaxLitigation $taxLitigation): string
    {
        $value = bcadd(
            (string) $taxLitigation->getAccruedBoy(),
            (string) ($taxLitigation->getPlEffectCy() ?? '0'),
            10
        );

        return BcNumberManipulator::round($value, 0);
    }
}
