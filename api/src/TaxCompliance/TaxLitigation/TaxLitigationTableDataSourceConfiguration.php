<?php

declare(strict_types=1);
namespace U2\TaxCompliance\TaxLitigation;

use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Entity\Configuration\Field\RiskType;
use U2\Entity\Configuration\Field\Specification;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Task\DataSource\AbstractTaskTypeDataSourceConfiguration;
use U2\Task\DataSource\PeriodFieldAdder;
use U2\Task\DataSource\UnitCurrencyAsCurrencyFieldAdder;

class TaxLitigationTableDataSourceConfiguration extends AbstractTaskTypeDataSourceConfiguration
{
    public function buildDataSource(DataSourceBuilder $builder): void
    {
        parent::buildDataSource($builder);

        PeriodFieldAdder::add($builder);
        UnitCurrencyAsCurrencyFieldAdder::add($builder);

        $builder
            ->addField(
                'TaxType',
                'string',
                'taxType.name',
                [
                    'choices' => [
                        'repository' => TaxType::class,
                        'field' => 'name',
                    ],
                ]
            )
            ->addField(
                'Specification',
                'string',
                'specification.name',
                [
                    'choices' => [
                        'repository' => Specification::class,
                        'field' => 'name',
                    ],
                ]
            )
            ->addField(
                'RiskType',
                'string',
                'riskType.name',
                [
                    'choices' => [
                        'repository' => RiskType::class,
                        'field' => 'name',
                    ],
                ]
            )
            ->addField(
                'Permanent',
                'boolean',
                'permanent'
            )
            ->addField(
                'TaxYear',
                'string',
                'taxYear'
            )
            ->addField(
                'Amount',
                'number',
                'amount'
            )
            ->addField(
                'RiskProbability',
                'percent',
                'riskProbability'
            )
            ->addField(
                'AccruedBoy',
                'number',
                'accruedBoy'
            )
            ->addField(
                'PlEffectCy',
                'number',
                'plEffectCy'
            )
            ->addField(
                'AccruedEoy',
                'number',
                'accruedEoy'
            );
    }

    public static function getEntityClass(): string
    {
        return TaxLitigation::class;
    }
}
