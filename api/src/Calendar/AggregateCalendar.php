<?php

declare(strict_types=1);
namespace U2\Calendar;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\Task\AbstractTaskCalendar;
use U2\User\CurrentUserProvider;
use U2\Util\Collection;

class AggregateCalendar
{
    /**
     * @var Calendar[]
     */
    protected array $calendars = [];

    public function __construct(protected AuthorizationCheckerInterface $authorizationChecker, private readonly CurrentUserProvider $currentUserProvider)
    {
    }

    /**
     * @return Calendar[]
     */
    public function getCalendars(): array
    {
        return $this->getAuthorizedCalendarsForRead();
    }

    public function addCalendar(Calendar $calendar): void
    {
        $this->calendars[] = $calendar;
    }

    /**
     * @return Collection<string, CalendarEntry[]>
     */
    public function getEntriesByDay(\DateTime $startDate, \DateTime $endDate): Collection
    {
        // Re-calculate the dates so they span from the beginning of the first day until the end of the last
        $startDate = new \DateTime($startDate->format('Y-m-d 00:00:00'));
        $endDate = new \DateTime($endDate->format('Y-m-d 23:59:59'));

        return new Collection($this->buildEntriesIndexByDay($startDate, $endDate));
    }

    /**
     * @return AbstractTaskCalendar[]
     */
    public function getCreatableCalendars(): array
    {
        $creatableCalendars = [];

        /** @var AbstractTaskCalendar $calendar */
        foreach ($this->getAuthorizedCalendarsForWrite() as $calendar) {
            if ($calendar->getRouteNew()) {
                $creatableCalendars[] = $calendar;
            }
        }

        return $creatableCalendars;
    }

    public function sortCalendarsByName(): void
    {
        // TODO: "@" in front of usort is a temporary fix for "usort(): Array was modified by the user comparison function"
        // Getting a name of calendar entry initializes translator object inside, and that's why it's recognized as modified.
        @usort(
            $this->calendars,
            static fn (Calendar $first, Calendar $second): int => strcasecmp($first->getName(), $second->getName())
        );
    }

    /**
     * @return Calendar[]
     */
    private function getAuthorizedCalendarsForRead(): array
    {
        return $this->getAuthorizedCalendars(
            fn (Calendar $calendar): string => $calendar->getAuthorizationNameToRead()
        );
    }

    /**
     * @return Calendar[]
     */
    private function getAuthorizedCalendarsForWrite(): array
    {
        return $this->getAuthorizedCalendars(
            fn (Calendar $calendar): string => $calendar->getAuthorizationNameToWrite()
        );
    }

    /**
     * @return Calendar[]
     */
    private function getAuthorizedCalendars(callable $authorizationNameGetter): array
    {
        $creatableCalendars = [];
        foreach ($this->calendars as $calendar) {
            if ($this->authorizationChecker->isGranted(\call_user_func($authorizationNameGetter, $calendar))) {
                $creatableCalendars[] = $calendar;
            }
        }

        return $creatableCalendars;
    }

    /**
     * @return array<string, CalendarEntry[]>
     */
    private function buildEntriesIndexByDay(\DateTime $startDate, \DateTime $endDate): array
    {
        $endDateNextDay = clone $endDate;
        $endDateNextDay->add(new \DateInterval('PT1S'));

        $entriesByDay = [];

        /** @var \DateTime $date */
        foreach (new \DatePeriod($startDate, new \DateInterval('P1D'), $endDateNextDay) as $date) {
            $entriesByDay[$date->format('Y-m-d')] = [];
        }

        return $this->addEntriesFromAuthorizedCalendars($entriesByDay, $startDate, $endDate);
    }

    /**
     * @param array<string, CalendarEntry[]> $entriesByDay
     *
     * @return array<string, CalendarEntry[]>
     */
    private function addEntriesFromAuthorizedCalendars(array $entriesByDay, \DateTime $startDate, \DateTime $endDate): array
    {
        $user = $this->currentUserProvider->get();

        /** @var Calendar $calendar */
        foreach ($this->getAuthorizedCalendarsForRead() as $calendar) {
            $entriesByDay = $this->addEntriesByDay(
                $entriesByDay,
                $calendar->getEntries($startDate, $endDate, $user)
            );
        }

        return $entriesByDay;
    }

    /**
     * @param CalendarEntry[]                $entries
     * @param array<string, CalendarEntry[]> $entriesByDay
     *
     * @return array<string, CalendarEntry[]>
     */
    private function addEntriesByDay(array $entriesByDay, array $entries): array
    {
        foreach ($entries as $entry) {
            $endDateNextDay = clone $entry->getEndDate();
            $endDateNextDay->add(new \DateInterval('P1D'));

            /** @var \DateTime $date */
            foreach (new \DatePeriod($entry->getStartDate(), new \DateInterval('P1D'), $endDateNextDay) as $date) {
                if (isset($entriesByDay[$date->format('Y-m-d')])) {
                    $entriesByDay[$date->format('Y-m-d')][] = $entry;
                }
            }
        }

        return $entriesByDay;
    }
}
