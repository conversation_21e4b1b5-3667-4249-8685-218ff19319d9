<?php

declare(strict_types=1);
namespace U2\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\AuditLog\LogValue;
use U2\Entity\Interfaces\Entity;
use U2\Repository\AddressRepository;

#[ORM\Entity(repositoryClass: AddressRepository::class)]
#[ORM\Table(name: 'address')]
class Address implements Entity, LogValue
{
    public function __construct(
        #[ORM\Id]
        #[ORM\Column(type: Types::INTEGER)]
        #[ORM\GeneratedValue(strategy: 'AUTO')]
        #[Groups(groups: ['unit:read', 'user-configuration-data:read', 'user-group-admin:read', 'current-user:read'])]
        private ?int $id = null,

        #[Assert\NotBlank]
        #[Assert\Length(max: 100)]
        #[ORM\Column(type: Types::STRING, length: 100)]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?string $line1 = null,

        #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?string $line2 = null,

        #[ORM\Column(type: Types::STRING, length: 100, nullable: true)]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?string $line3 = null,

        #[Assert\NotBlank]
        #[Assert\Length(max: 50)]
        #[ORM\Column(type: Types::STRING, length: 50, nullable: false)]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?string $city = null,

        #[ORM\Column(type: Types::STRING, length: 50, nullable: true)]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?string $state = null,

        #[ORM\Column(type: Types::STRING, length: 20, nullable: true)]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?string $postcode = null,

        #[Assert\NotNull]
        #[ORM\ManyToOne(targetEntity: Country::class)]
        #[ORM\JoinColumn(onDelete: 'SET NULL')]
        #[Groups(groups: ['unit:read', 'unit:write', 'user-configuration-data:read', 'user-configuration-data:write', 'user-group-admin:read', 'user-group-admin:write', 'current-user:read', 'current-user:write'])]
        private ?Country $country = null,
    ) {
    }

    public function getAuditLogValue(): string
    {
        $addressFreeText =
            (null !== $this->line1 ? $this->line1 . \PHP_EOL : '') .
            (null !== $this->line2 ? $this->line2 . \PHP_EOL : '') .
            (null !== $this->line3 ? $this->line3 . \PHP_EOL : '') .
            (null !== $this->postcode || null !== $this->city ? (null !== $this->postcode ? $this->postcode . ' ' : '') . $this->city . \PHP_EOL : '') .
            (null !== $this->state ? $this->state . \PHP_EOL : '') .
            (null !== $this->country ? $this->country->getNameShort() : '');

        return '' !== $addressFreeText ? $addressFreeText : 'Empty Address';
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLine1(): ?string
    {
        return $this->line1;
    }

    public function setLine1(?string $line1): void
    {
        $this->line1 = $line1;
    }

    public function getLine2(): ?string
    {
        return $this->line2;
    }

    public function setLine2(?string $line2): void
    {
        $this->line2 = $line2;
    }

    public function getLine3(): ?string
    {
        return $this->line3;
    }

    public function setLine3(?string $line3): void
    {
        $this->line3 = $line3;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function setCity(?string $city): void
    {
        $this->city = $city;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(?string $state): void
    {
        $this->state = $state;
    }

    public function getPostcode(): ?string
    {
        return $this->postcode;
    }

    public function setPostcode(?string $postcode): void
    {
        $this->postcode = $postcode;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): void
    {
        $this->country = $country;
    }

    public function isEmpty(): bool
    {
        return null === $this->city
            && null === $this->line1
            && null === $this->line2
            && null === $this->line3
            && null === $this->country
            && null === $this->postcode
            && null === $this->state;
    }

    public function updateFrom(self $address): void
    {
        $this->setLine1($address->getLine1());
        $this->setLine2($address->getLine2());
        $this->setLine3($address->getLine3());
        $this->setPostcode($address->getPostcode());
        $this->setCountry($address->getCountry());
        $this->setCity($address->getCity());
        $this->setState($address->getState());
    }
}
