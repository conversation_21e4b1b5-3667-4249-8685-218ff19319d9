<?php

declare(strict_types=1);
namespace U2\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\AuditLog\LogValue;
use U2\Entity\Interfaces\Enableable;

#[UniqueEntity('name')]
#[ORM\MappedSuperclass]
abstract class UserConfigurableDataType implements Enableable, LogValue
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['user-configuration-data:read'])]
    protected ?int $id = null;

    #[Assert\Length(max: 100)]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::STRING, unique: true)]
    #[Groups(groups: ['user-configuration-data:read', 'user-configuration-data:write'])]
    protected string $name;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN)]
    #[Groups(groups: ['user-configuration-data:read', 'user-configuration-data:write'])]
    protected bool $enabled = true;

    public function getAuditLogValue(): string
    {
        return $this->name;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(int $id): void
    {
        $this->id = $id;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): void
    {
        $this->enabled = $enabled;
    }
}
