<?php

declare(strict_types=1);
namespace U2\Entity\Interfaces;

use Doctrine\Common\Collections\Collection;
use U2\Entity\File;

interface FileAttachable extends Entity
{
    public function addFile(File $file): void;

    public function removeFile(File $file): void;

    /**
     * @return Collection<int, File>
     */
    public function getFiles(): Collection;

    public function getDisplayName(): string;
}
