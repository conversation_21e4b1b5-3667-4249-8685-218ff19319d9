<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Doctrine\Orm\Filter\SearchFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use ApiPlatform\OpenApi\Model\Operation;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Api\Filter\SearchFilter as U2SearchFilter;
use U2\Api\Processor\FileInputProcessor;
use U2\Api\Processor\FileRequestPermissionProcessor;
use U2\Dto\File\FileInput;
use U2\Dto\File\FileRequestPermission;
use U2\Entity\Interfaces\Entity;
use U2\Repository\FileRepository;
use U2\Security\Permissions\Assignable\PermissionableEntity;
use U2\Security\Voter\VoterAttributes;
use U2\Validator as U2Assert;

#[ORM\Entity(repositoryClass: FileRepository::class)]
#[ORM\Table(name: 'file')]
#[ApiFilter(
    filterClass: OrderFilter::class,
    properties: ['id', 'name', 'createdBy.username', 'updatedBy.username', 'createdAt', 'updatedAt', 'description'],
    arguments: ['orderParameterName' => 'sort']
)]
#[ApiFilter(
    filterClass: U2SearchFilter::class,
    properties: ['name' => 'ipartial', 'description' => 'ipartial', 'createdBy.username' => 'ipartial', 'updatedBy.username' => 'ipartial', 'types.name' => 'ipartial']
)]
#[ApiFilter(
    filterClass: SearchFilter::class,
    properties: ['accessType' => 'exact']
)]
#[ApiResource(
    operations: [
        new Get(),
        new Patch(
            denormalizationContext: ['groups' => ['file:write', 'user-permission:write', 'group-permission:write']],
            security: 'is_granted("' . VoterAttributes::write . '", object)',
            input: FileInput::class,
            processor: FileInputProcessor::class,
        ),
        new Delete(security: 'is_granted("' . VoterAttributes::delete . '", object)'),
        new GetCollection(),
        new Post(
            inputFormats: ['multipart' => ['multipart/form-data']],
            denormalizationContext: ['groups' => ['file:write', 'file:create', 'user-permission:write', 'group-permission:write']],
            input: FileInput::class,
            processor: FileInputProcessor::class,
        ),
        new Post(
            uriTemplate: '/files/{id}/request-permissions',
            openapi: new Operation(description: 'Requests permissions to a file'),
            input: FileRequestPermission::class,
            output: false,
            processor: FileRequestPermissionProcessor::class,
        ),
    ],
    normalizationContext: ['groups' => ['file:read']],
)]
class File implements Entity, PermissionableEntity
{
    public const string PROTECTED_ACCESS = 'protected';
    public const string SMART_ACCESS = 'smart';
    public const string PUBLIC_ACCESS = 'public';

    #[ORM\Column(type: Types::STRING, length: 255)]
    #[Groups(groups: ['file:read'])]
    private ?string $name = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    #[Groups(groups: ['file:read'])]
    private ?string $description = null;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: false)]
    private ?string $path = null;

    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['file:read'])]
    private ?int $id = null;

    /**
     * @var Collection<int, FileType>
     */
    #[Assert\Valid]
    #[ORM\ManyToMany(targetEntity: FileType::class)]
    #[Groups(groups: ['file:read'])]
    private Collection $types;

    #[Assert\Length(max: 10)]
    #[Assert\Choice(choices: [self::PUBLIC_ACCESS, self::SMART_ACCESS, self::PROTECTED_ACCESS])]
    #[Assert\Type(type: 'string')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::STRING, length: 10, nullable: false)]
    #[Groups(groups: ['file:read'])]
    private string $accessType;

    #[ORM\Column(type: Types::BIGINT, nullable: false)]
    private int $fileSize;

    #[ORM\Column(type: Types::STRING, nullable: true)]
    private ?string $mimeType;

    #[Assert\Valid]
    #[ORM\OneToOne(
        targetEntity: CollectivePermission::class,
        cascade: ['persist'],
        orphanRemoval: true
    )]
    #[ORM\JoinColumn(name: 'collective_permission_id', nullable: true, onDelete: 'SET NULL')]
    #[U2Assert\OwnerUserPermission]
    private CollectivePermission $permissions;

    private ?UploadedFile $uploadedFile = null;

    #[Gedmo\Blameable(on: 'create')]
    #[Groups(groups: ['file:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $createdBy;

    #[Gedmo\Blameable(on: 'update')]
    #[Groups(groups: ['file:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $updatedBy = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(groups: ['file:read'])]
    private ?\DateTime $createdAt = null;

    #[Gedmo\Timestampable(on: 'update')]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Groups(groups: ['file:read'])]
    private ?\DateTime $updatedAt = null;

    public function __construct(?string $name = null)
    {
        $this->name = $name;
        $this->types = new ArrayCollection();
        $this->permissions = new CollectivePermission();
        $this->accessType = self::SMART_ACCESS;
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public static function createFromFileInput(FileInput $fileInput): self
    {
        $file = new self();

        $file->setAccessType($fileInput->accessType);
        $file->setDescription($fileInput->description);
        $file->setTypes(new ArrayCollection($fileInput->types));
        $file->getPermissions()->setUserPermissions(new ArrayCollection($fileInput->userPermissions));
        $file->getPermissions()->setGroupPermissions(new ArrayCollection($fileInput->groupPermissions));
        \assert($fileInput->uploadedFile instanceof UploadedFile);
        $file->setUploadedFile($fileInput->uploadedFile);

        return $file;
    }

    public function getPermissions(): CollectivePermission
    {
        return $this->permissions;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setFileSize(int $fileSize): void
    {
        $this->fileSize = $fileSize;
    }

    public function getFileSize(): int
    {
        return $this->fileSize;
    }

    public function setMimeType(?string $mimeType): void
    {
        $this->mimeType = $mimeType;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): void
    {
        $this->name = $name;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getPath(): string
    {
        $filePath = $this->path;
        \assert(null !== $filePath);

        return $filePath;
    }

    public function setPath(string $path): void
    {
        $this->path = $path;
    }

    public function addType(FileType $type): void
    {
        if (!$this->types->contains($type)) {
            $this->types->add($type);
        }
    }

    public function removeType(FileType $type): void
    {
        $this->types->removeElement($type);
    }

    /**
     * @return Collection<int, FileType>
     */
    public function getTypes(): Collection
    {
        return $this->types;
    }

    public function getUploadedFile(): ?UploadedFile
    {
        return $this->uploadedFile;
    }

    public function setUploadedFile(UploadedFile $uploadedFile): void
    {
        $this->uploadedFile = $uploadedFile;
    }

    public function getAccessType(): string
    {
        return $this->accessType;
    }

    public function setAccessType(string $accessType): void
    {
        $this->accessType = $accessType;
    }

    /**
     * @return array<int,int>
     */
    public function getMasks(): array
    {
        return [
            MaskBuilder::MASK_VIEW,
            MaskBuilder::MASK_EDIT,
            MaskBuilder::MASK_DELETE,
            MaskBuilder::MASK_OWNER,
        ];
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    /**
     * @param Collection<int, FileType> $types
     */
    public function setTypes(Collection $types): void
    {
        $this->types = $types;
    }

    /**
     * @return array<int, UserPermission>
     */
    #[Groups(groups: ['file:read'])]
    public function getUserPermissions(): array
    {
        return $this->permissions->getUserPermissions()->getValues();
    }

    /**
     * @return array<int, GroupPermission>
     */
    #[Groups(groups: ['file:read'])]
    public function getGroupPermissions(): array
    {
        return $this->permissions->getGroupPermissions()->getValues();
    }

    public function isPublic(): bool
    {
        return self::PUBLIC_ACCESS === $this->accessType;
    }
}
