<?php

declare(strict_types=1);
namespace U2\Entity\Configuration\Field;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Interfaces\Enableable;
use U2\Entity\Interfaces\Entity;

#[UniqueEntity('name')]
#[ORM\MappedSuperclass]
abstract class Choice implements Entity, Enableable
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    protected ?int $id = null;

    #[Assert\Length(max: 100)]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::STRING, unique: true)]
    protected ?string $name = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN)]
    protected bool $enabled = true;

    /**
     * @var array<mixed>|null
     */
    #[ORM\Column(type: Types::JSON, nullable: true)]
    protected ?array $rules = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): void
    {
        $this->enabled = $enabled;
    }

    /**
     * @return array<mixed>|null
     */
    public function getRules(): ?array
    {
        return $this->rules;
    }

    /**
     * @param array<mixed>|null $rules
     */
    public function setRules(?array $rules): void
    {
        $this->rules = $rules;
    }
}
