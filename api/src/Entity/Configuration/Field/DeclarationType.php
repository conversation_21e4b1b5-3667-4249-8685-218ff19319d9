<?php

declare(strict_types=1);
namespace U2\Entity\Configuration\Field;

use ApiPlatform\Doctrine\Orm\Filter\OrderFilter;
use ApiPlatform\Metadata\ApiFilter;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use Doctrine\ORM\Mapping as ORM;
use U2\Api\Filter\SearchFilter;
use U2\EntityMetadata\Attribute as Metadata;
use U2\Security\UserRoles;
use U2\Security\Voter\VoterAttributes;

#[Metadata\ShortName(value: 'declaration-type')]
#[Metadata\ReadableName(value: 'u2_tcm.declaration_type')]
#[ORM\Entity]
#[ORM\Table(name: 'tcm_declaration_type')]
#[ApiFilter(
    filterClass: OrderFilter::class,
    properties: ['name', 'enabled'],
    arguments: ['orderParameterName' => 'sort']
)]
#[ApiFilter(
    filterClass: SearchFilter::class,
    properties: ['name' => 'ipartial']
)]
#[ApiResource(
    operations: [
        new Delete(security: 'is_granted("' . VoterAttributes::delete . '", object)'),
        new Get(security: 'is_granted("' . UserRoles::User->value . '")'),
        new Patch(security: 'is_granted("' . VoterAttributes::write . '", object)'),
        new GetCollection(security: 'is_granted("' . UserRoles::User->value . '")'),
        new Post(securityPostDenormalize: 'is_granted("' . VoterAttributes::write . '", object)'),
    ],
    routePrefix: '/configuration/field'
)]
class DeclarationType extends Choice
{
}
