<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Configuration\Field\ServiceType;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Igt\Igt5\Igt5Types;
use U2\Igt\Igt5\Igt5TypeToPropertiesMap;
use U2\Igt\TypeToPropertyMapInterface;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;
use U2\Repository\Igt5TransactionRepository;
use U2\Task\Field\ArmsLengthTrait;
use U2\Task\Field\ContractDateTrait;
use U2\Task\Field\IndirectTransactionsTrait;
use U2\Task\Field\LinkedBaseLocalGroupMoneyCommonTrait;
use U2\Task\Field\PartnerUnitTrait;
use U2\Task\Field\SingleEconomicOperationTrait;
use U2\Task\Field\TraceIdTrait;
use U2\Task\Field\TransactionDateTrait;
use U2\Task\Field\TransactionValueTrait;
use U2\Task\Field\TransferPricingMethodTrait;
use U2\Task\Field\TypeTrait;
use U2\Task\Interfaces\Typeable;
use U2\Task\TaskType\TransactionInterface;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[Assert\GroupSequence(['Igt5Transaction', 'LinkedBaseLocalGroupMoney'])]
#[ORM\Entity(repositoryClass: Igt5TransactionRepository::class)]
#[ORM\Table(name: 'igt_igt5_transaction')]
#[ReadableName(value: 'IGT 5 - Profit & Loss Transactions', translationDomain: false)]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
#[U2Assert\LinkedBaseLocalGroupMoney]
#[U2Assert\LinkedBaseLocalGroupMoneyExchangeRatesExist]
class Igt5Transaction extends TaskType implements TransactionInterface, Periodable, Typeable, LinkedBaseLocalGroupMoneyInterface
{
    use ArmsLengthTrait;
    use ContractDateTrait;
    use IndirectTransactionsTrait;
    use LinkedBaseLocalGroupMoneyCommonTrait;
    use PartnerUnitTrait;
    use PeriodableTrait;
    use SingleEconomicOperationTrait;
    use TraceIdTrait;
    use TransactionDateTrait;
    use TransactionValueTrait;
    use TransferPricingMethodTrait;
    use TypeTrait;

    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: ServiceType::class)]
    #[ORM\JoinColumn(nullable: true)]
    public ?ServiceType $serviceType = null;

    public static function getLinkedBaseLocalGroupMoneyFields(): array
    {
        return [
            'transactionValue',
        ];
    }

    /**
     * Gets the unique identifier to bind instances of this class to workflows.
     */
    public static function getWorkflowBindingId(): string
    {
        return 'igt_igt5_transaction';
    }

    /**
     * Gets a human-readable string to name this binding.
     */
    public static function getWorkflowBindingName(): string
    {
        return 'Intra Group Transactions 5 - Inter-company Loans / Finance';
    }

    public static function getTaskType(): string
    {
        return 'igt_igt5_transaction';
    }

    public function getName(): ?string
    {
        return "{$this->unit?->getRefId()} - {$this->getPartnerUnitName()}";
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->type}: {$this->getName()}, {$this->period?->getName()}";
    }

    /**
     * @return array<string>
     */
    public function getTransferPricingMethodMustBeSetTypes(): array
    {
        return static::getTypes();
    }

    public static function getTypeClass(): string
    {
        return Igt5Types::class;
    }

    /**
     * @return array<string>
     */
    public function getArmsLengthMustBeSetTypes(): array
    {
        return self::getTypes();
    }

    /**
     * @return class-string<TypeToPropertyMapInterface>
     */
    public static function getFieldConfigClass(): string
    {
        return Igt5TypeToPropertiesMap::class;
    }
}
