<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Entity\BaseLocalGroupMoney;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;
use U2\Repository\FinancialDataRepository;
use U2\Task\Field\LinkedBaseLocalGroupMoneyCommonTrait;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[Assert\GroupSequence(['FinancialData', 'LinkedBaseLocalGroupMoney'])]
#[ORM\Entity(repositoryClass: FinancialDataRepository::class)]
#[ORM\Table(name: 'tpm_financial_data')]
#[ReadableName(value: 'u2_tpm.financial_data')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
#[U2Assert\LinkedBaseLocalGroupMoney]
#[U2Assert\LinkedBaseLocalGroupMoneyExchangeRatesExist]
class FinancialData extends TaskType implements LinkedBaseLocalGroupMoneyInterface, Periodable, Transferable
{
    use LinkedBaseLocalGroupMoneyCommonTrait;
    use PeriodableTrait;

    #[Assert\NotNull]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    private ?BaseLocalGroupMoney $totalRevenueUnrelatedValue = null;

    #[Assert\NotNull]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    private ?BaseLocalGroupMoney $totalRevenueRelatedValue = null;

    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?BaseLocalGroupMoney $totalRevenueValue = null;

    #[Assert\NotNull]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    private ?BaseLocalGroupMoney $profitLossBeforeIncomeTaxValue = null;

    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?BaseLocalGroupMoney $incomeTaxPaidValue = null;

    #[Assert\NotNull]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    private ?BaseLocalGroupMoney $incomeTaxAccruedValue = null;

    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?BaseLocalGroupMoney $statedCapitalValue = null;

    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?BaseLocalGroupMoney $accumulatedEarningsValue = null;

    #[Assert\NotNull]
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?BaseLocalGroupMoney $tangibleAssetsValue = null;

    #[Assert\Range(min: '-999999.99', max: '999999.99')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::DECIMAL, precision: 8, scale: 2, nullable: false)]
    private ?string $numberOfEmployees = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN)]
    private bool $carryForward = false;

    public function __clone()
    {
        if (null !== $this->id) {
            $this->totalRevenueUnrelatedValue = clone $this->totalRevenueUnrelatedValue;
            $this->totalRevenueRelatedValue = clone $this->totalRevenueRelatedValue;
            $this->totalRevenueValue = clone $this->totalRevenueValue;
            $this->profitLossBeforeIncomeTaxValue = clone $this->profitLossBeforeIncomeTaxValue;
            $this->incomeTaxPaidValue = clone $this->incomeTaxPaidValue;
            $this->incomeTaxAccruedValue = clone $this->incomeTaxAccruedValue;
            $this->statedCapitalValue = clone $this->statedCapitalValue;
            $this->accumulatedEarningsValue = clone $this->accumulatedEarningsValue;
            $this->tangibleAssetsValue = clone $this->tangibleAssetsValue;
        }
        parent::__clone();
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tpm_financial_data';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TPM Financial Data';
    }

    public function getTotalRevenueUnrelatedValue(): ?BaseLocalGroupMoney
    {
        return $this->totalRevenueUnrelatedValue;
    }

    public function setTotalRevenueUnrelatedValue(?BaseLocalGroupMoney $totalRevenueUnrelatedValue = null): void
    {
        $this->totalRevenueUnrelatedValue = $totalRevenueUnrelatedValue;
    }

    public function getTotalRevenueRelatedValue(): ?BaseLocalGroupMoney
    {
        return $this->totalRevenueRelatedValue;
    }

    public function setTotalRevenueRelatedValue(?BaseLocalGroupMoney $totalRevenueRelatedValue = null): void
    {
        $this->totalRevenueRelatedValue = $totalRevenueRelatedValue;
    }

    public function getTotalRevenueValue(): ?BaseLocalGroupMoney
    {
        return $this->totalRevenueValue;
    }

    public function setTotalRevenueValue(?BaseLocalGroupMoney $totalRevenueValue = null): void
    {
        $this->totalRevenueValue = $totalRevenueValue;
    }

    public function getProfitLossBeforeIncomeTaxValue(): ?BaseLocalGroupMoney
    {
        return $this->profitLossBeforeIncomeTaxValue;
    }

    public function setProfitLossBeforeIncomeTaxValue(?BaseLocalGroupMoney $profitLossBeforeIncomeTaxValue = null): void
    {
        $this->profitLossBeforeIncomeTaxValue = $profitLossBeforeIncomeTaxValue;
    }

    public function getIncomeTaxPaidValue(): ?BaseLocalGroupMoney
    {
        return $this->incomeTaxPaidValue;
    }

    public function setIncomeTaxPaidValue(?BaseLocalGroupMoney $incomeTaxPaidValue = null): void
    {
        $this->incomeTaxPaidValue = $incomeTaxPaidValue;
    }

    public function getIncomeTaxAccruedValue(): ?BaseLocalGroupMoney
    {
        return $this->incomeTaxAccruedValue;
    }

    public function setIncomeTaxAccruedValue(?BaseLocalGroupMoney $incomeTaxAccruedValue = null): void
    {
        $this->incomeTaxAccruedValue = $incomeTaxAccruedValue;
    }

    public function getStatedCapitalValue(): ?BaseLocalGroupMoney
    {
        return $this->statedCapitalValue;
    }

    public function setStatedCapitalValue(?BaseLocalGroupMoney $statedCapitalValue = null): void
    {
        $this->statedCapitalValue = $statedCapitalValue;
    }

    public function getAccumulatedEarningsValue(): ?BaseLocalGroupMoney
    {
        return $this->accumulatedEarningsValue;
    }

    public function setAccumulatedEarningsValue(?BaseLocalGroupMoney $accumulatedEarningsValue = null): void
    {
        $this->accumulatedEarningsValue = $accumulatedEarningsValue;
    }

    public function getTangibleAssetsValue(): ?BaseLocalGroupMoney
    {
        return $this->tangibleAssetsValue;
    }

    public function setTangibleAssetsValue(?BaseLocalGroupMoney $tangibleAssetsValue = null): void
    {
        $this->tangibleAssetsValue = $tangibleAssetsValue;
    }

    public function getNumberOfEmployees(): ?string
    {
        return $this->numberOfEmployees;
    }

    public function setNumberOfEmployees(string $numberOfEmployees): void
    {
        $this->numberOfEmployees = $numberOfEmployees;
    }

    public function isCarryForward(): ?bool
    {
        return $this->carryForward;
    }

    public function setCarryForward(bool $carryForward): void
    {
        $this->carryForward = $carryForward;
    }

    #[Assert\Callback(groups: ['LinkedBaseLocalGroupMoney'])]
    public function validateTotalRevenueValue(ExecutionContextInterface $context): void
    {
        $totalRevenue = $this->getTotalRevenueValue()?->getBaseValue() ?? '0';
        $totalRevenueUnrelatedValue = $this->getTotalRevenueUnrelatedValue()?->getBaseValue() ?? '0';
        $totalRevenueRelatedValue = $this->getTotalRevenueRelatedValue()?->getBaseValue() ?? '0';
        $sumRevenueValues = bcadd($totalRevenueRelatedValue, $totalRevenueUnrelatedValue, 10);

        if ((float) $totalRevenue !== round((float) $sumRevenueValues, 0)) {
            $context
                ->buildViolation('u2.total_revenue_value_sum_incorrect')
                ->atPath('totalRevenueValue')
                ->addViolation();
        }
    }

    #[Assert\Callback]
    public function validateUnitCurrencyExist(ExecutionContextInterface $context): void
    {
        if (null !== $this->unit && null === $this->unit->getCurrency()) {
            $context
                ->buildViolation('u2_tpm.financial_data.unit.currency_not_blank')
                ->atPath('unit.currency')
                ->addViolation();
        }
    }

    /**
     * @return string[]
     */
    public static function getLinkedBaseLocalGroupMoneyFields(): array
    {
        return [
            'totalRevenueUnrelatedValue',
            'totalRevenueRelatedValue',
            'totalRevenueValue',
            'profitLossBeforeIncomeTaxValue',
            'incomeTaxPaidValue',
            'incomeTaxAccruedValue',
            'statedCapitalValue',
            'accumulatedEarningsValue',
            'tangibleAssetsValue',
        ];
    }

    public static function getTaskType(): string
    {
        return 'tpm_financial_data';
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->unit?->getRefId()}, {$this->period?->getName()}";
    }
}
