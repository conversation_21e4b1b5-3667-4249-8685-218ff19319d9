<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Entity\Configuration\Field\RestrictionReason;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\TaxAssessmentTaskType;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Interfaces\ValidDateRange;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\Entity\Traits\ValidFromToTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[ORM\Entity]
#[ORM\Table(name: 'tam_tax_relevant_restriction')]
#[ReadableName(value: 'u2_tam.tax_relevant_restriction')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
class TaxRelevantRestriction extends TaskType implements Transferable, ValidDateRange, Periodable, TaxAssessmentTaskType
{
    use PeriodableTrait;
    use ValidFromToTrait;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: TaxType::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?TaxType $taxType = null;

    #[Assert\NotNull]
    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $taxBase = null;

    #[Assert\NotNull]
    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $potentialTaxLiabilities = null;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: RestrictionReason::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?RestrictionReason $reason = null;

    public function getTaxType(): ?TaxType
    {
        return $this->taxType;
    }

    public function setTaxType(?TaxType $taxType): void
    {
        $this->taxType = $taxType;
    }

    public function getTaxBase(): ?int
    {
        return $this->taxBase;
    }

    public function setTaxBase(int $taxBase): void
    {
        $this->taxBase = $taxBase;
    }

    public function getPotentialTaxLiabilities(): ?int
    {
        return $this->potentialTaxLiabilities;
    }

    public function setPotentialTaxLiabilities(int $potentialTaxLiabilities): void
    {
        $this->potentialTaxLiabilities = $potentialTaxLiabilities;
    }

    public function getReason(): ?RestrictionReason
    {
        return $this->reason;
    }

    public function setReason(?RestrictionReason $reason): void
    {
        $this->reason = $reason;
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tam_tax_relevant_restriction';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TAM Tax Relevant Restriction';
    }

    #[Assert\Callback]
    public function validateValidToAndFromDatesAreNotNull(ExecutionContextInterface $context): void
    {
        if (null === $this->validFrom) {
            $context
                ->buildViolation('u2.this_value_must_not_be_null')
                ->atPath('validFrom')
                ->addViolation();
        }

        if (null === $this->validTo) {
            $context
                ->buildViolation('u2.this_value_must_not_be_null')
                ->atPath('validTo')
                ->addViolation();
        }
    }

    public static function getTaskType(): string
    {
        return 'tam_tax_relevant_restriction';
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->taxType?->getName()}: {$this->unit?->getRefId()}, {$this->period?->getName()}";
    }
}
