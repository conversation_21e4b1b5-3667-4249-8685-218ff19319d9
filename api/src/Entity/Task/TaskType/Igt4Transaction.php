<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Igt\Igt4\Igt4Types;
use U2\Igt\Igt4\Igt4TypeToPropertiesMap;
use U2\Igt\TypeToPropertyMapInterface;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;
use U2\Repository\Igt4TransactionRepository;
use U2\Task\Field\ArmsLengthTrait;
use U2\Task\Field\ClaimsExpensesTrait;
use U2\Task\Field\ContractDateTrait;
use U2\Task\Field\IndirectTransactionsTrait;
use U2\Task\Field\InterestOnDepositTrait;
use U2\Task\Field\InternalIdTrait;
use U2\Task\Field\LineOfBusinessTrait;
use U2\Task\Field\LinkedBaseLocalGroupMoneyCommonTrait;
use U2\Task\Field\MaxCoverByReinsurerTrait;
use U2\Task\Field\NetReceivablesTrait;
use U2\Task\Field\PartnerUnitTrait;
use U2\Task\Field\ReinsuranceResultTrait;
use U2\Task\Field\SingleEconomicOperationTrait;
use U2\Task\Field\TotalReinsuranceRecoverableTrait;
use U2\Task\Field\TraceIdTrait;
use U2\Task\Field\TransactionValueTrait;
use U2\Task\Field\TransferPricingMethodTrait;
use U2\Task\Field\TypeTrait;
use U2\Task\Field\ValidityPeriodExpiryDateTrait;
use U2\Task\Field\ValidityPeriodStartDateTrait;
use U2\Task\Interfaces\Typeable;
use U2\Task\TaskType\TransactionInterface;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[Assert\GroupSequence(['Igt4Transaction', 'LinkedBaseLocalGroupMoney'])]
#[ORM\Entity(repositoryClass: Igt4TransactionRepository::class)]
#[ORM\Table(name: 'igt_igt4_transaction')]
#[ReadableName(value: 'IGT 4 - Insurance & Reinsurance', translationDomain: false)]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
#[U2Assert\LinkedBaseLocalGroupMoney]
#[U2Assert\LinkedBaseLocalGroupMoneyExchangeRatesExist]
class Igt4Transaction extends TaskType implements TransactionInterface, Periodable, Transferable, Typeable, LinkedBaseLocalGroupMoneyInterface
{
    use ArmsLengthTrait;
    use ClaimsExpensesTrait;
    use ContractDateTrait;
    use IndirectTransactionsTrait;
    use InterestOnDepositTrait;
    use InternalIdTrait;
    use LineOfBusinessTrait;
    use LinkedBaseLocalGroupMoneyCommonTrait;
    use MaxCoverByReinsurerTrait;
    use NetReceivablesTrait;
    use PartnerUnitTrait;
    use PeriodableTrait;
    use ReinsuranceResultTrait;
    use SingleEconomicOperationTrait;
    use TotalReinsuranceRecoverableTrait;
    use TraceIdTrait;
    use TransactionValueTrait;
    use TransferPricingMethodTrait;
    use TypeTrait;
    use ValidityPeriodExpiryDateTrait;
    use ValidityPeriodStartDateTrait;

    public static function getLinkedBaseLocalGroupMoneyFields(): array
    {
        return [
            'transactionValue',
            'claimsExpenses',
            'interestOnDeposit',
            'maxCoverByReinsurer',
            'netReceivables',
            'reinsuranceResult',
            'totalReinsuranceRecoverable',
        ];
    }

    public static function getWorkflowBindingId(): string
    {
        return 'igt_igt4_transaction';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'Intra Group Transactions 4 - Inter-company';
    }

    public static function getTaskType(): string
    {
        return 'igt_igt4_transaction';
    }

    public function getName(): ?string
    {
        return "{$this->unit?->getRefId()} - {$this->getPartnerUnitName()}";
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->internalId} / {$this->type}: {$this->getName()}, {$this->period?->getName()}";
    }

    public static function getTypeClass(): string
    {
        return Igt4Types::class;
    }

    /**
     * @return array<string>
     */
    public function getTransferPricingMethodMustBeSetTypes(): array
    {
        return self::getTypes();
    }

    /**
     * @return array<string>
     */
    public function getInternalIdMustBeSetTypes(): array
    {
        return self::getTypes();
    }

    /**
     * @return array<string>
     */
    public function getArmsLengthMustBeSetTypes(): array
    {
        return self::getTypes();
    }

    /**
     * @return class-string<TypeToPropertyMapInterface>
     */
    public static function getFieldConfigClass(): string
    {
        return Igt4TypeToPropertiesMap::class;
    }
}
