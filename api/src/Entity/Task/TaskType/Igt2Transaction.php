<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Igt\Igt2\Igt2Types;
use U2\Igt\Igt2\Igt2TypeToPropertiesMap;
use U2\Igt\TypeToPropertyMapInterface;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;
use U2\Repository\Igt2TransactionRepository;
use U2\Task\Field\ArmsLengthTrait;
use U2\Task\Field\AssetsLiabilityUnderlyingTheDerivativeTrait;
use U2\Task\Field\CarryingAmountTrait;
use U2\Task\Field\ContractDateTrait;
use U2\Task\Field\CounterPartyNameTrait;
use U2\Task\Field\IndirectTransactionsTrait;
use U2\Task\Field\InstrumentIdTrait;
use U2\Task\Field\LinkedBaseLocalGroupMoneyCommonTrait;
use U2\Task\Field\MaturityDateTrait;
use U2\Task\Field\NotionalAmountTrait;
use U2\Task\Field\PartnerUnitTrait;
use U2\Task\Field\PurposeOfInstrumentTrait;
use U2\Task\Field\RevenuesFromDerivativesTrait;
use U2\Task\Field\S2CollateralValueTrait;
use U2\Task\Field\SingleEconomicOperationTrait;
use U2\Task\Field\SwapDeliveredCurrencyTrait;
use U2\Task\Field\SwapDeliveredInterestRateTrait;
use U2\Task\Field\SwapReceivedCurrencyTrait;
use U2\Task\Field\SwapReceivedInterestRateTrait;
use U2\Task\Field\TraceIdTrait;
use U2\Task\Field\TransactionTradeDateTrait;
use U2\Task\Field\TransferPricingMethodTrait;
use U2\Task\Field\TypeOfProtectionTrait;
use U2\Task\Field\TypeTrait;
use U2\Task\Interfaces\Typeable;
use U2\Task\TaskType\TransactionInterface;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[Assert\GroupSequence(['Igt2Transaction', 'LinkedBaseLocalGroupMoney'])]
#[ORM\Entity(repositoryClass: Igt2TransactionRepository::class)]
#[ORM\Table(name: 'igt_igt2_transaction')]
#[ReadableName(value: 'IGT 2 - Derivatives', translationDomain: false)]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
#[U2Assert\LinkedBaseLocalGroupMoney]
#[U2Assert\LinkedBaseLocalGroupMoneyExchangeRatesExist]
class Igt2Transaction extends TaskType implements TransactionInterface, Periodable, Typeable, LinkedBaseLocalGroupMoneyInterface
{
    use ArmsLengthTrait;
    use AssetsLiabilityUnderlyingTheDerivativeTrait;
    use CarryingAmountTrait;
    use ContractDateTrait;
    use CounterPartyNameTrait;
    use IndirectTransactionsTrait;
    use InstrumentIdTrait;
    use LinkedBaseLocalGroupMoneyCommonTrait;
    use MaturityDateTrait;
    use NotionalAmountTrait;
    use PartnerUnitTrait;
    use PeriodableTrait;
    use PurposeOfInstrumentTrait;
    use RevenuesFromDerivativesTrait;
    use S2CollateralValueTrait;
    use SingleEconomicOperationTrait;
    use SwapDeliveredCurrencyTrait;
    use SwapDeliveredInterestRateTrait;
    use SwapReceivedCurrencyTrait;
    use SwapReceivedInterestRateTrait;
    use TraceIdTrait;
    use TransactionTradeDateTrait;
    use TransferPricingMethodTrait;
    use TypeOfProtectionTrait;
    use TypeTrait;

    public static function getLinkedBaseLocalGroupMoneyFields(): array
    {
        return [
            'carryingAmount',
            'notionalAmount',
            'revenuesFromDerivatives',
            's2CollateralValue',
        ];
    }

    /**
     * Gets the unique identifier to bind instances of this class to workflows.
     */
    public static function getWorkflowBindingId(): string
    {
        return 'igt_igt2_transaction';
    }

    /**
     * Gets a human-readable string to name this binding.
     */
    public static function getWorkflowBindingName(): string
    {
        return 'Intra Group Transactions 2 - Derivatives';
    }

    public static function getTaskType(): string
    {
        return 'igt_igt2_transaction';
    }

    public function getName(): ?string
    {
        return "{$this->unit?->getRefId()} - {$this->getPartnerUnitName()}";
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->type}: {$this->getName()}, {$this->period?->getName()}";
    }

    public static function getTypeClass(): string
    {
        return Igt2Types::class;
    }

    /**
     * @return class-string<TypeToPropertyMapInterface>
     */
    public static function getFieldConfigClass(): string
    {
        return Igt2TypeToPropertiesMap::class;
    }
}
