<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Configuration\Field\TaxType;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\TaxComplianceFieldsTrait;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Repository\TaxAuthorityAuditObjectionRepository;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[ORM\Entity(repositoryClass: TaxAuthorityAuditObjectionRepository::class)]
#[ORM\Table(name: 'tcm_tax_authority_audit_objection')]
#[ReadableName(value: 'u2_tcm.tax_authority_audit_objection')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
class TaxAuthorityAuditObjection extends TaskType
{
    use TaxComplianceFieldsTrait;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: TaxType::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?TaxType $taxType = null;

    #[ORM\Column(nullable: true)]
    private ?string $name = null;

    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    private ?\DateTime $dateReceived = null;

    protected ?int $sourceId = null;

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    public function getDateReceived(): ?\DateTime
    {
        return $this->dateReceived;
    }

    public function setDateReceived(?\DateTime $dateReceived): void
    {
        $this->dateReceived = $dateReceived;
    }

    public function getTaxType(): ?TaxType
    {
        return $this->taxType;
    }

    public function setTaxType(?TaxType $taxType): void
    {
        $this->taxType = $taxType;
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tcm_tax_authority_audit_objection';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TCM Tax Authority / Audit Objection';
    }

    public static function getTaskType(): string
    {
        return 'tcm_tax_authority_audit_objection';
    }

    public function getDisplayName(): string
    {
        if (null === $this->name || '' === trim($this->name)) {
            return "#{$this->id} - {$this->taxType?->getName()}: {$this->unit?->getRefId()}, {$this->taxYear}" . (null !== $this->taxMonth ? " / {$this->taxMonth}" : '');
        }

        return "#{$this->id} - {$this->name}";
    }
}
