<?php

declare(strict_types=1);
namespace U2\Entity\Task\TaskType;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Configuration\Field\PricingMethod;
use U2\Entity\Configuration\Field\TransactionType;
use U2\Entity\Interfaces\Periodable;
use U2\Entity\Interfaces\TaxAssessmentTaskType;
use U2\Entity\Interfaces\Transferable;
use U2\Entity\Task\TaskType;
use U2\Entity\Traits\PeriodableTrait;
use U2\Entity\Unit;
use U2\EntityMetadata\Attribute\ReadableName;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Task\TaskTypeKnowledge;
use U2\Validator as U2Assert;

#[ORM\Entity]
#[ORM\Table(name: 'tam_transfer_pricing')]
#[ReadableName(value: 'u2_tam.transfer_pricing')]
#[ShortName(value: TaskTypeKnowledge::taskTypeClassToShortNameMap[self::class])]
class TransferPricing extends TaskType implements Transferable, Periodable, TaxAssessmentTaskType
{
    use PeriodableTrait;

    #[Assert\NotBlank]
    #[ORM\JoinColumn(nullable: false)]
    #[ORM\ManyToOne(targetEntity: Unit::class)]
    private ?Unit $partnerUnit = null;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: TransactionType::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?TransactionType $transactionType = null;

    #[Assert\Range(min: '1980', max: '2100')]
    #[Assert\NotBlank]
    #[ORM\Column(type: Types::INTEGER, length: 4, nullable: false)]
    private ?int $taxYear = null;

    #[Assert\NotNull]
    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $amount = null;

    #[Assert\NotNull]
    #[ORM\Column(type: Types::BIGINT, nullable: true)]
    private ?int $withholdingTaxPayable = null;

    #[Assert\NotBlank]
    #[U2Assert\Enabled]
    #[ORM\ManyToOne(targetEntity: PricingMethod::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?PricingMethod $pricingMethod = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN, nullable: false)]
    private ?bool $documentationRequired = null;

    #[Assert\Type(type: 'bool')]
    #[Assert\NotNull]
    #[ORM\Column(type: Types::BOOLEAN, nullable: false)]
    private ?bool $documentationAvailable = null;

    public function getTransactionType(): ?TransactionType
    {
        return $this->transactionType;
    }

    public function setTransactionType(?TransactionType $transactionType): void
    {
        $this->transactionType = $transactionType;
    }

    public function getTaxYear(): ?int
    {
        return $this->taxYear;
    }

    public function setTaxYear(?int $taxYear): void
    {
        $this->taxYear = $taxYear;
    }

    public function getAmount(): ?int
    {
        return $this->amount;
    }

    public function setAmount(?int $amount): void
    {
        $this->amount = $amount;
    }

    public function getWithholdingTaxPayable(): ?int
    {
        return $this->withholdingTaxPayable;
    }

    public function setWithholdingTaxPayable(?int $withholdingTaxPayable): void
    {
        $this->withholdingTaxPayable = $withholdingTaxPayable;
    }

    public function getPricingMethod(): ?PricingMethod
    {
        return $this->pricingMethod;
    }

    public function setPricingMethod(?PricingMethod $pricingMethod): void
    {
        $this->pricingMethod = $pricingMethod;
    }

    public function getDocumentationRequired(): ?bool
    {
        return $this->documentationRequired;
    }

    public function setDocumentationRequired(bool $documentationRequired): void
    {
        $this->documentationRequired = $documentationRequired;
    }

    public function getDocumentationAvailable(): ?bool
    {
        return $this->documentationAvailable;
    }

    public function setDocumentationAvailable(bool $documentationAvailable): void
    {
        $this->documentationAvailable = $documentationAvailable;
    }

    public function getPartnerUnit(): ?Unit
    {
        return $this->partnerUnit;
    }

    public function setPartnerUnit(?Unit $partnerUnit = null): void
    {
        $this->partnerUnit = $partnerUnit;
    }

    public static function getWorkflowBindingId(): string
    {
        return 'tam_transfer_pricing';
    }

    public static function getWorkflowBindingName(): string
    {
        return 'TAM Transfer Pricing';
    }

    public static function getTaskType(): string
    {
        return 'tam_transfer_pricing';
    }

    public function getDisplayName(): string
    {
        return "#{$this->id} - {$this->transactionType?->getName()}: {$this->unit?->getRefId()}, {$this->period?->getName()}";
    }
}
