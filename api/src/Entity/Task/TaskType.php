<?php

declare(strict_types=1);
namespace U2\Entity\Task;

use ApiPlatform\Metadata\ApiProperty;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Mapping\Annotation as Ged<PERSON>;
use Jet<PERSON>rains\PhpStorm\Deprecated;
use JetBrains\PhpStorm\Pure;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Unit;
use U2\Entity\User;
use U2\Entity\Workflow\Status;
use U2\Util\DateTime;
use U2\Validator as U2Assert;
use U2\Workflow\StatusTypes;

#[U2Assert\ChoiceFieldsObeyRules]
#[U2Assert\NoChangeIfPeriodIsClosed]
#[U2Assert\PeriodEndDateInValidDateRangeOfField(field: 'unit')]
#[U2Assert\PeriodEndDateInValidDateRangeOfField(field: 'partnerUnit')]
#[U2Assert\StatusExistsForAssignedWorkflow]
#[ORM\MappedSuperclass]
abstract class TaskType implements Entity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['country_by_country_report:read'])]
    #[ApiProperty(identifier: true)]
    protected ?int $id = null;

    #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
    #[ORM\OneToOne(targetEntity: Task::class, cascade: ['persist', 'remove'])]
    #[Assert\Valid]
    protected Task $task;

    #[ORM\ManyToOne(targetEntity: Unit::class)]
    #[ORM\JoinColumn(nullable: false)]
    protected ?Unit $unit = null;

    #[Gedmo\Blameable(on: 'create')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Deprecated(reason: 'This field has been moved to the entity: ' . Task::class . '. See: https://universalunits.atlassian.net/browse/UU-5951')]
    private ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[ORM\ManyToOne(targetEntity: User::class)]
    #[Deprecated(reason: 'This field has been moved to the entity: ' . Task::class . '. See: https://universalunits.atlassian.net/browse/UU-5951')]
    private ?User $updatedBy = null;

    #[Gedmo\Timestampable(on: 'create')]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Deprecated(reason: 'This field has been moved to the entity: ' . Task::class . '. See: https://universalunits.atlassian.net/browse/UU-5951')]
    private \DateTime $createdAt;

    #[Gedmo\Timestampable(on: 'update')]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    #[Deprecated(reason: 'This field has been moved to the entity: ' . Task::class . '. See: https://universalunits.atlassian.net/browse/UU-5951')]
    private \DateTime $updatedAt;

    abstract public static function getTaskType(): string;

    public function __construct(Status $status)
    {
        $this->task = new Task(static::getTaskType(), $status);
    }

    public function __clone()
    {
        if (null === $this->id) {
            return;
        }

        $task = new Task(static::getTaskType(), $this->getStatus());
        $task->setSourceId($this->id);

        $this->id = null;

        $task->setDescription($this->task->getDescription());
        foreach ($this->task->getFiles() as $file) {
            $task->addFile($file);
        }

        $task->setReporter($this->task->getReporter() ?? $this->task->getCreatedBy());
        $task->setDueDate($this->task->getDueDate());

        $this->setTask($task);

        $createdAt = DateTime::createNow();

        $this->createdAt = $createdAt;
        $this->updatedAt = $createdAt;
        $this->createdBy = null;
        $this->updatedBy = null;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTask(): Task
    {
        return $this->task;
    }

    public function setTask(Task $task): void
    {
        $this->task = $task;
    }

    public function getUnit(): ?Unit
    {
        return $this->unit;
    }

    public function setUnit(?Unit $unit): void
    {
        $this->unit = $unit;
    }

    #[Pure]
    public function getStatus(): Status
    {
        return $this->getTask()->getStatus();
    }

    public function setStatus(Status $status): void
    {
        $this->getTask()->setStatus($status);
    }

    #[Pure]
    public function isComplete(): bool
    {
        return StatusTypes::TYPE_COMPLETE === $this->getStatus()->getType();
    }

    #[Assert\Callback]
    public function validateUnit(ExecutionContextInterface $context): void
    {
        if (\in_array(static::class, [MasterFile::class, LocalFile::class, CountryByCountryReport::class], true)) {
            return;
        }

        if (null === $this->unit) {
            $context
                ->buildViolation('u2.this_value_must_not_be_null')
                ->atPath('unit')
                ->addViolation();
        }
    }

    #[Pure]
    public function getCreatedBy(): ?User
    {
        return $this->task->getCreatedBy() ?? $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;

        $this->task->setCreatedBy($createdBy);
    }

    #[Pure]
    public function getUpdatedBy(): ?User
    {
        return $this->task->getUpdatedBy() ?? $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;

        $this->task->setUpdatedBy($updatedBy);
    }

    public function setCreatedAt(\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;

        $this->task->setCreatedAt($createdAt);
    }

    #[Pure]
    public function getCreatedAt(): ?\DateTime
    {
        return $this->task->getCreatedAt();
    }

    public function setUpdatedAt(\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;

        $this->task->setUpdatedAt($updatedAt);
    }

    #[Pure]
    public function getUpdatedAt(): ?\DateTime
    {
        return $this->task->getUpdatedAt();
    }

    public function getSourceId(): ?int
    {
        return $this->getTask()->getSourceId();
    }

    public function setSourceId(?int $sourceId): void
    {
        $this->task->setSourceId($sourceId);
    }

    abstract public static function getWorkflowBindingId(): string;

    abstract public static function getWorkflowBindingName(): string;

    abstract public function getDisplayName(): string;

    public function getName(): ?string
    {
        return static::getWorkflowBindingName() . ' #' . $this->id;
    }
}
