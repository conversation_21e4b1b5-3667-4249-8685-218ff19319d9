<?php

declare(strict_types=1);
namespace U2\Entity\Traits;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Context;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Normalizer\DateTimeNormalizer;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use U2\AuditLog\Attribute\Audit;

/**
 * Trait: Checks if the validTo date is set after validFrom.
 */
trait ValidFromToTrait
{
    #[Audit]
    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    #[Groups(groups: ['unit:write', 'unit:read'])]
    #[Context(normalizationContext: [DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
    protected ?\DateTime $validFrom = null;

    #[Audit]
    #[ORM\Column(type: Types::DATE_MUTABLE, nullable: true)]
    #[Groups(groups: ['unit:write', 'unit:read'])]
    #[Context(normalizationContext: [DateTimeNormalizer::FORMAT_KEY => 'Y-m-d'])]
    protected ?\DateTime $validTo = null;

    public function getValidFrom(): ?\DateTime
    {
        return $this->validFrom;
    }

    public function setValidFrom(?\DateTime $validFrom = null): void
    {
        // We are required to check the timestamp since setting the same date again triggers a revision even if the old and new value are the same
        // See: https://universalunits.atlassian.net/browse/UU-6061
        if ($this->validFrom?->getTimestamp() === $validFrom?->getTimestamp()) {
            return;
        }

        $this->validFrom = $validFrom;
    }

    public function getValidTo(): ?\DateTime
    {
        return $this->validTo;
    }

    public function setValidTo(?\DateTime $validTo = null): void
    {
        // We are required to check the timestamp since setting the same date again triggers a revision even if the old and new value are the same
        // See: https://universalunits.atlassian.net/browse/UU-6061
        if ($this->validTo?->getTimestamp() === $validTo?->getTimestamp()) {
            return;
        }

        $this->validTo = $validTo;
    }

    #[Assert\Callback]
    public function isValidToDateAfterValidFromDate(?ExecutionContextInterface $context = null): bool
    {
        if (null === $this->getValidTo()) {
            return true;
        }

        if ($this->getValidFrom() > $this->getValidTo()) {
            if ($context instanceof ExecutionContextInterface) {
                $context
                    ->buildViolation('u2_core.valid_to_date_must_be_after_valid_from_date')
                    ->atPath('validTo')
                    ->addViolation();
            }

            return false;
        }

        return true;
    }
}
