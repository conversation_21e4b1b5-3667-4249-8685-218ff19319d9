<?php

declare(strict_types=1);
namespace U2\Entity;

/**
 * CAUTION: These enum values represent the names of the system settings in the database. Do not change them without migration.
 */
enum SystemSettingName: string
{
    case DateFormatShort = 'date_format.short_date';
    case DateFormatLong = 'date_format.long_date';
    case TimeFormat = 'date_format.time';
    case ApplicationCurrency = 'application_currency';
    case SecurityFileUploadWhitelist = 'security.file_upload_whitelist';
    case LoginColor = 'login_color';
    case MaxUploadSize = 'max_upload_size';
    case SecurityUnitEditFieldWhitelist = 'security.unit_edit_field_whitelist';
    case SecurityMaxLoginAttempts = 'security.max_login_attempts';
    case SecurityPasswordRulesMinLength = 'security.password_rules.min_length';
    case SecurityPasswordRulesRequireUppercaseLetter = 'security.password_rules.require_uppercase_letter';
    case SecurityPasswordRulesRequireLowercaseLetter = 'security.password_rules.require_lowercase_letter';
    case SecurityPasswordRulesRequireNumber = 'security.password_rules.require_number';
    case SecurityPasswordRulesRequireNonAlphanumericCharacter = 'security.password_rules.require_non_alphanumeric_character';
    case SecurityPasswordRulesUniqueHistoryCount = 'security.password_rules.unique_history_count';
    case SecurityPasswordRulesMaxAgeInDays = 'security.password_rules.max_age_in_days';
    case SecurityPasswordRulesResetHoursValid = 'security.password_rules.reset_hours_valid';
    case TaxAssessmentOpenBoy = 'tax_assessment.open_boy';
    case U2Locale = 'u2.locale';
    case SecurityTwoFactorIsEnforced = 'security.two_factor.is_enforced';
}
