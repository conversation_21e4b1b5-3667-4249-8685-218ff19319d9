<?php

declare(strict_types=1);
namespace U2\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use JetBrains\PhpStorm\Deprecated;
use U2\Entity\Interfaces\Entity;
use U2\Repository\UserPasswordHistoryRepository;

#[ORM\Entity(repositoryClass: UserPasswordHistoryRepository::class)]
class UserPasswordHistory implements Entity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    private ?int $id = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: false)]
    private \DateTime $creationDate;

    #[ORM\Column(type: Types::STRING, length: 255, nullable: false)]
    #[Deprecated(reason: 'Salt is no longer needed for passwords from symfony 5.3')]
    private string $salt = '';

    public function __construct(
        #[ORM\ManyToOne(targetEntity: User::class)]
        #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
        private readonly User $user,

        #[ORM\Column(length: 255, nullable: false)]
        private readonly string $password,
    ) {
        $this->creationDate = new \DateTime();
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreationDate(): \DateTime
    {
        return $this->creationDate;
    }
}
