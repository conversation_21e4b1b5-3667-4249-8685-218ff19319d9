<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Metadata\Get;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Datasheets\Item\ItemTypes;
use U2\Repository\ItemUnitValueRepository;
use U2\Security\Voter\VoterAttributes;

#[ORM\Entity(repositoryClass: ItemUnitValueRepository::class)]
#[ORM\Table(name: 'dtm_text_item_value')]
#[Get(
    uriTemplate: '/item-values/{id}',
    normalizationContext: ['groups' => ['item-unit-value:read']],
    security: 'is_granted("' . VoterAttributes::read . '", findOneBy("U2\\\\Entity\\\\Task\\\\TaskType\\\\UnitPeriod", ["unit", "period"], [object.getUnit(), object.getPeriod()]))'
)]
class TextItemUnitValue extends ItemUnitValue
{
    #[ORM\Column(type: Types::TEXT)]
    #[Groups(groups: ['item-unit-value:read'])]
    private string $comment = '';

    public function __construct(
        Item $item,
        Unit $unit,
        Period $period,
        string $comment = '',
    ) {
        \assert(ItemTypes::TEXT === $item->getType());
        $this->setComment($comment);
        parent::__construct($item, $unit, $period);
    }

    public function getComment(): string
    {
        return $this->comment;
    }

    public function setComment(string $comment): void
    {
        $this->comment = trim($comment);
    }

    public function hasEmptyValue(): bool
    {
        return '' === $this->comment;
    }

    public function getValue(): string
    {
        return $this->getComment();
    }

    public function getGroupCurrencyValue(): string
    {
        return $this->getValue();
    }
}
