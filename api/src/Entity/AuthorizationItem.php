<?php

declare(strict_types=1);
namespace U2\Entity;

use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Entity\Task\TaskType\Contract;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Entity\Task\TaskType\Igt1Transaction;
use U2\Entity\Task\TaskType\Igt2Transaction;
use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Entity\Task\TaskType\Igt4Transaction;
use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Entity\Task\TaskType\IncomeTaxPlanning;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\Entity\Task\TaskType\MainBusinessActivity;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Task\TaskType\OtherDeadline;
use U2\Entity\Task\TaskType\TaxAssessmentMonitor;
use U2\Entity\Task\TaskType\TaxAssessmentStatus;
use U2\Entity\Task\TaskType\TaxAuditRisk;
use U2\Entity\Task\TaskType\TaxAuthorityAuditObjection;
use U2\Entity\Task\TaskType\TaxConsultingFee;
use U2\Entity\Task\TaskType\TaxCredit;
use U2\Entity\Task\TaskType\TaxFilingMonitor;
use U2\Entity\Task\TaskType\TaxLitigation;
use U2\Entity\Task\TaskType\TaxRate;
use U2\Entity\Task\TaskType\TaxRelevantRestriction;
use U2\Entity\Task\TaskType\Transaction;
use U2\Entity\Task\TaskType\TransferPricing;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Security\Authorization\AuthorizationRight;

enum AuthorizationItem: string
{
    case ApmTransaction = 'APM_TRANSACTION';
    case Contract = 'CM_CONTRACT';
    case Igt1Transaction = 'IGT_IGT1_TRANSACTION';
    case Igt2Transaction = 'IGT_IGT2_TRANSACTION';
    case Igt3Transaction = 'IGT_IGT3_TRANSACTION';
    case Igt4Transaction = 'IGT_IGT4_TRANSACTION';
    case Igt5Transaction = 'IGT_IGT5_TRANSACTION';
    case IncomeTaxPlanning = 'TAM_INCOME_TAX_PLANNING';
    case LossCarryForward = 'TAM_LOSS_CARRY_FORWARD';
    case TaxAssessmentStatus = 'TAM_TAX_ASSESSMENT_STATUS';
    case TaxAuditRisk = 'TAM_TAX_AUDIT_RISK';
    case TaxConsultingFee = 'TAM_TAX_CONSULTING_FEE';
    case TaxCredit = 'TAM_TAX_CREDIT';
    case TaxLitigation = 'TAM_TAX_LITIGATION';
    case TaxRate = 'TAM_TAX_RATE';
    case TaxRelevantRestriction = 'TAM_TAX_RELEVANT_RESTRICTION';
    case TransferPricing = 'TAM_TRANSFER_PRICING';
    case OtherDeadline = 'TCM_OTHER_DEADLINE';
    case TaxAssessmentMonitor = 'TCM_TAX_ASSESSMENT_MONITOR';
    case TaxAuthorityAuditObjection = 'TCM_TAX_AUTHORITY_AUDIT_OBJECTION';
    case TaxFilingMonitor = 'TCM_TAX_FILING_MONITOR';
    case CountryByCountryReport = 'TPM_COUNTRY_BY_COUNTRY_REPORT';
    case FinancialData = 'TPM_FINANCIAL_DATA';
    case LocalFile = 'TPM_LOCAL_FILE';
    case MainBusinessActivity = 'TPM_MAIN_BUSINESS_ACTIVITY';
    case MasterFile = 'TPM_MASTER_FILE';
    case Transaction = 'TPM_TRANSACTION';
    case UnitPeriod = 'UNIT_PERIOD';
    case Unit = 'UNIT';
    case Period = 'PERIOD';

    /**
     * Get the associated entity class for this authorization item.
     *
     * @return class-string<TaskType|Unit|Period>
     */
    public function getAssociatedEntityClass(): string
    {
        return match ($this) {
            self::ApmTransaction => ApmTransaction::class,
            self::Contract => Contract::class,
            self::Igt1Transaction => Igt1Transaction::class,
            self::Igt2Transaction => Igt2Transaction::class,
            self::Igt3Transaction => Igt3Transaction::class,
            self::Igt4Transaction => Igt4Transaction::class,
            self::Igt5Transaction => Igt5Transaction::class,
            self::IncomeTaxPlanning => IncomeTaxPlanning::class,
            self::LossCarryForward => LossCarryForward::class,
            self::TaxAssessmentStatus => TaxAssessmentStatus::class,
            self::TaxAuditRisk => TaxAuditRisk::class,
            self::TaxConsultingFee => TaxConsultingFee::class,
            self::TaxCredit => TaxCredit::class,
            self::TaxLitigation => TaxLitigation::class,
            self::TaxRate => TaxRate::class,
            self::TaxRelevantRestriction => TaxRelevantRestriction::class,
            self::TransferPricing => TransferPricing::class,
            self::OtherDeadline => OtherDeadline::class,
            self::TaxAssessmentMonitor => TaxAssessmentMonitor::class,
            self::TaxAuthorityAuditObjection => TaxAuthorityAuditObjection::class,
            self::TaxFilingMonitor => TaxFilingMonitor::class,
            self::CountryByCountryReport => CountryByCountryReport::class,
            self::FinancialData => FinancialData::class,
            self::LocalFile => LocalFile::class,
            self::MainBusinessActivity => MainBusinessActivity::class,
            self::MasterFile => MasterFile::class,
            self::Transaction => Transaction::class,
            self::UnitPeriod => UnitPeriod::class,
            self::Unit => Unit::class,
            self::Period => Period::class,
        };
    }

    /**
     * Get the available rights for this authorization item.
     *
     * @return array<int, AuthorizationRight>
     */
    public function getAvailableRights(): array
    {
        return match ($this) {
            self::ApmTransaction, self::Contract, self::OtherDeadline, self::TaxAssessmentMonitor,
            self::TaxAuthorityAuditObjection, self::TaxFilingMonitor,
            self::UnitPeriod => [
                AuthorizationRight::CREATE,
                AuthorizationRight::READ,
                AuthorizationRight::UPDATE,
                AuthorizationRight::DELETE,
            ],
            self::Igt1Transaction, self::Igt2Transaction, self::Igt3Transaction,
            self::Igt4Transaction, self::Igt5Transaction, self::IncomeTaxPlanning,
            self::LossCarryForward, self::TaxAssessmentStatus, self::TaxAuditRisk,
            self::TaxConsultingFee, self::TaxCredit, self::TaxLitigation,
            self::TaxRate, self::TaxRelevantRestriction, self::TransferPricing,
            self::FinancialData, self::MainBusinessActivity, self::Transaction => [
                AuthorizationRight::CREATE,
                AuthorizationRight::READ,
                AuthorizationRight::UPDATE,
                AuthorizationRight::DELETE,
                AuthorizationRight::TRANSFER,
            ],
            self::CountryByCountryReport, self::LocalFile, self::MasterFile => [
                AuthorizationRight::ACCESS,
                AuthorizationRight::CREATE,
                AuthorizationRight::TRANSFER,
                AuthorizationRight::SUPERVISE,
            ],
            self::Period => [
                AuthorizationRight::UPDATE,
            ],
            self::Unit => [
                AuthorizationRight::MANAGE,
            ],
        };
    }
}
