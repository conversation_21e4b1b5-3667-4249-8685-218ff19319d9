<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\Common\Collections\Order;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Traits\DocumentSectionTrait;
use U2\Entity\Traits\FileAttachableTrait;
use U2\EntityMetadata\Attribute\ShortName;
use U2\Repository\CountryByCountryReportSectionRepository;
use U2\Security\Voter\DocumentVoterAttributes;

#[ORM\Entity(repositoryClass: CountryByCountryReportSectionRepository::class)]
#[ORM\Table(name: 'tpm_country_by_country_report_section')]
#[ShortName(value: 'tpm-country-by-country-report-section')]
#[ApiResource(
    operations: [
        new Get(security: 'is_granted("' . DocumentVoterAttributes::viewContent . '", object.getDocument())'),
    ],
    normalizationContext: ['groups' => ['document-section:read']],
)]
class CountryByCountryReportSection implements DocumentSection
{
    use DocumentSectionTrait;
    use FileAttachableTrait;

    #[Gedmo\Blameable(on: 'create')]
    #[Groups(groups: ['document-section:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[Groups(groups: ['document-section:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $updatedBy = null;

    #[Gedmo\Timestampable(on: 'create')]
    #[Groups(groups: ['document-section:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTime $createdAt = null;

    #[Gedmo\Timestampable(on: 'update')]
    #[Groups(groups: ['document-section:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTime $updatedAt = null;

    #[ORM\ManyToOne(targetEntity: CountryByCountryReport::class, inversedBy: 'sections')]
    #[ORM\JoinColumn(name: 'country_by_country_report_id', onDelete: 'CASCADE')]
    #[Groups(groups: ['file-linked-entity:read'])]
    protected ?StructuredDocumentInterface $document = null;

    /**
     * @var Collection<int, File>
     */
    #[ORM\ManyToMany(targetEntity: File::class, cascade: ['persist'], indexBy: 'id')]
    #[ORM\JoinTable(name: 'tpm_country_by_country_report_section_file')]
    #[ORM\JoinColumn(onDelete: 'CASCADE')]
    #[ORM\InverseJoinColumn(onDelete: 'RESTRICT')]
    #[ORM\OrderBy(value: ['name' => Order::Ascending->value])]
    protected Collection $files;

    public function __construct()
    {
        $this->editable = true;
        $this->include = true;
        $this->level = 1;
        $this->required = false;
        $this->files = new ArrayCollection();
    }

    public function __clone()
    {
        if (null !== $this->id) {
            $this->id = null;
            $files = $this->files;
            $this->files = new ArrayCollection();
            foreach ($files as $file) {
                $this->files->add($file);
            }
        }
    }

    /**
     * @throws \Exception
     */
    public function setDocument(?StructuredDocumentInterface $document): void
    {
        if (false === ($document instanceof CountryByCountryReport)) {
            throw new \InvalidArgumentException('CountryByCountryReportSection can only be used with CountryByCountryReport');
        }
        $this->document = $document;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy = null): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }

    public function setCreatedAt(?\DateTime $createdAt): void
    {
        $this->createdAt = $createdAt;
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }
}
