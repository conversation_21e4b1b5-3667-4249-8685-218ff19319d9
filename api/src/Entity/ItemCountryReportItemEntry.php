<?php

declare(strict_types=1);
namespace U2\Entity;

use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UlidType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Uid\Ulid;
use U2\Entity\Interfaces\Entity;

#[ORM\Table(name: 'dtm_item_country_report_to_item_entry')]
#[ORM\Entity]
#[UniqueEntity(fields: ['itemCountryReport', 'item'], errorPath: 'item')]
class ItemCountryReportItemEntry implements Entity
{
    #[ORM\Id]
    #[ORM\Column(type: UlidType::NAME, unique: true)]
    private Ulid $id;

    public function __construct(
        #[ORM\JoinColumn(nullable: false)]
        #[ORM\ManyToOne(targetEntity: ItemCountryReport::class, inversedBy: 'itemEntries')]
        private readonly ItemCountryReport $itemCountryReport,

        #[ORM\ManyToOne(targetEntity: Item::class)]
        #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
        private readonly Item $item,

        #[ORM\Column(nullable: false)]
        private readonly int $position,
    ) {
        $this->id = new Ulid();
    }

    public function getId(): Ulid
    {
        return $this->id;
    }

    public function getItem(): Item
    {
        return $this->item;
    }

    public function getItemCountryReport(): ItemCountryReport
    {
        return $this->itemCountryReport;
    }

    public function getPosition(): int
    {
        return $this->position;
    }
}
