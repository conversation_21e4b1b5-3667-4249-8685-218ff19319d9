<?php

declare(strict_types=1);
namespace U2\Entity;

use ApiPlatform\Metadata\Get;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Datasheets\Item\ItemTypes;
use U2\Repository\ItemUnitValueRepository;
use U2\Security\Voter\VoterAttributes;

#[ORM\Entity(repositoryClass: ItemUnitValueRepository::class)]
#[ORM\Table(name: 'dtm_number_item_value')]
#[Get(
    uriTemplate: '/item-values/{id}',
    normalizationContext: ['groups' => ['item-unit-value:read']],
    security: 'is_granted("' . VoterAttributes::read . '", findOneBy("U2\\\\Entity\\\\Task\\\\TaskType\\\\UnitPeriod", ["unit", "period"], [object.getUnit(), object.getPeriod()]))'
)]
class NumberItemUnitValue extends ItemUnitValue
{
    public const int DECIMAL_PLACES = 4;

    /**
     * @var numeric-string
     */
    #[ORM\Column(type: Types::DECIMAL, precision: 14, scale: self::DECIMAL_PLACES)]
    #[Assert\NotNull]
    #[Assert\Range(min: '-9999999999', max: '9999999999')]
    #[Groups(groups: ['item-unit-value:read'])]
    private string $value;

    public function __construct(
        Item $item,
        Unit $unit,
        Period $period,
        string $value = '0',
    ) {
        \assert(ItemTypes::NUMBER === $item->getType());

        $this->setValue($value);
        parent::__construct($item, $unit, $period);
    }

    /**
     * @return numeric-string
     */
    public function getValue(): string
    {
        return $this->value;
    }

    public function setValue(?string $value = null): void
    {
        $this->value = number_format((float) $value, self::DECIMAL_PLACES, '.', '');
    }

    public function hasEmptyValue(): bool
    {
        return '0.0000' === $this->value;
    }

    public function getGroupCurrencyValue(): string
    {
        return $this->getValue();
    }
}
