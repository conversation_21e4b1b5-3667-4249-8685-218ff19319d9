<?php

declare(strict_types=1);
namespace U2\Entity;

use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Task\Task;

#[ORM\Entity]
#[ORM\Table(name: 'review')]
class Review implements Entity
{
    #[ORM\Id]
    #[ORM\Column(type: Types::INTEGER)]
    #[ORM\GeneratedValue(strategy: 'AUTO')]
    #[Groups(groups: ['task:read'])]
    private ?int $id = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: false)]
    #[Groups(groups: ['task:read'])]
    private \DateTime $stamp;

    public function __construct(
        #[ORM\ManyToOne(targetEntity: Task::class, cascade: ['persist'], inversedBy: 'reviews')]
        #[ORM\JoinColumn(nullable: false, onDelete: 'CASCADE')]
        private readonly Task $task,

        #[Groups(groups: ['task:read'])]
        #[ORM\JoinColumn(nullable: false)]
        #[ORM\ManyToOne(targetEntity: User::class)]
        private readonly User $user,
    ) {
        $this->stamp = new \DateTime();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getStamp(): \DateTime
    {
        return $this->stamp;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getTask(): ?Task
    {
        return $this->task;
    }
}
