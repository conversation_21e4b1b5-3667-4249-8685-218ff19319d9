<?php

declare(strict_types=1);
namespace U2\Entity\Workflow;

use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Delete;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Patch;
use ApiPlatform\Metadata\Post;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Mapping\Annotation as Gedmo;
use Symfony\Bridge\Doctrine\Types\UlidType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Serializer\Normalizer\UidNormalizer;
use Symfony\Component\Uid\Ulid;
use Symfony\Component\Validator\Constraints as Assert;
use U2\Entity\Interfaces\Entity;
use U2\Entity\User;
use U2\Repository\Workflow\FieldConfigurationStatusesRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Validator as U2Assert;

#[UniqueEntity(fields: ['workflow', 'fieldConfiguration'], message: 'u2.field_configuration_statuses.field_configuration.invalid', errorPath: 'fieldConfiguration')]
#[U2Assert\FieldConfigurationStatuses]
#[ORM\Entity(repositoryClass: FieldConfigurationStatusesRepository::class)]
#[ORM\Table(name: 'workflow_to_field_configuration_statuses')]
#[ApiResource(
    shortName: 'FieldConfigurationStatusSet',
    operations: [
        new Delete(security: 'is_granted("' . VoterAttributes::delete . '", object)'),
        new Get(),
        new Patch(
            denormalizationContext: ['groups' => ['field_configuration_statuses:update']],
            security: 'is_granted("' . VoterAttributes::write . '", object)'
        ),
        new GetCollection(security: 'is_granted("' . VoterAttributes::read . '", \'U2\\\\Entity\\\\Workflow\\\\FieldConfigurationStatuses\')'),
        new Post(
            denormalizationContext: ['groups' => ['field_configuration_statuses:create']],
            securityPostDenormalize: 'is_granted("' . VoterAttributes::write . '", object)'
        ),
    ],
    normalizationContext: ['groups' => ['field_configuration_statuses:read'], UidNormalizer::NORMALIZATION_FORMAT_KEY => UidNormalizer::NORMALIZATION_FORMAT_RFC4122],
)]
class FieldConfigurationStatuses implements Entity
{
    #[Groups(groups: ['field_configuration_statuses:read'])]
    #[ORM\Id]
    #[ORM\Column(type: UlidType::NAME, unique: true)]
    private Ulid $id;

    /**
     * @var Collection<int, Status>
     */
    #[Assert\Count(min: 1)]
    #[Groups(groups: [
        'field_configuration_statuses:read',
        'field_configuration_statuses:update',
        'field_configuration_statuses:create', ]
    )]
    #[Assert\Unique]
    #[ORM\ManyToMany(targetEntity: Status::class)]
    #[ORM\JoinTable(name: 'workflow_field_configuration_statuses_to_status')]
    private Collection $statuses;

    #[Gedmo\Timestampable(on: 'create')]
    #[Groups(groups: ['field_configuration_statuses:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTime $createdAt;

    #[Gedmo\Timestampable(on: 'update')]
    #[Groups(groups: ['field_configuration_statuses:read'])]
    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private \DateTime $updatedAt;

    #[Gedmo\Blameable(on: 'create')]
    #[Groups(groups: ['field_configuration_statuses:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $createdBy = null;

    #[Gedmo\Blameable(on: 'update')]
    #[Groups(groups: ['field_configuration_statuses:read'])]
    #[ORM\ManyToOne(targetEntity: User::class)]
    private ?User $updatedBy = null;

    /**
     * @param ArrayCollection<int, Status>|array<int, Status> $statuses
     */
    public function __construct(
        #[ORM\JoinColumn(onDelete: 'CASCADE')]
        #[ORM\ManyToOne(targetEntity: Workflow::class, inversedBy: 'fieldConfigurationStatuses')]
        #[Groups(groups: ['field_configuration_statuses:read', 'field_configuration_statuses:create'])]
        private readonly Workflow $workflow, ArrayCollection|array $statuses,

        #[ORM\ManyToOne(targetEntity: FieldConfiguration::class)]
        #[Groups(groups: ['field_configuration_statuses:read', 'field_configuration_statuses:update', 'field_configuration_statuses:create'])]
        #[Assert\NotNull]
        private FieldConfiguration $fieldConfiguration,
    ) {
        $this->id = new Ulid();

        $this->workflow->addFieldConfigurationStatuses($this);

        $this->statuses = \is_array($statuses) ? new ArrayCollection($statuses) : $statuses;

        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    public function getId(): Ulid
    {
        return $this->id;
    }

    public function getFieldConfiguration(): FieldConfiguration
    {
        return $this->fieldConfiguration;
    }

    public function getWorkflow(): Workflow
    {
        return $this->workflow;
    }

    public function setFieldConfiguration(FieldConfiguration $fieldConfiguration): void
    {
        $this->fieldConfiguration = $fieldConfiguration;
    }

    public function getCreatedAt(): \DateTime
    {
        return $this->createdAt;
    }

    public function setUpdatedAt(\DateTime $updatedAt): void
    {
        $this->updatedAt = $updatedAt;
    }

    public function getUpdatedAt(): \DateTime
    {
        return $this->updatedAt;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): void
    {
        $this->createdBy = $createdBy;
    }

    public function getUpdatedBy(): ?User
    {
        return $this->updatedBy;
    }

    public function setUpdatedBy(?User $updatedBy = null): void
    {
        $this->updatedBy = $updatedBy;
    }

    /**
     * @return Collection<int, Status>
     */
    public function getStatuses(): Collection
    {
        return $this->statuses;
    }

    public function addStatus(Status $status): void
    {
        if (!$this->statuses->contains($status)) {
            $this->statuses->add($status);
        }
    }

    public function removeStatus(Status $status): void
    {
        if ($this->statuses->contains($status)) {
            $this->statuses->removeElement($status);
        }
    }
}
