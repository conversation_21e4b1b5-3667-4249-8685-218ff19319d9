<?php

declare(strict_types=1);
namespace U2\Entity\Workflow\Condition;

use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Link;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Workflow\Transition;
use U2\Repository\ConditionRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Workflow\Condition\CurrentUserIsNotLastUpdaterConditionType;

#[ORM\Entity(repositoryClass: ConditionRepository::class)]
#[Get(
    uriTemplate: '/transitions/{transitionId}/conditions/{id}',
    uriVariables: [
        'transitionId' => new Link(fromProperty: 'conditions', fromClass: Transition::class),
        'id' => new Link(fromClass: self::class),
    ],
    openapi: false,
    normalizationContext: ['groups' => ['workflow_transition_condition:read']],
    security: 'is_granted("' . VoterAttributes::write . '", object.getTransition())'
)]
class CurrentUserIsNotLastUpdaterCondition extends Condition implements Entity
{
    public function getDescription(): string
    {
        return 'User must not be the last updater';
    }

    public static function getType(): string
    {
        return CurrentUserIsNotLastUpdaterConditionType::type;
    }

    public static function getName(): string
    {
        return CurrentUserIsNotLastUpdaterConditionType::getName();
    }

    public static function getHelp(): string
    {
        return CurrentUserIsNotLastUpdaterConditionType::getHelp();
    }

    /**
     * @return array<mixed>
     */
    #[Groups(groups: ['workflow_transition:read', 'workflow_transition_condition:read'])]
    public function getParameters(): array
    {
        return [];
    }

    /**
     * @param array<mixed> $parameters
     */
    public function setParameters(array $parameters): void
    {
    }
}
