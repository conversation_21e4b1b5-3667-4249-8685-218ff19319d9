<?php

declare(strict_types=1);
namespace U2\Entity\Workflow\Action;

use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\Link;
use Doctrine\ORM\Mapping as ORM;
use U2\Entity\Interfaces\Entity;
use U2\Entity\Workflow\Transition;
use U2\Repository\ActionRepository;
use U2\Security\Voter\VoterAttributes;
use U2\Workflow\Action\AddReviewActionType;

#[ORM\Entity(repositoryClass: ActionRepository::class)]
#[Get(
    uriTemplate: '/transitions/{transitionId}/actions/{id}',
    uriVariables: [
        'transitionId' => new Link(fromProperty: 'actions', fromClass: Transition::class),
        'id' => new Link(fromClass: self::class),
    ],
    openapi: false,
    normalizationContext: ['groups' => ['workflow_transition_action:read']],
    security: 'is_granted("' . VoterAttributes::write . '", object.getTransition())'
)]
class AddReviewAction extends Action implements Entity
{
    public static function getDescription(): string
    {
        return AddReviewActionType::getDescription();
    }

    public static function getType(): string
    {
        return AddReviewActionType::type;
    }

    public static function getName(): string
    {
        return AddReviewActionType::getName();
    }

    public static function getHelp(): string
    {
        return AddReviewActionType::getHelp();
    }
}
