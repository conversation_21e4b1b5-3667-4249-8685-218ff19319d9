<?php

declare(strict_types=1);
namespace U2\Log\Monolog;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;
use U2\MultiTenancy\CurrentTenantProvider;

class TenantProcessor implements ProcessorInterface
{
    public function __construct(
        private readonly CurrentTenantProvider $currentTenantProvider,
    ) {
    }

    /**
     * Add tenant information.
     */
    public function __invoke(LogRecord $record): LogRecord
    {
        $tenant = $this->currentTenantProvider->getCurrentTenant();
        if (null !== $tenant) {
            $record['extra']['Tenant']['Name'] = $tenant->getName();
        }

        return $record;
    }
}
