<?php

declare(strict_types=1);
namespace U2\Log\Monolog;

use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\ServerBag;

class ClientProcessor implements ProcessorInterface
{
    public function __construct(private readonly RequestStack $requestStack)
    {
    }

    /**
     * Add client information.
     */
    public function __invoke(LogRecord $record): LogRecord
    {
        $serverData = $this->getServerData();

        if (null === $serverData) {
            return $record;
        }

        $clientIp = $serverData->get('REMOTE_ADDR');
        $record['extra']['Client']['IP'] = $clientIp;

        $userAgent = $serverData->get('HTTP_USER_AGENT');
        $record['extra']['Client']['User Agent'] = $userAgent;

        return $record;
    }

    private function getServerData(): ?ServerBag
    {
        $currentRequest = $this->requestStack->getCurrentRequest();

        if (null === $currentRequest) {
            return null;
        }

        return $this->requestStack->getCurrentRequest()->server;
    }
}
