<?php

declare(strict_types=1);
namespace U2\User;

use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\Entity\AuthorizationItem;
use U2\Entity\User;
use U2\Security\Authorization\AuthorizationManager;
use U2\Security\Authorization\AuthorizationRight;
use U2\Security\UserRoles;

class UserManager
{
    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly AuthorizationManager $authorizationManager,
    ) {
    }

    public function isCurrentUserUnitManager(): bool
    {
        return $this->authorizationManager->isAuthorized($this->currentUserProvider->get(), AuthorizationItem::Unit->value, AuthorizationRight::MANAGE->value);
    }

    public function isLoggedInUser(User $user): bool
    {
        return $user->getId() === $this->currentUserProvider->get()->getId();
    }

    public function loggedInUserIsUserAdmin(): bool
    {
        return $this->authorizationChecker->isGranted(UserRoles::UserGroupAdmin->value);
    }
}
