<?php

declare(strict_types=1);
namespace U2\Validator;

use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\InvalidArgumentException;
use U2\Security\Password\PasswordHistoryChecker;
use U2\User\NewPassword;

class PasswordHistoryValidator extends ConstraintValidator
{
    public function __construct(private readonly PasswordHistoryChecker $userPasswordHistoryChecker)
    {
    }

    public function validate(mixed $newPassword, Constraint $constraint): void
    {
        if (!($newPassword instanceof NewPassword)) {
            throw new InvalidArgumentException('This validator can only validate users');
        }

        if (null === $newPassword->getPassword()) {
            return;
        }

        if (!$this->userPasswordHistoryChecker->checkPasswordIsOk($newPassword->getUser(), $newPassword->getPassword())) {
            $this->context
                ->buildViolation('u2.password_was_already_in_use_recently')
                ->atPath('password')
                ->addViolation();
        }
    }
}
