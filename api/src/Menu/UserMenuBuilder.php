<?php

declare(strict_types=1);
namespace U2\Menu;

use Knp\Menu\FactoryInterface;
use Knp\Menu\ItemInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;

use function Symfony\Component\Translation\t;

use U2\User\CurrentUserProvider;

class UserMenuBuilder
{
    public function __construct(
        private readonly FactoryInterface $factory,
        private readonly AuthorizationCheckerInterface $authorizationChecker,
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly SeparatorBuilder $separatorBuilder,
    ) {
    }

    public function create(): ItemInterface
    {
        $menu = $this->factory->createItem('user-menu');

        if (!$this->authorizationChecker->isGranted('IS_AUTHENTICATED_REMEMBERED')) {
            $menu->addChild(
                $this->factory->createItem('Login', [
                    'label' => t('u2_core.login'),
                    'uri' => '#',
                    'extras' => ['vueRoute' => ['name' => 'AppLogin']],
                ])
            );

            return $menu;
        }

        $currentUser = $this->currentUserProvider->get();

        $userMenu = $this->factory->createItem('Username', [
            'label' => $currentUser->getUsername(),
        ]);

        $menu->addChild($userMenu);

        $userMenu->addChild('Profile', [
            'label' => t('u2_core.profile'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'UserEdit', 'params' => ['id' => $currentUser->getId()]]],
        ]);

        $userMenu->addChild('UserSettings', [
            'label' => t('u2_core.user_settings'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'UserSettingsEdit']],
        ]);

        $userMenu->addChild('Calendar', [
            'label' => t('u2_core.calendar'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'CalendarOverview']],
        ]);

        $userMenu->addChild($this->separatorBuilder->create());

        $userMenu->addChild('Logout', [
            'label' => t('u2_core.logout'),
            'uri' => '#',
            'extras' => ['vueRoute' => ['name' => 'AppLogout']],
        ]);

        return $menu;
    }
}
