<?php

declare(strict_types=1);
namespace U2\Menu;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\Apm\Transaction\TransactionTableType;
use U2\Entity\AuthorizationItem;
use U2\Entity\Task\TaskType\ApmTransaction;
use U2\Event\Menu\ConfigureRootMenuEvent;
use U2\Module\Module;
use U2\Module\ModuleStatusChecker;
use U2\Security\Authorization\AuthorizationRight;

readonly class ApmMenuSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private AuthorizationCheckerInterface $authorizationChecker,
        private TaskTypeMenuBuilder $genericMenuLayoutBuilder,
        private ModuleStatusChecker $enabledStatusChecker,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            ConfigureRootMenuEvent::class => ['onMenuConfigureRoot', 50],
        ];
    }

    public function onMenuConfigureRoot(ConfigureRootMenuEvent $event): void
    {
        if (!$this->enabledStatusChecker->isEnabled(Module::apm)) {
            return;
        }

        if (!$this->authorizationChecker->isGranted(AuthorizationItem::ApmTransaction->value . ':' . AuthorizationRight::READ->value)) {
            return;
        }

        $factory = $event->getFactory();

        $transactionsMenu = $this->genericMenuLayoutBuilder->init($factory, ApmTransaction::class)
            ->setMenuTitle('APM')
            ->setTableViewConfigurationFullyQualifiedClass(TransactionTableType::class)
            ->setTranslationDomain(false)
            ->build();

        if (\count($transactionsMenu->getChildren()) > 0) {
            $transactionsMenu->setUri(null);
        }

        $parentMenu = $event->getMenu();
        $parentMenu->addChild($transactionsMenu);
    }
}
