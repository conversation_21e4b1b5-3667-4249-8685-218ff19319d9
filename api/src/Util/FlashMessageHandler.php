<?php

declare(strict_types=1);
namespace U2\Util;

use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Session\Flash\FlashBagInterface;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Contracts\Translation\TranslatorInterface;

class FlashMessageHandler
{
    public function __construct(
        private readonly RequestStack $requestStack,
        private readonly TranslatorInterface $translator,
    ) {
    }

    public function getFlashBag(): FlashBagInterface
    {
        /** @var Session $session */
        $session = $this->requestStack->getSession();

        return $session->getFlashBag();
    }

    public function addSuccess(string $successMessage): void
    {
        $this->getFlashBag()->add('success', $successMessage);
    }

    public function addNotice(string $noticeMessage): void
    {
        $this->getFlashBag()->add('notice', $noticeMessage);
    }

    public function addError(string $errorMessage): void
    {
        $this->getFlashBag()->add('error', $errorMessage);
    }

    public function addWarning(string $warningMessage): void
    {
        $this->getFlashBag()->add('warning', $warningMessage);
    }

    public function addSavedMessage(): void
    {
        $savedMessage = $this->translator->trans('u2_core.success_saved');
        $this->addSuccess($savedMessage);
    }

    public function addSaveErrorMessage(): void
    {
        $saveErrorMessage = $this->translator->trans('u2_core.error_could_not_save_check_the_highlighted_fields');
        $this->addError($saveErrorMessage);
    }

    public function addRemovedMessage(): void
    {
        $removedMessage = $this->translator->trans('u2.success_removed');
        $this->addSuccess($removedMessage);
    }

    public function all(): array
    {
        return $this->getFlashBag()->all();
    }
}
