<?php

declare(strict_types=1);
namespace U2\Util;

class BcNumberManipulator
{
    public static function round(string $bcNumber, int $scale): string
    {
        $dotPos = strpos($bcNumber, '.');
        if (false === $dotPos) {
            return $bcNumber;
        }

        /** @var numeric-string $integralPart */
        $integralPart = substr($bcNumber, 0, $dotPos);
        $fractionalPart = '0.' . substr($bcNumber, $dotPos + 1);
        if (self::isNegative($integralPart, $scale)) {
            $fractionalPart = '-' . $fractionalPart;
        }
        $fractionalPart = (string) round((float) $fractionalPart, $scale);

        return bcadd($integralPart, $fractionalPart, $scale);
    }

    /**
     * @param numeric-string $bcNumber
     */
    public static function abs(string $bcNumber, int $scale): string
    {
        if (self::isNegative($bcNumber, $scale)) {
            $bcNumber = bcmul($bcNumber, '-1', $scale);
        }

        return $bcNumber;
    }

    /**
     * @param numeric-string $bcNumber
     */
    public static function isZero(string $bcNumber, int $scale): bool
    {
        return 0 === bccomp($bcNumber, '0', $scale);
    }

    /**
     * @param numeric-string $bcNumber
     */
    public static function isNegative(string $bcNumber, int $scale): bool
    {
        return -1 === bccomp($bcNumber, '0', $scale);
    }
}
