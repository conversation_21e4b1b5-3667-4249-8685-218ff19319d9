<?php

declare(strict_types=1);
namespace U2\Table\SavedFilter;

use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;
use U2\Entity\SavedFilter;
use U2\Table\StateFactory;

class RoutingHelper
{
    private SavedFilterDefinitionsCollection $saveableFilterDefinitionsCollection;

    public function __construct(private readonly RouterInterface $router, SavedFilterDefinitionsCollection $savedFilterDefinitionsCollection)
    {
        $this->saveableFilterDefinitionsCollection = $savedFilterDefinitionsCollection;
    }

    /**
     * @param class-string $class
     *
     * @throws RouteNotFoundException
     */
    public function translateTableViewConfigurationFullyQualifiedClassToRoute(string $class): ?string
    {
        $route = $this->saveableFilterDefinitionsCollection->tableViewConfigurationFullyQualifiedClassToRoute($class);

        if (null === $route) {
            throw new RouteNotFoundException('There is no route available to apply this filter');
        }

        return $route;
    }

    /**
     * @param array<string,bool|string|int> $parameters
     */
    public function generateUrlToSavedFilterTable(SavedFilter $savedFilter, bool $absoluteUrl = false, array $parameters = []): string
    {
        $tableViewConfigurationClassName = $savedFilter->getTableViewConfigurationFullyQualifiedClass();
        \assert(null !== $tableViewConfigurationClassName);

        $route = $this->translateTableViewConfigurationFullyQualifiedClassToRoute($tableViewConfigurationClassName);
        $referenceType = $absoluteUrl ? UrlGeneratorInterface::ABSOLUTE_URL : UrlGeneratorInterface::ABSOLUTE_PATH;

        return $this->router->generate(
            $route,
            array_merge([
                StateFactory::URL_PARAMETER_SAVED_FILTER => $savedFilter->getId(),
            ], $parameters),
            $referenceType
        );
    }
}
