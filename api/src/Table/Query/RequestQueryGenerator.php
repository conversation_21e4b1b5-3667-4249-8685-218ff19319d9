<?php

declare(strict_types=1);
namespace U2\Table\Query;

use Symfony\Component\HttpFoundation\RequestStack;
use U2\DataSourcery\DataSource\DataSource;
use U2\DataSourcery\Query\Pagination;
use U2\DataSourcery\Query\Query;
use U2\DataSourcery\Query\Sort;
use U2\DataSourcery\Query\SortCondition;
use U2\DataSourcery\UQL\Interpreter;
use U2\Util\ImmutableCollection;

class RequestQueryGenerator
{
    public const string QUERY_PARAMETER_UQL = 'q';  // as in Query

    public const string QUERY_PARAMETER_SELECT = 'c';  // as in Column

    public const string QUERY_PARAMETER_PAGINATION = 'p';  // as in Pagination

    public const string QUERY_PARAMETER_SORT = 's';  // as in Sort

    public function __construct(private readonly RequestStack $requestStack, private readonly Interpreter $interpreter)
    {
    }

    public function canGenerateQueryFromEnvironment(): bool
    {
        $query = $this->requestStack->getCurrentRequest()->query;

        return
            $query->has(self::QUERY_PARAMETER_UQL)
            || $query->has(self::QUERY_PARAMETER_SELECT)
            || $query->has(self::QUERY_PARAMETER_PAGINATION)
            || $query->has(self::QUERY_PARAMETER_SORT);
    }

    public function generate(DataSource $dataSource, ?Query $previousQuery = null): Query
    {
        $query = new Query(
            $this->buildSelect(),
            $this->buildSort(),
            $this->interpreter->interpret(
                $this->getRequestQueryParameter(self::QUERY_PARAMETER_UQL),
                new ImmutableCollection($dataSource->getFields()),
                $dataSource->getEntityClass()
            ),
            $this->buildPagination()
        );

        if (null !== $previousQuery) {
            $query = $this->resetPaginationIfRequired($query, $previousQuery);
        }

        return $query;
    }

    private function buildSelect(): array
    {
        $fields = $this->trimAll(explode(',', $this->getRequestQueryParameter(self::QUERY_PARAMETER_SELECT)));

        return array_unique($fields);
    }

    private function buildPagination(): Pagination
    {
        $pagination = $this->getRequestQueryParameter(self::QUERY_PARAMETER_PAGINATION);
        $rawData = $this->trimAll(explode(':', $pagination));

        if (1 === \count($rawData)) {
            return new Pagination((int) $rawData[0]);
        }

        if (2 !== \count($rawData)) {
            return new Pagination();
        }

        [$page, $count] = $rawData;

        return new Pagination((int) $page, (int) $count);
    }

    private function buildSort(): Sort
    {
        $rawData = $this->trimAll(explode(',', $this->getRequestQueryParameter(self::QUERY_PARAMETER_SORT)));
        $sortConditions = array_map(
            function (string $sortStatement): SortCondition {
                $parts = $this->trimAll(explode(':', $sortStatement));
                $field = $parts[0];
                $direction = 2 === \count($parts) ? strtoupper($parts[1]) : SortCondition::ASC;
                if (!\in_array($direction, [SortCondition::ASC, SortCondition::DESC], true)) {
                    $direction = SortCondition::ASC;
                }

                return new SortCondition($field, $direction);
            },
            $rawData
        );

        return new Sort($sortConditions);
    }

    /**
     * Helper function: trim all elements of an array of strings, and drop the empty results.
     */
    private function trimAll(array $array): array
    {
        $trimmedFields = array_map('trim', $array);

        return array_filter(
            $trimmedFields,
            static fn ($value): bool => '' !== $value
        );
    }

    private function getRequestQueryParameter(string $parameterName): string
    {
        $request = $this->requestStack->getCurrentRequest();

        return null !== $request ? $request->query->get($parameterName, '') : '';
    }

    private function resetPaginationIfRequired(Query $query, Query $previousQuery): Query
    {
        if ($query->getFilter()->getUql() !== $previousQuery->getFilter()->getUql()) {
            $pagination = $query->getPagination()->withPage(0);

            return $query->withPagination($pagination);
        }

        return $query;
    }
}
