<?php

declare(strict_types=1);
namespace U2\ControllerVue;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;

class FileController extends AbstractController
{
    /**
     * Proxy the file download links to the legacy links.
     */
    #[Route(path: '/file/{id}/download', name: 'u2_file_download_redirect')]
    public function redirectFileDownload(int $id): RedirectResponse
    {
        return $this->redirectToRoute('u2_file_download', ['id' => $id]);
    }
}
