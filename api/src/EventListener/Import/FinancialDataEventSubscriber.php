<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Entity\Task\TaskType\FinancialData;
use U2\Event\Import\PreBindDataImportEvent;

class FinancialDataEventSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            PreBindDataImportEvent::class => 'setCurrenciesOnInterpretedData',
        ];
    }

    public function setCurrenciesOnInterpretedData(PreBindDataImportEvent $event): void
    {
        $entity = $event->getEntity();
        if (!$entity instanceof FinancialData) {
            return;
        }

        $interpretedData = $event->getInterpretedData();

        $interpretedData['transactionCurrency'] = $interpretedData['currency'] ?? null;

        unset($interpretedData['currency']);

        $event->setInterpretedData($interpretedData);
    }
}
