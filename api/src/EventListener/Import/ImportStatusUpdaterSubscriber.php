<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Messenger\MessageBusInterface;
use U2\Event\Import\PostInterpretImportEvent;
use U2\Event\Import\PostRowInterpretImportEvent;
use U2\Import\Messenger\ImportStatusUpdateMessage;
use U2\Util\DateTime;

class ImportStatusUpdaterSubscriber implements EventSubscriberInterface
{
    public const int REFRESH_INTERVAL = 3;

    public const int INTERPRETATION_MAX_PROGRESS = 80;

    public function __construct(
        private readonly MessageBusInterface $messageBus,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PostRowInterpretImportEvent::class => 'postRowInterpretProgressUpdate',
            PostInterpretImportEvent::class => ['postInterpretProgressUpdate', 10],
        ];
    }

    public function postRowInterpretProgressUpdate(PostRowInterpretImportEvent $event): void
    {
        $import = $event->getLog()->getImport();
        $secondsSinceLastUpdate = DateTime::createNow()->getTimestamp() - $import->getUpdatedAt()->getTimestamp();

        if ($secondsSinceLastUpdate > self::REFRESH_INTERVAL) {
            $progress = (int) round(self::INTERPRETATION_MAX_PROGRESS * $event->getIndex() / $event->getRowCount());
            $import->setProgress($progress);
            $this->messageBus->dispatch(new ImportStatusUpdateMessage($import->getId(), $progress));
        }
    }

    public function postInterpretProgressUpdate(PostInterpretImportEvent $event): void
    {
        $import = $event->getLog()->getImport();
        $progress = self::INTERPRETATION_MAX_PROGRESS;
        $import->setProgress($progress);
        $this->messageBus->dispatch(new ImportStatusUpdateMessage($import->getId(), $progress));
    }
}
