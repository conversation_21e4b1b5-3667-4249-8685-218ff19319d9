<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Entity\PercentItemUnitValue;
use U2\Event\Import\PreBindDataImportEvent;
use U2\Exception\ImportInvalidValueException;
use U2\Form\DataTransformer\PercentToLocalizedStringTransformer;

class PercentItemUnitValueUpdateEventSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            PreBindDataImportEvent::class => 'transformValue',
        ];
    }

    /**
     * @throws ImportInvalidValueException
     */
    public function transformValue(PreBindDataImportEvent $event): void
    {
        $itemUnitValue = $event->getEntity();
        if (!($itemUnitValue instanceof PercentItemUnitValue)) {
            return;
        }

        $dataTransformer = new PercentToLocalizedStringTransformer(4, 'fractional', \NumberFormatter::ROUND_HALFUP);

        try {
            $interpretedData['value'] = $dataTransformer->reverseTransform($event->getInterpretedData()['value']);
        } catch (\Throwable) {
            throw new ImportInvalidValueException($event->getInterpretedData()['value']);
        }

        $event->setInterpretedData($interpretedData);
    }
}
