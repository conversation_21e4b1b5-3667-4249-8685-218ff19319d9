<?php

declare(strict_types=1);
namespace U2\EventListener\Import;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Entity\ItemUnitValue;
use U2\Entity\Task\TaskType;
use U2\Entity\Unit;
use U2\Event\Import\PostBindDataImportEvent;
use U2\Import\Interpreter\Field\FieldError;
use U2\Unit\Assignment\UserUnitAssignmentChecker;
use U2\User\CurrentUserProvider;

class AssignedUnitEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly UserUnitAssignmentChecker $userUnitAssignmentChecker,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            PostBindDataImportEvent::class => 'addErrorIfUserHasNoPermissionToUnit',
        ];
    }

    public function addErrorIfUserHasNoPermissionToUnit(PostBindDataImportEvent $event): void
    {
        $entity = $event->entity;

        if (!($entity instanceof ItemUnitValue || $entity instanceof TaskType)) {
            return;
        }

        $unit = $entity->getUnit();
        if (!($unit instanceof Unit)) {
            return;
        }

        if (!$this->userUnitAssignmentChecker->check($this->currentUserProvider->get(), [$unit])) {
            $event->addFieldError(
                new FieldError(
                    \sprintf('You are not allowed to upload data for unit “%s”', $unit->getRefId()),
                    $event->getConfiguration()->getField('unit')->getLabel()
                )
            );
        }
    }
}
