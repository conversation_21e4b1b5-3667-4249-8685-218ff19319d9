<?php

declare(strict_types=1);
namespace U2\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Events;
use U2\Entity\Interfaces\TaskTypeWithUnitHierarchy;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\UnitHierarchyDefinition;

/**
 * TODO: work out if we really need this listener since it seems like it might be bad for performance
 * Maybe there is a better solution to this problem.
 */
#[AsDoctrineListener(event: Events::onFlush)]
class UnitHierarchyDefinitionListener
{
    public function onFlush(OnFlushEventArgs $args): void
    {
        $entityManager = $args->getObjectManager();
        $uow = $entityManager->getUnitOfWork();
        $entities = [
            ...array_values($uow->getScheduledEntityUpdates()),
            ...array_values($uow->getScheduledEntityDeletions()),
        ];
        foreach ($entities as $entity) {
            if ($entity instanceof UnitHierarchyDefinition) {
                $documentTypes = [
                    MasterFile::class,
                    LocalFile::class,
                    CountryByCountryReport::class,
                ];
                foreach ($documentTypes as $documentType) {
                    $documentRepository = $entityManager->getRepository($documentType);
                    $unitHierarchyId = $entity->getUnitHierarchy()->getId();
                    \assert(null !== $unitHierarchyId);

                    $documents = $documentRepository->findByUnitHierarchyAndStartDate(
                        $unitHierarchyId,
                        $entity->getStartDate()
                    );

                    if (0 !== \count($documents)) {
                        foreach ($documents as $document) {
                            \assert($document instanceof TaskTypeWithUnitHierarchy);

                            $document->setUnitHierarchyDefinitionsChanged(true);
                            $classMetadata = $entityManager->getMetadataFactory()->getMetadataFor($document::class);
                            /*
                             * According to the documentation at http://docs.doctrine-project.org/en/2.0.x/reference/events.html#onflush
                             * this function should be used:
                             * $uow->recomputeSingleEntityChangeSet($classMetadata, $document);
                             * However, this does no lead to changes being persisted.
                             * Therefore use the computeChangeSet method. It is public, but marked for internal use only,
                             * probably because it triggers the preFLush event on the entity.
                             * Use it, but keep in mind that preFlush events for the entity involved could be triggered multiple times.
                             */
                            $uow->computeChangeSet($classMetadata, $document);
                        }
                    }
                }
            }
        }
    }
}
