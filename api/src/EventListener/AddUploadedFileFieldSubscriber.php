<?php

declare(strict_types=1);
namespace U2\EventListener;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

use function Symfony\Component\Translation\t;

use U2\Entity\File;

class AddUploadedFileFieldSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [FormEvents::PRE_SET_DATA => 'preSetData'];
    }

    public function preSetData(FormEvent $event): void
    {
        /** @var File|null $data */
        $data = $event->getData();
        $form = $event->getForm();
        if (null === $data || null === $data->getId()) {
            $form->add(
                'uploadedFile',
                FileType::class,
                [
                    'label' => t('u2.file'),
                ]
            );
        }
    }
}
