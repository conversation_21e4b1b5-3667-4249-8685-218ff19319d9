<?php

declare(strict_types=1);
namespace U2\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\OnFlushEventArgs;
use Doctrine\ORM\Events;
use Doctrine\ORM\Mapping\ClassMetadata;
use U2\Entity\DocumentSection;
use U2\Entity\Task\TaskType\AbstractDocument;
use U2\Exception\Exception;
use U2\User\CurrentUserProvider;

#[AsDoctrineListener(event: Events::onFlush)]
class AbstractDocumentSectionListener
{
    public function __construct(private readonly CurrentUserProvider $currentUserProvider)
    {
    }

    /**
     * TODO: Find another solution to update created at/by of the main document.
     * Doctrine listeners are heavy weight and not good for performance
     * because they run on every entity operation.
     * They should be used as a last resort.
     */
    public function onFlush(OnFlushEventArgs $args): void
    {
        $entityManager = $args->getObjectManager();
        $uow = $entityManager->getUnitOfWork();
        $now = new \DateTime();
        try {
            $user = $this->currentUserProvider->get();
        } catch (Exception) {
            return;
        }

        foreach ($uow->getScheduledEntityUpdates() as $entity) {
            if (false === ($entity instanceof DocumentSection)) {
                continue;
            }

            $document = $entity->getDocument();
            if (false === ($document instanceof AbstractDocument)) {
                continue;
            }

            $task = $document->getTask();
            $task->setUpdatedBy($user);
            $task->setUpdatedAt($now);

            $classMetadata = $entityManager->getMetadataFactory()->getMetadataFor($task::class);
            \assert($classMetadata instanceof ClassMetadata);
            $uow->recomputeSingleEntityChangeSet($classMetadata, $task);
        }
    }
}
