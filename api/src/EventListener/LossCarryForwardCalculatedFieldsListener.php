<?php

declare(strict_types=1);
namespace U2\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Doctrine\ORM\Events;
use U2\Entity\Task\TaskType\LossCarryForward;
use U2\TaxCompliance\LossCarryForward\LossCarryForwardUpdater;

#[AsDoctrineListener(event: Events::prePersist)]
#[AsDoctrineListener(event: Events::preUpdate)]
class LossCarryForwardCalculatedFieldsListener
{
    private LossCarryForwardUpdater $updater;

    public function __construct(LossCarryForwardUpdater $lossCarryForwardUpdater)
    {
        $this->updater = $lossCarryForwardUpdater;
    }

    public function prePersist(PrePersistEventArgs $arguments): void
    {
        $this->update($arguments);
    }

    public function preUpdate(PreUpdateEventArgs $arguments): void
    {
        $this->update($arguments);
    }

    private function update(PrePersistEventArgs|PreUpdateEventArgs $arguments): void
    {
        $entity = $arguments->getObject();
        if ($entity instanceof LossCarryForward) {
            $this->updater->update($entity);
        }
    }
}
