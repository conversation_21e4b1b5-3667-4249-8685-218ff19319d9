<?php

declare(strict_types=1);
namespace U2\EventListener;

use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\Event\PrePersistEventArgs;
use U2\Document\DocumentDefaultPermissionPopulator;
use U2\Document\DocumentSectionPopulator;
use U2\Entity\Task\TaskType\AbstractDocument;

#[AsDoctrineListener(event: 'prePersist')]
class AbstractDocumentListener
{
    public function __construct(
        private readonly DocumentSectionPopulator $documentSectionPopulator,
        private readonly DocumentDefaultPermissionPopulator $documentDefaultPermissionPopulator,
    ) {
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $document = $args->getObject();
        if ($document instanceof AbstractDocument && null !== $document->getBaseTemplate()) {
            $this->documentSectionPopulator->fromBaseTemplate($document);
            $this->documentDefaultPermissionPopulator->copyDefaultPermissions($document);
        }
    }
}
