<?php

declare(strict_types=1);
namespace U2\DataSourcery\DataSource\Driver\Doctrine\QueryBuilder;

use U2\DataSourcery\DataSource\DataSource;
use U2\DataSourcery\Query\Query;
use U2\DataSourcery\Transformer\TransformerInterface;

class JoinFieldNamesFinder
{
    public function __construct(private readonly DependentFieldNamesFinder $dependentFieldNamesFinder)
    {
    }

    /**
     * @return string[]
     */
    public function find(Query $query, DataSource $dataSource): array
    {
        $fieldsRequiredByTransformers = array_values(
            array_reduce(
                $dataSource->getTransformers(),
                static fn ($requiredFields, TransformerInterface $transformer): array => array_merge($requiredFields, $transformer->getRequiredFieldNames()),
                []
            )
        );

        $fieldNames = array_merge(
            $query->extractRequiredFields(),
            $fieldsRequiredByTransformers
        );

        return array_unique(
            array_merge(
                $fieldNames,
                $this->dependentFieldNamesFinder->find(
                    $fieldNames,
                    $dataSource->getFields()
                )
            )
        );
    }
}
