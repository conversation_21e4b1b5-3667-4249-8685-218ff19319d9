<?php

declare(strict_types=1);
namespace U2\DataSourcery\DataType;

use U2\DataSourcery\Query\FilterCondition;

class EntityDataType extends AbstractDataType
{
    /**
     * Return an array of available filter methods.
     *
     * @return array<int,string>
     */
    public function getAvailableFilterMethods(): array
    {
        return [
            FilterCondition::METHOD_NUMERIC_EQ,
            FilterCondition::METHOD_NUMERIC_NEQ,
        ];
    }

    /**
     * Return the identifier of the default sort method.
     */
    public function getDefaultFilterMethod(): string
    {
        return FilterCondition::METHOD_NUMERIC_EQ;
    }

    /**
     * Return the name of the type, unique.
     */
    public function getName(): string
    {
        return 'entity';
    }
}
