<?php

declare(strict_types=1);
namespace U2\DataSourcery\UQL;

use U2\Exception\DataSourcery\UqlLexerException;

/**
 * Class Lexer.
 *
 * The Lexer component has the task of translating the user-typed syntax into
 * concrete Tokens with definite meanings in the language's syntax.
 */
class Lexer
{
    /**
     * Lex the input string by moving through it trying to match any of
     * the defined language tokens.
     *
     * @throws \Exception
     *
     * @return array<int, Token|false>
     */
    public static function lex(string $string): array
    {
        $tokens = [];
        $cursor = 0;

        while ($cursor < \strlen($string)) {
            $result = static::matchToken($string, $cursor);
            if (false === $result) {
                throw new UqlLexerException('Can\'t parse at character ' . $cursor . ': "' . substr($string, $cursor, 50) . '[...]"');
            }
            if ('T_WHITESPACE' !== $result->getToken()) {
                // We found a non-whitespace token. Store it.
                $tokens[] = $result;
            }
            $cursor += \strlen($result->getValue());
        }

        return $tokens;
    }

    /**
     * Tries to match any of the tokens to the current cursor position.
     */
    public static function matchToken(string $string, int $cursor): Token|false
    {
        $string = substr($string, $cursor);
        foreach (Tokens::$terminalTokens as $regex => $token) {
            if (preg_match($regex, $string, $matches)) {
                return new Token($token, $matches[1]);
            }
        }

        return false;
    }
}
