<?php

declare(strict_types=1);
namespace U2\Command\Maintenance;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;

#[AsCommand(name: 'u2:maintenance:start', description: 'Puts the application into maintenance mode')]
class MaintenanceModeEnableCommand extends Command
{
    public function __construct(private readonly MaintenanceMode $maintenanceMode)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        if ($input->isInteractive() && !$this->isConfirmed($input, $output)) {
            $output->writeln('Aborted!');

            return 0;
        }

        $this->maintenanceMode->enable();
        $output->writeln('Maintenance mode enabled.');

        return 0;
    }

    private function isConfirmed(InputInterface $input, OutputInterface $output): bool
    {
        $helper = $this->getHelper('question');
        $question = new ConfirmationQuestion('Are you sure you want to enable maintenance mode [y/N]? ', false);

        return $helper->ask($input, $output, $question);
    }
}
