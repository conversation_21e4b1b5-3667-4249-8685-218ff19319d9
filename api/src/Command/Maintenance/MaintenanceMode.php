<?php

declare(strict_types=1);
namespace U2\Command\Maintenance;

use Psr\Cache\CacheItemPoolInterface;

class MaintenanceMode
{
    private CacheItemPoolInterface $cache;

    private const string cacheKey = 'maintenance';

    public function __construct(CacheItemPoolInterface $tenantCache)
    {
        $this->cache = $tenantCache;
    }

    public function enable(): void
    {
        $item = $this->cache->getItem(self::cacheKey);
        $item->set(true);
        $this->cache->save($item);
    }

    public function disable(): void
    {
        $this->cache->deleteItem(self::cacheKey);
    }

    public function isEnabled(): bool
    {
        $item = $this->cache->getItem(self::cacheKey);

        return $item->get() ?? false;
    }
}
