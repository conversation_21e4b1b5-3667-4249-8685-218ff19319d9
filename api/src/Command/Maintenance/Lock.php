<?php

declare(strict_types=1);
namespace U2\Command\Maintenance;

use Psr\Cache\CacheItemPoolInterface;
use Symfony\Component\Uid\Uuid;
use U2\Exception\Exception;

class Lock
{
    private CacheItemPoolInterface $cache;

    private const string cacheKey = 'lock';

    public function __construct(CacheItemPoolInterface $tenantCache)
    {
        $this->cache = $tenantCache;
    }

    /**
     * @throws Exception
     */
    public function lock(): void
    {
        if ($this->cache->hasItem(self::cacheKey)) {
            throw new Exception('Lock has been already created by other process');
        }

        $this->doLock();
    }

    public function unlock(): void
    {
        $this->cache->deleteItem(self::cacheKey);
    }

    /**
     * @throws Exception
     */
    private function doLock(): void
    {
        $id = Uuid::v4()->toRfc4122();
        $item = $this->cache->getItem(self::cacheKey);
        $item->set($id);
        $this->cache->save($item);

        /*
         * This sleep is here to try to detect problems when more than one process is trying to acquire the lock.
         * This solution is not perfect and needs a better implementation, e.g. by using SETNX/SET NX redis commands or
         * the Redlock algorithm. At the moment we cannot do this because the cache layer doesn't allow using the "NX"
         * flag while setting a key value and it's difficult to access the redis client object used by the cache object.
         *
         * To load test that race conditions are prevented you can scale up the php service to 5 replicas and run the
         * following command:
         * ```
         * ./develop console --tenant dev u2:main:unlock -n && seq 1 50 | parallel -j5 'n=$(( ({} - 1) % 5 + 1 )); docker exec u2-php-$n console --tenant dev u2:main:lock -n'
         * ```
         */
        usleep(random_int(100_000, 1_000_000));

        $item = $this->cache->getItem(self::cacheKey);
        $storedId = $item->get();
        if ($id !== $storedId) {
            throw new Exception('Failed to acquire a lock');
        }
    }

    public function isLocked(): bool
    {
        $item = $this->cache->getItem(self::cacheKey);

        return $item->isHit();
    }
}
