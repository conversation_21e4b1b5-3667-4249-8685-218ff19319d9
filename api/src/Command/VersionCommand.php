<?php

declare(strict_types=1);
namespace U2\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Prints version info.
 */
#[AsCommand(name: 'u2:version', description: 'Shows the version info')]
class VersionCommand extends Command
{
    public function __construct(private readonly string $version)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $output->writeln($this->version);

        return 0;
    }
}
