<?php

declare(strict_types=1);
namespace U2\Command\Xbrl;

use Symfony\Component\Console\Attribute\Argument;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Attribute\Option;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\OutputStyle;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use U2\Validator\XmlSchema;

#[AsCommand(name: 'u2:xbrl:validate', description: 'Validate a XBRL XML document')]
class ValidateCommand
{
    public function __construct(private readonly ValidatorInterface $validator, private readonly array $schemaVersionToXsdMap)
    {
    }

    protected function configure(): void
    {
        $schemaVersions = array_keys($this->schemaVersionToXsdMap);
        reset($schemaVersions);
    }

    public function __invoke(
        SymfonyStyle $io,
        #[Argument(description: 'A XBRL XML file to validate', name: 'xml-file')]
        string $xmlFile,
        #[Option(name: 'schema-version')]
        string $schemaVersion,
        OutputInterface $output,
    ): int {
        $xmlContent = file_get_contents($xmlFile);
        if (false === $xmlContent) {
            $io->error("Failed to load $xmlFile XML file");

            return 1;
        }

        $xsdFile = $this->schemaVersionToXsdMap[$schemaVersion] ?? null;
        if (null === $xsdFile) {
            $io->error(
                \sprintf(
                    'The "%s" schema version is not valid. Try one of: %s',
                    $schemaVersion,
                    implode(', ', array_keys($this->schemaVersionToXsdMap))
                )
            );

            return 2;
        }

        if (!$this->isXmlSchemaValid($io, $xmlContent, $xsdFile)) {
            $io->error("The $xmlFile XML file is not valid");

            return 3;
        }

        $io->success("The $xmlFile is valid");

        return 0;
    }

    /**
     * @throws \Exception
     */
    private function isXmlSchemaValid(OutputStyle $io, string $xmlContent, string $xsdFile): bool
    {
        $xmlDocument = new \DOMDocument();
        if (false === $xmlDocument->loadXML($xmlContent)) {
            $io->error('Failed to load XML');

            return false;
        }

        $violations = $this->validator->validate(
            $xmlDocument,
            new XmlSchema($xsdFile)
        );

        if (\count($violations) <= 0) {
            return true;
        }

        foreach ($violations as $violation) {
            $io->error((string) $violation->getMessage());
        }

        return false;
    }
}
