<?php

declare(strict_types=1);
namespace U2\Command\Database;

use Doctrine\DBAL\Connection;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Filesystem\Filesystem;

#[AsCommand(name: 'u2:database:dump-foreign-keys', description: 'Dumps a php file that will provide all foreign keys used in the database.')]
final class DumpForeignKeysCommand extends Command
{
    public const string className = 'ForeignKeysDumpProvider';

    public const string fileLocation = '/src/Command/Database';

    public const string methodName = 'get';

    public function __construct(private readonly Connection $connection, private readonly string $projectDir)
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        /** @var non-empty-array<int,array{TABLE_NAME: literal-string&non-falsy-string, REFERENCED_TABLE_NAME: literal-string&non-falsy-string, CONSTRAINT_NAME: literal-string&non-falsy-string}> $resultSet */
        $resultSet = $this->connection->fetchAllAssociative(
            \sprintf(
                "SELECT CONSTRAINT_NAME, TABLE_NAME, REFERENCED_TABLE_NAME FROM information_schema.REFERENTIAL_CONSTRAINTS WHERE CONSTRAINT_SCHEMA = '%s'",
                $this->connection->getDatabase()
            )
        );

        $data = "[\n";

        usort($resultSet, static fn (array $a, array $b): int => strcasecmp($a['CONSTRAINT_NAME'], $b['CONSTRAINT_NAME']));

        foreach ($resultSet as $result) {
            $data .= \sprintf(
                <<<'DATA'
                    '%s' => [
                        '%s' => %s,
                        '%s' => %s,
                    ],

                    DATA,
                $result['CONSTRAINT_NAME'],
                'TABLE_NAME',
                var_export($result['TABLE_NAME'], true),
                'REFERENCED_TABLE_NAME',
                var_export($result['REFERENCED_TABLE_NAME'], true),
            );
        }
        $data .= ']';

        $filesystem = new Filesystem();

        $filePath = \sprintf('%s%s/%s.php', $this->projectDir, self::fileLocation, self::className);
        $filesystem->dumpFile(
            $filePath,
            \sprintf(
                <<<'CLASS'
                    <?php

                    declare(strict_types=1);
                    namespace U2\Command\Database;

                    class %s
                    {
                        /**
                         * @return non-empty-array<literal-string&non-falsy-string, array{TABLE_NAME: literal-string&non-falsy-string, REFERENCED_TABLE_NAME: literal-string&non-falsy-string}>
                         */
                        public static function get(): array
                        {
                            return %s;
                        }
                    }
                    CLASS,
                self::className,
                $data
            )
        );

        $output->write("Foreign keys have been dumped to: $filePath");

        return 0;
    }
}
