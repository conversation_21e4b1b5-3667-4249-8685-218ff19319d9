<?php

declare(strict_types=1);
namespace U2\SystemMessage;

use U2\Util\AbstractConstantChoiceBag;

/**
 * @extends AbstractConstantChoiceBag<string>
 */
class SystemMessageTypes extends AbstractConstantChoiceBag
{
    public const string TYPE_INFO = 'INFO';

    public const string TYPE_WARNING = 'WARNING';

    public static function getReadableMap(): array
    {
        return [
            self::TYPE_INFO => 'u2.system_message.types.type_info',
            self::TYPE_WARNING => 'u2.system_message.types.type_warning',
        ];
    }
}
