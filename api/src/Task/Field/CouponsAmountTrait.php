<?php

declare(strict_types=1);
namespace U2\Task\Field;

use Doctrine\ORM\Mapping as ORM;
use U2\Entity\BaseLocalGroupMoney;
use U2\Validator as U2Assert;

trait CouponsAmountTrait
{
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[U2Assert\BaseLocalGroupRange(min: '0')]
    public ?BaseLocalGroupMoney $couponsAmount = null;

    public function getCouponsAmount(): ?BaseLocalGroupMoney
    {
        return $this->couponsAmount;
    }

    public function setCouponsAmount(?BaseLocalGroupMoney $couponsAmount): void
    {
        $this->couponsAmount = $couponsAmount;
    }
}
