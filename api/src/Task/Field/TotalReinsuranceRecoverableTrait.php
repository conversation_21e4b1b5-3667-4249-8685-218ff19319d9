<?php

declare(strict_types=1);
namespace U2\Task\Field;

use Doctrine\ORM\Mapping as ORM;
use U2\Entity\BaseLocalGroupMoney;

trait TotalReinsuranceRecoverableTrait
{
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    public ?BaseLocalGroupMoney $totalReinsuranceRecoverable = null;

    public function getTotalReinsuranceRecoverable(): ?BaseLocalGroupMoney
    {
        return $this->totalReinsuranceRecoverable;
    }

    public function setTotalReinsuranceRecoverable(?BaseLocalGroupMoney $totalReinsuranceRecoverable): void
    {
        $this->totalReinsuranceRecoverable = $totalReinsuranceRecoverable;
    }
}
