<?php

declare(strict_types=1);
namespace U2\Task\Field;

use Doctrine\ORM\Mapping as ORM;
use U2\Entity\BaseLocalGroupMoney;
use U2\Validator as U2Assert;

trait CurrentPeriodBookValueTrait
{
    #[ORM\ManyToOne(targetEntity: BaseLocalGroupMoney::class, cascade: ['persist'], fetch: 'EAGER')]
    #[U2Assert\BaseLocalGroupRange(min: '0')]
    public ?BaseLocalGroupMoney $currentPeriodBookValue = null;

    public function getCurrentPeriodBookValue(): ?BaseLocalGroupMoney
    {
        return $this->currentPeriodBookValue;
    }

    public function setCurrentPeriodBookValue(?BaseLocalGroupMoney $currentPeriodBookValue): void
    {
        $this->currentPeriodBookValue = $currentPeriodBookValue;
    }
}
