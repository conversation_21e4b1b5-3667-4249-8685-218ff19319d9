<?php

declare(strict_types=1);
namespace U2\Task\TableType;

use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;

class ReportingDateColumnAdder
{
    /**
     * @param ColumnDefinitionCollection<ColumnDefinition> $columnDefinitionCollection
     */
    public static function add(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        $columnDefinitionCollection
            ->add(
                'ReportingDate',
                'date',
                [
                    'filterable' => true,
                    'name' => 'Reporting Date',
                    'sortable' => true,
                ]
            );
    }
}
