<?php

declare(strict_types=1);
namespace U2\Task\TableType;

use U2\Entity\Task\TaskType;
use U2\Money\LinkedBaseLocalGroupMoneyInterface;
use U2\Table\View\Column\ColumnDefinitionCollection;
use U2\Task\Interfaces\Typeable;

abstract class ApmIgtTransactionTableType extends AbstractTaskTypeTableType
{
    protected function addTaskTypeFields(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        TypeColumnAdder::add($columnDefinitionCollection);

        UnitColumnAdder::add($columnDefinitionCollection);
        PartnerUnitColumnAdder::add($columnDefinitionCollection);
        PeriodColumnAdder::add($columnDefinitionCollection);
        TableTypeBaseLocalGroupMoneyColumnAdder::add($columnDefinitionCollection, static::getEntityClass());

        /** @var class-string<TaskType&LinkedBaseLocalGroupMoneyInterface&Typeable> $entityClass */
        $entityClass = static::getEntityClass();

        $moneyFields = $entityClass::getLinkedBaseLocalGroupMoneyFields();

        foreach ($entityClass::getAllFields() as $fieldName) {
            if (\in_array($fieldName, $moneyFields, true)) {
                continue;
            }

            $dataSourceFieldAdderClass = \sprintf("\U2\Task\TableType\%sColumnAdder", ucfirst($fieldName));
            $dataSourceFieldAdderClass::add($columnDefinitionCollection);
        }
    }

    /**
     * @return class-string<TaskType&Typeable>
     */
    abstract public static function getEntityClass(): string;
}
