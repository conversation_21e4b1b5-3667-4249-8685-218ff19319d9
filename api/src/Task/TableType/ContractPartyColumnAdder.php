<?php

declare(strict_types=1);
namespace U2\Task\TableType;

use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;

class ContractPartyColumnAdder
{
    /**
     * @param ColumnDefinitionCollection<ColumnDefinition> $columnDefinitionCollection
     */
    public static function add(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        $columnDefinitionCollection
            ->add(
                'ContractParty',
                null,
                [
                    'filterable' => true,
                    'name' => 'Contract Party',
                    'sortable' => true,
                    'selectedByDefault' => false,
                ]
            );
    }
}
