<?php

declare(strict_types=1);
namespace U2\Task\TableType;

use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;

class ValidityPeriodStartDateColumnAdder
{
    /**
     * @param ColumnDefinitionCollection<ColumnDefinition> $columnDefinitionCollection
     *
     * @throws \Exception
     */
    public static function add(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        $columnDefinitionCollection
            ->add(
                'ValidityPeriodStartDate',
                'date',
                [
                    'filterable' => true,
                    'name' => 'Validity Period Start Date',
                    'sortable' => true,
                    'selectedByDefault' => false,
                ]
            );
    }
}
