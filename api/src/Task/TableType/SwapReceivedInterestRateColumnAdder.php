<?php

declare(strict_types=1);
namespace U2\Task\TableType;

use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;

class SwapReceivedInterestRateColumnAdder
{
    /**
     * @param ColumnDefinitionCollection<ColumnDefinition> $columnDefinitionCollection
     */
    public static function add(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        $columnDefinitionCollection
            ->add(
                'SwapReceivedInterestRate',
                'percentage',
                [
                    'filterable' => true,
                    'name' => 'Swap Received Interest Rate (for Buyer)',
                    'sortable' => true,
                    'selectedByDefault' => false,
                ]
            );
    }
}
