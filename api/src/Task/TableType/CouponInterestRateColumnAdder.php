<?php

declare(strict_types=1);
namespace U2\Task\TableType;

use U2\Table\View\Column\ColumnDefinition;
use U2\Table\View\Column\ColumnDefinitionCollection;

class CouponInterestRateColumnAdder
{
    /**
     * @param ColumnDefinitionCollection<ColumnDefinition> $columnDefinitionCollection
     *
     * @throws \Exception
     */
    public static function add(ColumnDefinitionCollection $columnDefinitionCollection): void
    {
        $columnDefinitionCollection
            ->add(
                'CouponInterestRate',
                'percentage',
                [
                    'filterable' => true,
                    'name' => 'Coupon/Interest Rate',
                    'sortable' => true,
                    'selectedByDefault' => false,
                ]
            );
    }
}
