<?php

declare(strict_types=1);
namespace U2\Task\TaskType\UnitPeriod;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use U2\Entity\Period;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\Unit;

readonly class FindUnitPeriodInRequest
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private RequestStack $requestStack,
    ) {
    }

    public function __invoke(): UnitPeriod
    {
        $request = $this->requestStack->getCurrentRequest();
        \assert(null !== $request);

        $queryParameter = $request->query;
        if (
            (false === $queryParameter->has('unit_id') && false === $queryParameter->has('unit_refId'))
            || (null === $queryParameter->get('unit_id') && null === $queryParameter->get('unit_refId'))
            || ($queryParameter->has('unit_id') && $queryParameter->has('unit_refId'))
        ) {
            throw new BadRequestHttpException('Unit id or refId must be provided.');
        }

        if (
            (false === $queryParameter->has('period_id') && false === $queryParameter->has('period_name'))
            || (null === $queryParameter->get('period_id') && null === $queryParameter->get('period_name'))
            || ($queryParameter->has('period_id') && $queryParameter->has('period_name'))
        ) {
            throw new BadRequestHttpException('Period id or name must be provided.');
        }

        $hasUnitId = $queryParameter->has('period_id');
        $unit = $this->entityManager->getRepository(Unit::class)->findOneBy([
            $hasUnitId ? 'id' : 'refId' => $hasUnitId ? $queryParameter->get('unit_id') : $queryParameter->get('unit_refId'),
        ]);

        if (null === $unit) {
            throw new NotFoundHttpException('Unit for given id or refId not found.');
        }

        $hasPeriodId = $queryParameter->has('period_id');
        $period = $this->entityManager->getRepository(Period::class)->findOneBy([
            $hasPeriodId ? 'id' : 'name' => $hasPeriodId ? $queryParameter->get('period_id') : $queryParameter->get('period_name'),
        ]);

        if (null === $period) {
            throw new NotFoundHttpException('Period for given id or name not found.');
        }

        $unitPeriod = $this->entityManager->getRepository(UnitPeriod::class)->findOneBy([
            'unit' => $unit,
            'period' => $period,
        ]);

        if (null === $unitPeriod) {
            throw new NotFoundHttpException('UnitPeriod for given unit and period not found.');
        }

        return $unitPeriod;
    }
}
