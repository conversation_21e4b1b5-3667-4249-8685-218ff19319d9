<?php

declare(strict_types=1);
namespace U2\Task;

use U2\Entity\Task\TaskType;
use U2\Entity\Workflow\FieldConfigurationStatuses;
use U2\Entity\Workflow\FieldState;
use U2\Repository\WorkflowBindingRepository;

class FieldStateResolver
{
    public function __construct(
        private readonly WorkflowBindingRepository $workflowBindingRepository,
    ) {
    }

    public function isHidden(string $fieldName, TaskType $taskType): bool
    {
        $statusFieldConfig = $this->getFieldConfigForTaskStatus($taskType);
        if (null === $statusFieldConfig) {
            return false;
        }

        foreach ($statusFieldConfig->getFieldConfiguration()->getFieldStates() as $fieldState) {
            if ($fieldName === $fieldState->getField()) {
                return FieldState::hidden === $fieldState->getState();
            }
        }

        return false;
    }

    public function isDisabled(string $fieldName, TaskType $taskType): bool
    {
        $statusFieldConfig = $this->getFieldConfigForTaskStatus($taskType);
        if (null === $statusFieldConfig) {
            return false;
        }

        foreach ($statusFieldConfig->getFieldConfiguration()->getFieldStates() as $configuration) {
            if ($fieldName === $configuration->getField()) {
                return FieldState::disabled === $configuration->getState();
            }
        }

        return false;
    }

    private function getFieldConfigForTaskStatus(TaskType $taskType): ?FieldConfigurationStatuses
    {
        $workflowBinding = $this->workflowBindingRepository->findOneBy(['bindingId' => $taskType::getWorkflowBindingId()]);
        if (null === $workflowBinding) {
            return null;
        }

        $workflow = $workflowBinding->getWorkflow();

        foreach ($workflow->getFieldConfigurationStatuses() as $fieldConfigurationStatuses) {
            foreach ($fieldConfigurationStatuses->getStatuses() as $status) {
                if ($status === $taskType->getStatus()) {
                    return $fieldConfigurationStatuses;
                }
            }
        }

        return null;
    }
}
