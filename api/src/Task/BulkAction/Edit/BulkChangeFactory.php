<?php

declare(strict_types=1);
namespace U2\Task\BulkAction\Edit;

use U2\Entity\Task\TaskType;
use U2\Util\EntityPropertyMappingHelper;

class BulkChangeFactory
{
    public function __construct(
        private readonly ChangeFactory $changeFactory,
        private readonly EntityPropertyMappingHelper $propertyMappingInformation,
    ) {
    }

    /**
     * @param class-string<TaskType> $bluePrintClassName
     */
    public function create(string $bluePrintClassName): BulkChange
    {
        return new BulkChange(
            $bluePrintClassName,
            $this->propertyMappingInformation,
            new ChangeSet(),
            $this->changeFactory
        );
    }
}
