<?php

declare(strict_types=1);
namespace U2\Task\DataSource;

use U2\DataSourcery\DataSource\DataSourceBuilder;
use U2\Task\Field\ContractPartyTypes;

class ContractPartyFieldAdder
{
    public static function add(DataSourceBuilder $builder): void
    {
        $builder
            ->addField(
                'ContractParty',
                'string',
                'contractParty',
                [
                    'choices' => ContractPartyTypes::getReadableMap(),
                ]
            );
    }
}
