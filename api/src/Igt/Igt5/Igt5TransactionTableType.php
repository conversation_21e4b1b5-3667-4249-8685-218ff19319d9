<?php

declare(strict_types=1);
namespace U2\Igt\Igt5;

use U2\Entity\Task\TaskType\Igt5Transaction;
use U2\Task\TableType\ApmIgtTransactionTableType;

class Igt5TransactionTableType extends ApmIgtTransactionTableType
{
    public function getName(): string
    {
        return 'u2_intragrouptransactions_igt5_transactions';
    }

    public static function getEntityClass(): string
    {
        return Igt5Transaction::class;
    }
}
