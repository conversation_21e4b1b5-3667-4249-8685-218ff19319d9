<?php

declare(strict_types=1);
namespace U2\Igt\Igt3;

use U2\Entity\Task\TaskType\Igt3Transaction;
use U2\Task\TableType\ApmIgtTransactionTableType;

class Igt3TransactionTableType extends ApmIgtTransactionTableType
{
    public function getName(): string
    {
        return 'u2_intragrouptransactions_igt3_transactions';
    }

    public static function getEntityClass(): string
    {
        return Igt3Transaction::class;
    }
}
