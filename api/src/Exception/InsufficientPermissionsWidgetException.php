<?php

declare(strict_types=1);
namespace U2\Exception;

use U2\Widget\Dashboard\DashboardWidgetInterface;
use U2\Widget\Document\DocumentWidgetInterface;

class InsufficientPermissionsWidgetException extends WidgetException
{
    public function __construct(DashboardWidgetInterface|DocumentWidgetInterface $widget)
    {
        parent::__construct($widget, \sprintf('Insufficient permissions to view widget “%s”.', $widget->getName()));
    }
}
