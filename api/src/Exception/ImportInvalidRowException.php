<?php

declare(strict_types=1);
namespace U2\Exception;

use U2\Import\Interpreter\Field\FieldError;

class ImportInvalidRowException extends ImportInterpreterException
{
    /**
     * @param FieldError[] $errors
     */
    public function __construct(private readonly array $errors)
    {
        parent::__construct('Import row has errors');
    }

    /**
     * @return FieldError[]
     */
    public function getErrors(): array
    {
        return $this->errors;
    }
}
