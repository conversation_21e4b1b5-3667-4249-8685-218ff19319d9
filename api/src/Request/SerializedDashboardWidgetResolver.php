<?php

declare(strict_types=1);
namespace U2\Request;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Controller\ValueResolverInterface;
use Symfony\Component\HttpKernel\ControllerMetadata\ArgumentMetadata;
use U2\Widget\Dashboard\DashboardWidgetInterface;
use U2\Widget\Dashboard\DashboardWidgetSerializer;

class SerializedDashboardWidgetResolver implements ValueResolverInterface
{
    public function __construct(private readonly DashboardWidgetSerializer $widgetSerializer)
    {
    }

    /**
     * @return iterable<DashboardWidgetInterface>
     */
    public function resolve(Request $request, ArgumentMetadata $argument): iterable
    {
        $argumentType = $argument->getType();
        if (null === $argumentType || !is_a($argumentType, DashboardWidgetInterface::class, true)
        ) {
            return [];
        }

        return [$this->widgetSerializer->deserialize($request->query->get($argument->getName(), ''))];
    }
}
