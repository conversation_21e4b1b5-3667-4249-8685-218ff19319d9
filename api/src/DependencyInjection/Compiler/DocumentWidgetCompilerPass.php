<?php

declare(strict_types=1);
namespace U2\DependencyInjection\Compiler;

use Symfony\Component\DependencyInjection\Compiler\CompilerPassInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Reference;
use U2\Widget\Document\DocumentWidgetInterface;
use U2\Widget\Document\DocumentWidgetProvider;

class DocumentWidgetCompilerPass implements CompilerPassInterface
{
    public function process(ContainerBuilder $container): void
    {
        if (!$container->hasDefinition(DocumentWidgetProvider::class)) {
            return;
        }

        $documentWidgetRenderer = $container->getDefinition(DocumentWidgetProvider::class);

        foreach ($container->findTaggedServiceIds(DocumentWidgetInterface::class) as $id => $attributes) {
            $documentWidgetRenderer->addMethodCall('registerWidget', [new Reference($id)]);
        }
    }
}
