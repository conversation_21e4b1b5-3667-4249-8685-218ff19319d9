<?php

declare(strict_types=1);
namespace U2\Workflow\Action\Executor;

use Doctrine\ORM\EntityManagerInterface;
use JetBrains\PhpStorm\Pure;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use U2\Datasheets\Item\UnitValue\DefaultSetter;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\Workflow\Action\SetDefaultItemValuesAction;
use U2\Entity\Workflow\Transition;
use U2\Event\WorkflowPostTransitionEvent;

class SetDefaultItemValuesActionExecutor implements EventSubscriberInterface
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly DefaultSetter $defaultSetter,
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [WorkflowPostTransitionEvent::class => 'execute'];
    }

    public function execute(WorkflowPostTransitionEvent $event): void
    {
        $entity = $event->getEntity();
        if (!$entity instanceof UnitPeriod) {
            return;
        }

        $transition = $event->getTransition();
        if (false === $this->shouldReset($transition)) {
            return;
        }

        $previousLimit = (int) \ini_get('max_execution_time');
        set_time_limit(60);
        $this->defaultSetter->set($entity);
        $this->entityManager->flush();
        set_time_limit($previousLimit);
    }

    #[Pure]
    private function shouldReset(Transition $transition): bool
    {
        foreach ($transition->getActions() as $action) {
            if ($action instanceof SetDefaultItemValuesAction) {
                return true;
            }
        }

        return false;
    }
}
