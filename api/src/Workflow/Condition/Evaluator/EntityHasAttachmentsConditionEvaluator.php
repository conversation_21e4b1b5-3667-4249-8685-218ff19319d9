<?php

declare(strict_types=1);
namespace U2\Workflow\Condition\Evaluator;

use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\CountryByCountryReport;
use U2\Entity\Task\TaskType\LocalFile;
use U2\Entity\Task\TaskType\MasterFile;
use U2\Entity\Workflow\Condition\Condition;
use U2\Entity\Workflow\Condition\EntityHasAttachmentsCondition;
use U2\Exception\WorkflowConditionNotAllowedException;

class EntityHasAttachmentsConditionEvaluator implements ConditionEvaluatorInterface
{
    /**
     * @throws WorkflowConditionNotAllowedException
     */
    public function isAllowed(TaskType $entity, Condition $condition): bool
    {
        if (false === ($condition instanceof EntityHasAttachmentsCondition)) {
            throw new WorkflowConditionNotAllowedException($condition);
        }

        if (\in_array($entity::class, [MasterFile::class, LocalFile::class, CountryByCountryReport::class], true)) {
            return false;
        }

        return $entity->getTask()->getFiles()->count() >= $condition->getNumberOfAttachments();
    }
}
