<?php

declare(strict_types=1);
namespace U2\Workflow\Condition\Evaluator;

use U2\Entity\Task\TaskType;
use U2\Entity\Workflow\Condition\Condition;
use U2\Entity\Workflow\Condition\CurrentUserIsInGroupCondition;
use U2\Exception\Exception;
use U2\Exception\WorkflowConditionNotAllowedException;
use U2\User\CurrentUserProvider;

class CurrentUserIsInGroupConditionEvaluator implements ConditionEvaluatorInterface
{
    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
    ) {
    }

    /**
     * @throws WorkflowConditionNotAllowedException|Exception
     */
    public function isAllowed(TaskType $entity, Condition $condition): bool
    {
        if (false === $condition instanceof CurrentUserIsInGroupCondition) {
            throw new WorkflowConditionNotAllowedException($condition);
        }

        $groups = $this->currentUserProvider->get()->getGroups();

        foreach ($groups as $group) {
            foreach ($condition->getGroups() as $allowedGroup) {
                if ($allowedGroup === $group) {
                    return true;
                }
            }
        }

        return false;
    }
}
