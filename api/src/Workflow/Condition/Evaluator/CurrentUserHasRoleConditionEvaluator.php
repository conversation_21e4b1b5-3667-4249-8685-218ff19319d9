<?php

declare(strict_types=1);
namespace U2\Workflow\Condition\Evaluator;

use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use U2\Entity\Task\TaskType;
use U2\Entity\Workflow\Condition\Condition;
use U2\Entity\Workflow\Condition\CurrentUserHasRoleCondition;
use U2\Exception\Exception;
use U2\Exception\WorkflowConditionNotAllowedException;
use U2\User\CurrentUserProvider;

class CurrentUserHasRoleConditionEvaluator implements ConditionEvaluatorInterface
{
    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
        private readonly RoleHierarchyInterface $roleHierarchy,
    ) {
    }

    /**
     * @throws WorkflowConditionNotAllowedException|Exception
     */
    public function isAllowed(TaskType $entity, Condition $condition): bool
    {
        if (!$condition instanceof CurrentUserHasRoleCondition) {
            throw new WorkflowConditionNotAllowedException($condition);
        }

        $roles = $this->roleHierarchy->getReachableRoleNames(
            $this->currentUserProvider->get()->getRoles()
        );

        return $condition->areUserRolesAllowed($roles);
    }
}
