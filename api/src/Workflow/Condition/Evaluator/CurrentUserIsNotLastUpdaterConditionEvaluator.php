<?php

declare(strict_types=1);
namespace U2\Workflow\Condition\Evaluator;

use U2\Entity\Task\TaskType;
use U2\Entity\Workflow\Condition\Condition;
use U2\Entity\Workflow\Condition\CurrentUserIsNotLastUpdaterCondition;
use U2\Exception\Exception;
use U2\Exception\WorkflowConditionNotAllowedException;
use U2\User\CurrentUserProvider;

class CurrentUserIsNotLastUpdaterConditionEvaluator implements ConditionEvaluatorInterface
{
    public function __construct(
        private readonly CurrentUserProvider $currentUserProvider,
    ) {
    }

    /**
     * @throws WorkflowConditionNotAllowedException|Exception
     */
    public function isAllowed(TaskType $entity, Condition $condition): bool
    {
        if (false === $condition instanceof CurrentUserIsNotLastUpdaterCondition) {
            throw new WorkflowConditionNotAllowedException($condition);
        }

        return $this->currentUserProvider->get() !== $entity->getTask()->getUpdatedBy();
    }
}
