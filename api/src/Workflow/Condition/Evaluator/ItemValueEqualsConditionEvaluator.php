<?php

declare(strict_types=1);
namespace U2\Workflow\Condition\Evaluator;

use U2\Datasheets\Item\ItemTypes;
use U2\Entity\CheckboxItemUnitValue;
use U2\Entity\DiffItemUnitValue;
use U2\Entity\MoneyItemUnitValue;
use U2\Entity\NumberItemUnitValue;
use U2\Entity\PercentItemUnitValue;
use U2\Entity\Task\TaskType;
use U2\Entity\Task\TaskType\UnitPeriod;
use U2\Entity\TextItemUnitValue;
use U2\Entity\Workflow\Condition\Condition;
use U2\Entity\Workflow\Condition\ItemValueEqualsCondition;
use U2\Exception\WorkflowConditionNotAllowedException;
use U2\Repository\ItemUnitValueRepository;

class ItemValueEqualsConditionEvaluator implements ConditionEvaluatorInterface
{
    public function __construct(
        private readonly ItemUnitValueRepository $itemUnitValueRepository,
    ) {
    }

    /**
     * @throws WorkflowConditionNotAllowedException
     */
    public function isAllowed(TaskType $entity, Condition $condition): bool
    {
        if (false === $condition instanceof ItemValueEqualsCondition) {
            throw new WorkflowConditionNotAllowedException($condition);
        }

        if (!$entity instanceof UnitPeriod) {
            return true;
        }

        $item = $condition->getItem();
        $unit = $entity->getUnit();
        \assert(null !== $unit);
        $period = $entity->getPeriod();
        \assert(null !== $period);

        $itemUnitValue = $this->itemUnitValueRepository->findOneBy(['item' => $item, 'unit' => $unit, 'period' => $period]);
        if (null === $itemUnitValue) {
            $itemUnitValue = match ($item->getType()) {
                ItemTypes::CHECKBOX => new CheckboxItemUnitValue($item, $unit, $period),
                ItemTypes::DIFF => new DiffItemUnitValue($item, $unit, $period),
                ItemTypes::MONEY => new MoneyItemUnitValue($item, $unit, $period),
                ItemTypes::NUMBER => new NumberItemUnitValue($item, $unit, $period),
                ItemTypes::PERCENT => new PercentItemUnitValue($item, $unit, $period),
                ItemTypes::TEXT => new TextItemUnitValue($item, $unit, $period),
                default => throw new \UnexpectedValueException('Unknown item type: ' . $item->getType()),
            };
        }

        return match ($itemUnitValue::class) {
            CheckboxItemUnitValue::class => $itemUnitValue->getValue() === (bool) $condition->getValue(),
            DiffItemUnitValue::class => 0 === bccomp($itemUnitValue->getValue(), $condition->getValue(), DiffItemUnitValue::DECIMAL_PLACES),
            MoneyItemUnitValue::class => 0 === bccomp($itemUnitValue->getValue(), $condition->getValue(), MoneyItemUnitValue::DECIMAL_PLACES),
            NumberItemUnitValue::class => 0 === bccomp($itemUnitValue->getValue(), $condition->getValue(), NumberItemUnitValue::DECIMAL_PLACES),
            PercentItemUnitValue::class => 0 === bccomp(bcmul($itemUnitValue->getValue(), '100', 10), $condition->getValue(), PercentItemUnitValue::DECIMAL_PLACES),
            TextItemUnitValue::class => trim($itemUnitValue->getValue()) === trim($condition->getValue()),
            default => throw new \UnexpectedValueException('Unknown item type'),
        };
    }
}
