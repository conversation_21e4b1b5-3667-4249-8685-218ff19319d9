<?php

declare(strict_types=1);
namespace U2\Workflow\Condition;

use <PERSON>ymfony\Component\Serializer\Attribute\Groups;

class CurrentUserIsUserConditionTypeParameter implements ConditionTypeParameter
{
    #[Groups(groups: ['condition_type:read'])]
    public static function getType(): string
    {
        return 'array';
    }

    #[Groups(groups: ['condition_type:read'])]
    public static function getProperty(): string
    {
        return 'users';
    }

    #[Groups(groups: ['condition_type:read'])]
    public static function getAllowedValues(): array
    {
        return [
            'Users' => 'Array of user IDs that will validate as allowed.',
        ];
    }
}
