<?php

declare(strict_types=1);
namespace U2\Api\Processor;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProcessorInterface;
use ApiPlatform\Validator\Exception\ValidationException;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationList;
use Symfony\Contracts\Translation\TranslatorInterface;
use U2\Api\Resource\UnitHierarchyStructure;
use U2\Exception\UnitHierarchyStructureRemoveUnitException;
use U2\Repository\UnitHierarchyRepository;
use U2\Unit\Hierarchy\SnapshotToJsTreeTransformer;
use U2\Unit\Hierarchy\UnitHierarchyManager;
use U2\Unit\Hierarchy\UnitHierarchyStructureFactory;

/**
 * @implements  ProcessorInterface<UnitHierarchyStructure, UnitHierarchyStructure>
 */
readonly class UnitHierarchyStructureProcessor implements ProcessorInterface
{
    public function __construct(
        private UnitHierarchyRepository $unitHierarchyRepository,
        private UnitHierarchyManager $unitHierarchyManager,
        private SnapshotToJsTreeTransformer $snapshotToJsTreeTransformer,
        private RequestStack $requestStack,
        private TranslatorInterface $translator,
        private UnitHierarchyStructureFactory $unitHierarchyStructureFactory,
    ) {
    }

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): UnitHierarchyStructure
    {
        \assert(\array_key_exists('id', $uriVariables));
        \assert(\array_key_exists('date', $uriVariables));

        /** @var int $id */
        $id = $uriVariables['id'];
        $unitHierarchy = $this->unitHierarchyRepository->find($id);
        if (null === $unitHierarchy) {
            throw new \RuntimeException(\sprintf('Unable to find unit hierarchy with id "%d"', $id));
        }

        $request = $this->requestStack->getCurrentRequest();
        \assert(null !== $request);
        $requestData = json_decode($request->getContent(), true, 512, \JSON_THROW_ON_ERROR);
        if (\is_array($requestData) && !\array_key_exists('tree', $requestData)) {
            throw new BadRequestHttpException('Missing required property "tree"');
        }

        $snapshot = $this->snapshotToJsTreeTransformer->reverseTransform($request->toArray()['tree']);

        try {
            $this->unitHierarchyManager->saveSnapshot($unitHierarchy, $snapshot, $data->date);
        } catch (UnitHierarchyStructureRemoveUnitException $e) {
            throw new ValidationException(new ConstraintViolationList([new ConstraintViolation(message: $this->translator->trans('unit_hierarchy.structure.remove_unit_exception', ['%unitRefId%' => $e->unit->getRefIdAndName(), '%futureChangeDate%' => $e->date->format('Y-m-d')], 'validators'), messageTemplate: null, parameters: [], root: null, propertyPath: 'tree', invalidValue: null)]));
        }

        return $this->unitHierarchyStructureFactory->create($unitHierarchy, $data->date);
    }
}
