<?php

declare(strict_types=1);
namespace U2\Api\Extension;

use ApiPlatform\Doctrine\Orm\Extension\QueryCollectionExtensionInterface;
use ApiPlatform\Doctrine\Orm\Util\QueryNameGeneratorInterface;
use ApiPlatform\Metadata\Operation;
use Doctrine\ORM\QueryBuilder;
use Symfony\Component\Security\Acl\Permission\MaskBuilder;
use U2\Entity\File;
use U2\Security\Permissions\Assignable\QueryBuilderHelper;
use U2\User\CurrentUserProvider;

readonly class FileFilterForCurrentUserExtension implements QueryCollectionExtensionInterface
{
    public function __construct(
        private CurrentUserProvider $currentUserProvider,
        private QueryBuilderHelper $queryBuilderHelper,
    ) {
    }

    /**
     * @param array<mixed> $context
     */
    public function applyToCollection(QueryBuilder $queryBuilder, QueryNameGeneratorInterface $queryNameGenerator, string $resourceClass, ?Operation $operation = null, array $context = []): void
    {
        if (!is_a($resourceClass, File::class, true) || !$this->currentUserProvider->hasUser()) {
            return;
        }

        /** @var literal-string $rootAlias */
        $rootAlias = $queryBuilder->getRootAliases()[0];

        $this->queryBuilderHelper->addSecurityCondition(
            $queryBuilder,
            $rootAlias,
            MaskBuilder::MASK_VIEW,
            [
                $queryBuilder->expr()->eq("$rootAlias.accessType", ':public'),
            ]
        );
        $queryBuilder->setParameter(':public', File::PUBLIC_ACCESS);
    }
}
