<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Security\Core\Role\RoleHierarchyInterface;
use U2\Api\Resource\Role;
use U2\Security\UserRoles;

/**
 * @implements ProviderInterface<Role>
 */
readonly class RoleItemProvider implements ProviderInterface
{
    public function __construct(
        private RoleHierarchyInterface $roleHierarchy,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): Role
    {
        \assert(isset($uriVariables['name']) && \is_string($uriVariables['name']));
        if (!\in_array($uriVariables['name'], $this->roleHierarchy->getReachableRoleNames([UserRoles::Admin->value]), true)) {
            throw new NotFoundHttpException(\sprintf('Role with name "%s" not found', $uriVariables['name']));
        }

        return new Role($uriVariables['name']);
    }
}
