<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use U2\Entity\User;
use U2\Repository\SavedFilterSubscriptionRepository;

/**
 * @implements ProviderInterface<User>
 */
readonly class SavedFilterSubscriptionInheritedUsersProvider implements ProviderInterface
{
    public function __construct(
        private SavedFilterSubscriptionRepository $savedFilterSubscriptionRepository,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     *
     * @return array<int,User>
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        \assert(isset($uriVariables['id']) && \is_int($uriVariables['id']));
        $savedFilterSubscription = $this->savedFilterSubscriptionRepository->find($uriVariables['id']);
        if (null === $savedFilterSubscription) {
            throw new NotFoundHttpException(\sprintf('Saved Filter Subscription with id "%s" not found', $uriVariables['id']));
        }

        return $savedFilterSubscription->getInheritedUsers();
    }
}
