<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\Uid\UuidV4;
use U2\Entity\User;
use U2\Repository\Task\TaskRepository;
use U2\Repository\UserRepository;
use U2\Security\Authorization\AssignmentHelper;
use U2\Task\TaskTypeKnowledge;
use U2\Task\TaskTypeResolver;

/**
 * @implements ProviderInterface<User>
 */
readonly class PermittedUsersProvider implements ProviderInterface
{
    public function __construct(
        private TaskRepository $taskRepository,
        private TaskTypeResolver $taskTypeResolver,
        private AssignmentHelper $authorizationHelper,
        private UserRepository $userRepository,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     *
     * @return array<int,User>
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        \assert(isset($uriVariables['id']) && $uriVariables['id'] instanceof UuidV4);

        $task = $this->taskRepository->find($uriVariables['id']);
        if (null === $task) {
            throw new NotFoundHttpException();
        }

        $taskType = $this->taskTypeResolver->resolve($task);
        $usersAllowedByAuthorization = $this->userRepository->findAssignedToAuthorizationItem(
            TaskTypeKnowledge::taskTypeClassToAuthorizationItem[$taskType::class]
        );

        $allowedUsers = [];
        foreach ($usersAllowedByAuthorization as $user) {
            if ($this->authorizationHelper->canTaskBeAssignedToUser($task, $user)) {
                $allowedUsers[] = $user;
            }
        }

        return $allowedUsers;
    }
}
