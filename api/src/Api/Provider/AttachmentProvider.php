<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\IriConverterInterface;
use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use U2\Entity\File;
use U2\Entity\Interfaces\FileAttachable;
use U2\File\Attachment;
use U2\File\AttachmentFactory;
use U2\Repository\FileRepository;

/**
 * @implements ProviderInterface<Attachment>
 */
readonly class AttachmentProvider implements ProviderInterface
{
    public function __construct(
        private IriConverterInterface $iriConverter,
        private FileRepository $fileRepository,
        private AttachmentFactory $attachmentFactory,
    ) {
    }

    public function provide(Operation $operation, array $uriVariables = [], array $context = []): Attachment
    {
        \assert(\array_key_exists('id', $uriVariables) && \array_key_exists('request_uri', $context));

        /** @var File $file */
        $file = $this->fileRepository->find($uriVariables['id']);

        /** @var string $iri */
        $iri = $context['request_uri'];
        $length = strpos($iri, '/attachments');
        \assert(false !== $length);

        /** @var FileAttachable $fileAttachable */
        $fileAttachable = $this->iriConverter->getResourceFromIri(substr($iri, 0, $length));

        return $this->attachmentFactory->create($file, $fileAttachable);
    }
}
