<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use U2\Entity\SavedFilter;
use U2\Entity\User;
use U2\Repository\SavedFilterRepository;

/**
 * @implements ProviderInterface<User>
 */
readonly class SavedFilterInheritedUsersProvider implements ProviderInterface
{
    public function __construct(
        private SavedFilterRepository $savedFilterRepository,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     *
     * @return array<int, User>
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        \assert(isset($uriVariables['id']) && \is_int($uriVariables['id']));
        $savedFilter = $this->savedFilterRepository->find($uriVariables['id']);
        if (!($savedFilter instanceof SavedFilter)) {
            throw new NotFoundHttpException(\sprintf('Saved Filter Subscription with id "%s" not found', $uriVariables['id']));
        }

        return $savedFilter->getInheritedUsers();
    }
}
