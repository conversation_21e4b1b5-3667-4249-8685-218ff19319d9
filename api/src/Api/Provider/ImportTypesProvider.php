<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\Security\Core\Authorization\AuthorizationCheckerInterface;
use U2\Api\Resource\ImportType;
use U2\EntityMetadata\Entity\ReadableNameTranslator;
use U2\EntityMetadata\MetadataProvider;
use U2\Import\Configuration\Collection\ConfigurationCollection;
use U2\Import\ConfigurationClassExtractor;
use U2\Module\GroupResolver;
use U2\Module\ModuleStatusChecker;
use U2\Security\Voter\VoterAttributes;
use U2\Util\StringManipulator;

/**
 * @implements ProviderInterface<ImportType>
 */
readonly class ImportTypesProvider implements ProviderInterface
{
    public function __construct(
        private MetadataProvider $metadataProvider,
        private GroupResolver $groupResolver,
        private ReadableNameTranslator $nameTranslator,
        private AuthorizationCheckerInterface $authorizationChecker,
        private ConfigurationClassExtractor $configurationClassExtractor,
        private ModuleStatusChecker $enabledStatusChecker,
        private ConfigurationCollection $configurationCollection,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     *
     * @return array<ImportType>
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        $classes = $this->configurationClassExtractor->getClasses();
        $authorizedClasses = array_filter(
            $classes,
            fn (string $class): bool => $this->enabledStatusChecker->isEnabledForEntity($class)
                && $this->authorizationChecker->isGranted(VoterAttributes::import, $class)
        );

        ksort($authorizedClasses);

        return array_map(
            function (string $class): ImportType {
                $shortName = $this->metadataProvider->getShortName($class);

                return new ImportType(
                    $shortName,
                    $this->groupResolver->getGroup($class),
                    $this->nameTranslator->translateClass($class),
                    $this->configurationCollection->get(
                        StringManipulator::dashSeparatedToUnderscoreSeparated($shortName)
                    )
                );
            }, $authorizedClasses);
    }
}
