<?php

declare(strict_types=1);
namespace U2\Api\Provider;

use ApiPlatform\Metadata\Operation;
use ApiPlatform\State\ProviderInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use U2\Api\Resource\Role;
use U2\Entity\UserGroup;
use U2\Repository\UserGroupRepository;

/**
 * @implements ProviderInterface<Role>
 */
readonly class UserGroupRolesProvider implements ProviderInterface
{
    public function __construct(
        private UserGroupRepository $userGroupRepository,
    ) {
    }

    /**
     * @param array<string, mixed> $uriVariables
     * @param array<string, mixed> $context
     *
     * @return array<Role>
     */
    public function provide(Operation $operation, array $uriVariables = [], array $context = []): array
    {
        \assert(isset($uriVariables['id']) && \is_int($uriVariables['id']));
        $userGroup = $this->userGroupRepository->find($uriVariables['id']);
        if (!($userGroup instanceof UserGroup)) {
            throw new NotFoundHttpException(\sprintf('User Group with id "%s" not found', $uriVariables['id']));
        }

        return array_map(
            static fn (string $role): Role => new Role($role),
            $userGroup->getRoles()
        );
    }
}
