<?php

declare(strict_types=1);
namespace U2\Api\Resource;

use ApiPlatform\Metadata\ApiProperty;
use ApiPlatform\Metadata\ApiResource;
use ApiPlatform\Metadata\Get;
use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Api\Provider\DocumentTemplateSectionRenderedContentProvider;
use U2\Api\Provider\DocumentTemplateSectionsRenderedContentsProvider;
use U2\Entity\DocumentSection;
use U2\Security\Voter\DocumentVoterAttributes;

#[ApiResource(
    operations: [
        new Get(
            uriTemplate: '/document-templates/{documentTemplateId}/rendered-sections/{sectionId}',
            uriVariables: [
                'documentTemplateId' => new Link(fromClass: self::class, identifiers: ['documentTemplateId'], compositeIdentifier: false),
                'sectionId' => new Link(fromClass: self::class, identifiers: ['sectionId'], compositeIdentifier: false),
            ],
            paginationEnabled: false,
            paginationClientEnabled: false,
            security: 'is_granted("' . DocumentVoterAttributes::viewContent . '", findEntity("U2\\\\Entity\\\\DocumentTemplate", request.get("documentTemplateId")))',
            provider: DocumentTemplateSectionRenderedContentProvider::class,
        ),
        new GetCollection(
            uriTemplate: '/document-templates/{documentTemplateId}/rendered-sections',
            uriVariables: [
                'documentTemplateId' => new Link(fromClass: self::class, identifiers: ['documentTemplateId'], compositeIdentifier: false),
            ],
            paginationEnabled: false,
            paginationClientEnabled: false,
            security: 'is_granted("' . DocumentVoterAttributes::viewContent . '", findEntity("U2\\\\Entity\\\\DocumentTemplate", request.get("documentTemplateId")))',
            provider: DocumentTemplateSectionsRenderedContentsProvider::class,
        ),
    ],
    normalizationContext: ['groups' => ['document-template-section-content:read']],
)]
readonly class DocumentTemplateSectionContent
{
    #[Groups(['document-template-section-content:read'])]
    #[ApiProperty(identifier: true)]
    public int $sectionId;

    #[Groups(['document-template-section-content:read'])]
    #[ApiProperty(identifier: true)]
    public int $documentTemplateId;

    public function __construct(
        #[Groups(['document-template-section-content:read'])]
        public string $content,
        public DocumentSection $section,
    ) {
        $this->sectionId = $section->getId();

        $structuredDocument = $section->getDocument();
        \assert(null !== $structuredDocument && null !== $structuredDocument->getId());
        $this->documentTemplateId = $structuredDocument->getId();
    }
}
