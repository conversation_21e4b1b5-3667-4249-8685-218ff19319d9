<?php

declare(strict_types=1);
namespace U2\Api\Resource;

use ApiPlatform\Metadata\GetCollection;
use ApiPlatform\Metadata\Link;
use ApiPlatform\OpenApi\Model\Operation;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Api\Provider\FileLinkedEntitiesProvider;
use U2\Entity\DocumentSection;
use U2\Entity\File;
use U2\Entity\Task\Task;
use U2\Entity\Unit;
use U2\Security\Voter\VoterAttributes;

#[GetCollection(
    uriTemplate: '/files/{id}/linked-entities',
    uriVariables: [
        'id' => new Link(fromClass: File::class, identifiers: ['id']),
    ],
    openapi: new Operation(description: 'Lists all records that are linked to a file.'),
    normalizationContext: ['groups' => ['file-linked-entity:read']],
    security: 'is_granted("' . VoterAttributes::read . '", findEntity("U2\\\\Entity\\\\File", request.get("id")))',
    provider: FileLinkedEntitiesProvider::class
)]
readonly class FileLinkedEntity
{
    public function __construct(
        #[Groups(groups: ['file-linked-entity:read'])]
        public File $file,
        #[Groups(groups: ['file-linked-entity:read'])]
        public Unit|Task|DocumentSection|null $linkedEntity,
    ) {
    }
}
