<?php

declare(strict_types=1);
namespace U2\Api\Resource;

use ApiPlatform\Metadata\Get;
use JetBrains\PhpStorm\Deprecated;
use Symfony\Component\Serializer\Attribute\Groups;
use U2\Api\Provider\ItemUnitHierarchyValueProvider;
use U2\Entity\AuthorizationItem;
use U2\Entity\Item;
use U2\Entity\Period;
use U2\Entity\UnitHierarchy;
use U2\Security\Authorization\AuthorizationRight;

#[Get(
    uriTemplate: '/item-unit-hierarchy-values/{id}',
    openapi: false,
    normalizationContext: ['groups' => ['item-unit-hierarchy-value:read']],
    security: 'is_granted("' . AuthorizationItem::UnitPeriod->value . ':' . AuthorizationRight::READ->value . '")',
    provider: ItemUnitHierarchyValueProvider::class,
)]
class TextItemUnitHierarchyValue extends ItemUnitHierarchyValue
{
    public function __construct(
        Item $item,
        UnitHierarchy $unitHierarchy,
        Period $period,
        private readonly ?string $comment = null,
    ) {
        parent::__construct($item, $unitHierarchy, $period);
    }

    #[Deprecated(reason: 'Use "getValue" instead.')]
    #[Groups(groups: ['item-unit-hierarchy-value:read'])]
    public function getComment(): ?string
    {
        return $this->comment;
    }

    #[Groups(groups: ['item-unit-hierarchy-value:read'])]
    public function getValue(): ?string
    {
        return $this->comment;
    }
}
