<?php

declare(strict_types=1);
namespace U2\ExpressionLanguage;

use Symfony\Component\ExpressionLanguage\ExpressionFunction;
use Symfony\Component\ExpressionLanguage\ExpressionFunctionProviderInterface;

class NativePhpFunctionProvider implements ExpressionFunctionProviderInterface
{
    /**
     * @return array<int,ExpressionFunction>
     */
    public function getFunctions(): array
    {
        return [
            new ExpressionFunction(
                'strtotime',
                static fn ($value): string => \sprintf('strtotime(%1$s)', $value),
                static fn ($arguments, $str): int => strtotime($str)
            ),
            new ExpressionFunction(
                'time',
                static fn (): string => 'time()',
                static fn ($arguments): int => time()
            ),
        ];
    }
}
