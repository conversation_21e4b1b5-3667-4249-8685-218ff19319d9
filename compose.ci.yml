services:
  web:
    image: universalunits/u2-nginx:${GIT_COMMIT_SHORT}
    networks:
      default:
        aliases:
          - test.u2.web
    depends_on:
      - php

  php:
    image: universalunits/u2-php-dev:${GIT_COMMIT_SHORT}
    depends_on:
      - redis
      - db
    environment:
      - APP_ENV
    volumes:
      - type: volume
        source: behat_fixtures
        target: /app/api/tests/behat/fixtures
      - type: volume
        source: logs
        target: /app/api/var/logs

  php_consumer:
    image: universalunits/u2-php-dev:${GIT_COMMIT_SHORT}
    command: supervisord -c /etc/supervisord.conf
    depends_on:
      - redis
      - db
    environment:
      - APP_ENV
    volumes:
      - type: volume
        source: logs
        target: /app/api/var/logs

  db:
    image: mysql:8.0.36
    command: --sql_mode=0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: u2_test
      MYSQL_USER: u2
      MYSQL_PASSWORD: u2

  redis:
    image: redis:6.2

  selenium:
    image: selenium/standalone-chromium:136.0
    #image: selenium/standalone-firefox:latest
    #shm_size: 2gb
    #mem_reservation: 2gb
    volumes:
      - type: volume
        source: behat_fixtures
        target: /app/api/tests/behat/fixtures
        read_only: true
    environment:
      - START_XVFB=false
      - SE_START_XVFB=false
volumes:
  behat_fixtures:
  logs:
    name: $LOGS_VOLUME
    external: true
