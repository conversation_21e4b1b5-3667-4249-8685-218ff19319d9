# Migrations 

Migrations are very sensitive and prone to data loss. Please ensure they are
tested to avoid accidental data loss or getting db's stuck in maintenance mode.

- Create small, atomic migrations that reduce the possibility of errors. If you need to add 5 tables, it may be worth 
  adding them in individual migrations.
  
- Do not run DDL statements in the same migration step as data changes.
  I.e. Do not create a table and add data at the same time. DDL statements are not part of a transaction
  and cannot be rolled back. 

- Do not make changes outside the normal migration process. I.e. use `$this->addSql` etc.
  Using doctrine services directly to execute statements etc will mean they are not processed with the main migration 
  transaction and cannot be rolled back together.
  
- Ensure there are phpunit tests for all migrations.
  If they use a real db they should inherit \Doctrine\Migrations\AbstractMigration and use the annotations:
  ```
  @group migration
  @runTestsInSeparateProcesses
  ```

Due to the way that migrations run, it is recommended to organise your migrations
using the following order. Using the comments as headers makes this clearer:

```PHP
        // REMOVE DATA

        // REMOVE CONSTRAINTS

        // REMOVE UNIQUE INDEXES

        // REMOVE INDEXES

        // CREATE TABLES

        // ADD FIELDS

        // CHANGE FIELDS

        // RENAME TABLES

        // MIGRATE DATA

        // DROP FIELDS

        // DROP TABLES

        // ADD INDEXES

        // ADD UNIQUE INDEXES

        // ADD CONSTRAINTS
```
