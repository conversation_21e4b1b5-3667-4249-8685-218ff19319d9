services:
  web:
    image: universalunits/u2-nginx
    networks:
      default:
        aliases:
          - test.u2.web
    build:
      context: .
      target: u2_nginx
    depends_on:
      - php
    ports:
      - ${NGINX_SSH_PORT:-443}:443
    volumes:
      - type: bind
        source: ./docker/nginx/conf.d/shared/location.dev.conf
        target: /etc/nginx/conf.d/shared/location.conf
        read_only: true
      - type: bind
        source: ./api/public
        target: /app/api/public
        read_only: true
      - type: bind
        source: ./client/public/build
        target: /app/client
        read_only: true
      - type: bind
        source: ${DOMAIN_CERT:-./certs/u2.localhost.crt}
        target: /certs/domain.crt
        read_only: true
      - type: bind
        source: ${DOMAIN_CERT_KEY:-./certs/u2.localhost.key}
        target: /certs/domain.key
        read_only: true

  php:
    image: universalunits/u2-php-dev
    scale: 1
    build:
      context: .
      target: u2_php_dev
    depends_on:
      - redis
      - db
    environment:
      - COMPOSER_AUTH
      - XDEBUG_CONFIG
      - XDEBUG_MODE
      - AWS_ACCESS_KEY_ID
      - AWS_SECRET_ACCESS_KEY
    init: true
    volumes:
      - type: bind
        source: ./api
        target: /app/api
      - type: bind
        source: ./client/assets/icons
        target: /app/client/assets/icons
        read_only: true
      - type: bind
        source: ./client/src
        target: /app/client/src
        read_only: true
      - type: volume
        source: u2-var
        target: /app/api/var

  php_consumer:
    image: universalunits/u2-php-dev
    command: supervisord -c /etc/supervisord.conf
    depends_on:
      - redis
      - db
    volumes:
      - type: bind
        source: ./api
        target: /app/api
      - type: volume
        source: u2-var
        target: /app/api/var

  node:
    image: universalunits/u2-node
    build:
      context: .
      target: u2_node
    ports:
      - "3000:3000"
    environment:
      - BASE_URL=https://${APP_HOSTNAME:-u2.localhost}:8080
      - TAILWIND_MODE=watch
    volumes:
      - type: volume
        source: u2-node-modules
        target: /app/client/node_modules
      - type: bind
        source: ./client
        target: /app/client
      - type: bind
        source: ${DOMAIN_CERT:-./certs/u2.localhost.crt}
        target: /app/certs/domain.crt
        read_only: true
      - type: bind
        source: ${DOMAIN_CERT_KEY:-./certs/u2.localhost.key}
        target: /app/certs/domain.key
        read_only: true
      - type: bind
        source: ./api/templates
        target: /app/api/templates
      - type: bind
        source: ./api/shared
        target: /app/api/shared
        read_only: true
      - type: volume
        source: u2-var
        target: /app/client/var
    command: pnpm dev-server
    # If you want to run the app with built assets then use this command instead (dont forget to build the assets first):
    # command: vite preview --port 3000

  db:
    image: mysql:${DATABASE_VERSION}
    command: --sql_mode=0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: u2_dev
      MYSQL_USER: u2
      MYSQL_PASSWORD: u2
    ports:
      - ${DB_PORT:-3306}:3306
    volumes:
      - type: volume
        source: u2-db
        target: /var/lib/mysql
      - type: bind
        source: ./docker/mysql
        target: /docker-entrypoint-initdb.d
        read_only: true
      - type: bind
        source: ./api/tests/phpunit/migration
        target: /migrations

  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
    volumes:
      - type: volume
        source: u2-redis
        target: /data

  mailer:
    image: schickling/mailcatcher
    ports:
      - "1025:1025"
      - "1080:1080"

volumes:
  u2-db:
  u2-node-modules:
  u2-redis:
  u2-var:

